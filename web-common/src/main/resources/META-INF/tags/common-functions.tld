<?xml version="1.0" encoding="UTF-8"?>
<taglib xmlns="http://java.sun.com/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-jsptaglibrary_2_1.xsd"
        version="2.1">

    <tlib-version>1.0</tlib-version>
    <short-name>CommonFunctions</short-name>
    <uri>http://www.gumtree.com/common/functions</uri>

    <function>
        <name>esc</name>
        <function-class>org.springframework.web.util.HtmlUtils</function-class>
        <function-signature>java.lang.String htmlEscape(java.lang.String)</function-signature>
    </function>

    <function>
        <name>jsesc</name>
        <function-class>org.springframework.web.util.JavaScriptUtils</function-class>
        <function-signature>java.lang.String javaScriptEscape(java.lang.String)</function-signature>
    </function>

    <function>
        <name>renderList</name>
        <function-class>com.gumtree.web.common.functions.Functions</function-class>
        <function-signature>java.lang.String renderList(java.util.List,java.lang.String)</function-signature>
    </function>

    <function>
        <name>renderListWithStrongFinalItem</name>
        <function-class>com.gumtree.web.common.functions.Functions</function-class>
        <function-signature>java.lang.String renderListWithStrongFinalItem(java.util.List,java.lang.String)
        </function-signature>
    </function>

    <function>
        <name>contains</name>
        <function-class>com.gumtree.web.common.functions.Functions</function-class>
        <function-signature>boolean contains(java.util.Collection,java.lang.Object)</function-signature>
    </function>

    <function>
        <name>categoryIds</name>
        <function-class>com.gumtree.web.common.functions.Functions</function-class>
        <function-signature>java.util.List categoryIds(java.lang.String)</function-signature>
    </function>

    <function>
        <name>priceFrequency</name>
        <function-class>com.gumtree.web.common.functions.Functions</function-class>
        <function-signature>java.lang.String getPriceFrequency(com.gumtree.domain.advert.Advert)</function-signature>
    </function>

    <function>
        <name>nl2br</name>
        <function-class>com.gumtree.web.common.functions.Functions</function-class>
        <function-signature>java.lang.String nl2br(java.lang.String)</function-signature>
    </function>

    <function>
        <name>hasProperty</name>
        <function-class>com.gumtree.web.common.functions.Functions</function-class>
        <function-signature>boolean hasProperty(java.lang.Object, java.lang.String)</function-signature>
    </function>

    <function>
        <name>instanceOf</name>
        <function-class>com.gumtree.web.common.functions.Functions</function-class>
        <function-signature>boolean instanceOf(java.lang.Object, java.lang.String)</function-signature>
    </function>


</taglib>