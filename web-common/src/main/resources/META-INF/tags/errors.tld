<?xml version="1.0" encoding="UTF-8"?>
<taglib xmlns="http://java.sun.com/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-jsptaglibrary_2_1.xsd"
        version="2.1">

    <tlib-version>1.0</tlib-version>
    <short-name>Errors</short-name>
    <uri>http://www.gumtree.com/common/errors</uri>

    <function>
        <name>hasError</name>
        <function-class>com.gumtree.web.common.functions.ErrorFunctions</function-class>
        <function-signature>java.lang.Boolean hasError(com.gumtree.web.common.error.MessageResolvingErrorSource,java.lang.String)</function-signature>
    </function>

    <function>
        <name>getError</name>
        <function-class>com.gumtree.web.common.functions.ErrorFunctions</function-class>
        <function-signature>java.lang.String getError(com.gumtree.web.common.error.MessageResolvingErrorSource,java.lang.String)</function-signature>
    </function>

    <function>
        <name>hasGlobalError</name>
        <function-class>com.gumtree.web.common.functions.ErrorFunctions</function-class>
        <function-signature>java.lang.Boolean hasError(com.gumtree.web.common.error.MessageResolvingErrorSource)</function-signature>
    </function>

    <function>
        <name>getGlobalError</name>
        <function-class>com.gumtree.web.common.functions.ErrorFunctions</function-class>
        <function-signature>java.lang.String getError(com.gumtree.web.common.error.MessageResolvingErrorSource)</function-signature>
    </function>

</taglib>