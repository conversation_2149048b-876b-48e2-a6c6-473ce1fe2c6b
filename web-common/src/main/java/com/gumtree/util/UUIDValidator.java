package com.gumtree.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.util.UUID.fromString;

public final class UUIDValidator {

    private UUIDValidator() {

    }

    private static final Logger LOGGER = LoggerFactory.getLogger(UUIDValidator.class);

    public static boolean isValid(String maybeUUID) {
        try {
            fromString(maybeUUID);
            return true;
        } catch (Exception e) {
            LOGGER.warn("Value not a UUID {}", maybeUUID);
            return false;
        }
    }

}
