package com.gumtree.util;

import com.gumtree.api.HystrixUtils;

public abstract class ExceptionUtils {
    private ExceptionUtils() {
    }

    public static String toShortMessage(Throwable error) {
        Throwable unwrappedError = HystrixUtils.unwrapThrowable(error);
        boolean isHystrixException = !error.equals(unwrappedError);
        String perfix = isHystrixException ? "HystrixRuntimeException > " : "";
        return perfix + unwrappedError.getClass().getSimpleName() + ": " + unwrappedError.getMessage();
    }

    public static String toShortMessage(String message, Throwable error) {
        Throwable unwrappedError = HystrixUtils.unwrapThrowable(error);
        boolean isHystrixException = !error.equals(unwrappedError);
        String perfix = isHystrixException ? "HystrixRuntimeException > " : "";
        return message + " " + perfix + unwrappedError.getClass().getSimpleName() + ": " + unwrappedError.getMessage();
    }
}
