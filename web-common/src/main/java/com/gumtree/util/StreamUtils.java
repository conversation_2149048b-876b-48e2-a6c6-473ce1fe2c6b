package com.gumtree.util;

import java.util.stream.Stream;

public final class StreamUtils {
    private StreamUtils() {

    }

    /**
     * Turns an Optional<A> into a Stream<A> of length zero or one depending upon
     * whether a value is present.
     * java 8 doesn't support it natively, they filled the gap into
     * Java 9 https://docs.oracle.com/javase/9/docs/api/java/util/Optional.html#stream--
     */
    public static <A> Stream<A> streamOptional(com.google.common.base.Optional<A> item) {
        return item.isPresent() ? Stream.of(item.get()) : Stream.empty();
    }

}
