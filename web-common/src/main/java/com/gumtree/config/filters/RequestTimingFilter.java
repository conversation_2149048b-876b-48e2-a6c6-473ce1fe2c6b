package com.gumtree.config.filters;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.MwebProperty;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * This filter logs request level metrics using Micrometer with the key `http_server_requests`.
 * <p>
 * The following tags will be associated with the timer entry :-
 * - uri: path. URLs will not contain unique identifiers, and use the @RequestParam
 * annotation values instead.- e.g. a GET request to /manage/ads/page3 will result in '/manage/ads/{page}'.
 * - method: http method
 * - status: the status code of the response
 * - isJson: whether the request contains the correct x-gt-get-model header, and therefore used by the BFF to populate
 * pages.
 * <p>
 * This filter should be as high up the stack as possible, to capture as much of the request time as possible.
 */
public class RequestTimingFilter implements Filter {

    public static final String METRICS_LOGGING_REQUEST_ATTRIBUTE_URI = "MetricsLoggingRequestAttributeURI";
    public static final String METRICS_LOGGING_REQUEST_ATTRIBUTE_METHOD = "MetricsLoggingRequestAttributeMethod";

    private MeterRegistry meterRegistry;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        WebApplicationContext context = WebApplicationContextUtils
                .getRequiredWebApplicationContext(filterConfig.getServletContext());

        this.meterRegistry = context.getBean(MeterRegistry.class);
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        Long startDate = new Date().getTime();

        try {
            chain.doFilter(request, response);
        } finally {
            Long endDate = new Date().getTime();
            String uri = (String) request.getAttribute(METRICS_LOGGING_REQUEST_ATTRIBUTE_URI);
            String method = (String) request.getAttribute(METRICS_LOGGING_REQUEST_ATTRIBUTE_METHOD);

            if (uri != null && request instanceof HttpServletRequest && response instanceof HttpServletResponse) {
                HttpServletRequest req = (HttpServletRequest) request;
                HttpServletResponse resp = (HttpServletResponse) response;

                boolean isJson = GtProps.getStr(MwebProperty.MODEL_JSON_HEADER_SECRET).equals(req.getHeader("x-gt-get-model"));

                Timer timer = Timer
                        .builder("http_server_requests")
                        .tag("status", String.valueOf(resp.getStatus()))
                        .tag("isJson", String.valueOf(isJson))
                        .tag("uri", uri)
                        .tag("method", StringUtils.defaultString(method))
                        .publishPercentiles(0.5, 0.95, 0.99)
                        .register(meterRegistry);

                timer.record((endDate - startDate), TimeUnit.MILLISECONDS);
            }
        }
    }

    @Override
    public void destroy() {
    }
}
