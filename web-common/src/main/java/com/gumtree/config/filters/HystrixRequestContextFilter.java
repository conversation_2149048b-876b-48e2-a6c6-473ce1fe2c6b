package com.gumtree.config.filters;

import com.netflix.hystrix.Hystrix;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;

/**
 * This filter enables request context, which is used for things like local caching of calls.
 * <p/>
 * As a reminded, calls in Hystrix are cached per request, that is, to avoid multiple calls with the same
 * values to do the same network request over and over.
 * <p/>
 * As the context is destroyed after the request finishes, the next request will do the network call, if any.
 */
public class HystrixRequestContextFilter implements Filter {

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HystrixRequestContext context = HystrixRequestContext.initializeContext();
        try {
            chain.doFilter(request, response);
        } finally {
            context.shutdown();
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void destroy() {
        Hystrix.reset();
    }
}
