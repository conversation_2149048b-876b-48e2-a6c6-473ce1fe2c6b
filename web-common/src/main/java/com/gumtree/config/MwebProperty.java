package com.gumtree.config;

import com.gumtree.common.properties.GtProps;

public enum MwebProperty implements GtProps.GtProperty {

    MWEB_HOST("gumtree.mweb.host", ""),
    GUMTREE_SELLER_URL("gumtree.url.seller.secure.base_uri", ""),
    DISPLAY_ERROR_ENABLED("gumtree.display.error.enabled", ""),
    TASKEXECUTOR_POOL_MIN("gumtree.mobile.web.taskexecutor.pool.min", ""),
    TASKEXECUTOR_POOL_MAX("gumtree.mobile.web.taskexecutor.pool.max", ""),
    TASKEXECUTOR_QUEUE_MAX("gumtree.mobile.web.taskexecutor.queue.max", ""),
    XML_SITEMAP_PS_PAGES_CHANGE_FREQ("gumtree.xmlsitemap.pspages.changefreq", ""),
    XML_SITEMAP_PS_PAGES_PRIORITY("gumtree.xmlsitemap.pspages.priority", ""),
    XML_SITEMAP_PS_KEYWORDS("gumtree.xmlsitemap.ps.keywords", ""),
    XML_SITEMAP_PS_PRIORITY("gumtree.xmlsitemap.ps.priority", ""),
    XML_SITEMAP_PS_CHANGE_FREQ("gumtree.xmlsitemap.ps.changefreq", ""),
    XML_SITEMAP_PS_FILES("gumtree.xmlsitemap.ps.files", ""),
    XML_SITEMAP_LISTINGS_CHANGE_FREQ("gumtree.xmlsitemap.listings.changefreq", ""),
    XML_SITEMAP_LISTINGS_PRIORITY("gumtree.xmlsitemap.listings.priority", ""),
    XML_SITEMAP_LISTINGS_FILES("gumtree.xmlsitemap.listings.files", ""),
    XML_SITEMAP_LOCATION_FILES("gumtree.xmlsitemap.location.files", ""),
    XML_SITEMAP_VIP_ITEMS_PER_PAGE("gumtree.xmlsitemap.vip.items.per.page", ""),
    ADVERTISING_PARTNERSHIP_HTTPCLIENT_SOCKET_TIMEOUT("gumtree.advertising.partnerships.httpclient.socket.timeout", ""),
    ADVERTISING_PARTNERSHIP_HTTPCLIENT_CONNECTION_TIMEOUT("gumtree.advertising.partnerships.httpclient.connection.timeout", ""),
    ADVERT_MESSAGING_URI("gumtree.rabbitmq.advert.messaging.uri.mweb", "Uri used to connect to the advert exchange on the messaging bus"),
    ADVERTISING_PARTNERSHIP_HTTPCLIENT_RETRY_COUNT("gumtree.advertising.partnerships.httpclient.retry.count", ""),
    ADVERTISING_BIDDING_RUBICON_ENABLED("gumtree.advertising.bidding.rubicon.enabled", "Rubicon feature switch"),
    ADVERTISING_PREBID_GA_TRACKING_PERCENTAGE("gumtree.advertising.prebid.ga.tracking.percentage",
            "percentage of prebid ads to track in GA"),
    ADVERTISING_PREBID_TIMEOUT("gumtree.advertising.prebid.timeout.ms", "prebid timeout"),
    SEARCH_PAGE_SIZE("gumtree.mweb.search.page.size", ""),
    SEARCH_PAGE_SIZE_LOCATIONS("gumtree.mweb.search.page.size.locations", ""),
    VALID_MEDIA_TYPES("gumtree.reply.attachment.validTypes", ""),
    GEOIP_DATABASE_LOCATION("gumtree.mweb.geoip.database.location", "Location of the GEOIP database in the local filesystem."),
    MODEL_JSON_HEADER_SECRET("gumtree.model.display.header.secret", ""),

    VIP_SIMILAR_ADS_MAX("gumtree.web.vip.maxsimilarads", ""),
    VIP_SPOTLIGHT_ADS_MAX("gumtree.web.vip.spotlightads", ""),
    VIP_SPOTLIGHT_ADS_MOBILE_MAX("gumtree.web.vip.spotlightads.mobile", ""),

    SRP_FEATURED_ADS_MAX("gumtree.mweb.search.featured.size", ""),
    SRP_FEATURED_ADS_MOBILE_MAX("gumtree.mweb.search.mfeatured.size", ""),
    HP_ADS_MAX("gumtree.mweb.search.homepage.latest.size", ""),

    // Memcached will still be used initially, but Redis will take over.
    DISTRIBUTED_CACHE("gumtree.distributed_cache", "Default implementation of the cache used by app"),
    MEMCACHED_SERVERS("gumtree.memcache.servers", "Comma separated list of memcache server addresses"),
    DEFAULT_CACHE_EXPIRY("gumtree.memcache.expiry", "Memcache expiration time is in seconds. 600 s -> 10m"),
    RELEVANT_ADS_CACHE_EXPIRY("gumtree.mweb.cache.relevantads.expiry", "Expiration time is in seconds. 60s s -> 1m "),

    REPLY_ATTACHMENT_MAX_SIZE("gumtree.reply.attachment.maxSize", "Max attachment size for a reply"),


    CONTACT_FORCE_LOGIN_WHITELISTED_CATEGORIES_ENABLED("gumtree.contact.force.login.whitelisted.categories.enabled",
            "if true, allows non-logged users to reply to an advert of certain categories"),
    CONTACT_FORCE_LOGIN_WHITELISTED_CATEGORIES("gumtree.contact.force.login.whitelisted.categories",
            "List of advert categories that DO NOT require login to reply"),

    FREESPEE_CALLTRACKING_ENABLED("gumtree.freespee.calltracking.enabled", "Enables calltracking via Freespee"),
    FREESPEE_ACCOUNT_ID("gumtree.freespee.account.id", "Freespee account id to connect with Freespee"),
    FREESPEE_ACCOUNT_KEY("gumtree.freespee.account.key", "Freespee account key to connect with Freespee"),
    FREESPEE_URL("gumtree.freespee.url", "Freespee url"),
    FREESPEE_VALIDATE_SSL("gumtree.freespee.validateSSL", "Validate SSL for Freespee"),
    FREESPEE_CONNECTION_TIMEOUT("gumtree.freespee.connection.timeout", "Freespee connection timeout"),
    FREESPEE_SOCKET_TIMEOUT("gumtree.freespee.socket.timeout", "Freespee socket timeout"),
    FREESPEE_CONNECTION_POOL_MAX_TOTAL("gumtree.freespee.connection.pool.max.total", "Freespee connection pool max"),
    FREESPEE_CONNECTION_POOL_MAX_PER_ROUTE("gumtree.freespee.connection.pool.max.per.route", "Freespee connection pool max per route"),
    FREESPEE_BACKGROUND_CALL_ENABLED("gumtree.freespee.background.call.enabled", "Enabling a backend call to Freespee"),

    REDIS_MODE("gumtree.redis.mode", "Redis connection mode. Supported values: standalone, sentinel"),
    REDIS_SENTINEL_MASTER("gumtree.sentinel.master", "Name of name of the Redis sentinel master node."),
    REDIS_SENTINEL_NODES("gumtree.sentinel.nodes", "Comma separated list of host:port pairs describing Redis sentinel nodes."),
    SINGLE_NODE_REDIS_HOSTNAME("gumtree.redis.host", "Hostname of the Redis cache"),
    SINGLE_NODE_REDIS_PORT("gumtree.redis.port", "TCP port of ther Redis cache"),
    HYSTRIX_CACHE_CALL_TIMEOUT("gumtree.mweb.hystrix.cache.timeout", "Timeout of calls to the cache. In milliseconds."),
    USE_JVM_CACHE("gumtree.mweb.use.jvm.cache", "Flag to switch from distributed to jvm level caching."),
    REPLY_TOKEN_VALIDATION_ENABLED("gumtree.reply.token.validation.enabled", "Flag to enable validation in Replies"),
    HYSTRIX_DEFAULT_TIMEOUT("gumtree.mweb.hystrix.default.client.timeout", "Hystrix's default client timeout value usually 1000"),

    // Rate Limiters
    REVEAL_PHONE_NUMBER_LIMIT("gumtree.rate_limiter.reveal_phone_number.limit", ""),
    REVEAL_PHONE_NUMBER_LIMIT_INTERVAL("gumtree.rate_limiter.reveal_phone_number.interval.s", ""),
    REVEAL_PHONE_NUMBER_LIMIT_STORE("gumtree.rate_limiter.reveal_phone_number.store",
            "What type of a data store to use to store the rate limiter data. If no value then in-memory storage is used."),

    // AD SEARCH - LIVE
    AD_SEARCH_LIVE_HOST("gumtree.adsearch.live.host", "Ad Search Service (LIVE) host"),
    AD_SEARCH_LIVE_CONNECTION_TIMEOUT("gumtree.adsearch.live.connection_timeout", "Ad Search Service (LIVE) connection timeout in millis"),
    AD_SEARCH_LIVE_READ_TIMEOUT("gumtree.adsearch.live.read_timeout", "Ad Search Service (LIVE) read timeout in millis"),
    AD_SEARCH_LIVE_RETRIES("gumtree.adsearch.live.retries", "Ad Search Service (LIVE) max number of retries of failed request"),
    AD_SEARCH_LIVE_USERNAME("gumtree.adsearch.live.username", "Ad Search Service (LIVE) username or blank"),
    AD_SEARCH_LIVE_PASSWORD("gumtree.adsearch.live.password", "Ad Search Service (LIVE) password or blank"),

    // AD SEARCH - FULL
    AD_SEARCH_FULL_HOST("gumtree.adsearch.full.host", "Ad Search Service (FULL) host"),
    AD_SEARCH_FULL_CONNECTION_TIMEOUT("gumtree.adsearch.full.connection_timeout", "Ad Search Service (FULL) connection timeout in millis"),
    AD_SEARCH_FULL_READ_TIMEOUT("gumtree.adsearch.full.read_timeout", "Ad Search Service (FULL) read timeout in millis"),
    AD_SEARCH_FULL_RETRIES("gumtree.adsearch.full.retries", "Ad Search Service (FULL) max number of retries of failed request"),
    AD_SEARCH_FULL_USERNAME("gumtree.adsearch.full.username", "Ad Search Service (FULL) username or blank"),
    AD_SEARCH_FULL_PASSWORD("gumtree.adsearch.full.password", "Ad Search Service (FULL) password or blank"),

    // USER REVIEWS
    USER_REVIEWS_API_HOST("gumtree.userreviews.api.host", "User Reviews API host"),
    USER_REVIEWS_API_CONNECTION_TIMEOUT("gumtree.userreviews.api.connection_timeout", "User Reviews API connection timeout in millis"),
    USER_REVIEWS_API_READ_TIMEOUT("gumtree.userreviews.api.read_timeout", "User Reviews API read timeout in millis"),

    // Madgex GumtreeAPI
    MADGEX_GTAPI_HOST("gumtree.madgex.gumtreeapi.host", "Madgex GumtreeApi host"),
    MADGEX_GTAPI_CONNECTION_TIMEOUT("gumtree.madgex.gumtreeapi.connection_timeout", "Madgex GumtreeApi connection timeout in millis"),
    MADGEX_GTAPI_READ_TIMEOUT("gumtree.madgex.gumtreeapi.read_timeout", "Madgex GumtreeApi read timeout in millis"),
    MADGEX_GTAPI_RETRIES("gumtree.madgex.gumtreeapi.retries", "Madgex GumtreeApi max number of retries of failed request"),
    MADGEX_GTAPI_AUTH_TOKEN("gumtree.madgex.gumtreeapi.auth_token", "Madgex GumtreeApi bearer auth token"),

    // RECAPTCHA
    RECAPTCHA_HTTPCLIENT_SOCKET_TIMEOUT("gumtree.recaptcha.httpclient.socket.timeout", ""),
    RECAPTCHA_HTTPCLIENT_CONNECTION_TIMEOUT("gumtree.recaptcha.httpclient.connection.timeout", ""),
    RECAPTCHA_HTTPCLIENT_RETRY_COUNT("gumtree.recaptcha.httpclient.retry.count", ""),

    JWT_SECRET("gumtree.mweb.token.secret", "Used to encrypt data stored in token"),
    TOKEN_VALID_NOT_BEFORE_SECONDS("gumtree.mweb.token.not.valid.before.seconds", "Token Not Valid Before"),
    TOKEN_VALID_NOT_AFTER_SECONDS("gumtree.mweb.token.not.valid.after.seconds", "Expire Token"),

    PARTAPI_CONNECTION_POOL_MAX("gumtree.partapi.connection.max", "Max connections in the connection pool"),

    // popular searches
    NO_OF_POPULAR_SEARCHES("gumtree.mweb.popularSearchNumber", ""),
    NO_OF_RELATED_SEARCHES("gumtree.mweb.relatedSearchNumber", ""),

    // category api
    CATSAPI_HOST("gumtree.category.api.hostname", ""),
    CATSAPI_PORT("gumtree.category.api.port", ""),
    CATSAPI_SOCKET_TIMEOUT("gumtree.category.api.socket.timeout", ""),
    CATSAPI_CONNECTION_TIMEOUT("gumtree.category.api.connection.timeout", ""),
    CATSAPI_RETRY_COUNT("gumtree.category.api.retry.count", ""),
    CATSAPI_CACHE_RELOAD_INTERVAL("gumtree.category.api.cache.reload.interval", ""),

    //Message Centre notifications
    NOTIFICATION_POLLING_FREQUENCY("gumtree.messagecentre.notification.pollingfrequency.minutes",
            "Frequency to use for polling unread messages in minutes"),

    //ANALYTICS
    GA_TRACKING_ID("gumtree.analytics.ga.trackingid", "Tracking Id for google analytics"),
    GA_HOST_URL("gumtree.analytics.ga.host", "GA host"),
    GA_SERVER_STUB_ENABLED("gumtree.analytics.ga.server.stub.enabled", "enables the ga server stub for testing"),
    BING_PROXY_SHOULD_BLACKLIST_MOBILE_APPS("gumtree.bing.proxy.should_blacklist_mobile_apps",
            "If true, the bing proxy will return empty results for requests coming from mobile apps"),

    // SOURCEPOINT
    SOURCE_POINT_URL("gumtree.sourcepoint.url", "URL for SourcePoint"),
    SOURCE_POINT_API_KEY("gumtree.sourcepoint.api.key", "API Key for SourcePoint"),

    // METRICS
    METRICS_REPORT_PERIOD_MIN("gumtree.metrics.report.period.min", ""),

    // INFLUXDB
    INFLUXDB_HOST("gumtree.metrics.report.influx.host", ""),
    INFLUXDB_PORT("gumtree.metrics.report.influx.port", ""),
    INFLUXDB_USERNAME("gumtree.metrics.report.influx.username", ""),
    INFLUXDB_PASSWORD("gumtree.metrics.report.influx.password", ""),
    INFLUXDB_DBNAME("gumtree.metrics.report.influx.dbname", ""),
    INFLUXDB_TAGS("gumtree.metrics.report.influx.tags", ""),
    INFLUXDB_CONNECTION_TIMEOUT("gumtree.metrics.report.influx.connection.timeout.ms", ""),
    INFLUXDB_SOCKET_TIMEOUT("gumtree.metrics.report.influx.socket.timeout.ms", ""),

    // Dominant Category Service (ECG service)
    DCS_HOST("gumtree.ecg.dcs.host", ""),
    DCS_USERNAME("gumtree.ecg.dcs.username", ""),
    DCS_PASSWORD("gumtree.ecg.dcs.password", ""),
    DCS_TENANT_ID("gumtree.ecg.dcs.tenant_id", ""),
    DCS_CONNECTION_TIMEOUT("gumtree.ecg.dcs.connection.timeout.ms", ""),
    DCS_SOCKET_TIMEOUT("gumtree.ecg.dcs.socket.timeout.ms", ""),

    JOBS_ID_OFFSET("gumtree.jobs.id.offset", ""),

    KAFKA_BOOTSTRAP_SERVERS("gumtree.kafka.bootstrap-servers", ""),
    SCHEMA_REGISTRY_URL("gumtree.schema-registry.url", ""),

    // Browse component A/B test
    LOCATIONS_WITH_BROWSE_COMPONENT("gumtree.browsecomponent.abtest.locations.enabled", ""),

    ZUTO_AES_KEY("gumtree.zuto.vrn.encryption.key", ""),

    CTM_AES_KEY("gumtree.ctm.vrn.encryption.key", ""),

    CAP_HPI_AES_KEY("gumtree.hpi.vrn.encryption.key", ""),

    // Locations
    LOCATIONS_URL("gumtree.locations.url", "Locations URL"),
    LOCATIONS_CONNECTION_TIMEOUT("gumtree.locations.connection_timeout", "Connection timeout in millis"),
    LOCATIONS_READ_TIMEOUT("gumtree.locations.read_timeout", "Read timeout in millis"),

    TSA_ADCOUNTERS_API_BASE_URL("gumtree.tsa.adcounters.api.baseurl", ""),
    TSA_ADCOUNTERS_API_CLIENT_ID("gumtree.tsa.adcounters.api.clientid", ""),
    TSA_AD_COUNTERS_API_CONNECTION_TIMEOUT("gumtree.tsa.adcounters.api.connection.timeout", ""),
    TSA_AD_COUNTERS_API_READ_TIMEOUT("gumtree.tsa.adcounters.api.read.timeout", ""),

    GUMTREE_SELLER_GCP_AUTH_CREDENTIALS("gumtree.seller.google.auth.credentials","This google credentials are used " +
            "to fetch access token for service-to service communication"),

    GUMTREE_TSA_ADCOUNTERS_AUDIENCE("gumtree.tsa.adcounters.audience","The audience is to fetch token to access that service"),

    //Recently viewed items
    RECENTLY_VIEWED_ITEMS_HOST("gumtree.recently.viewed.host", " Odin host for recently viewed items"),
    RECENTLY_VIEWED_CONNECTION_TIMEOUT("gumtree.recently.viewed.connectionTimeout", ""),
    RECENTLY_VIEWED_READ_TIMEOUT("gumtree.recently.viewed.readTimeout", ""),

    //Seo FAQ - simiar.ai
    SEO_FAQ_SIMILAR_AI_BASE_URL("gumtree.seo.faq.similar.ai.baseurl", "Seo FAQ - similar.ai base url"),
    SEO_FAQ_SIMILAR_AI_API_KEY("gumtree.seo.faq.similar.ai.api.key", "Seo FAQ - similar.ai API key"),

    // Cdata Odin Api Authentication
    CDATA_ODIN_API_AUTH_USERNAME("gumtree.cdata.odin.api.auth.username", ""),
    CDATA_ODIN_API_AUTH_PASSWORD("gumtree.cdata.odin.api.auth.password", ""),

    // MOTORS.CO.UK
    MCU_LEAD_API_BASE_URL("gumtree.mcu.lead.api.baseurl", ""),
    MCU_LEAD_API_BUYER_KEY("gumtree.mcu.lead.api.key", ""),
    MCU_LEAD_API_BUYER_ID("gumtree.mcu.lead.api.id", ""),
    MCU_LEAD_API_BUYER_SECRET("gumtree.mcu.lead.api.secret", ""),
    MCU_LEAD_API_CONNECTION_TIMEOUT("gumtree.mcu.lead.api.connection.timeout", ""),
    MCU_LEAD_API_READ_TIMEOUT("gumtree.mcu.lead.api.read.timeout", ""),

    MADGEX_ANALYTICS_API_URL("gumtree.madgex.analytics.url", ""),
    MADGEX_ANALYTICS_API_AUTH_KEY("gumtree.madgex.analytics.api.auth_key", ""),

    GUMTREE_OFFICIAL_ACCOUNT_ID("gumtree.official.account.id", "account id to retrieve all gumtree official ads"),

    //Motors price guidance
    MOTORS_PRICE_GUIDANCE_BASE_URL("gumtree.motors.price.guidance.baseurl", "Base URL"),

    //category Predictor config
    CATEGORY_PREDICTOR_HOST("gumtree.category.predictor.host","category.predictor.host"),
    CATEGORY_PREDICTOR_PORT("gumtree.category.predictor.port","category.predictor.port"),
    CATEGORY_PREDICTOR_CONNECTION_TIMEOUT("gumtree.category.predictor.connection_timeout","connection_timeout"),
    CATEGORY_PREDICTOR_READ_TIMEOUT("gumtree.category.predictor.read_timeout","read_timeout"),
    CATEGORY_PREDICTOR_RETRIES("gumtree.category.predictor.retries","retries"),


    //Security
    CONTENT_SECURITY_POLICY("gumtree.secure.content.policy", "Content Security Policy HTTP Header");

    private String propertyName;
    private String description;


    MwebProperty(String propertyName, String description) {
        this.propertyName = propertyName;
        this.description = description;
    }

    @Override
    public String getPropertyName() {
        return propertyName;
    }

    public String getDescription() {
        return description;
    }
}
