package com.gumtree.config;

import com.codahale.metrics.MetricRegistry;
import com.gumtree.common.properties.GtProps;
import com.gumtree.web.abtest.ExperimentsBotFilterWrapper;
import com.gumtree.web.abtest.ExperimentsBucketMetricsWrapper;
import com.gumtree.web.abtest.ExperimentsCounterWrapper;
import com.gumtree.web.abtest.ExperimentsOverrideWrapper;
import com.gumtree.web.abtest.ExperimentsProvider;
import com.gumtree.web.abtest.ExperimentsProviderFactory;
import com.gumtree.web.abtest.RequestScopeCachedExperimentsProvider;
import com.gumtree.web.abtest.StubExperimentsProvider;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.GumtreeCookieProperty;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.feature.FeatureSwitchManager;
import com.gumtree.web.feature.FeatureSwitchManagerImpl;
import com.gumtree.web.feature.FeatureSwitchOverrideWrapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.web.context.WebApplicationContext;

import javax.servlet.http.HttpServletRequest;

/**
 * Spring wiring configuration for experiments and feature switches.
 */
@Configuration
public class FeatureToggleConfig {

    private final String cookieDomain = GtProps.getStr(GumtreeCookieProperty.COOKIES_DOMAIN);

    // To be used to get ExperimentsProvider if out of request scope.
    @Bean
    public ExperimentsProviderFactory experimentsProviderFactory(
            CookieResolver cookieResolver,
            UserSessionService userSessionService,
            MetricRegistry metricRegistry
    ) {
        return request -> {
            // Stub experiments provider
            ExperimentsProvider experimentsProvider =
                    new StubExperimentsProvider();
            // Wrap it with cookie override
            ExperimentsProvider cookieOverrideProvider =
                    new ExperimentsOverrideWrapper(cookieDomain, experimentsProvider, request);
            // Wrap it with bot filter
            // with get method counter
            ExperimentsProvider botFilterProvider = decorateWithCallCounter(
                    new ExperimentsBotFilterWrapper(cookieOverrideProvider, request), metricRegistry
            );
            // Wrap it with request base cache support
            // with experiment bucket metrics
            return new RequestScopeCachedExperimentsProvider(
                    decorateWithExperimentsBucketMetrics(botFilterProvider, metricRegistry), request);
        };
    }

    private ExperimentsProvider decorateWithCallCounter(ExperimentsProvider baseExperimentsProvider, MetricRegistry metricRegistry) {
        return new ExperimentsCounterWrapper(baseExperimentsProvider, metricRegistry, "",
                baseExperimentsProvider.getClass().getSimpleName());
    }

    private ExperimentsProvider decorateWithExperimentsBucketMetrics(ExperimentsProvider baseExperimentsProvider, MetricRegistry metricRegistry) {
        return new ExperimentsBucketMetricsWrapper(baseExperimentsProvider, metricRegistry, "");
    }

    // To be used to get ExperimentsProvider in request scope.
    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.INTERFACES)
    public ExperimentsProvider experimentsProvider(ExperimentsProviderFactory experimentsProviderFactory,
                                                   HttpServletRequest request) {
        return experimentsProviderFactory.create(request);
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.INTERFACES)
    public FeatureSwitchManager featureSwitchManager(CookieResolver cookieResolver, HttpServletRequest request) {
        return new FeatureSwitchOverrideWrapper(
                cookieDomain,
                new FeatureSwitchManagerImpl(cookieResolver, request),
                request);
    }

}
