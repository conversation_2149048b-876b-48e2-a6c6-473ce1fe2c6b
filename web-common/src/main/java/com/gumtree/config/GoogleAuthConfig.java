package com.gumtree.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.gumtree.authgenerator.GoogleJwtTokenProvider;
import com.gumtree.authgenerator.GoogleJwtTokenProviderImpl;
import com.gumtree.common.properties.GtProps;
import com.gumtree.google.authservice.GoogleAuthService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class GoogleAuthConfig {

    @Bean
    public GoogleAuthService adCountersGoogleAuthService() throws IOException {
        String adCountersAudience = GtProps.getStr(MwebProperty.GUMTREE_TSA_ADCOUNTERS_AUDIENCE);
        GoogleJwtTokenProvider jwtTokenProvider = new GoogleJwtTokenProviderImpl();
        GoogleCredentials googleCredentials = GoogleCredentials.getApplicationDefault();
        return new GoogleAuthService(adCountersAudience, googleCredentials, jwtTokenProvider);
    }
}
