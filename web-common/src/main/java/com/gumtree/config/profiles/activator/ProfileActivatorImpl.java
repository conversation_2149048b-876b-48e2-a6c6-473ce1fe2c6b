package com.gumtree.config.profiles.activator;

import org.springframework.core.env.ConfigurableEnvironment;

public final class ProfileActivatorImpl implements ProfileActivator {
    private final ConfigurableEnvironment environment;
    private String switchProperty;
    private boolean defaultSwitch = false;
    private String onProfile;
    private String offProfile;

    public ProfileActivatorImpl(ConfigurableEnvironment environment, String switchProp, String onProfile,
                                String offProfile, boolean defaultSwitch) {
        this.environment = environment;
        this.switchProperty = switchProp;
        this.onProfile = onProfile;
        this.offProfile = offProfile;
        this.defaultSwitch = defaultSwitch;
    }

    @Override
    public String getActiveProfile() {
        return isEnabled() ? onProfile : offProfile;
    }

    @Override
    public String getOnProfile() {
        return onProfile;
    }

    @Override
    public String getOffProfile() {
        return offProfile;
    }

    private boolean isEnabled() {
        return environment.getProperty(switchProperty, Boolean.class, defaultSwitch);
    }
}
