package com.gumtree.config.profiles;

/**
 * Common Spring Profile names used in the {@link org.springframework.context.annotation.Profile} annotation
 */
public final class CommonConfigProfiles {

    private CommonConfigProfiles() {
        // added to fix checkstyle complain
    }
    public static final String SESSION_PERSISTENCE_REDIS = "redis";
    public static final String SESSION_PERSISTENCE_STUB = "stub-" + SESSION_PERSISTENCE_REDIS;
    public static final String CATEGORY_API = "category-api";
    public static final String BUSHFIRE_API = "bushfire-api";
    public static final String ELASTIC_SEARCH_API = "elastic-search";
    public static final String PRODUCTION_MODELS = "production-models";
}
