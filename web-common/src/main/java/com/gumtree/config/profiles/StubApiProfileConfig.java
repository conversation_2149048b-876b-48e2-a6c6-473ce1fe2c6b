package com.gumtree.config.profiles;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.Locations;
import com.gumtree.api.Outcodes;
import com.gumtree.api.client.StubBushfireApi;
import com.gumtree.api.client.stub.config.StubBushfireApiConfig;
import com.gumtree.api.client.util.BushfireApiExceptionTranslator;
import com.gumtree.api.client.util.ExceptionTranslator;
import com.gumtree.util.stub.api.ApiStubs;
import com.gumtree.util.stub.api.StubAdvertApi;
import com.gumtree.util.stub.api.StubLocationApi;
import com.gumtree.util.stub.api.StubOrderApi;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.ClassPathResource;

/**
 */
@Configuration
@Profile("stub-api")
@Import(value = StubBushfireApiConfig.class)
public class StubApiProfileConfig implements ApiProfileConfig {

    @Autowired
    private ObjectMapper objectMapper;

    @Bean
    public ApiStubs apiStubs() {
        return new ApiStubs();
    }

    @Bean
    public StubBushfireApi bushfireApi() throws Exception {
        return new SellerStubBushfireApi();
    }

    @Bean
    public StubBushfireApi bushfireWriteApi() throws Exception {
        return new SellerStubBushfireApi();
    }

    @Override
    @Bean
    public BushfireApiKey defaultBushfireApiKey() {
        return new BushfireApiKey();
    }

    @Bean
    public StubLocationApi locationApi() throws Exception {
        Locations locations = objectMapper.readValue(new ClassPathResource(
                "stub/location/locations.json").getInputStream(),
                Locations.class);

        Outcodes outcodes = objectMapper.readValue(new ClassPathResource(
                "stub/location/outcodes.json").getInputStream(),
                Outcodes.class);

        return new StubLocationApi(locations, outcodes);
    }

    // TODO back due to BumpUpControllerMvcTest
    @Bean
    public StubAdvertApi advertApi() throws Exception {
        return new StubAdvertApi();
    }
    @Bean
    public StubOrderApi orderApi() throws Exception {
        return new StubOrderApi();
    }

    /**
     * @return  ExceptionTranslator BushfireApiExceptionTranslator
     */
    @Bean
    public ExceptionTranslator exceptionTranslator() {
        return new BushfireApiExceptionTranslator();
    }
}
