package com.gumtree.config.profiles;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.util.ExceptionTranslator;
import org.springframework.context.annotation.Bean;

/**
 * Interface for BAPI config profile classes. Used by the real BAPI config and the stub API config.
 */
public interface ApiProfileConfig {

    /**
     * @return BushfireApi instance
     * @throws Exception exception
     */
    @Bean
    BushfireApi bushfireApi() throws Exception;

    /**
     * @return  BushfireApiKey the defaultBushfireApiKey
     */
    @Bean
    BushfireApiKey defaultBushfireApiKey();

    /**
     * @return  ExceptionTranslator BushfireApiExceptionTranslator
     */
    @Bean
    ExceptionTranslator exceptionTranslator();
}
