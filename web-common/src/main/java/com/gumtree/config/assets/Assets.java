package com.gumtree.config.assets;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.GtProps;
import com.gumtree.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.FileSystemResource;

import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.codahale.metrics.MetricRegistry.name;

public class Assets {

    private static final Logger LOG = LoggerFactory.getLogger(Assets.class);
    private static final String CACHE_KEY = "ASSETS_MAPPING";
    private static final ImmutableMap<String, String> EMPTY_MAPPING = ImmutableMap.of();

    private final Path assetsPath;
    private final LoadingCache<String, Map<String, String>> cache;
    private final Counter reloadFailureCounter;
    private final Counter noMappingCounter;

    public Assets(Path assetsPath, Long templatesUpdateDelay, MetricRegistry metricRegistry) {
        this.assetsPath = assetsPath.toAbsolutePath().normalize();
        this.cache = CacheBuilder.newBuilder().maximumSize(1).expireAfterWrite(templatesUpdateDelay, TimeUnit.SECONDS).build(cacheLoader());
        this.reloadFailureCounter = metricRegistry.counter(name(Assets.class, "reloadFailure"));
        this.noMappingCounter = metricRegistry.counter(name(Assets.class, "noMapping"));
    }

    private CacheLoader<String, Map<String, String>> cacheLoader() {
        return new CacheLoader<String, Map<String, String>>() {
            @Override
            public Map<String, String> load(String key) {
                if (GtProps.getEnv() == Env.DEV) {
                    return EMPTY_MAPPING;
                }

                HashMap<String, String> staticVars = new HashMap<>();
                ObjectMapper mapper = JacksonUtils.createSnakeCaseObjectMapper();

                try {
                    JsonNode jsonNode = mapper.readTree(new FileSystemResource(assetsPath.toFile()).getInputStream());
                    for (JsonNode next : jsonNode) {
                        String src = next.get("src").asText();
                        String dest = next.get("dest").asText();
                        staticVars.put(src, dest);
                    }
                    return staticVars;
                } catch (Exception e) {
                    reloadFailureCounter.inc();
                    LOG.error("Error loading cache buster mappings, assets json not found on " + assetsPath.toString(), e);
                    return EMPTY_MAPPING;
                }
            }
        };
    }

    public String get(String key) {
        Map<String, String> assetsMapping = cache.getUnchecked(CACHE_KEY);

        String value = assetsMapping.get(key);
        if (value != null) {
            return value;
        }

        if(assetsMapping != EMPTY_MAPPING) {
            noMappingCounter.inc();
            LOG.warn("Could not find cache buster mapping entry for " + key);
        }

        return key;
    }
}
