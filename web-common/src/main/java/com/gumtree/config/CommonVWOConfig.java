package com.gumtree.config;

import com.gumtree.web.common.page.model.thirdparty.vwo.DefaultVWOConfigurer;
import com.gumtree.web.common.page.model.thirdparty.vwo.VWOConfigurer;
import com.gumtree.web.common.page.model.thirdparty.vwo.VisualWebOptimizerConfigurerFactory;
import com.gumtree.web.common.page.model.thirdparty.vwo.VisualWebOptimizerConfigurerFactoryImpl;
import com.gumtree.web.common.page.model.thirdparty.vwo.VisualWebOptimizerViewModelAppender;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CommonVWOConfig {

    @Bean
    public VisualWebOptimizerConfigurerFactory visualWebOptimizerConfigurerFactory() {
        return new VisualWebOptimizerConfigurerFactoryImpl();
    }

    @Bean
    public VWOConfigurer defaultVWOConfigurer() {
        return new DefaultVWOConfigurer();
    }

    @Bean
    public VisualWebOptimizerViewModelAppender visualWebOptimizerViewModelAppender() {
        return new VisualWebOptimizerViewModelAppender(visualWebOptimizerConfigurerFactory());
    }
}
