package com.gumtree.config.service;

import com.gumtree.api.bapi.BapiLocationsResolvingService;
import com.gumtree.api.bapi.BapiLocationsService;
import com.gumtree.api.client.BushfireApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

@Configuration
public class BapiLocationServiceConfig {

    @Autowired
    private BushfireApi bushfireApi;

    @Bean
    @DependsOn("hystrixMetricsPublisher") // make sure that hystrix is configured as otherwise it will use defaults
    public BapiLocationsService bapiLocationsService() {
        return new BapiLocationsResolvingService(bushfireApi);
    }

}
