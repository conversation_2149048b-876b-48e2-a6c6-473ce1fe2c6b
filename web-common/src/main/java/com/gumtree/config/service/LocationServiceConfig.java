package com.gumtree.config.service;

import com.gumtree.api.bapi.BapiLocationsService;
import com.gumtree.api.locations.LocationsApiFactory;
import com.gumtree.api.locations.infrastructure.LocationsApi;
import com.gumtree.api.service.LocationsResolvingService;
import com.gumtree.api.service.LocationsService;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.CommonProperty;
import com.gumtree.config.MwebProperty;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

@Configuration
public class LocationServiceConfig {

    @Autowired
    private BapiLocationsService bapiLocationsService;

    @Autowired
    private MeterRegistry meterRegistry;

    @Bean
    public LocationsApi locationsApi() {
        return LocationsApiFactory.create(
                GtProps.getStr(MwebProperty.LOCATIONS_URL),
                GtProps.getInt(MwebProperty.LOCATIONS_CONNECTION_TIMEOUT),
                GtProps.getInt(MwebProperty.LOCATIONS_READ_TIMEOUT),
                GtProps.getStr(CommonProperty.K8S_INGRESS_USER),
                GtProps.getStr(CommonProperty.K8S_INGRESS_PASSWORD),
                meterRegistry);
    }

    @Bean
    @DependsOn("hystrixMetricsPublisher") // make sure that hystrix is configured as otherwise it will use defaults
    public LocationsService locationsService() {
        return new LocationsResolvingService(bapiLocationsService, locationsApi());
    }

}
