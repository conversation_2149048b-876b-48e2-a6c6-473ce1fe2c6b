package com.gumtree.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AuthenticationMetrics {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthenticationMetrics.class);

    public static final String NAME = "authentication_total";
    public static final String ACTION = "action";
    public static final String STATUS = "status";

    public enum Action {
        GUMTREE_LOGIN,
        GOOGLE_LOGIN,
        FACEBOOK_LOGIN,
        UNKNOWN_PROVIDER_LOGIN,
        ACCESS_TOKEN,
        REMEMBER_ME_COOKIE,
        LOGOUT;

        @Override
        public String toString(){
            return name().toLowerCase();
        }
    }

    public enum Status  {
        SUCCESS,
        FAILURE;

        @Override
        public String toString(){
            return name().toLowerCase();
        }
    }

    public static void incrementCounter(MeterRegistry meterRegistry, Action action, Status status) {
        try {
            meterRegistry.counter(NAME, ACTION, action.toString(), STATUS, status.toString()).increment();
        } catch(Exception e) {
            // Catching any runtime exceptions to avoid Prometheus metrics problems affecting site functionality
            LOGGER.error("Exception while updating Prometheus metrics. {}", e.getMessage(), e);
        }
    }

    private AuthenticationMetrics(){
    }
}
