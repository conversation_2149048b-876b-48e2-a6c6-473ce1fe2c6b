package com.gumtree.service.messagecentre.api.model;


public final class Advert {
    private Long id;
    private String title;
    private String description;
    private String status;
    private String url;
    private String primaryImage;
    private String date;
    private String price;
    private String priceFrequency;
    private Long categoryId;


    private Advert(Builder builder) {
        this.id = builder.id;
        this.title = builder.title;
        this.description = builder.description;
        this.status = builder.status;
        this.url = builder.url;
        this.primaryImage = builder.primaryImage;
        this.date = builder.date;
        this.price = builder.price;
        this.priceFrequency = builder.priceFrequency;
        this.categoryId = builder.categoryId;
    }

    public Long getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public String getDescription() {
        return description;
    }

    public String getStatus() {
        return status;
    }

    public String getUrl() {
        return url;
    }

    public String getPrimaryImage() {
        return primaryImage;
    }

    public String getDate() {
        return date;
    }

    public String getPrice() {
        return price;
    }

    public String getPriceFrequency() {
        return priceFrequency;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public static class Builder {
        private Long id;
        private String title;
        private String description;
        private String status;
        private String url;
        private String primaryImage;
        private String date;
        private String price;
        private String priceFrequency;
        private Long categoryId;

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setDescription(String description) {
            this.description = description;
            return this;
        }

        public Builder setStatus(String status) {
            this.status = status;
            return this;
        }

        public Builder setUrl(String url) {
            this.url = url;
            return this;
        }

        public Builder setPrimaryImage(String primaryImage) {
            this.primaryImage = primaryImage;
            return this;
        }

        public Builder setDate(String date) {
            this.date = date;
            return this;
        }

        public Builder setPrice(String price) {
            this.price = price;
            return this;
        }

        public Builder setPriceFrequency(String priceFrequency) {
            this.priceFrequency = priceFrequency;
            return this;
        }

        public Builder setId(Long id) {
            this.id = id;
            return this;
        }

        public Builder setCategoryId(Long categoryId) {
            this.categoryId = categoryId;
            return this;
        }

        public Advert build() {
            return new Advert(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof Advert)){
            return false;
        }

        Advert advert = (Advert) o;

        if(price != null ? !price.equals(advert.price) : advert.price != null){
            return false;
        }
        if(priceFrequency != null ? !priceFrequency.equals(advert.priceFrequency) : advert.priceFrequency != null){
            return false;
        }
        if(date != null ? !date.equals(advert.date) : advert.date != null){
            return false;
        }
        if(description != null ? !description.equals(advert.description) : advert.description != null){
            return false;
        }
        if(id != null ? !id.equals(advert.id) : advert.id != null){
            return false;
        }
        if(primaryImage != null ? !primaryImage.equals(advert.primaryImage) : advert.primaryImage != null){
            return false;
        }
        if(status != null ? !status.equals(advert.status) : advert.status != null){
            return false;
        }
        if(title != null ? !title.equals(advert.title) : advert.title != null){
            return false;
        }
        if(url != null ? !url.equals(advert.url) : advert.url != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (title != null ? title.hashCode() : 0);
        result = 31 * result + (description != null ? description.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (url != null ? url.hashCode() : 0);
        result = 31 * result + (primaryImage != null ? primaryImage.hashCode() : 0);
        result = 31 * result + (date != null ? date.hashCode() : 0);
        result = 31 * result + (price != null ? price.hashCode() : 0);
        result = 31 * result + (priceFrequency != null ? priceFrequency.hashCode() : 0);
        return result;
    }
}
