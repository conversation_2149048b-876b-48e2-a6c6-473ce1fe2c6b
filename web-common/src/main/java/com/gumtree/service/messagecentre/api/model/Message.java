package com.gumtree.service.messagecentre.api.model;


import com.gumtree.web.common.domain.messagecentre.MessageDirection;

import java.util.List;

public final class Message {

    private String receivedDate;
    private MessageDirection direction;
    private String text;
    private final List<String> attachments;

    private Message(Builder builder) {
        this.receivedDate = builder.receivedDate;
        this.direction = builder.direction;
        this.text = builder.text;
        this.attachments = builder.attachments;
    }

    public String getReceivedDate() {
        return receivedDate;
    }

    public MessageDirection getDirection() {
        return direction;
    }

    public String getText() {
        return text;
    }

    public List<String> getAttachments() {
        return attachments;
    }

    public static class Builder {
        private String receivedDate;
        private MessageDirection direction;
        private String text;
        private List<String> attachments;

        public Builder setReceivedDate(String receivedDate) {
            this.receivedDate = receivedDate;
            return this;
        }

        public Builder setDirection(MessageDirection direction) {
            this.direction = direction;
            return this;
        }

        public Builder setText(String text) {
            this.text = text;
            return this;
        }

        public Builder setAttachments(List<String> attachments) {
            this.attachments = attachments;
            return this;
        }

        public Message build() {
            return new Message(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof Message)){
            return false;
        }

        Message message = (Message) o;

        if(attachments != null ? !attachments.equals(message.attachments) : message.attachments != null){
            return false;
        }
        if(direction != message.direction){
            return false;
        }
        if(receivedDate != null ? !receivedDate.equals(message.receivedDate) : message.receivedDate != null){
            return false;
        }
        if(text != null ? !text.equals(message.text) : message.text != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = receivedDate != null ? receivedDate.hashCode() : 0;
        result = 31 * result + (direction != null ? direction.hashCode() : 0);
        result = 31 * result + (text != null ? text.hashCode() : 0);
        result = 31 * result + (attachments != null ? attachments.hashCode() : 0);
        return result;
    }
}
