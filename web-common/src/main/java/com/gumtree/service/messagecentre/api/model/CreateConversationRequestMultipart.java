package com.gumtree.service.messagecentre.api.model;

import org.jboss.resteasy.annotations.providers.multipart.PartType;

import javax.ws.rs.FormParam;
import javax.ws.rs.core.MediaType;
import java.io.File;

public class CreateConversationRequestMultipart {

    private CreateConversationRequest createConversationRequest;
    private File file;

    @FormParam("reply")
    @PartType(MediaType.APPLICATION_JSON)
    public CreateConversationRequest getCreateConversationRequest() {
        return createConversationRequest;
    }

    @FormParam("file")
    @PartType(MediaType.APPLICATION_OCTET_STREAM)
    public File getFile() {
        return file;
    }

    public void setCreateConversationRequest(CreateConversationRequest createConversationRequest) {
        this.createConversationRequest = createConversationRequest;
    }


    public void setFile(File file) {
        this.file = file;
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof CreateConversationRequestMultipart)){
            return false;
        }

        CreateConversationRequestMultipart that = (CreateConversationRequestMultipart) o;

        if(createConversationRequest != null ?
                !createConversationRequest.equals(that.createConversationRequest)
                : that.createConversationRequest != null){
            return false;
        }
        if(file != null ? !file.equals(that.file) : that.file != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = createConversationRequest != null ? createConversationRequest.hashCode() : 0;
        result = 31 * result + (file != null ? file.hashCode() : 0);
        return result;
    }
}
