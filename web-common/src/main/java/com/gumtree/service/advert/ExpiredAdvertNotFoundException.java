package com.gumtree.service.advert;

/**
 * Exception to be thrown when a requested advert cannot be found
 */
public class ExpiredAdvertNotFoundException extends RuntimeException {
    private Long id;

    /**
     * Constructor.
     *
     * @param id    the unrecognised id
     * @param cause the cause of this exception
     */
    public ExpiredAdvertNotFoundException(Long id, Exception cause) {
        super(cause);
        this.id = id;
    }

    /**
     * Constructor.
     *
     * @param id the unrecognised id
     */
    public ExpiredAdvertNotFoundException(Long id) {
        this.id = id;
    }

    public final Long getId() {
        return id;
    }
}
