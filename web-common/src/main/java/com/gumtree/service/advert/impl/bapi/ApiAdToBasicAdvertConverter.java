package com.gumtree.service.advert.impl.bapi;

import com.gumtree.api.Ad;
import com.gumtree.api.Location;
import com.gumtree.domain.advert.BasicAdvert;
import com.gumtree.domain.advert.entity.AdvertEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Conversion class from {@link Ad} to {@link BasicAdvert}
 */
@Component
public class ApiAdToBasicAdvertConverter {

    private ApiAdvertStatusToAdvertStatusConverter statusConverter;

    /**
     * Constructor
     *
     * @param statusConverter - An AdvertStatus converter, API -> internal
     */
    @Autowired
    public ApiAdToBasicAdvertConverter(ApiAdvertStatusToAdvertStatusConverter statusConverter) {
        this.statusConverter = statusConverter;
    }

    /**
     * Perform the conversion
     *
     * @param apiAdvert the API {@link Ad} to convert
     * @return a converted {@link BasicAdvert}
     */
    public final BasicAdvert convert(Ad apiAdvert) {
        AdvertEntity advert = new AdvertEntity();
        advert.setId(apiAdvert.getId());
        advert.setDescription(apiAdvert.getDescription());
        advert.setTitle(apiAdvert.getTitle());
        advert.setStatus(statusConverter.convert(apiAdvert.getStatus()));
        if (apiAdvert.getLiveDate() != null) {
            advert.setLiveDate(apiAdvert.getLiveDate().toDate());
        }
        if (apiAdvert.getLocationId() != null) {
            advert.setLocationId(apiAdvert.getLocationId().intValue());
        }
        List<Integer> locationIds = new ArrayList<Integer>();
        if (apiAdvert.getLocations() != null) {
            for (Location location : apiAdvert.getLocations()) {
                locationIds.add(location.getId().intValue());
            }
        }
        advert.setLocationIds(locationIds);
        if (apiAdvert.getCategoryId() != null) {
            advert.setCategoryId(apiAdvert.getCategoryId());
        }
        return advert;
    }
}
