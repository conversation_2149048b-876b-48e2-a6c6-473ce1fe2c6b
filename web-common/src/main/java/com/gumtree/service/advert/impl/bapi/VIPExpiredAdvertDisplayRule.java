package com.gumtree.service.advert.impl.bapi;

import com.gumtree.domain.advert.AdvertStatus;
import com.gumtree.domain.advert.BasicAdvert;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * A rule to determine whether or not a given {@link BasicAdvert} should be displayed
 */
@Component
public class VIPExpiredAdvertDisplayRule {

    /**
     * Return true if the given {@link BasicAdvert} should be displayed
     *
     * @param advert - the {@link BasicAdvert} to check
     * @return true or false
     */
    public final boolean shouldDisplay(BasicAdvert advert) {
        try {
            if (AdvertStatus.EXPIRED.equals(advert.getStatus())) {
                checkIfNull(advert.getLiveDate());
                checkIfNull(advert.getId());
                checkIfNull(advert.getDescription());
                checkIfNull(advert.getTitle());
                checkIfNull(advert.getCategoryId());
                checkIfNull(advert.getLocationId());
                Date archivedDate = advert.getArchivedDate();
                if (archivedDate == null) {
                    return true;
                } else {
                    DateTime archivedDT = new DateTime(archivedDate);
                    return !archivedDT.isBeforeNow();
                }
            }
        } catch (ValueIsNullException ignore) {
            ignore.printStackTrace();
        }
        return false;
    }

    private void checkIfNull(Object value) throws ValueIsNullException {
        if (value == null) {
            throw new ValueIsNullException();
        }
    }

    /**
     * An exception thrown if a parameter expected not to be null is null
     */
    private static class ValueIsNullException extends Exception {
    }
}
