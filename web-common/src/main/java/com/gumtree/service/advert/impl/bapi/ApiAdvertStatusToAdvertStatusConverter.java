package com.gumtree.service.advert.impl.bapi;

import com.gumtree.api.AdStatus;
import com.gumtree.domain.advert.AdvertStatus;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * converter to convert BAPI AdStatus to domain.advert.AdvertStatus
 */
@Component
public class ApiAdvertStatusToAdvertStatusConverter {

    private static Map<AdStatus, AdvertStatus> mapping = new HashMap<AdStatus, AdvertStatus>();

    static {
        mapping.put(AdStatus.LIVE, AdvertStatus.LIVE);
        mapping.put(AdStatus.EXPIRED, AdvertStatus.EXPIRED);
        mapping.put(AdStatus.DRAFT, AdvertStatus.PRE_PUBLISHED);
        mapping.put(AdStatus.AWAITING_ACTIVATION, AdvertStatus.PRE_PUBLISHED);
        mapping.put(AdStatus.AWAITING_SCREENING, AdvertStatus.PRE_PUBLISHED);
        mapping.put(AdStatus.AWAITING_PAYMENT, AdvertStatus.PRE_PUBLISHED);
        mapping.put(AdStatus.NEEDS_EDITING, AdvertStatus.PRE_PUBLISHED);
        mapping.put(AdStatus.AWAITING_CS_REVIEW, AdvertStatus.PRE_PUBLISHED);
        mapping.put(AdStatus.DELETED_CS, AdvertStatus.DELETED_CS);
        mapping.put(AdStatus.DELETED_USER, AdvertStatus.EXPIRED);
    }

    /**
     * converts BAPI AdStatus to corresponding AdvertStatus
     *
     * @param apiAdStatus BAPI AdStatus
     * @return AdvertStatus
     */
    public final AdvertStatus convert(AdStatus apiAdStatus) {
        if (!mapping.containsKey(apiAdStatus)) {
            throw new IllegalArgumentException("Unable to convert unexpected AdStatus "
                    + (apiAdStatus != null ? apiAdStatus.getName() : "null"));
        }
        return mapping.get(apiAdStatus);
    }
}
