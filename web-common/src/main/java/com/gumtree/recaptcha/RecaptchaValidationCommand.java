package com.gumtree.recaptcha;

import com.google.common.collect.Lists;
import com.google.common.primitives.Ints;
import com.gumtree.api.command.GumtreeHystrixCommand;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.util.http.GumtreeHttpClient;
import com.gumtree.config.CommonProperty;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.params.ConnRoutePNames;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;

class RecaptchaValidationCommand extends GumtreeHystrixCommand<RecaptchaValidationResult> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecaptchaValidator.class);

    private static final String VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";

    private static final String GROUP_NAME = "Recaptcha";
    private static final String NAME = RecaptchaValidationCommand.class.getName();

    private static final HttpParams REQUEST_PARAMS = getRequestParams();

    private static final List<String> INVALID_RECAPTCHA_ERROR_CODES = Arrays.asList("invalid-input-response", "timeout-or-duplicate");

    private final GumtreeHttpClient httpClient;
    private final String clientIp;
    private final String userResponse;

    public RecaptchaValidationCommand(GumtreeHttpClient httpClient, String userResponse, String clientIp) {
        super(GROUP_NAME, NAME);
        this.httpClient = httpClient;
        this.clientIp = clientIp;
        this.userResponse = userResponse;
    }

    @Override
    protected RecaptchaValidationResult run() throws Exception {
        try {
            HttpPost req = buildRecaptchaApiRequest();
            RecaptchaApiResponse response = httpClient.post(req, RecaptchaApiResponse.class);
            return parseRecaptchaApiResponse(response);
        } catch (Exception e) {
            LOGGER.warn("Problem validating recaptcha.", e);
            throw e;
        }
    }

    @Override
    protected RecaptchaValidationResult getFallback() {
        return RecaptchaValidationResult.ERROR;
    }

    private HttpPost buildRecaptchaApiRequest() {
        String recaptchaSecret = GtProps.getStr(CommonProperty.RECAPTCHA_SECRET);

        List<NameValuePair> nvps = Lists.newArrayList(
                new BasicNameValuePair("secret", recaptchaSecret),
                new BasicNameValuePair("response", userResponse),
                new BasicNameValuePair("remoteip", clientIp));

        HttpEntity entity;
        try {
            entity = new UrlEncodedFormEntity(nvps);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        HttpPost req = new HttpPost(VERIFY_URL);
        req.setEntity(entity);
        req.addHeader("accept", "application/json");
        req.setParams(REQUEST_PARAMS);
        return req;
    }

    private RecaptchaValidationResult parseRecaptchaApiResponse(RecaptchaApiResponse response) {
        if (!ArrayUtils.isEmpty(response.getErrorCodes())) {
            if (Arrays.stream(response.getErrorCodes())
                    .anyMatch(c -> INVALID_RECAPTCHA_ERROR_CODES.contains(c))) {
                return RecaptchaValidationResult.INVALID;
            } else {
                LOGGER.warn("Bad request errors raised by Recaptcha: {}", StringUtils.join(response.getErrorCodes(), ","));
                return RecaptchaValidationResult.ERROR;
            }
        }

        return response.isSuccess() ? RecaptchaValidationResult.VALID : RecaptchaValidationResult.INVALID;
    }

    private static HttpParams getRequestParams() {
        try {
            HttpParams httpReqConf = new BasicHttpParams();

            // configure HTTP and HTTPS proxies
            String httpProxyHost = System.getProperty("http.proxyHost");
            Integer httpProxyPort = Ints.tryParse(System.getProperty("http.proxyPort", ""));
            LOGGER.info("HTTP proxy: {} on port {}", httpProxyHost, httpProxyPort);
            if (StringUtils.isNotBlank(httpProxyHost) && httpProxyPort != null) {
                httpReqConf.setParameter(ConnRoutePNames.DEFAULT_PROXY, new HttpHost(httpProxyHost, httpProxyPort, "http"));
            }
            return httpReqConf;
        } catch (Exception e) {
            throw new RuntimeException("Failed to configure HTTP(S) proxies for recaptcha APIs", e);
        }
    }

}