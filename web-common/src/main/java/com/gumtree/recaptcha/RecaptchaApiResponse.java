package com.gumtree.recaptcha;

import org.codehaus.jackson.annotate.JsonProperty;

class RecaptchaApiResponse {

    private boolean success;

    /**
     * timestamp of the challenge load (ISO format yyyy-MM-dd'T'HH:mm:ssZZ)
     **/
    @<PERSON>son<PERSON>roperty("challenge_ts")// TODO fully migrate to fasterxml version(different package in web and seller)
    @com.fasterxml.jackson.annotation.JsonProperty("challenge_ts")
    private String challengeTs;

    /**
     * the hostname of the site where the reCAPTCHA was solved
     **/
    private String hostname;

    @JsonProperty("error-codes")// TODO fully migrate to fasterxml version(different package in web and seller)
    @com.fasterxml.jackson.annotation.JsonProperty("error-codes")
    private String[] errorCodes;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getChallengeTs() {
        return challengeTs;
    }

    public void setChallengeTs(String challengeTs) {
        this.challengeTs = challengeTs;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public String[] getErrorCodes() {
        return errorCodes;
    }

    public void setErrorCodes(String[] errorCodes) {
        this.errorCodes = errorCodes;
    }

}
