package com.gumtree.recaptcha;

import com.gumtree.common.util.http.GumtreeHttpClient;

public class RecaptchaValidator {

    private final GumtreeHttpClient httpClient;

    public RecaptchaValidator(GumtreeHttpClient httpClient) {
        this.httpClient = httpClient;
    }

    public RecaptchaValidationResult validateResponse(String userResponse, String clientIp) {
        return new RecaptchaValidationCommand(httpClient, userResponse, clientIp).execute();

    }

}
