package com.gumtree.google.authservice;

import com.google.auth.oauth2.GoogleCredentials;
import com.gumtree.authgenerator.GoogleJwtTokenProvider;

public class GoogleAuthService {
    private final String audience;
    private final GoogleCredentials credentials;
    private final GoogleJwtTokenProvider jwtTokenProvider;

    public GoogleAuthService(String audience, GoogleCredentials credentials, GoogleJwtTokenProvider tokenProvider) {
        this.audience = audience;
        this.credentials = credentials;
        this.jwtTokenProvider = tokenProvider;
    }

    public String getAuthKey() {
        return jwtTokenProvider.getToken(credentials, this.audience);
    }
}
