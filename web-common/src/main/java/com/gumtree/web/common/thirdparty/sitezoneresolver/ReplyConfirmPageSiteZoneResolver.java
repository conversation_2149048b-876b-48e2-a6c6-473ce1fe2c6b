package com.gumtree.web.common.thirdparty.sitezoneresolver;

import com.gumtree.web.common.thirdparty.LegacySiteNameResolver;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Used to resolve site names into their legacy system equivalents.
 */
@Component
public final class ReplyConfirmPageSiteZoneResolver extends DefaultSiteZoneResolver {

    @Autowired
    public ReplyConfirmPageSiteZoneResolver(LegacySiteNameResolver legacySiteNameResolver) {
        super(legacySiteNameResolver);
    }

    @Override
    public String resolveSiteName(ThirdPartyRequestContext<?> ctx) {
        String site = super.resolveSiteName(ctx);
        return "propertyforsale".equals(site) ? "propforsale" : site;
    }

}
