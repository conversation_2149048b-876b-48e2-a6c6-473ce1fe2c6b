package com.gumtree.web.common.thirdparty.sitezoneresolver;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.web.common.thirdparty.LegacySiteNameResolver;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Used to resolve site names into their legacy system equivalents.
 */
@Component
public class DefaultSiteZoneResolver implements SiteZoneResolver {

    protected static final String TOP_LEVEL = "toplevel";

    private final LegacySiteNameResolver legacySiteNameResolver;

    @Autowired
    public DefaultSiteZoneResolver(LegacySiteNameResolver legacySiteNameResolver) {
        this.legacySiteNameResolver = legacySiteNameResolver;
    }

    @Override
    public String resolveSiteName(ThirdPartyRequestContext<?> ctx) {
        String defaultSite;
        Category category = ctx.getCategory();
        if (Categories.ALL.is(category) || Categories.FLATS_AND_HOUSES.is(category)) {
            defaultSite = TOP_LEVEL;
        } else {
            defaultSite = ctx.getCategory(1).getSeoName();
            // TODO: Could remove in future
            // Resolve site name (generally for legacy reasons)
            defaultSite = legacySiteNameResolver.resolveSiteName(defaultSite);
        }
        return defaultSite;
    }

    @Override
    public String resolveZoneName(ThirdPartyRequestContext<?> ctx) {
        String defaultZone;
        Category category = ctx.getCategory();
        if (Categories.ALL.is(category)) {
            defaultZone = TOP_LEVEL;
        } else if (Categories.FLATS_AND_HOUSES.is(category)) {
            defaultZone = Categories.FLATS_AND_HOUSES.getSeoName();
        } else {
            Category l2Category = ctx.getCategory(2);
            defaultZone = l2Category == null ? TOP_LEVEL : l2Category.getSeoName();
        }
        return defaultZone;
    }
}
