package com.gumtree.web.common.thirdparty;

import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.legacy.LegacySite;
import com.gumtree.util.helper.DisplayAdsViewMode;
import com.gumtree.web.abtest.AbTestType;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.zeno.core.domain.PageType;

import javax.servlet.http.HttpServletRequest;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Class that adapts a {@link com.gumtree.web.common.page.context.GumtreePageContext}. Turns it into a {@link
 * ThirdPartyRequestContext}.
 */
public final class ThirdPartyRequestContextAdapter<T> implements ThirdPartyRequestContext<T> {

    private final GumtreePageContext<T> pageContext;

    private Map<Integer, Category> categoryLevelsMap = new HashMap<Integer, Category>();

    private Category l1Category;

    private Map<Integer, Location> locationHierarchyMap;

    /**
     * @param pageContext      the context to adapt
     */
    public ThirdPartyRequestContextAdapter(GumtreePageContext<T> pageContext) {
        this.pageContext = pageContext;
        this.locationHierarchyMap = pageContext.getLocationHierarchy();
        processCategoryLevels();
    }

    @Override
    public PageType getPageType() {
        return pageContext.getPageType();
    }

    @Override
    public Location getLocation() {
        return pageContext.getLocation();
    }

    @Override
    public String getLocationUserInput() {
        return pageContext.getLocationUserInput();
    }

    @Override
    public Boolean isRadialSearch() {
        return pageContext.isRadialSearch();
    }

    @Override
    public Location getLocation(int level) {
        return locationHierarchyMap.get(level);
    }

    @Override
    public LegacySite getLegacySite() {
        return pageContext.getLegacySite();
    }

    @Override
    public Category getCategory() {
        return pageContext.getCategory();
    }

    @Override
    public Account getAccount() {
        return pageContext.getAccount();
    }

    @Override
    public User getUser() {
        return pageContext.getUser();
    }

    @Override
    public Boolean hasContent() {
        return pageContext.hasContent();
    }

    @Override
    public String getSearchTerm() {
        return pageContext.getSearchTerm();
    }

    public DisplayAdsViewMode getDisplayAdsViewMode() {
        return pageContext.getDisplayAdsViewMode();
    }

    public Integer getPageNumber() {
        return pageContext.getPageNumber();
    }

    @Override
    public String getUrl() {
        return pageContext.getUrl();
    }

    @Override
    public List<AbTestType> getAbTestTypes() {
        return pageContext.getAbTestTypes();
    }

    public T getPageModel() {
        return pageContext.getPageModel();
    }

    @Override
    public Category getL1Category() {
        return l1Category;
    }

    @Override
    public Category getCategory(int level) {
        return categoryLevelsMap.get(level);
    }

    @Override
    public Location getCounty() {
        return pageContext.getCounty();
    }

    @Override
    public URL getAbsoluteRequestUrl() {
        return pageContext.getAbsoluteRequestUrl();
    }

    @Override
    public String getReferrer() {
        return pageContext.getReferrer();
    }

    @Override
    public HttpServletRequest getHttpServletRequest(){
        return pageContext.getHttpServletRequest();
    }

    @Override
    public Order getOrder() {
        return pageContext.getOrder();
    }

    @Override
    public String getDeviceType() {
        return pageContext.getDeviceType();
    }

    public GumtreePageContext getPageContext() {
        return pageContext;
    }

    /**
     * TODO: We really need to look at getting rid of the need to treat flats & houses differently.
     * <p/>
     * We have ugly code here due to the need to treat flats & houses category differently - i.e. the l2 flats &
     * houses categories are treated as l1s.
     */
    private void processCategoryLevels() {
        Category category = getCategory();
        if (Categories.ALL.is(category) || Categories.FLATS_AND_HOUSES.is(category)) {
            categoryLevelsMap.put(0, category);
        } else {
            Map<Integer, Category> levels = pageContext.getCategoryLevelHierarchy();
            l1Category = levels.get(1);
            for (Integer level : levels.keySet()) {
                categoryLevelsMap.put(getCategoryLevel(l1Category, level), levels.get(level));
            }
        }
    }

    private int getCategoryLevel(Category l1Category, Integer level) {
        return level > 0 && Categories.FLATS_AND_HOUSES.is(l1Category) ? level - 1 : level;
    }

}
