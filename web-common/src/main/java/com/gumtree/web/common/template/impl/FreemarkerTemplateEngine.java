package com.gumtree.web.common.template.impl;

import com.gumtree.web.common.template.TemplateEngine;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

/**
 * Freemarker implementation of {@link TemplateEngine}
 */
@Component
public class FreemarkerTemplateEngine implements TemplateEngine {

    private Configuration configuration;

    /**
     * Constructor.
     *
     * @param configurer the {@link org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer}
     */
    @Autowired
    public FreemarkerTemplateEngine(FreeMarkerConfigurer configurer) {
        this.configuration = configurer.getConfiguration();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String process(String templateName, Map<String, Object> model) {
        try {
            StringWriter writer = new StringWriter();
            Template template = configuration.getTemplate(templateName + ".ftl");
            template.process(model, writer);
            return writer.toString();
        } catch (IOException ioex) {
            throw new RuntimeException(ioex);
        } catch (TemplateException tex) {
            throw new RuntimeException(tex);
        }
    }
}
