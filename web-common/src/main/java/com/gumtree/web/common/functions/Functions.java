package com.gumtree.web.common.functions;

import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.value.PriceFrequency;
import com.gumtree.util.SymbolicNameMapper;
import com.gumtree.web.common.styling.DoNothingStringStyler;
import com.gumtree.web.common.styling.ListItemStyler;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.util.HtmlUtils;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.PRICE_FREQUENCY;

/**
 * Utility functions for JSP pages.
 */
@Component
public final class Functions {

    /**
     * Private constructor.
     */
    private Functions() {

    }

    /**
     * Extract display text for price frequency.
     *
     * @param advert the advert from which to extract price frequency
     * @return display text for price frequency
     * @deprecated Put this value in the model using the attribute service !
     */
    @Deprecated
    public static String getPriceFrequency(Advert advert) {

        if (advert != null) {
            Attribute priceFrequencyAttribute = advert.getAttribute(PRICE_FREQUENCY.getName());

            if (priceFrequencyAttribute != null) {
                PriceFrequency priceFrequency = priceFrequencyAttribute.getValue().as(PriceFrequency.class);
                if (priceFrequency == PriceFrequency.PER_MONTH) {
                    return "pm";
                } else if (priceFrequency == PriceFrequency.PER_WEEK) {
                    return "pw";
                }
            }
        }

        return "";
    }

    /**
     * Check whether a collection contains a given object.
     *
     * @param coll the collection to check
     * @param o    the object to check for
     * @return true or false
     */
    public static boolean contains(Collection<?> coll, Object o) {
        return coll != null && coll.contains(o);
    }

    /**
     * Convert symbolic category names to their fixed ids.
     *
     * @param data the comma separated list of symbolic category names
     * @return category ids for the given symbolic names.
     */
    public static List<Long> categoryIds(String data) {
        List<Long> categoryIds = new ArrayList<Long>();

        if (data != null) {
            String[] names = data.split(",");
            for (String name : names) {
                Long catId = SymbolicNameMapper.getCategoryId(name);

                if (catId != null) {
                    categoryIds.add(catId);
                } else {
                    throw new IllegalArgumentException("No category with symbolic name: " + name);
                }
            }
        }

        return categoryIds;
    }

    /**
     * Replace all new lines (\n) with HTML <br/> tags.
     *
     * @param data the data to format.
     * @return all new lines (\n) with HTML <br/> tags.
     */
    public static String nl2br(String data) {
        return data.replaceAll("\n", "<br/>");
    }

    /**
     * Render a list of strings with separators.
     *
     * @param items     the items to render
     * @param separator the item separator to use
     * @return a list of strings with separators rendered as a string
     */
    public static String renderList(List<String> items, String separator) {
        return renderList(items, separator, new DoNothingStringStyler());
    }

    /**
     * Render a list of strings with separators where the last item is given <strong> emphasis.
     *
     * @param items     the items to render
     * @param separator the item separator to use
     * @return a list of strings with separators rendered as a string
     */
    public static String renderListWithStrongFinalItem(List<String> items, String separator) {
        return renderList(items, separator, new StrongItemStyler());
    }

    /**
     * Render a list of strings with separators with optional item styling.
     *
     * @param items     the items to render
     * @param separator the item separator to use
     * @param styler    styling to add to item
     * @return a list of strings with separators rendered as a string
     */
    public static String renderList(List<String> items, String separator, ListItemStyler<String> styler) {
        Assert.notNull(separator);
        StringBuilder builder = new StringBuilder();
        if (items != null) {
            int index = 0;
            for (String item : items) {
                if (index > 0) {
                    builder.append(separator);
                }
                builder.append(styler.styleItem(HtmlUtils.htmlEscape(item != null ? item : ""), index, items.size()));
                index++;
            }
        }
        return builder.toString();
    }

    /**
     * Checks to see if the supplied object has the given property name. Need to have a getter method for that property
     * on the object.
     * @param o - The supplied object
     * @param propertyName - Property name on object
     * @return true if the propertyName exists on the given object. False otherwise
     */
    public static boolean hasProperty(Object o, String propertyName) {
        if (o == null || propertyName == null) {
            return false;
        }
        BeanInfo beanInfo;
        try {
            beanInfo = java.beans.Introspector.getBeanInfo(o.getClass());
        } catch (IntrospectionException e) {
            return false;
        }

        for (final PropertyDescriptor pd : beanInfo.getPropertyDescriptors()) {
            if (propertyName.equals(pd.getName())) {
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param o - Supplied Object o
     * @param className - The supplied class name
     * @return true if o is a class of type className, false otherwise
     */

    public static boolean instanceOf(Object o, String className) {
        try {
            return Class.forName(className).isInstance(o);
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * Styles last item in a list with strong emphasis.
     */
    private static final class StrongItemStyler implements ListItemStyler<String> {
        @Override
        public String styleItem(String item, int index, int totalItems) {
            if (index == (totalItems - 1)) {
                StringBuilder builder = new StringBuilder();
                builder.append("<strong>").append(item).append("</strong>");
                return builder.toString();
            }
            return item;
        }
    }
}
