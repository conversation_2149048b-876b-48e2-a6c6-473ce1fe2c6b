package com.gumtree.web.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.gumtree.web.common.domain.location.Location;

import java.io.IOException;

/**
 * Serializer to clean model for FE
 */
public class LocationSerializer extends JsonSerializer<Location> {

    @Override
    public void serialize(Location location, JsonGenerator jgen, SerializerProvider provider) throws IOException {
        jgen.writeStartObject();

        writeCommonAttributes(location, jgen);

        serializeParent(location, jgen, 0);

        jgen.writeEndObject();
    }

    private void serializeParent(Location location, JsonGenerator jgen, int depth) throws IOException {
        Location parent = location.getParent();
        //show only 2 for FE model display purposes
        if (parent != null && depth < 2) {
            jgen.writeFieldName("parent");

            jgen.writeStartObject();
            writeCommonAttributes(parent, jgen);

            //only render this for the direct parent
            if (depth == 0) {
                jgen.writeFieldName("zoomIns");
                jgen.writeStartArray();
                //show only 2 for FE model display purposes
                for (int i = 0; i < 2 && i < parent.getZoomIns().size(); i++) {
                    Location zin = parent.getZoomIns().get(i);
                    jgen.writeStartObject();
                    writeCommonAttributes(zin, jgen);
                    jgen.writeEndObject();
                }

                jgen.writeEndArray();
            }

            serializeParent(parent, jgen, depth + 1);

            jgen.writeEndObject();
        }
    }

    private void writeCommonAttributes(Location location, JsonGenerator jgen) throws IOException {
        jgen.writeFieldName("id");
        jgen.writeNumber(location.getId());
        jgen.writeFieldName("seoName");
        jgen.writeString(location.getSeoName());
        jgen.writeFieldName("name");
        jgen.writeString(location.getName());

        jgen.writeFieldName("latitude");
        jgen.writeNumber(location.getLatitude());
        jgen.writeFieldName("longitude");
        jgen.writeNumber(location.getLongitude());

        jgen.writeFieldName("landingPage");
        jgen.writeBoolean(location.hasLandingPage());

        jgen.writeFieldName("locality");
        jgen.writeString(location.getLocality());
        jgen.writeFieldName("region");
        jgen.writeString(location.getRegion());
        jgen.writeFieldName("country");
        jgen.writeString(location.getCountry());
    }
}
