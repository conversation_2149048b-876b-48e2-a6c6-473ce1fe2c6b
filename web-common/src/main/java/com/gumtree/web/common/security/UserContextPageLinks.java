package com.gumtree.web.common.security;

import com.gumtree.util.model.Link;

/**
 * Represents the links in the application that can vary based on the current user context, i.e.
 * <p/>
 * - not logged in
 * - logged into legacy
 * - logged into bushfire
 */
public interface UserContextPageLinks {
    /**
     * @return the login link
     */
    Link getLoginLink();

    /**
     * @return the create account link
     */
    Link getCreateAccountLink();

    /**
     * @return the post ad link
     */
    Link getPostAdLink();

    /**
     * @return the post event link
     */
    Link getPostEventLink();

    /**
     * @return the manage ads link
     */
    Link getManageAdsLink();

    /**
     * @return the edit account link
     */
    Link getEditAccountLink();

    /**
     * @return Logout User Url
     */
    Link getLogoutLink();
}
