package com.gumtree.web.common.template;

import java.util.Map;

/**
 * A template engine is responsible for taking a model of data and processing it through
 * a template to produce {@link String} output.
 */
public interface TemplateEngine {

    /**
     * Take a model of data and process it through a template to produce {@link String} output.
     *
     * @param templateName the name of the template to generate the output.
     * @param model        the model to send to the template.
     * @return the {@link String} output after successful processing.
     */
    String process(String templateName, Map<String, Object> model);
}
