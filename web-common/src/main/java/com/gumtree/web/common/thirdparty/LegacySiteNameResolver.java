package com.gumtree.web.common.thirdparty;

import com.google.common.collect.ImmutableMap;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class LegacySiteNameResolver {

    private static final Map<String, String> LEGACY_SITE_NAMES =  ImmutableMap.of(
            "cars-vans-motorbikes", "cars",
            "for-sale", "forsale",
            "property-for-sale", "propertyforsale",
            "flats-and-houses-for-rent", "rentals",
            "business-services", "services"
    );

    public String resolveSiteName(String siteName) {
        return LEGACY_SITE_NAMES.containsKey(siteName) ? LEGACY_SITE_NAMES.get(siteName) : siteName;
    }

}
