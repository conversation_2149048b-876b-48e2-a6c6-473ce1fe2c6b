package com.gumtree.web.common;

import java.util.LinkedList;

import static com.gumtree.web.common.NameValuePairs.Errors.*;

public final class NameValuePairs extends LinkedList<NameValuePair> {

    public static NameValuePairs empty() {
        return new NameValuePairs();
    }

    public NameValuePairs add(String errorCode) {
        this.add(new NameValuePair("message", errorCode));
        return this;
    }

    public NameValuePairs add(String name, String value) {
        this.add(new NameValuePair(name, value));
        return this;
    }

    public NameValuePairs withBapiReplyError() {
        this.add(BAPI_REPLY_ERROR);
        return this;
    }

    public NameValuePairs withInvalidAdvertReplyError() {
        this.add(INVALID_REPLY_ADVERT_ERROR);
        return this;
    }

    public NameValuePairs withAlreadyAppliedReplyError() {
        this.add(ALREADY_APPLIED_REPLY_ERROR);
        return this;
    }

    public NameValuePairs withInvalidJobIdentityError() {
        this.add(INVALID_JOB_ID_ERROR);
        return this;
    }

    public NameValuePairs withInvalidEmailError() {
        this.add(INVALID_EMAIL_ERROR);
        return this;
    }

    public NameValuePairs withBadCvError() {
        this.add(BAD_CV_ERROR);
        return this;
    }

    public static final class Errors {

        private Errors() {}

        public static final NameValuePair BAPI_REPLY_ERROR =
                new NameValuePair("global", "reply.global.bapierror");

        public static final NameValuePair INVALID_REPLY_ADVERT_ERROR =
                new NameValuePair("global", "reply.advertid.invalid");

        public static final NameValuePair ALREADY_APPLIED_REPLY_ERROR =
                new NameValuePair("global", "reply.global.already_applied");

        public static final NameValuePair INVALID_JOB_ID_ERROR =
           new NameValuePair("global", "reply.global.invalid_job_id");

        public static final NameValuePair INVALID_EMAIL_ERROR =
                new NameValuePair("global", "reply.global.invalid_email");

        public static final NameValuePair BAD_CV_ERROR =
                new NameValuePair("global", "reply.global.cv_not_ok");
    }
}
