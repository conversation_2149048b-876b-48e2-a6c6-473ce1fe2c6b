package com.gumtree.web.common.interceptor;

import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * A simple {@link HandlerInterceptor} that inserts a set of custom response headers
 * into the {@link HttpServletResponse} post request handling.
 *
 * <AUTHOR>
 */
public class CustomResponseHeadersInterceptor implements HandlerInterceptor {

    /**
     * The custom headers to apply to the outgoing response
     */
    private Map<String, String> customHeaders;

    /**
     * Constructor
     *
     * @param customHeaders custom headers to apply to the outgoing response
     */
    public CustomResponseHeadersInterceptor(Map<String, String> customHeaders) {
        Assert.notNull(customHeaders);
        this.customHeaders = customHeaders;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                                   Object handler) throws Exception {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                                 ModelAndView modelAndView) throws Exception {
        Assert.notNull(response);
        for (String key : customHeaders.keySet()) {
            String value = customHeaders.get(key);
            if (StringUtils.hasLength(value)) {
                response.setHeader(key, value);
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                      Object handler, Exception ex) throws Exception {

    }
}
