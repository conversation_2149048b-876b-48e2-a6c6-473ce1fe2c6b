package com.gumtree.web.common.security;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;

/**
 * Factory for {@link UserContextPageLinks}
 */
public interface UserContextPageLinksFactory {

    /**
     * Create page links given the current contextual information.
     *
     * @param location location context
     * @param category category context
     * @return page links given the current contextual information.
     */
    UserContextPageLinks createPageLinks(Location location, Category category);
}
