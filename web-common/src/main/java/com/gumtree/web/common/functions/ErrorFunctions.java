package com.gumtree.web.common.functions;

import com.gumtree.web.common.error.MessageResolvingErrorSource;

/**
 * Error functions for displaying errors in JSPs.
 */
public final class ErrorFunctions {

    /**
     * Constructor for static utility class.
     */
    private ErrorFunctions() {

    }

    /**
     * Determines if the given error source contains global errors.
     *
     * @param errors the error source.
     * @return true or false
     */
    public static Boolean hasError(MessageResolvingErrorSource errors) {
        return errors != null ? errors.containsGlobalErrors() : null;
    }

    /**
     * Get a global error.
     *
     * @param errors the error source
     * @return a global error.
     */
    public static String getError(MessageResolvingErrorSource errors) {
        // This just takes the first global error in the list (if there are any errors in the list)
        return hasError(errors) ? errors.getResolvedGlobalErrorMessages().get(0) : null;
    }

    /**
     * Determines if the given error source contains a specified field error.
     *
     * @param errors the error source
     * @param field  the specified field
     * @return true or false
     */
    public static Boolean hasError(MessageResolvingErrorSource errors, String field) {
        return errors != null ? errors.containsFieldError(field) : null;
    }

    /**
     * Get a field error.
     *
     * @param errors the error source
     * @param field  the specified field
     * @return a field error.
     */
    public static String getError(MessageResolvingErrorSource errors, String field) {
        // This just takes the first field error in the list (if there are any errors in the list)
        return hasError(errors, field) ? errors.getResolvedFieldErrorMessages(field).get(0) : null;
    }
}
