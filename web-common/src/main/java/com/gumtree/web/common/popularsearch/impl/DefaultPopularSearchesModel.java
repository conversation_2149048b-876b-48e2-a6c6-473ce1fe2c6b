package com.gumtree.web.common.popularsearch.impl;

import com.gumtree.util.model.Link;
import com.gumtree.web.common.page.model.PopularSearchesModel;

import java.util.List;

/**
 * DefaultPopularSearchesModel
 */
public class DefaultPopularSearchesModel implements PopularSearchesModel {

    private String ukPopularSearchesDescription;
    private String locationPopularSearchesDescription;
    private List<Link> locationPopularSearchesLinks;
    private List<Link> ukPopularSearchesLinks;

    /**
     * @param ukPopularSearchesDescription ukPopularSearchesDescription
     * @param ukPopularSearchesLinks       ukPopularSearchesLinks
     */
    public DefaultPopularSearchesModel(String ukPopularSearchesDescription, List<Link> ukPopularSearchesLinks) {
        this.ukPopularSearchesDescription = ukPopularSearchesDescription;
        this.ukPopularSearchesLinks = ukPopularSearchesLinks;
    }

    /**
     * @param ukPopularSearchesDescription ukPopularSearchesDescription
     * @param ukPopularSearchesLinks       ukPopularSearchesLinks
     * @param locationPopularSearchesDescription
     *                                     locationPopularSearchesDescription
     * @param locationPopularSearchesLinks locationPopularSearchesLinks
     */
    public DefaultPopularSearchesModel(String ukPopularSearchesDescription, List<Link> ukPopularSearchesLinks,
                                       String locationPopularSearchesDescription,
                                       List<Link> locationPopularSearchesLinks) {
        this.ukPopularSearchesDescription = ukPopularSearchesDescription;
        this.locationPopularSearchesDescription = locationPopularSearchesDescription;
        this.locationPopularSearchesLinks = locationPopularSearchesLinks;
        this.ukPopularSearchesLinks = ukPopularSearchesLinks;
    }

    @Override
    public final String getPopularSearchesDescription() {
        return locationPopularSearchesDescription;
    }

    @Override
    public final String getUKPopularSearchesDescription() {
        return ukPopularSearchesDescription;
    }

    @Override
    public final List<Link> getPopularSearchesLinks() {
        return locationPopularSearchesLinks;
    }

    @Override
    public final List<Link> getUKPopularSearchesLinks() {
        return ukPopularSearchesLinks;
    }
}
