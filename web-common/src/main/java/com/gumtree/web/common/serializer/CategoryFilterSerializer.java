package com.gumtree.web.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.gumtree.api.category.domain.Category;

import java.io.IOException;

/**
 * Serializer to clean model for FE
 */
public class CategoryFilterSerializer extends JsonSerializer<Category> {

    @Override
    public void serialize(Category c, JsonGenerator jgen, SerializerProvider provider) throws IOException {
        jgen.writeStartObject();
        jgen.writeFieldName("seoName");
        jgen.writeString(c.getSeoName());

        jgen.writeFieldName("name");
        jgen.writeString(c.getName());

        renderChildren(c, jgen);

        jgen.writeEndObject();
    }

    private void renderChildren(Category cat, JsonGenerator jgen) throws IOException {

        jgen.writeArrayFieldStart("children");

        for (Category c : cat.getChildren()) {

            jgen.writeStartObject();
            jgen.writeFieldName("seoName");
            jgen.writeString(c.getSeoName());

            jgen.writeFieldName("name");
            jgen.writeString(c.getName());

            renderChildren(c, jgen);

            jgen.writeEndObject();
        }

        jgen.writeEndArray();
    }
}
