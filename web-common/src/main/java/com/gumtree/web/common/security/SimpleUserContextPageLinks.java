package com.gumtree.web.common.security;

import com.gumtree.util.model.Link;

/**
 * Default implementation of {@link UserContextPageLinks}.
 */
public final class SimpleUserContextPageLinks implements UserContextPageLinks {

    private Link loginLink;

    private Link createAccountLink;

    private Link postAdLink;

    private Link postEventLink;

    private Link manageAdsLink;

    private Link editAccountLink;

    private Link logoutLink;

    /**
     * Constructor.
     *
     * @param loginLink         the login link
     * @param createAccountLink the create account link
     * @param postAdLink        the post ad link
     * @param postEventLink     the post event link
     * @param manageAdsLink     the manage ads link
     * @param editAccountLink   the edit account link
     * @param logoutLink        the logout link
     */
    public SimpleUserContextPageLinks(
            Link loginLink,
            Link createAccountLink,
            Link postAdLink,
            Link postEventLink,
            Link manageAdsLink,
            Link editAccountLink,
            Link logoutLink) {

        this.loginLink = loginLink;
        this.createAccountLink = createAccountLink;
        this.postAdLink = postAdLink;
        this.postEventLink = postEventLink;
        this.manageAdsLink = manageAdsLink;
        this.editAccountLink = editAccountLink;
        this.logoutLink = logoutLink;
    }

    @Override
    public Link getLoginLink() {
        return loginLink;
    }

    @Override
    public Link getCreateAccountLink() {
        return createAccountLink;
    }

    @Override
    public Link getPostAdLink() {
        return postAdLink;
    }

    @Override
    public Link getPostEventLink() {
        return postEventLink;
    }

    @Override
    public Link getManageAdsLink() {
        return manageAdsLink;
    }

    @Override
    public Link getEditAccountLink() {
        return editAccountLink;
    }

    @Override
    public Link getLogoutLink() {
        return logoutLink;
    }
}
