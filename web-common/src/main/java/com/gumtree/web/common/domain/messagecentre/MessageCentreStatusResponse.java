package com.gumtree.web.common.domain.messagecentre;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MessageCentreStatusResponse {

    private static final Logger LOGGER = LoggerFactory.getLogger(MessageCentreStatusResponse.class);

    private String state;
    private String details;
    private String errorLog;

    public MessageCentreStatusResponse(Builder builder) {
        this.state = builder.state;
        this.details = builder.details;
        this.errorLog = builder.errorLog;
    }

    public String getState() {
        return state;
    }

    public String getDetails() {
        return details;
    }

    public String getErrorLog() {
        return errorLog;
    }

    public static class Builder {
        private String state;
        private String details;
        private String errorLog;

        public Builder setState(String state) {
            this.state = state;
            return this;
        }

        public Builder setDetails(String details) {
            this.details = details;
            return this;
        }

        public Builder setErrorLog(String errorLog) {
            this.errorLog = errorLog;
            return this;
        }

        public MessageCentreStatusResponse build() {
            return new MessageCentreStatusResponse(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof MessageCentreStatusResponse)){
            return false;
        }

        MessageCentreStatusResponse that = (MessageCentreStatusResponse) o;

        if(details != null ? !details.equals(that.details) : that.details != null){
            return false;
        }
        if(errorLog != null ? !errorLog.equals(that.errorLog) : that.errorLog != null){
            return false;
        }
        if(state != null ? !state.equals(that.state) : that.state != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = state != null ? state.hashCode() : 0;
        result = 31 * result + (details != null ? details.hashCode() : 0);
        result = 31 * result + (errorLog != null ? errorLog.hashCode() : 0);
        return result;
    }
}
