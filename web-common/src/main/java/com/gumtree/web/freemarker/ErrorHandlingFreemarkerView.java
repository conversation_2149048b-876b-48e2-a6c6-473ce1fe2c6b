package com.gumtree.web.freemarker;

import com.gumtree.common.properties.GtProps;
import freemarker.template.SimpleHash;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.utility.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.support.RequestContext;
import org.springframework.web.servlet.view.freemarker.FreeMarkerView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;


/**
 * Freemarker view that handles an errors in original template processing and report it back to user for quick problem
 * diagnostic. This is indented to be used only in dev and qa, do not use in prod.
 */
public class ErrorHandlingFreemarkerView extends FreeMarkerView {

    private Logger logger = LoggerFactory.getLogger(ErrorHandlingFreemarkerView.class);
    private String exceptionsHandlingStrategy = GtProps.getStr("gumtree.web.templates.freemarker.exceptions_strategy");

    private static final String FONT_RESET_CSS =
            "color:#A80000; font-size:12px; font-style:normal; font-variant:normal; "
                    + "font-weight:normal; text-decoration:none; text-transform: none";

    @Override
    protected void processTemplate(Template template,
                                   SimpleHash model,
                                   HttpServletResponse response) throws IOException, TemplateException {

        StringWriter sw = new StringWriter();

        try {
            template.process(model, sw);
        } catch (TemplateException ex) {
            Object springMacroRequestContext = model.toMap().get("springMacroRequestContext");
            boolean isGlobalDebug = exceptionsHandlingStrategy.toLowerCase().contains("debug");
            boolean isPageDebug = false;
            if (springMacroRequestContext != null && springMacroRequestContext instanceof RequestContext) {
                String queryString = ((RequestContext) springMacroRequestContext).getQueryString();
                if (queryString != null && queryString.contains("ftl_debug=true")) {
                    isPageDebug = true;
                }
            }

            if (!isGlobalDebug && !isPageDebug) {
                sw.getBuffer().setLength(0);
            }

            if (!isGlobalDebug || isPageDebug) {
                printError(sw, ex);
            }
        }

        try {
            response.getWriter().write(sw.toString());
        } catch (IllegalStateException e) {
            Object springMacroRequestContext = model.toMap().get("springMacroRequestContext");
            String queryString = ((RequestContext) springMacroRequestContext).getQueryString();
            logger.error("There was an error generating response from Freemarker template. " +
                    "Possibly getOutputStream() has been called for this response, request was {}", queryString, e);
        }
    }

    private void printError(StringWriter sw, TemplateException ex) {
        // inspired by TemplateExceptionHandler.HTML_DEBUG_HANDLER
        PrintWriter pw = new PrintWriter(sw);
        pw.print("<!-- FREEMARKER ERROR MESSAGE STARTS HERE -->"
                + "<!-- ]]> -->"
                + "<script language=javascript>//\"></script>"
                + "<script language=javascript>//'></script>"
                + "<script language=javascript>//\"></script>"
                + "<script language=javascript>//'></script>"
                + "</title></xmp></script></noscript></style></object>"
                + "</head></pre></table>"
                + "</form></table></table></table></a></u></i></b>"
                + "<div align='left' "
                + "style='background-color:#FFFF7C; "
                + "display:block; border-top:double; padding:4px; margin:0; "
                + "font-family:Arial,sans-serif; ");
        pw.print(FONT_RESET_CSS);
        pw.print("'>"
                + "<b style='font-size:12px; font-style:normal; font-weight:bold; "
                + "text-decoration:none; text-transform: none;'>FreeMarker template error "
                + " (HTML_DEBUG mode; use RETHROW in production!)</b>"
                + "<pre style='display:block; background: none; border: 0; margin:0; padding: 0;"
                + "font-family:monospace; ");
        pw.print(FONT_RESET_CSS);
        pw.println("; white-space: pre-wrap; white-space: -moz-pre-wrap; white-space: -pre-wrap; "
                + "white-space: -o-pre-wrap; word-wrap: break-word;'>");

        StringWriter stackTraceSW = new StringWriter();
        PrintWriter stackPW = new PrintWriter(stackTraceSW);
        ex.printStackTrace(stackPW, false, true, true);
        stackPW.close();
        pw.println();
        pw.println(StringUtil.XMLEncNQG(stackTraceSW.toString()));

        pw.println("</pre></div></html>");
    }
}
