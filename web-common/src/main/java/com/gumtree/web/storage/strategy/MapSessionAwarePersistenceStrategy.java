package com.gumtree.web.storage.strategy;

import com.gumtree.common.util.time.Clock;
import com.gumtree.web.cookie.UserSessionService;


public class MapSessionAwarePersistenceStrategy extends MapPersistenceStrategy {

    private UserSessionService userSessionService;

    public MapSessionAwarePersistenceStrategy(UserSessionService userSessionService, Long mapClearExpiredCheckMs, Clock clock) {
        super(mapClearExpiredCheckMs, clock);
        this.userSessionService = userSessionService;
    }

    @Override
    public void writeOperation(WriteOperationCallback writeOperationCallback) {
        writeOperationCallback.doWriteOperation(new MapSessionAwareWriteOperationHandler());
    }

    @Override
    public String readOperation(String key) {
        return dataMap.get(userSessionService.getSessionCookieId() + key);
    }

    private class MapSessionAwareWriteOperationHandler extends MapWriteOperationHandler {

        @Override
        public void set(String key, String value, int ttl) {
            super.set(formatKey(key), value, ttl);
        }

        @Override
        public void expire(String key, int ttl) {
            super.expire(formatKey(key), ttl);
        }

        @Override
        public void del(String key) {
            super.del(formatKey(key));
        }
    }

    private String formatKey(String key) {
        return userSessionService.getSessionCookieId() + key;
    }
}
