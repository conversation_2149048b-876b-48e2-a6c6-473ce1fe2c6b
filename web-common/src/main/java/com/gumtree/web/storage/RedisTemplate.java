package com.gumtree.web.storage;

import com.gumtree.web.IgnoredException;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisConnectionException;
import redis.clients.util.Pool;

import java.util.concurrent.ExecutorService;

public class RedisTemplate implements RedisOps {
    private static final Logger LOGGER = LoggerFactory.getLogger(RedisTemplate.class);

    private final Pool<Jedis> primaryPool;
    private final Pool<Jedis> secondaryPool;
    private final ExecutorService executor;
    private final MeterRegistry meterRegistry;

    public RedisTemplate(Pool<Jedis> primaryPool, Pool<Jedis> secondaryPool, ExecutorService executor, MeterRegistry meterRegistry) {
        Assert.notNull(primaryPool, "Primary redis pool is mandatory");
        this.primaryPool = primaryPool;
        this.secondaryPool = secondaryPool;
        this.executor = executor;
        this.meterRegistry = meterRegistry;
    }

    public String get(String key) {
        return meterRegistry.timer("redis.read.key").record(() -> {
            Jedis poolResource = null;
            try {
                poolResource = getResource(primaryPool);
                return poolResource.get(key);
            } finally {
                releaseResource(primaryPool, poolResource);
            }
        });
    }

    public void set(String key, String value, Integer expire) {
        meterRegistry.timer("redis.write.key").record(() -> {
            executeSafely(new SetCommand(key, value, expire, secondaryPool));
            set(primaryPool, key, value, expire);
        });
    }

    public void expire(String key, int expire) {
        meterRegistry.timer("redis.expire.key").record(() -> {
            executeSafely(new ExpireCommand(key, expire, secondaryPool));
            expire(primaryPool, key, expire);
        });
    }

    public void del(String key) {
        meterRegistry.timer("redis.delete.key").record(() -> {
            executeSafely(new DeleteCommand(key, secondaryPool));
            del(primaryPool, key);
        });
    }

    public Long ttl(String key) {
        Jedis poolResource = null;
        try {
            poolResource = getResource(primaryPool);
            Long ttl = poolResource.ttl(key);
            return ttl == null ? 0L : ttl;
        } finally {
            releaseResource(primaryPool, poolResource);
        }
    }

    public Pool<Jedis> getPrimaryPool() {
        return primaryPool;
    }

    public Pool<Jedis> getSecondaryPool() {
        return secondaryPool;
    }

    private void set(Pool<Jedis> pool, String key, String value, Integer expire) {
        Jedis poolResource = null;
        try {
            poolResource = getResource(pool);
            if (poolResource != null) {
                poolResource.set(key, value);
                if (expire != null) {
                    poolResource.expire(key, expire);
                }
            }
        } finally {
            releaseResource(pool, poolResource);
        }
    }

    private void expire(Pool<Jedis> pool, String key, int expire) {
        Jedis poolResource = null;
        try {
            poolResource = getResource(pool);
            if (poolResource != null) {
                poolResource.expire(key, expire);
            }
        } finally {
            releaseResource(pool, poolResource);
        }
    }

    private void del(Pool<Jedis> pool, String key) {
        Jedis poolResource = null;
        try {
            poolResource = getResource(pool);
            if (poolResource != null) {
                poolResource.del(key);
            }
        } finally {
            releaseResource(pool, poolResource);
        }
    }

    private Jedis getResource(Pool<Jedis> pool) {
        Jedis poolResource = pool.getResource();
        if (poolResource == null && isPrimary(pool)) {
            throw new JedisConnectionException("Cannot connect to Redis.");
        }

        return poolResource;
    }

    private boolean isPrimary(Pool<Jedis> pool) {
        return primaryPool == pool;
    }

    private void releaseResource(Pool<Jedis> pool, Jedis resource) {
        if (pool != null) {
            pool.returnResource(resource);
        }
    }

    private void executeSafely(Runnable task) {
        try {
            if (executor != null) {
                executor.execute(task);
            }
        } catch (Exception e) {
            LOGGER.warn("Failed to execute an assigned task. Task: " + task, new IgnoredException(e));
        }
    }

    private final class DeleteCommand implements Runnable {
        private final String key;
        private final Pool<Jedis> pool;

        private DeleteCommand(String key, Pool<Jedis> pool) {
            this.key = key;
            this.pool = pool;
        }

        @Override
        public void run() {
            del(pool, key);
        }

        @Override
        public String toString() {
            return "DeleteCommand{" +
                    "key='" + key + '\'' +
                    ", pool=" + pool +
                    '}';
        }
    }

    private final class SetCommand implements Runnable {
        private final String key;
        private final String value;
        private final Integer expire;
        private final Pool<Jedis> pool;

        private SetCommand(String key, String value, Integer expire, Pool<Jedis> pool) {
            this.key = key;
            this.value = value;
            this.expire = expire;
            this.pool = pool;
        }

        @Override
        public void run() {
            set(pool, key, value, expire);
        }

        @Override
        public String toString() {
            return "SetCommand{" +
                    "key='" + key + '\'' +
                    ", value='" + value + '\'' +
                    ", expire=" + expire +
                    ", pool=" + pool +
                    '}';
        }
    }

    private final class ExpireCommand implements Runnable {
        private final String key;
        private final Integer expire;
        private final Pool<Jedis> pool;

        private ExpireCommand(String key, Integer expire, Pool<Jedis> pool) {
            this.key = key;
            this.expire = expire;
            this.pool = pool;
        }

        @Override
        public void run() {
            expire(pool, key, expire);
        }

        @Override
        public String toString() {
            return "ExpireCommand{" +
                    "key='" + key + '\'' +
                    ", expire=" + expire +
                    ", pool=" + pool +
                    '}';
        }
    }
}
