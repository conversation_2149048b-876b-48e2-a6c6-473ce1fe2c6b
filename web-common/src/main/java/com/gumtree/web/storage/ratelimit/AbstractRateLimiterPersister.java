package com.gumtree.web.storage.ratelimit;

import com.gumtree.web.storage.exception.SessionDataAccessException;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Optional;

public abstract class AbstractRateLimiterPersister implements RateLimiterPersister {

    @Override
    public final RateCheckResult checkRate(int limitPerInterval, String key) {
        try {
            Optional<Pair<String, Integer>> valueWithTTL = readWithTTL(key);
            if (valueWithTTL.isPresent()) {
                int count = Integer.parseInt(valueWithTTL.get().getLeft());
                int ttl = valueWithTTL.get().getRight();
                if (count > limitPerInterval) {
                    return new RateCheckResult(count, ttl);
                } else {
                    return new RateCheckResult(count, 0);
                }
            }
        } catch (Exception e) {
            throw new SessionDataAccessException(e);
        }

        return RateCheckResult.EMPTY;
    }

    @Override
    public final void incRateCounter(int intervalSeconds, String key) {
        try {
            Optional<Pair<String, Integer>> valueWithTTL = readWithTTL(key);
            if (valueWithTTL.isPresent()) {
                int count = Integer.parseInt(valueWithTTL.get().getLeft());
                int ttl = valueWithTTL.get().getRight();
                write(key, Integer.toString(count + 1), ttl);
            } else {
                write(key, "1", intervalSeconds);
            }

        } catch (Exception e) {
            throw new SessionDataAccessException(e);
        }
    }

    protected abstract Optional<Pair<String, Integer>> readWithTTL(String key);

    protected abstract void write(String key, String value, int ttl);
}
