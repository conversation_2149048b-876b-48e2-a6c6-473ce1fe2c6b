package com.gumtree.web.storage.flash;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import org.springframework.web.servlet.FlashMap;

import java.io.Serializable;
import java.util.List;

/**
 */
public class SerializableFlashMapList implements Serializable {
    private List<SerializableFlashMap> serializableFlashMaps;

    public List<SerializableFlashMap> getSerializableFlashMaps() {
        return serializableFlashMaps;
    }

    public void setSerializableFlashMaps(List<SerializableFlashMap> serializableFlashMaps) {
        this.serializableFlashMaps = serializableFlashMaps;
    }

    public List<FlashMap> toFlashMapList() {
        List<FlashMap> flashMaps = Lists.newArrayList();

        if (serializableFlashMaps != null) {
            Iterables.addAll(flashMaps,
                    Iterables.transform(serializableFlashMaps, input -> input.toFlashMap())
            );
        }

        return flashMaps;
    }

    public static class Builder {
        private List<FlashMap> flashMaps;

        public Builder withFlashMaps(List<FlashMap> flashMaps) {
            this.flashMaps = flashMaps;
            return this;
        }

        public SerializableFlashMapList build() {
            List<SerializableFlashMap> serializableFlashMaps = Lists.newArrayList();

            if (flashMaps != null) {
                Iterables.addAll(serializableFlashMaps,
                        Iterables.transform(flashMaps, input -> new SerializableFlashMap.Builder().withFlashMap(input).build())
                );
            }

            SerializableFlashMapList list = new SerializableFlashMapList();
            list.setSerializableFlashMaps(serializableFlashMaps);

            return list;
        }

    }
}
