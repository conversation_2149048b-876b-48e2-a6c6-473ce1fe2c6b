package com.gumtree.web.storage.strategy;

/**
 *
 */
public interface SessionPersistenceStrategy {
    /**
     * Do a write on the underlying storage
     *
     * @param writeOperationCallback - callback that actually contains the write instruction(s)
     */
    void writeOperation(WriteOperationCallback writeOperationCallback);

    /**
     * Read a value from the underlying storage
     *
     * @param key - lookup key
     * @return found value
     */
    String readOperation(String key);

    /**
     * Inner class for actually performing write operations on the underlying storage. Called from the doWriteOperation
     * method on WriteOperationCallback, meaning clients can interact directly with an instance of this object.
     */
    interface WriteOperationHandler {
        /**
         * Set a value in storage
         *
         * @param key - identifier
         * @param value - the value to store
         * @param ttl - how long the record should be stored for in seconds
         */
        void set(String key, String value, int ttl);

        /**
         * Set a key's time to live in storage
         *
         * @param key - identifier
         * @param ttl - how long the record should be stored for in seconds
         */
        void expire(String key, int ttl);

        /**
         * Delete a value from storage
         *
         * @param key - identifier
         */
        void del(String key);
    }

    /**
     * Inner class for handling callback for doing an operation on the means of persistence.
     *
     * Implementations of this need to be as lightweight as possible because the persistence resource will be open
     * at the time of calling and closed only after the method has returned.
     */
    interface WriteOperationCallback {
        /**
         * Callback for clients to interact with underlying storage
         *
         * @param handler - object with methods for manipulating data
         */
        void doWriteOperation(WriteOperationHandler handler);
    }
}
