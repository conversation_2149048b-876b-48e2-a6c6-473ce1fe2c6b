package com.gumtree.web.storage.ratelimit;

public class DefaultRateLimiter implements RateLimiter {

    private int limitPerInterval;
    private int intervalSeconds;

    private RateLimiterPersister persister;

    public DefaultRateLimiter(RateLimiterPersister persister, int limitPerInterval, int intervalSeconds) {
        this.persister = persister;
        this.limitPerInterval = limitPerInterval;
        this.intervalSeconds = intervalSeconds;
    }

    @Override
    public RateCheckResult checkRate(String credential) {
        return persister.checkRate(limitPerInterval, credential);
    }

    @Override
    public void incRateCounter(String credential) {
        persister.incRateCounter(intervalSeconds, credential);
    }
}
