package com.gumtree.web.storage.strategy;

import com.gumtree.web.storage.RedisTemplate;
import com.gumtree.web.storage.exception.SessionDataAccessException;

public class JedisPersistenceStrategy implements SessionPersistenceStrategy {
    protected RedisTemplate redisTemplate;

    public JedisPersistenceStrategy(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void writeOperation(WriteOperationCallback writeOperationCallback) {
        try {
            writeOperationCallback.doWriteOperation(
                    new JedisWriteOperationHandler(redisTemplate));
        } catch (Exception je) {
            throw new SessionDataAccessException(je);
        }
    }

    @Override
    public String readOperation(String key) {
        try {
            return redisTemplate.get(key);
        } catch (Exception je) {
            throw new SessionDataAccessException(je);
        }
    }

    public class JedisWriteOperationHandler implements WriteOperationHandler {
        private RedisTemplate redisTemplate;

        public JedisWriteOperationHandler(RedisTemplate redisTemplate) {
            this.redisTemplate = redisTemplate;
        }

        @Override
        public void set(String key, String value, int ttl) {
            redisTemplate.set(key, value, ttl);
        }

        @Override
        public void expire(String key, int ttl) {
            redisTemplate.expire(key, ttl);
        }

        @Override
        public void del(String key) {
            redisTemplate.del(key);
        }
    }
}
