package com.gumtree.web.storage.strategy;

import com.gumtree.common.util.time.Clock;

import java.util.Iterator;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;


public class MapPersistenceStrategy implements SessionPersistenceStrategy {
    protected Map<String, String> dataMap = new ConcurrentHashMap<>();
    protected Map<String, Long> dataTtls = new ConcurrentHashMap<>();

    protected Clock clock;

    public MapPersistenceStrategy(Long mapClearExpiredCheckMs, Clock clock) {
        this.clock = clock;

        // Every n time we should clear expired map entries
        Timer timer = new Timer();
        timer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                clearExpiredEntries();
            }
        }, mapClearExpiredCheckMs, mapClearExpiredCheckMs);
    }

    @Override
    public void writeOperation(WriteOperationCallback writeOperationCallback) {
        writeOperationCallback.doWriteOperation(new MapWriteOperationHandler());
    }

    @Override
    public String readOperation(String key) {
        if (dataMap.containsKey(key)) {
            if (!isExpired(key)) {
                return dataMap.get(key);
            }
        }
        return null;
    }

    protected class MapWriteOperationHandler implements WriteOperationHandler {

        @Override
        public void set(String key, String value, int ttl) {
            dataMap.put(key, value);
            dataTtls.put(key, makeTtlValue(ttl));
        }

        @Override
        public void expire(String key, int ttl) {
            dataTtls.put(key, makeTtlValue(ttl));
        }

        @Override
        public void del(String key) {
            dataMap.remove(key);
            dataTtls.remove(key);
        }
    }

    // ttl is in seconds
    private long makeTtlValue(int ttl) {
        return clock.now() + (ttl * 1000);
    }

    public void clearExpiredEntries() {
        for (Iterator<String> ttlIter = dataTtls.keySet().iterator(); ttlIter.hasNext();) {
            String key = ttlIter.next();
            if (isExpired(key)) {
                ttlIter.remove();
                dataMap.remove(key);
            }
        }
    }

    private boolean isExpired(String key) {
        return clock.now() > dataTtls.get(key);
    }
}
