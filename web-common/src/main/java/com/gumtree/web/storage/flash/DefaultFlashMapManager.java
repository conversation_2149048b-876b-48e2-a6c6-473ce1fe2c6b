package com.gumtree.web.storage.flash;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.GtProps;
import com.gumtree.web.storage.KeyValueRepository;
import com.gumtree.web.storage.RedisTemplate;
import com.gumtree.web.storage.exception.SessionDataAccessException;
import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.FlashMap;
import org.springframework.web.servlet.support.AbstractFlashMapManager;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

import static com.codahale.metrics.MetricRegistry.name;
import static com.gumtree.common.util.json.JsonSerializeUtils.readFromString;
import static com.gumtree.common.util.json.JsonSerializeUtils.writeToString;

/**
 * A different implementation of Spring's SessionFlashMapManager.
 * This will store flashmaps in Redis & Cassandra than as session attributes.
 * Its purpose is to make it so we don't have to have sticky sessions in order to use flash attributes.
 */
public class DefaultFlashMapManager extends AbstractFlashMapManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultFlashMapManager.class);
    private static final String FLASH_MAP_PREFIX = "flashMap_" + GtProps.getStr(Env.Properties.APP_NAME) + "_";

    private final ObjectMapper mapper;
    private final RedisTemplate redisTemplate;
    private final KeyValueRepository cassandraRepository;

    private final Counter cassandraReadFailureCounter;
    private final Counter cassandraWriteFailureCounter;

    public DefaultFlashMapManager(KeyValueRepository cassandraRepository, RedisTemplate redisTemplate, ObjectMapper mapper,
                                  MetricRegistry metricRegistry) {
        this.redisTemplate = redisTemplate;
        this.cassandraRepository = cassandraRepository;
        this.mapper = mapper;

        this.cassandraReadFailureCounter = metricRegistry.counter(name(DefaultFlashMapManager.class, "cassandra.read.failure"));
        this.cassandraWriteFailureCounter = metricRegistry.counter(name(DefaultFlashMapManager.class, "cassandra.write.failure"));
    }

    @Override
    protected List<FlashMap> retrieveFlashMaps(HttpServletRequest request) {
        try {
            String value = read(request);
            LOGGER.debug("Retrieving flash values:[{}]", value);

            return deserialize(value);
        } catch (Exception e) {
            throw new SessionDataAccessException(e);
        }
    }

    @Override
    protected void updateFlashMaps(List<FlashMap> flashMaps, HttpServletRequest request, HttpServletResponse response) {
        try {
            String value = serialize(flashMaps);

            LOGGER.debug("Storing flash values:[{}]", value);
            write(request, value, 60); // auto-remove value after 60 seconds
        } catch (Exception e) {
            throw new SessionDataAccessException(e);
        }
    }

    private String serialize(List<FlashMap> flashMaps) {
        SerializableFlashMapList flashMapList = new SerializableFlashMapList.Builder().withFlashMaps(flashMaps).build();
        return writeToString(mapper, flashMapList);
    }

    private List<FlashMap> deserialize(String value) {
        if (!Strings.isNullOrEmpty(value)) {
            SerializableFlashMapList flashMapList = readFromString(mapper, value, SerializableFlashMapList.class);
            return flashMapList.toFlashMapList();
        }

        return Lists.newArrayList();
    }

    /*
     * Propagating exceptions:
     * - if only cassandra trows exc - ignore it
     * - if both cassandra & redis throw exc - propagate it
     */
    private String read(HttpServletRequest request) {
        String key = getFlashMapKeyForSession(request);
        String keyWithPrefix = getFlashMapKeyForSessionWithPrefix(request);

        try {
            Optional<String> value = cassandraRepository.get(key);
            if (value.isPresent()) {
                return value.get();
            }
        } catch (Exception e) {
            LOGGER.warn("Cassandra read failure");
            cassandraReadFailureCounter.inc();
        }


        return redisTemplate.get(keyWithPrefix);
    }

    /*
     * Propagating exceptions:
     * - if only cassandra trows exc - ignore it
     * - if both cassandra & redis throw exc - propagate it
     */
    private void write(HttpServletRequest request, String value, int ttl) {
        String key = getFlashMapKeyForSession(request);
        String keyWithPrefix = getFlashMapKeyForSessionWithPrefix(request);

        try {
            cassandraRepository.set(key, value, ttl);
        } catch (Exception e) {
            LOGGER.warn("Cassandra write failure");
            cassandraWriteFailureCounter.inc();
        }

        redisTemplate.set(keyWithPrefix, value, ttl);
    }

    private String getFlashMapKeyForSession(HttpServletRequest request) {
        return request.getSession().getId();
    }

    private String getFlashMapKeyForSessionWithPrefix(HttpServletRequest request) {
        return FLASH_MAP_PREFIX + getFlashMapKeyForSession(request);
    }
}
