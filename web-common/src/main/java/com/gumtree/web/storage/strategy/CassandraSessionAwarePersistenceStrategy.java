package com.gumtree.web.storage.strategy;

import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.storage.KeyValueRepository;
import com.gumtree.web.storage.exception.SessionDataAccessException;

public class CassandraSessionAwarePersistenceStrategy extends CassandraPersistenceStrategy {
    private final KeyValueRepository repository;
    private final UserSessionService userSessionService;

    public CassandraSessionAwarePersistenceStrategy(KeyValueRepository repository, UserSessionService userSessionService) {
        super(repository);
        this.repository = repository;
        this.userSessionService = userSessionService;
    }

    @Override
    public void writeOperation(WriteOperationCallback writeOperationCallback) {
        try {
            writeOperationCallback.doWriteOperation(
                    new CassandraSessionAwareWriteOperationHandler(repository, userSessionService.getSessionCookieId()));
        } catch (Exception je) {
            throw new SessionDataAccessException(je);
        }
    }

    @Override
    public String readOperation(String key) {
        return super.readOperation(formatKey(userSessionService.getSessionCookieId(), key));
    }


    private String formatKey(String prefix, String baseKey) {
        return prefix + "_" + baseKey;
    }

    public class CassandraSessionAwareWriteOperationHandler extends CassandraPersistenceStrategy.CassandraWriteOperationHandler {
        private String keyPrefix;

        public CassandraSessionAwareWriteOperationHandler(KeyValueRepository repository, String keyPrefix) {
            super(repository);
            this.keyPrefix = keyPrefix;
        }

        @Override
        public void set(String key, String value, int ttl) {
            super.set(formatKey(keyPrefix, key), value, ttl);
        }

        @Override
        public void expire(String key, int ttl) {
            super.expire(formatKey(keyPrefix, key), ttl);
        }

        @Override
        public void del(String key) {
            super.del(formatKey(keyPrefix, key));
        }
    }
}
