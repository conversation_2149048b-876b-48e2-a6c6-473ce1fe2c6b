package com.gumtree.web.service;

import com.gumtree.api.ApiContactEmail;
import com.gumtree.api.User;

public interface ContactEmailService {

    String getPreferred(User user);

    Iterable<String> getForReply(User user);

    Iterable<String> getForPosting(User user);

    Iterable<ApiContactEmail> getEditable(User user);

    Boolean createNew(User user, String email);

    Boolean delete(User user, String email);

    Boolean setPreferred(User user, String email);

    Boolean sendVerification(User user, String email);
}
