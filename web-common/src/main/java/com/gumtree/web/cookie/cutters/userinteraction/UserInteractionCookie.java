package com.gumtree.web.cookie.cutters.userinteraction;

import com.gumtree.web.cookie.MultiValueCookie;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;

public class UserInteractionCookie extends MultiValueCookie {
    private static final Boolean HTTP_ONLY = true;
    public static final String NAME = "gt_userIntr";
    private static final String INTERRACTION_COUNTER_KEY = "cnt";

    public UserInteractionCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());
    }

    protected UserInteractionCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY);
    }

    @Override
    public String getName() {
        return NAME;
    }

    public int getUserInterractionCounter() {
        String s = get(INTERRACTION_COUNTER_KEY);
        return StringUtils.isNumeric(s) ? Integer.parseInt(s) : 0;
    }

    public void incrementUserInterractionCounter() {
        put(INTERRACTION_COUNTER_KEY, String.valueOf(getUserInterractionCounter() + 1));
    }
}
