package com.gumtree.web.cookie.cutters.feature;

import com.gumtree.web.cookie.CookieCutter;
import com.gumtree.web.cookie.MultiValueCookie;
import com.gumtree.web.feature.FeatureSwitch;
import com.gumtree.web.feature.FeatureSwitchConfig;

import javax.servlet.http.Cookie;
import java.util.Map;
import java.util.Optional;

/**
 * There will be no cutter for this cookie. It will be only set by hidden/experiments.
 */
public class FeatureSwitchOverrideCookie extends MultiValueCookie {

    public static final String NAME = "gt_feat_ovr";

    private static final String PATH = "/";

    public FeatureSwitchOverrideCookie(String domain) {
        super(domain, CookieCutter.MAX_AGE_COOKIE, PATH, true);
    }

    public FeatureSwitchOverrideCookie(String domain, Cookie cookie) {
        super(domain, CookieCutter.MAX_AGE_COOKIE, PATH, true, cookie.getValue());
    }

    @Override
    public String getName() {
        return NAME;
    }

    public Map<String, String> getOverrides() {
        return getValue();
    }

    public void setFeatureSwitch(FeatureSwitch feature, FeatureSwitchConfig.State switchValue) {
        put(feature.name(), switchValue.name());
    }

    public Optional<FeatureSwitchConfig.State> getFeatureSwitchValue(FeatureSwitch feature) {
        return Optional.ofNullable(getValue().get(feature.name())).flatMap(FeatureSwitchConfig.State::of);
    }

}
