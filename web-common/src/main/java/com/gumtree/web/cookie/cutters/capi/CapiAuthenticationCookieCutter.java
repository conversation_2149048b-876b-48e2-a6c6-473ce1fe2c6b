package com.gumtree.web.cookie.cutters.capi;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;

public class CapiAuthentication<PERSON>ookieCutter extends CookieCutter<CapiAuthenticationCookie> {
    private static final String PATH = "/";

    public CapiAuthenticationCookieCutter(String domain) {
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return CapiAuthenticationCookie.NAME;
    }

    @Override
    protected CapiAuthenticationCookie cutExisting(Cookie existingCookie) {
        return new CapiAuthenticationCookie(domain, SESSION_COOKIE_AGE, PATH, existingCookie);
    }

    @Override
    protected CapiAuthenticationCookie cutNew() {
        return new CapiAuthenticationCookie(domain, SESSION_COOKIE_AGE, PATH);
    }

    @Override
    protected Class<CapiAuthenticationCookie> getSupportedCookieType() {
        return CapiAuthenticationCookie.class;
    }

}
