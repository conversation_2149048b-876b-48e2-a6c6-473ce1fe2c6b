package com.gumtree.web.cookie.cutters.appbanner;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gumtree.web.cookie.MultiValueCookie;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;

import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieCutter.MAX_AGE_COOKIE;
import static java.time.ZoneOffset.UTC;
import static java.time.format.DateTimeFormatter.RFC_1123_DATE_TIME;

/**
 * <PERSON>ie will be dropped when a user does some specified activity.
 * The client side java script will be responsible for changing visibility of the banner on dismissal and extending the
 * expiry date of the cookie. The maxAge of the cookie must be controlled so that it is removed after 15 days if the
 * user doesn't dismiss the banner, or 15 days once they dismiss the banner.
 *
 * Making this cookie as immutable as possible to avoid spurious behaviour.
 */
public class AppBannerCookie extends MultiValueCookie {

    public static final String NAME = "gt_appBanner";

    static final String ACTION_TRIGGERED = "actionTriggered";
    private static final String APP_BANNER_VISIBLE = "appBannerVisible"; // FE - popup banner recommending app installation

    static final String EXPIRY_DATE = "expiryDate";

    private static final Boolean HTTP_ONLY = false;

    /**
     * New Cookie.
     */
    AppBannerCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY);
    }

    /**
     * Create new cookie based on existing cookie value.
     */
    AppBannerCookie(String domain, String path, Map<String, String> entries) {
        super(domain, calculateMaxAge(entries), path, HTTP_ONLY, entries);
    }

    /**
     * If cookie has expiry date set, then
     * @param entries
     * @return
     */
    private static int calculateMaxAge(Map<String, String> entries) {
        if(entries.containsKey(EXPIRY_DATE)) {
            ZonedDateTime localDateTime = ZonedDateTime.now(UTC);
            ZonedDateTime expiryDate = ZonedDateTime.parse(entries.get(EXPIRY_DATE), RFC_1123_DATE_TIME);
            return (int) ChronoUnit.SECONDS.between(localDateTime, expiryDate);
        } else {
            return MAX_AGE_COOKIE;
        }
    }

    void setActionTriggered(String actionTriggered) {
        if (actionTriggered != null) {
            put(ACTION_TRIGGERED, actionTriggered);
            if (getAppBannerVisible() == null) {
                put(APP_BANNER_VISIBLE, "true");
                addExpiryDate();
            }
        }
    }

    @Override
    public String getName() {
        return NAME;
    }

    @JsonIgnore
    boolean isVisible() {
        return Boolean.parseBoolean(get(APP_BANNER_VISIBLE));
    }

    public Boolean getAppBannerVisible() {
        return get(APP_BANNER_VISIBLE) != null ? isVisible() : null;
    }

    public String getActionTriggered() {
        return get(ACTION_TRIGGERED);
    }

    public String getExpiryDate() {
        return get(EXPIRY_DATE);
    }

    /**
     * To ensure cookie maxAge isn't horribly extended.
     */
    private void addExpiryDate() {
        put(EXPIRY_DATE, RFC_1123_DATE_TIME.format(ZonedDateTime.now(UTC).plusSeconds(MAX_AGE_COOKIE)));
    }

}
