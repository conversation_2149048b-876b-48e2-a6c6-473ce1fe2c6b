package com.gumtree.web.cookie.cutters.userpreferences;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;
import java.util.concurrent.TimeUnit;

public class ShortTermUserPrefCookieCutter extends CookieCutter<ShortTermUserPrefCookie> {

    private static final String PATH = "/";

    public static final int MAX_AGE_COOKIE = (int) TimeUnit.DAYS.toSeconds(30);

    public ShortTermUserPrefCookieCutter(String domain) {
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return ShortTermUserPrefCookie.NAME;
    }

    @Override
    protected ShortTermUserPrefCookie cutExisting(Cookie existingCookie) {
        return new ShortTermUserPrefCookie(domain, MAX_AGE_COOKIE, PATH, existingCookie);
    }

    @Override
    protected ShortTermUserPrefCookie cutNew() {
        return new ShortTermUserPrefCookie(domain, MAX_AGE_COOKIE, PATH);
    }

    @Override
    protected Class<ShortTermUserPrefCookie> getSupportedCookieType() {
        return ShortTermUserPrefCookie.class;
    }

}
