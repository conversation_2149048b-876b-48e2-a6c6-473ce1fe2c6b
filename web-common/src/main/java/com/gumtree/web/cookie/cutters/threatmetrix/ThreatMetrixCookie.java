package com.gumtree.web.cookie.cutters.threatmetrix;


import com.gumtree.web.cookie.BaseCookie;
import javax.servlet.http.Cookie;
import java.util.UUID;


public class ThreatMetrix<PERSON>ookie extends BaseCookie<String>{

    public static final String NAME = "gt_tm";

    private static final Boolean HTTP_ONLY = true;

    protected ThreatMetrixCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());
    }

    protected ThreatMetrixCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY, UUID.randomUUID().toString());
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public String getDefaultValue() {
        return getValue();
    }

    @Override
    protected String getValueAsString() {
        return getValue();
    }
}
