package com.gumtree.web.cookie.cutters.messagecentre;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;

public class MessageCentreCookieCutter extends CookieCutter<MessageCentreCookie> {

    private static final String PATH = "/";

    public MessageCentreCookieCutter(String domain) {
        super(domain);
    }

    @Override
    public String getBaseName() {
        return MessageCentreCookie.NAME;
    }

    @Override
    public MessageCentreCookie cutExisting(<PERSON><PERSON> existingCookie) {
        return new MessageCentreCookie(domain, 0, PATH, existingCookie);
    }

    @Override
    public MessageCentreCookie cutNew() {
        return new MessageCentreCookie(domain, MAX_AGE_COOKIE, PATH);
    }

    @Override
    public Class<MessageCentreCookie> getSupportedCookieType() {
        return MessageCentreCookie.class;
    }
}
