package com.gumtree.web.cookie.cutters.capi;

import com.gumtree.web.cookie.BaseCookie;
import com.gumtree.web.cookie.CookieSerializer;
import org.apache.commons.lang3.StringUtils;
import javax.servlet.http.Cookie;

import java.util.Optional;

public class CapiAuthenticationCookie extends BaseCookie<String> {
    public static final String NAME = "gt_capi_auth";

    private static final Boolean HTTP_ONLY = true;

    public CapiAuthenticationCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, CookieSerializer.deserialize(cookie.getValue()));
    }

    public CapiAuthenticationCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY, "");
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public String getDefaultValue() {
        return "";
    }

    public Optional<String> getValueOpt() {
        return StringUtils.isNotBlank(getValue()) ? Optional.of(getValue()) : Optional.empty();
    }

    @Override
    protected String getValueAsString() {
        return CookieSerializer.serialize(getValue());
    }
}
