package com.gumtree.web.cookie.cutters.userpreferences;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;

public class UserPreferences<PERSON>ook<PERSON><PERSON>utter extends CookieCutter<UserPreferencesCookie> {

    private static final String PATH = "/";

    public UserPreferencesCookieCutter(String domain) {
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return UserPreferencesCookie.NAME;
    }

    @Override
    protected UserPreferencesCookie cutExisting(Cookie existingCookie) {
        return new UserPreferencesCookie(domain, CookieCutter.MAX_AGE_COOKIE, PATH, existingCookie);
    }

    @Override
    protected UserPreferencesCookie cutNew() {
        return new UserPreferencesCookie(domain, CookieCutter.MAX_AGE_COOKIE, PATH);
    }

    @Override
    protected Class<UserPreferencesCookie> getSupportedCookieType() {
        return UserPreferencesCookie.class;
    }

}
