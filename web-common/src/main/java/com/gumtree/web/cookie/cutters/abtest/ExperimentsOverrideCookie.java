package com.gumtree.web.cookie.cutters.abtest;

import com.gumtree.web.cookie.CookieCutter;
import com.gumtree.web.cookie.MultiValueCookie;

import javax.servlet.http.Cookie;
import java.util.Map;

/**
 *
 * There will be no cutter for this cookie. It will be only set by hidden/experiments.
 *
 */
public class ExperimentsOverrideCookie extends MultiValueCookie {

    public static final String NAME = "gt_exp_ovr";

    public static final String NONE_VALUE = "NONE";

    private static final String PATH = "/";

    public ExperimentsOverrideCookie(String domain) {
        super(domain,  CookieCutter.MAX_AGE_COOKIE, PATH, true);
    }

    public ExperimentsOverrideCookie(String domain, Cookie cookie) {
        super(domain, CookieCutter.MAX_AGE_COOKIE, PATH, true, cookie.getValue());
    }

    @Override
    public String getName() {
        return NAME;
    }

    public Map<String, String> getOverrides() {
        return getValue();
    }

}
