package com.gumtree.web.cookie.cutters.appbanner;

import com.gumtree.web.cookie.CookieCutter;
import com.gumtree.web.cookie.CookieSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.Cookie;
import java.util.Map;

import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookie.EXPIRY_DATE;

public class AppBanner<PERSON>ookie<PERSON>utter extends <PERSON><PERSON><PERSON>utter<AppBannerCookie> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppBannerCookieCutter.class);

    public static final String PATH = "/";

    public static final int MAX_AGE_COOKIE = 60 * 60 * 24 * 15; // 15 days

    public AppBannerCookieCutter(String domain) {
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return AppBannerCookie.NAME;
    }

    /**
     * If cookie has refreshDate, then ensure maxAge
     * @param cookie
     * @return
     */
    @Override
    protected AppBannerCookie cutExisting(Cookie cookie) {
        Map<String, String> entries = CookieSerializer.deserializeCookieMap(cookie.getValue());

        if (entries.containsKey(EXPIRY_DATE)) {
            // ensure maxAge is honoured, should never be more than MAX_AGE_COOKIE from time first created
            try {
                return new AppBannerCookie(domain, PATH, entries);
            } catch (Exception e)  {
                LOGGER.warn("failed to parse expiry date {}", entries.get(EXPIRY_DATE), e.getMessage());
            }
        }
        return cutNew();
    }

    @Override
    protected AppBannerCookie cutNew() {
        return new AppBannerCookie(domain, MAX_AGE_COOKIE, PATH);
    }

    @Override
    protected Class<AppBannerCookie> getSupportedCookieType() {
        return AppBannerCookie.class;
    }

}
