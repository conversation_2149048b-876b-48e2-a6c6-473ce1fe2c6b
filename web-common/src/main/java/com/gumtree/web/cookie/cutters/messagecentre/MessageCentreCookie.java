package com.gumtree.web.cookie.cutters.messagecentre;

import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.GtProps;
import com.gumtree.web.cookie.MultiValueCookie;
import org.springframework.util.Assert;

import javax.servlet.http.Cookie;
import java.util.Optional;

public class MessageCentreCookie extends MultiValueCookie {

    //HTTP false since this cookie will need to be modified on the frontend
    private static final Boolean HTTP_ONLY = false;
    private static final String NUM_UNREAD_CONVERSATIONS_KEY = "nuc";
    private static final String REFRESH_COUNT_DATE_KEY = "rcd";

    public static final String NAME = "gt_mc";

    protected MessageCentreCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY);
    }

    public MessageCentreCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());
    }

    public MessageCentreCookie setNumberUnreadConversations(int numberUnreadConversations) {
        put(NUM_UNREAD_CONVERSATIONS_KEY, Integer.toString(numberUnreadConversations));

        return this;
    }

    public MessageCentreCookie setRefreshCountDateKey(String refreshCountDateKey) {
        Assert.hasLength(refreshCountDateKey);
        put(REFRESH_COUNT_DATE_KEY, refreshCountDateKey);

        return this;
    }

    public Optional getNumberUnreadConversations() {
        return Optional.ofNullable(get(NUM_UNREAD_CONVERSATIONS_KEY));
    }

    public Optional getRefreshCountDateKey() {
        return Optional.ofNullable(get(REFRESH_COUNT_DATE_KEY));
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public String toString() {
        return this.getValueAsString();
    }

    public String getEnvironmentAwareName() {
        Env environment = GtProps.getEnv();
        return environment == Env.PROD ? this.getName() : environment.name() + "_" + this.getName();
    }
}
