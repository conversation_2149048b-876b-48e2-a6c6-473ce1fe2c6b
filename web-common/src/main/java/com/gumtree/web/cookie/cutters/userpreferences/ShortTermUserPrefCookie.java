package com.gumtree.web.cookie.cutters.userpreferences;

import com.gumtree.web.cookie.MultiValueCookie;
import org.springframework.util.StringUtils;

import javax.servlet.http.Cookie;

public class ShortTermUserPrefCookie extends MultiValueCookie {

    public static final String NAME = "gt_stUserPref";

    public static final String TRUE = "true";

    private static final Boolean HTTP_ONLY = false;
    private static final String MESSAGE_CENTER_APP_BANNER_KEY = "mcab";

    public ShortTermUserPrefCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY);
        setCookieValues();
    }

    public ShortTermUserPrefCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());
    }

    private void setCookieValues() {
        if (StringUtils.isEmpty(get(MESSAGE_CENTER_APP_BANNER_KEY))) {
            put(MESSAGE_CENTER_APP_BANNER_KEY, TRUE);
        }
    }

    @Override
    public String getName() {
        return NAME;
    }

    public boolean showAppBanner() {
        String value = get(MESSAGE_CENTER_APP_BANNER_KEY);
        return value == null || TRUE.equals(value);
    }
}
