package com.gumtree.web.cookie.cutters.userpreferences;

import com.gumtree.web.cookie.MultiValueCookie;


/**
 * Cookie stores:
 * - consent: yes/no/implicit
 * - consentString: granular consent
 * - modified flag: is consent modifed, is string consent modified.
 * <p>
 * Modified flag:
 * isStateModified isConsentStringModified mod value
 * 0 0 0
 * 1 0 1
 * 0 1 2
 * 1 1 3
 */
public class UserAdPreferenceCookie extends MultiValueCookie {

    public static final String NAME = "gt_adconsent";

    private static final Boolean HTTP_ONLY = false;
    private static final String CONSENT_KEY = "state";
    private static final String CONSENT_STRING_KEY = "consentString";
    private static final String MODIFIED_KEY = "mod";

    public UserAdPreferenceCookie(String domain, int maxAge, String path, String value) {
        super(domain, maxAge, path, HTTP_ONLY, value);
    }

    public UserAdPreferenceCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY);
    }

    @Override
    public String getName() {
        return NAME;
    }

    public boolean isConsentValueSet() {
        return null != get(CONSENT_KEY);
    }

    public String getConsentValue() {
        return get(CONSENT_KEY);
    }

    public void setConsentValue(String value) {
        put(CONSENT_KEY, value);
    }

    public String getStringConsentValue() {
        return get(CONSENT_STRING_KEY);
    }

    public void setStringConsentValue(String value) {
        put(CONSENT_STRING_KEY, value);
    }

    /**
     * Ruturns true if {@link UserAdPreferenceCookie#getConsentValue} was modified since last time user was logged and consent
     * needs to be sync with value stored in db
     */
    public boolean isConsentModified() {
        String mod = get(MODIFIED_KEY);
        return "1".equals(mod) || "3".equals(mod);
    }

    /**
     * Ruturns true if {@link UserAdPreferenceCookie#getStringConsentValue} was modified since last time user was logged and consent
     * needs to be sync with value stored in db
     */
    public boolean isConsentStringModified() {
        String mod = get(MODIFIED_KEY);
        return "2".equals(mod) || "3".equals(mod);
    }

    public void resetConsentModificationFlag() {
        final String before = get(MODIFIED_KEY);
        String after;
        if ("3".equals(before) || "2".equals(before)) {
            after = "2";
        } else {
            after = "0";
        }
        put(MODIFIED_KEY, after);
    }

    public void resetStringConsentModificationFlag() {
        final String before = get(MODIFIED_KEY);
        String after;
        if ("3".equals(before) || "1".equals(before)) {
            after = "1";
        } else {
            after = "0";
        }
        put(MODIFIED_KEY, after);
    }

}
