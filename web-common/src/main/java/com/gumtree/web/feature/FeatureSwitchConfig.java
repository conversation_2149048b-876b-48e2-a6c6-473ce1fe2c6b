package com.gumtree.web.feature;

import java.util.Optional;

/**
 *
 * Features defined here should be temporary like a rollout or a feature toggle for ongoing development.
 * Once there is no need fot the switch, it should be removed.
 *
 * For permanent feature switches try to use properties {@link com.gumtree.common.properties.GtProps}.
 *
 */
public enum FeatureSwitchConfig implements FeatureSwitch {

    SEARCH_ES6(100),
    POST_MESSAGE_API(State.OFF),
    POST_AD_APP_SIGN_POST(State.OFF);

    private final int percentage;

    FeatureSwitchConfig(int percentage) {
        this.percentage = percentage;
    }

    FeatureSwitchConfig(State state) {
        switch (state) {
            case ON:
                percentage = 100;
                break;
            case OFF:
            default:
                percentage = 0;
        }
    }

    public int getPercentage() {
        return percentage;
    }

    public static Optional<FeatureSwitchConfig> of(String valueStr) {
        try {
            return Optional.of(valueOf(valueStr));
        } catch (IllegalArgumentException ex) {
            return Optional.empty();
        }
    }

    public enum State {
        ON,
        OFF;

        public static Optional<State> of(String valueStr) {
            try {
                return Optional.of(valueOf(valueStr));
            } catch (IllegalArgumentException ex) {
                return Optional.empty();
            }
        }
    }

}
