package com.gumtree.web.feature;

import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.abtest.AbExperimentsCookie;

import javax.servlet.http.HttpServletRequest;
import java.util.Random;
import java.util.zip.CRC32;

public class FeatureSwitchManagerImpl implements FeatureSwitchManager {

    private static final int FULL_PERCENTAGE = 100;

    private final CookieResolver cookieResolver;
    private final HttpServletRequest request;

    public FeatureSwitchManagerImpl(CookieResolver cookieResolver, HttpServletRequest request) {
        this.cookieResolver = cookieResolver;
        this.request = request;
    }

    @Override
    public FeatureSwitchConfig.State getFeatureState(FeatureSwitch featureSwitch) {
        if (featureSwitch.getPercentage() == FULL_PERCENTAGE) {
            return FeatureSwitchConfig.State.ON;
        }
        if (featureSwitch.getPercentage() == 0) {
            return FeatureSwitchConfig.State.OFF;
        }
        AbExperimentsCookie abExperimentsCookie = cookieResolver.resolve(request, AbExperimentsCookie.class);
        int userPercentage = calcPercentBucketHash(abExperimentsCookie.getLuckyNumber() + featureSwitch.name());
        if (userPercentage < featureSwitch.getPercentage()) {
            return FeatureSwitchConfig.State.ON;
        } else {
            return FeatureSwitchConfig.State.OFF;
        }
    }

    private static int calcPercentBucketHash(String value) {
        CRC32 checksum = new CRC32();
        checksum.update(value.getBytes());
        return new Random(checksum.getValue()).nextInt(FULL_PERCENTAGE);
    }

}
