package com.gumtree.web.comscore;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 08/06/2016.
 */
public class ComscoreProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(ComscoreProvider.class);

    public Comscore getComscoreVertical(CategoryModel categoryModel, Category category) {
        //if we are at all skip the switch and just return
        if (category.getId().equals(CategoryConstants.ALL_ID)) {
            return new Comscore(1L, 1L, "GTCSID_ALL");
        }
        Optional<Category> l1Cat = categoryModel.getL1CategoryFor(category.getId());

        if (l1Cat.isPresent()) {
            switch (l1Cat.get().getSeoName()) {
                case "community":
                    return new Comscore(2550L, getL2Id(categoryModel, category), "GTCSID_COMMUNITY");
                case "flats-houses":
                    return new Comscore(10201L, getL2Id(categoryModel, category), "GTCSID_REAL_ESTATE");
                case "for-sale":
                    return new Comscore(2549L, getL2Id(categoryModel, category), "GTCSID_RETAIL_ALL");
                case "jobs":
                    return new Comscore(2553L, getL2Id(categoryModel, category), "GTCSID_CAREER_SERVICES_AND_DEVELOPMENT_JOB_SEARCH");
                case "cars-vans-motorbikes":
                    return new Comscore(2551L, getL2Id(categoryModel, category), "GTCSID_AUTOMOTIVE_RESOURCES");
                case "pets":
                    return new Comscore(2526L, getL2Id(categoryModel, category), "GTCSID_PETS");
                case "business-services":
                    return new Comscore(2554L, getL2Id(categoryModel, category), "GTCSID_SERVICES");
                default:
                    LOGGER.debug("Invalid l1Cateogry " + l1Cat.get().getSeoName());
            }
        }
        return new Comscore(1L, 1L, "GTCSID_ALL");
    }

    private Long getL2Id(CategoryModel model, Category category) {
        if (category.getDepth().equals(0)) { //all
            return 0L;
        }
        if (category.getDepth().equals(1)) { //l1
            return 0L;
        }
        if (category.getDepth().equals(2)) { //l2
            return category.getId();
        }
        if (category.getDepth().equals(3)) { //l3
            return category.getParentId();
        }
        if (category.getDepth().equals(4)) { //l4
            if (model.getCategory(category.getParentId()).isPresent()) {
                Optional<Category> l3Category = model.getCategory(category.getParentId());
                if (l3Category.isPresent()) {
                    return l3Category.get().getParentId();
                }
            }
        }
        return 0L;
    }
}
