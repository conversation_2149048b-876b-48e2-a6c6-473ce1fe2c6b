package com.gumtree.web.advertising.partnership.api;

import com.google.common.collect.ImmutableMap;
import com.gumtree.api.category.domain.Category;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.domain.page.Page;
import com.gumtree.web.advertising.partnership.Partnership;
import com.gumtree.web.advertising.partnership.PartnershipsParameters;
import com.gumtree.web.common.device.DefaultDevice;
import com.gumtree.web.common.domain.ad.Advert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nonnull;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.gumtree.domain.page.Page.HOME_RESPONSIVE;
import static com.gumtree.domain.page.Page.REPLY_CONFIRMATION_RESPONSIVE;
import static com.gumtree.domain.page.Page.SEARCH_RESULTS_RESPONSIVE;
import static com.gumtree.domain.page.Page.VIP;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.joining;
import static org.apache.commons.lang.StringUtils.isEmpty;


public class PartnershipsApi {
    public static final Logger LOG = LoggerFactory.getLogger(PartnershipsApi.class);

    private final PartnershipsApiContract partnershipsApiContract;
    private final PropSupplier<Boolean> isEnabled;

    private final Map<Page, String> pageTypeLabels = ImmutableMap.of(
            VIP, "vip",
            SEARCH_RESULTS_RESPONSIVE, "srp",
            REPLY_CONFIRMATION_RESPONSIVE, "rtacp",
            HOME_RESPONSIVE, "home"
    );

    public PartnershipsApi(PartnershipsApiContract partnershipsApiContract, PropSupplier<Boolean> isEnabled) {
        this.partnershipsApiContract = partnershipsApiContract;
        this.isEnabled = isEnabled;
    }

    public List<Partnership> getContent(PartnershipsParameters params) {
        if (isEnabled.get()) {
            return pageTypeFor(params.getPage())
                    .map(pageType -> getContentFromParams(params, pageType))
                    .orElse(emptyList());
        } else {
            return emptyList();
        }
    }

    private List<Partnership> getContentFromParams(@Nonnull PartnershipsParameters params, @Nonnull String pageType) {
        String lat = params.getLocation().getLatitude() != null ? params.getLocation().getLatitude().toPlainString() : null;
        String lon = params.getLocation().getLongitude() != null ? params.getLocation().getLongitude().toPlainString() : null;
        return partnershipsApiContract.getContent(
                pageType,
                categoryHierarchyToString(params.getCategoryHierarchy()),
                params.getPrice(),
                params.getOutcode(),
                params.getLocation().getName(),
                lat,
                lon,
                attributesToString(params.getAttributes()),
                extractDeviceType(params.getDevice()),
                params.getAdvertImage(),
                extractParticipatingExperimentsAsString(params.getExperiments()),
                params.getAccountId());
    }

    private String categoryHierarchyToString(List<Category> categoryHierarchy) {
        if (categoryHierarchy == null) {
            return null;
        }

        return categoryHierarchy.stream()
                .map(Category::getSeoName)
                .reduce(null, (cats, currCat) -> cats != null ? cats + "," + currCat : currCat);
    }

    private String attributesToString(List<Advert.Attribute> attributes) {
        String value = attributes.stream()
                .map(entry -> entry.getKey() + ":" + entry.getValue())
                .collect(joining(","));
        return isEmpty(value) ? null : value;
    }

    private Optional<String> pageTypeFor(Page page) {
        return Optional.ofNullable(pageTypeLabels.get(page));
    }

    private String extractDeviceType(DefaultDevice device) {
        if (device == null) {
            return "desktop";
        }
        if (device.isMobile()) {
            return "mobile";
        } else if (device.isTablet()) {
            return "tablet";
        } else {
            return "desktop";
        }
    }

    private static String extractParticipatingExperimentsAsString(Map<String, String> experiments) {
        return experiments.entrySet().stream()
                .sorted(Comparator.comparing(Map.Entry::getKey))
                .map(kv -> kv.getKey() + "=" + kv.getValue())
                .collect(joining(","));
    }
}