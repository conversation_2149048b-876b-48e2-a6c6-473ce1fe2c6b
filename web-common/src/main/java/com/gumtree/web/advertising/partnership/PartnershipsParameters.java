package com.gumtree.web.advertising.partnership;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.page.Page;
import com.gumtree.web.common.device.DefaultDevice;
import com.gumtree.web.common.domain.ad.Advert.Attribute;
import com.gumtree.web.common.domain.location.Location;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class PartnershipsParameters {

    private Page page;
    private List<Category> categoryHierarchy;
    private String price;
    private String outcode;
    private Location location;
    private List<Attribute> attributes;
    private DefaultDevice device;
    private String advertImage;
    private Map<String, String> experiments;
    private Long accountId;

    public PartnershipsParameters(Page page, List<Category> categoryHierarchy,
                                  String price, String outcode, Location location,
                                  List<Attribute> attributes, DefaultDevice device, String advertImage,
                                  Map<String, String> experiments,
                                  Long accountId) {
        this.page = page;
        this.categoryHierarchy = categoryHierarchy;
        this.price = price;
        this.outcode = outcode;
        this.location = location;
        this.attributes = attributes;
        this.device = device;
        this.advertImage = advertImage;
        this.experiments = experiments;
        this.accountId = accountId;
    }

    public Page getPage() {
        return page;
    }

    public List<Category> getCategoryHierarchy() {
        return categoryHierarchy;
    }

    public String getPrice() {
        return price;
    }

    public String getOutcode() {
        return outcode;
    }

    public Location getLocation() {
        return location;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public DefaultDevice getDevice() {
        return device;
    }

    public String getAdvertImage() {
        return advertImage;
    }

    public Map<String, String> getExperiments() {
        return experiments;
    }

    public Long getAccountId() {
        return accountId;
    }

    @Override
    public String toString() {
        return "PartnershipsParameters{" +
                "page=" + page +
                ", categoryHierarchy=" + categoryHierarchy +
                ", price='" + price + '\'' +
                ", outcode='" + outcode + '\'' +
                ", location=" + location +
                ", attributes=" + attributes +
                ", device=" + device +
                ", advertImage='" + advertImage + '\'' +
                ", experiments=" + experiments +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PartnershipsParameters that = (PartnershipsParameters) o;
        return page == that.page &&
                Objects.equals(categoryHierarchy, that.categoryHierarchy) &&
                Objects.equals(price, that.price) &&
                Objects.equals(outcode, that.outcode) &&
                Objects.equals(location, that.location) &&
                Objects.equals(attributes, that.attributes) &&
                Objects.equals(device, that.device) &&
                Objects.equals(advertImage, that.advertImage) &&
                Objects.equals(experiments, that.experiments) &&
                Objects.equals(accountId, that.accountId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(page, categoryHierarchy, price, outcode, location, attributes, device, advertImage, experiments, accountId);
    }


    public static PartnershipsParametersBuilder builder() {
        return new PartnershipsParametersBuilder();
    }

    public static class PartnershipsParametersBuilder {

        private Page page;
        private List<Category> categoryHierarchy;
        private String price;
        private String outcode;
        private Location location;
        private List<Attribute> attributes;
        private DefaultDevice device;
        private String advertImage;
        private Map<String, String> experiments;
        private Long accountId;

        public PartnershipsParametersBuilder withPage(Page page) {
            this.page = page;
            return this;
        }

        public PartnershipsParametersBuilder withCategoryHierarchy(List<Category> categoryHierarchy) {
            this.categoryHierarchy = categoryHierarchy;
            return this;
        }

        public PartnershipsParametersBuilder withPrice(String price) {
            this.price = price;
            return this;
        }

        public PartnershipsParametersBuilder withOutcode(String outcode) {
            this.outcode = outcode;
            return this;
        }

        public PartnershipsParametersBuilder withLocation(Location location) {
            this.location = location;
            return this;
        }

        public PartnershipsParametersBuilder withAttributes(List<Attribute> attributes) {
            this.attributes = attributes;
            return this;
        }

        public PartnershipsParametersBuilder withDevice(DefaultDevice device) {
            this.device = device;
            return this;
        }

        public PartnershipsParametersBuilder withAdvertImage(String advertImage) {
            this.advertImage = advertImage;
            return this;
        }

        public PartnershipsParametersBuilder withExperiments(Map<String, String> experiments) {
            this.experiments = experiments;
            return this;
        }

        public PartnershipsParametersBuilder withAccountId(Long accountId) {
            this.accountId = accountId;
            return this;
        }

        public PartnershipsParameters build() {
            return new PartnershipsParameters(page,
                    categoryHierarchy, price, outcode, location, attributes, device, advertImage,
                    experiments, accountId);
        }

    }

}