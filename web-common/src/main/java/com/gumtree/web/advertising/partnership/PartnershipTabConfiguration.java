package com.gumtree.web.advertising.partnership;


import java.util.List;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

public final class PartnershipTabConfiguration {

    private List<PartnershipTab> partnerList;

    private PartnershipTabConfiguration(List<PartnershipTab> partnershipTabList) {
        this.partnerList = partnershipTabList;
    }

    public List<PartnershipTab> getPartnerList() {
        return getPartnershipsByVersion("default");
    }

    /** this is used by freemarker to get the srpLeft/small_srp/compact page **/
    public List<PartnershipTab> getPartnerCompactList() {
        return getPartnershipsByVersion("compact");
    }

    public List<PartnershipTab> getPartnershipsByVersion(String version) {
        return partnerList.stream()
                .filter(partner -> partner.getPartnershipVersion() == null || version.equals(partner.getPartnershipVersion()))
                .collect(toList());
    }

    public static final class Builder {
        private List<PartnershipTab> partnershipTabList;

        public Builder withPartnerList(List<PartnershipTab> partnershipTabList) {
            this.partnershipTabList = partnershipTabList;
            return this;
        }

        public PartnershipTabConfiguration build() {
            return new PartnershipTabConfiguration(partnershipTabList);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PartnershipTabConfiguration that = (PartnershipTabConfiguration) o;
        return Objects.equals(partnerList, that.partnerList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(partnerList);
    }
}
