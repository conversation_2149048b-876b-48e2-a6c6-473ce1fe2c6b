package com.gumtree.web.advertising.partnership.api;

import com.google.common.collect.ImmutableList;
import com.gumtree.api.command.GumtreeHystrixCommand;
import com.gumtree.web.advertising.partnership.Partnership;
import com.gumtree.web.advertising.partnership.PartnershipsParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Single;

import java.util.List;

import static com.gumtree.common.properties.GtProps.getInt;
import static com.gumtree.config.MwebProperty.PARTAPI_CONNECTION_POOL_MAX;

public class HystrixEnabledPartnershipsApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(HystrixEnabledPartnershipsApi.class);

    public static final String COMMAND_NAME = "GetConfig";
    public static final String GROUP_NAME = "PartnerAdsApi";

    private static final int MAX_CONCURRENT_REQUESTS = getInt(PARTAPI_CONNECTION_POOL_MAX);

    private final PartnershipsApi partnershipsApi;
    private int timeoutMs;

    public HystrixEnabledPartnershipsApi(PartnershipsApi partnershipsApi, int timeoutMs) {
        this.partnershipsApi = partnershipsApi;
        this.timeoutMs = timeoutMs;
    }

    public Single<List<Partnership>> getContent(PartnershipsParameters partnershipsParameters) {
        return new PartnershipAdsCommand(partnershipsApi, partnershipsParameters, timeoutMs).toObservable().toSingle();
    }

    public static final class PartnershipAdsCommand extends GumtreeHystrixCommand<List<Partnership>> {
        private PartnershipsApi partnershipsApi;
        private PartnershipsParameters partnershipsParameters;

        PartnershipAdsCommand(PartnershipsApi partnershipsApi, PartnershipsParameters partnershipsParameters, int timeoutMs) {
            super(createSetterWithThreadPool(COMMAND_NAME, GROUP_NAME, new ThreadPoolSize(MAX_CONCURRENT_REQUESTS), timeoutMs));
            this.partnershipsApi = partnershipsApi;
            this.partnershipsParameters = partnershipsParameters;
        }

        @Override
        protected List<Partnership> run() {
            try {
                return partnershipsApi.getContent(partnershipsParameters);
            } catch (Exception e) {
                LOGGER.info("Error calling partnerships api. Error Message: {}", e.getMessage());
                throw e;
            }
        }

        @Override
        protected List<Partnership> getFallback() {
            return ImmutableList.of();
        }

        @Override
        protected String getCacheKey() {
            return partnershipsParameters.toString();
        }
    }
}