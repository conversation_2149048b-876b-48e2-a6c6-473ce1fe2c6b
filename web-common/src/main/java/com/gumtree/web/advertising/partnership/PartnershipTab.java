package com.gumtree.web.advertising.partnership;

import java.util.Objects;

public final class PartnershipTab {

    private Partnership partnership;
    private boolean isMain = false;

    private PartnershipTab(Partnership partnership, boolean isMain) {
        this.partnership = partnership;
        this.isMain = isMain;
    }

    public Partnership getPartnerName() {
        return partnership;
    }

    public String getPartnerStringName() {
        return partnership.getId();
    }

    public String getPartnershipTabTitle() {
        return partnership.getTitle();
    }

    public String getPartnershipHtml() {
        return partnership.getHtml().orNull();
    }

    public String getPartnershipVersion() {
        return partnership.getVersion().orNull();
    }

    public boolean isMain() {
        return isMain;
    }

    public static final class Builder {

        private Partnership partnership;
        private boolean isMain;

        public Builder withPartnerName(Partnership partnership) {
            this.partnership = partnership;
            return this;
        }

        public Builder withIsMain(boolean isMain) {
            this.isMain = isMain;
            return this;
        }

        public PartnershipTab build() {
            return new PartnershipTab(this.partnership, this.isMain);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PartnershipTab that = (PartnershipTab) o;
        return isMain == that.isMain &&
                Objects.equals(partnership, that.partnership);
    }

    @Override
    public int hashCode() {
        return Objects.hash(partnership, isMain);
    }
}
