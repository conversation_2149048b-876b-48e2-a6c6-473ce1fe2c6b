package com.gumtree.mobile.web.vip;

import com.gumtree.madgex.MadgexUtils;
import com.gumtree.web.common.path.AbstractPath;
import com.gumtree.web.common.path.PathBuilder;
import org.apache.commons.lang3.BooleanUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

import static com.gumtree.web.common.path.PathBuilder.path;

public class VIPPath extends AbstractPath {
    public static final String REPORT_AD_SUCCESS = "reportAdSuccess";
    public static final String MAKE_OFFER_SUCCESS = "makeOfferSuccess";
    public static final String PREFIX = "p";
    public static final String MAPPING = "/" + PREFIX + "/{categoryName}/{adTitle}/{adId}";

    private final String categoryName;
    private final String adTitle;
    private final Long adId;
    private final boolean allowMadgex;

    public VIPPath(String categoryName, String adTitle, Long adId) {
        this(categoryName, adTitle, adId, true);
    }

    public VIPPath(String categoryName, String adTitle, Long adId, Boolean allowMadgex) {
        this.categoryName = categoryName;
        this.adTitle = adTitle;
        this.adId = adId;
        this.allowMadgex = allowMadgex;
    }


    public VIPPath addReportAdSuccessParam(boolean value) {
        addQueryParam(REPORT_AD_SUCCESS, String.valueOf(value));
        return this;
    }

    public VIPPath addMakeOfferSuccessParam(Boolean value) {
        if (value != null) {
            addQueryParam(MAKE_OFFER_SUCCESS, String.valueOf(value));
        }
        return this;
    }

    @Override
    protected String getPathSegment() {
        PathBuilder pathBuilder = path()
                .addPathSegment(PREFIX)
                .addPathSegment(categoryName)
                .addPathSegment(adTitle, true)
                .addPathSegment(adId.toString());
        return pathBuilder.build();
    }

    public static final Optional<Boolean> parseBoolParam(String name, HttpServletRequest request) {
        String value = request.getParameter(name);
        return Optional.ofNullable(BooleanUtils.toBooleanObject(value));
    }

    public static final Optional<Boolean> parseMakeOfferSuccess(HttpServletRequest request) {
        String value = request.getParameter(VIPPath.MAKE_OFFER_SUCCESS);
        return Optional.ofNullable(BooleanUtils.toBooleanObject(value));
    }

    public static boolean isMadgexAdvertId(Long advertId) {
        return advertId > MadgexUtils.MADGEX_ADS_OFFSET;
    }
}
