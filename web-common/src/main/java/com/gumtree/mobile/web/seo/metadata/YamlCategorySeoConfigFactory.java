package com.gumtree.mobile.web.seo.metadata;

import org.springframework.core.io.Resource;
import org.springframework.util.Assert;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.error.YAMLException;

import java.io.IOException;
import java.io.InputStream;

/**
 * Factory class for the {@link com.gumtree.mobile.web.seo.metadata.CategorySeoConfig} that load config from the YAML file
 */
public class YamlCategorySeoConfigFactory implements CategorySeoConfigFactory {
    private Resource configFile;

    public YamlCategorySeoConfigFactory(Resource configFile) {
        Assert.notNull(configFile);
        this.configFile = configFile;
    }

    /**
     * Get category config loaded from the supplied config file
     * @return the category config
     * @throws java.io.IOException if file is not found or there was problem reading the config files
     */
    public CategorySeoConfig getNewInstance() throws IOException {
        return new ReloadableCategorySeoConfig(getNewInstance(configFile.getInputStream()));
    }

    public static CategorySeoConfig getNewInstance(InputStream is) throws IOException {
        try {
            return new Yaml().loadAs(is, YamlCategorySeoConfigs.class);
        } catch (YAMLException e) {
            throw new IOException(e);
        }
    }
}
