package com.gumtree.mobile.web.seo.metadata;

import com.google.common.collect.ImmutableMap;
import com.gumtree.api.category.domain.CategoryConstants.Attribute;

import java.util.Map;

public enum SEOMetadataProperty {

    CATEGORY("[Category]", "category"),
    LOCATION("[Location]", "location"),
    IN_LOCATION("[In-Location]", null),
    PAGINATION("[x]/[x]", "pagination"),
    AD_COUNT("[Ad-count]"),
    AD_TITLE("[Ad-title]"),
    AD_COPY("[Ad-copy]"),
    SEARCH_TERM("[Search-term]", "search-term"),
    SEARCH_TERM_CAPITALIZED("[Search-term-capitalized]", null),
    BLANK_SEARCH_TERM(null, "blank-search"),
    VIP(null, "vip"),
    EMPLOYER("[employer]", "employer"),
    CAR_MAKE("[Car-make]", Attribute.VEHICLE_MAKE.getName()),
    CAR_MODEL("[Car-model]", Attribute.VEHICLE_MODEL.getName()),
    TOP_CAR_MAKES("[top-car-makes]", null),
    TOP_CAR_MODELS("[top-car-models]", null),
    POPULAR_SEARCH(null, "popular-search"),
    CAR_TRANSMISSION("[Car-Transmission]", Attribute.VEHICLE_TRANSMISSION.getName()),
    CAR_BODY_TYPE("[Car-body-type]", Attribute.VEHICLE_BODY_TYPE.getName()),
    CAR_ENGINE_SIZE("[Car-engine-size]", Attribute.VEHICLE_ENGINE_SIZE.getName()),
    SELLER_TYPE("[seller-type]", Attribute.SELLER_TYPE.getName()),
    CAR_FUEL_TYPE("[Car-fuel-type]", Attribute.VEHICLE_FUEL_TYPE.getName()),
    BIKE_MAKE("[Motorbike-make]", Attribute.MOTORBIKE_MAKE.getName()),
    PROPERTY_TYPE("[property_type]", Attribute.PROPERTY_TYPE.getName()),
    PROPERTY_NUMBER_BEDS("[property_number_beds]", Attribute.BEDS.getName()),
    PROPERTY_ROOM_TYPE("[property_room_type]", Attribute.PROPERTY_ROOM_TYPE.getName()),
    JOB_LEVEL("["+ Attribute.JOB_LEVEL.getName()+"]", Attribute.JOB_LEVEL.getName()),
    JOB_LANG("["+ Attribute.JOB_LANG.getName()+"]", Attribute.JOB_LANG.getName()),
    JOB_CONTRACT_TYPE("["+ Attribute.JOB_CONTRACT_TYPE.getName()+"]", Attribute.JOB_CONTRACT_TYPE.getName()),
    RECRUITER_TYPE("["+ Attribute.RECRUITER_TYPE.getName()+"]", Attribute.RECRUITER_TYPE.getName()),
    JOB_HOURS("["+ Attribute.JOB_HOURS.getName()+"]", Attribute.JOB_HOURS.getName());

    public static final Map<String, SEOMetadataProperty> SEO_METADATA_PROPERTY_KEY_MAP = buildPropertyKeyMap();

    private String placeHolder;
    private String key;

    SEOMetadataProperty(String placeHolder, String key) {
        this.placeHolder = placeHolder;
        this.key = key;
    }

    SEOMetadataProperty(String placeHolder) {
        this.placeHolder = placeHolder;
    }

    public String getPlaceHolder() {
        return placeHolder;
    }

    public String getKey() {
        return key;
    }

    public boolean hasPlaceholder() {
        return placeHolder != null;
    }

    public boolean hasKey() {
        return key != null;
    }

    private static ImmutableMap<String, SEOMetadataProperty> buildPropertyKeyMap() {
        ImmutableMap.Builder<String, SEOMetadataProperty> mapBuilder = new ImmutableMap.Builder<String, SEOMetadataProperty>();
        for (SEOMetadataProperty seoMetadataProperty : SEOMetadataProperty.values()) {
            String key = seoMetadataProperty.getKey();
            if (key != null) {
                mapBuilder.put(key, seoMetadataProperty);
            }
        }
        return mapBuilder.build();
    }
}
