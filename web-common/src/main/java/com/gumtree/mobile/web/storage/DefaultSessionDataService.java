package com.gumtree.mobile.web.storage;

import com.gumtree.common.util.search.AdvertClickSource;
import com.gumtree.web.cookie.UserSessionService;
import org.springframework.util.StringUtils;

public class DefaultSessionDataService implements SessionDataService {

    private static final String CLICK_SOURCE_KEY_PREFIX = "clicksource_";

    protected final UserSessionService userSessionService;

    public DefaultSessionDataService(UserSessionService userSessionService) {
        this.userSessionService = userSessionService;
    }

    @Override
    public AdvertClickSource getAdvertClickThroughType(Long adId) {
        for (AdvertClickSource clickSource : AdvertClickSource.values()) {
            String key = getClickSourceKey(clickSource);
            String value = userSessionService.getSessionCookie().getClickThroughTypeForAdvert(key);
            if (StringUtils.hasText(value) && value.contains(adId.toString())) {
                return clickSource;
            }
        }
        return AdvertClickSource.OTHER;
    }

    protected String getClickSourceKey(AdvertClickSource clickSource) {
        return CLICK_SOURCE_KEY_PREFIX + clickSource.getParameterValue();
    }

}
