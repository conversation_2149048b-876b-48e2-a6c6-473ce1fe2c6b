package com.gumtree.mobile.web.seo.metadata;

import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class ReloadableCategorySeoConfig implements CategorySeoConfig, ApplicationListener<ReloadableCategorySeoConfig.ConfigChangeEvent> {
    private CategorySeoConfig delegate;

    public ReloadableCategorySeoConfig(CategorySeoConfig delegate) {
        this.delegate = delegate;
    }

    @Override
    public Map<String, String> getPageConfig(List<String> categoryPath, Set<String> pageId) {
        return delegate.getPageConfig(categoryPath, pageId);
    }

    @Override
    public void onApplicationEvent(ConfigChangeEvent event) {
        this.delegate = event.getConfig();
    }

    public static class ConfigChangeEvent extends ApplicationEvent {
        private final CategorySeoConfig config;

        public ConfigChangeEvent(CategorySeoConfig config, Object source) {
            super(source);
            this.config = config;
        }

        public CategorySeoConfig getConfig() {
            return config;
        }
    }

    public CategorySeoConfig getDelegate() {
        return delegate;
    }
}
