package com.gumtree.api.bapi;


import com.google.common.collect.Lists;
import com.gumtree.api.Location;
import com.gumtree.api.Locations;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertNotNull;

public class BapiLocationsHelperTest {

    @Test
    public void testCreateRootLocation() {
        //given
        Locations locations = new Locations();
        List<Location> allLocations = generateLocationsTree();
        locations.setLocations(allLocations);

        //when
        com.gumtree.web.common.domain.location.Location root = new BapiLocationsHelper().createRootLocation(locations);

        //then
        assertNotNull(root);
        assertThat(root.getId(), is(1l));
        assertThat(root.getSeoName(), is("parent"));
        assertThat(root.getZoomIns().size(), is(1));

        com.gumtree.web.common.domain.location.Location child = root.getZoomIns().get(0);
        assertThat(child.getId(), is(2l));
        assertThat(child.getSeoName(), is("child"));
        assertThat(child.getZoomIns().size(), is(1));

        com.gumtree.web.common.domain.location.Location gchild = child.getZoomIns().get(0);
        assertThat(gchild.getId(), is(3l));
        assertThat(gchild.getSeoName(), is("gchild"));
    }

    private List<Location> generateLocationsTree() {
        List<Location> allLocations = Lists.newArrayList();

        Location gchild = new Location();
        Location child = new Location();
        Location parent = new Location();

        gchild.setId(3L);
        gchild.setName("GChild");
        gchild.setSeoName("gchild");
        gchild.setRadius(BigDecimal.ONE);
        gchild.setLongitude(BigDecimal.ONE);
        gchild.setLatitude(BigDecimal.ONE);
        gchild.setLanding(true);
        gchild.setZoomOuts(Lists.newArrayList(2L));

        child.setId(2L);
        child.setName("Child");
        child.setSeoName("child");
        child.setRadius(BigDecimal.ONE);
        child.setLongitude(BigDecimal.ONE);
        child.setLatitude(BigDecimal.ONE);
        child.setLanding(true);
        child.setChildren(new Location[]{gchild});
        child.setZoomOuts(Lists.newArrayList(1L));
        child.setZoomIns(Lists.newArrayList(3L));

        parent.setId(1L);
        parent.setName("Parent");
        parent.setSeoName("parent");
        parent.setRadius(BigDecimal.ONE);
        parent.setLongitude(BigDecimal.ONE);
        parent.setLatitude(BigDecimal.ONE);
        parent.setLanding(true);
        parent.setChildren(new Location[]{child});
        parent.setZoomIns(Lists.newArrayList(2L));

        allLocations.add(parent);
        allLocations.add(child);
        allLocations.add(gchild);

        return allLocations;
    }
}
