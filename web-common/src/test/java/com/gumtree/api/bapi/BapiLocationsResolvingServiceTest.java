package com.gumtree.api.bapi;

import com.gumtree.api.Location;
import com.gumtree.api.Locations;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.service.LocationsService;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;

import static com.gumtree.api.service.LocationsService.Level.COUNTRY;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BapiLocationsResolvingServiceTest {

    private static final long ROOT_ID = 10000392L;
    private static final long ENGLAND_ID = 123L;
    private static final long LONDON_ID = 10000344L;
    private BapiLocationsResolvingService locationsService;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private BushfireApi bushfireApi;

    private static HystrixRequestContext context;

    @Before
    public void before() {
        context = HystrixRequestContext.initializeContext();

        when(bushfireApi.locationApi().locations()).thenReturn(stubLocations());

        locationsService = new BapiLocationsResolvingService(bushfireApi);

        Mockito.reset(bushfireApi);
    }

    @After
    public void after() {
        context.shutdown();
    }

    @Test
    public void returnsValidCategoryTree() {
        assertThat(locationsService.getRoot().getName(), is("United Kingdom"));
        assertThat(locationsService.getRoot().getId(), is(ROOT_ID));
        assertThat(locationsService.getRoot().getCountry(), is("United Kingdom"));
        assertThat(locationsService.getRoot().hasLandingPage(), is(true));
        assertThat(locationsService.getRoot().getZoomIns().size(), is(1));
        assertThat(locationsService.getLocation(10000344L).get().getName(), is("London"));
        assertThat(locationsService.getLocation(10000344L).get().getRadius(), is(new BigDecimal("31.063000")));
        assertThat(locationsService.getLocation(10000344L).get().getLongitude(), is(new BigDecimal("-0.129356")));
        assertThat(locationsService.getLocation(10000344L).get().getLatitude(), is(new BigDecimal("51.507248")));
        assertThat(locationsService.get(COUNTRY, 10000344L).get().getName(), is("England"));
    }

    @Test
    public void returnsInMemoryResolvedLocationFromName() {
        assertTrue(locationsService.getLocation("london").isPresent());
        assertThat(locationsService.getLocation("london").get().getId(), is(LONDON_ID));
        verifyZeroInteractions(bushfireApi);
    }

    @Test
    public void returnsInMemoryResolvedLocationFromId() {
        assertTrue(locationsService.getLocation(LONDON_ID).isPresent());
        assertThat(locationsService.getLocation(LONDON_ID).get().getName(), is("London"));
        verifyZeroInteractions(bushfireApi);
    }

    @Test
    public void returnsInMemoryResolvedLocationFromDisplayName() {
        assertTrue(locationsService.getLocationByDisplayName("London").isPresent());
        assertThat(locationsService.getLocationByDisplayName("London").get().getId(), is(LONDON_ID));
        verifyZeroInteractions(bushfireApi);
    }

    @Test
    public void returnsRootLocationWhenIdIsNotMapped() {
        assertThat(locationsService.getBestMatch(123456L).getId(), is(ROOT_ID));
        verifyZeroInteractions(bushfireApi);
    }

    @Test
    public void returnsResolvedLocationWhenStoredInMemory() {
        assertThat(locationsService.resolveLocation(LONDON_ID).getIdName(), is("london"));
        verifyZeroInteractions(bushfireApi);
    }

    @Test
    public void returnsUnresolvedLocationWhenNotStored() {
        assertThat(locationsService.resolveLocation(9999L).getIdName(), is("9999"));
        verifyZeroInteractions(bushfireApi);
    }

    @Test
    public void returnsSpecificLevelForLocationId() {
        assertTrue(locationsService.get(LocationsService.Level.COUNTRY, LONDON_ID).isPresent());
        assertThat(locationsService.get(LocationsService.Level.COUNTRY, LONDON_ID).get().getName(), is("England"));
        verifyZeroInteractions(bushfireApi);
    }

    @Test
    public void returnsHierarchyForLocationId() {
        assertThat(locationsService.getHierarchy(locationsService.getLocation(LONDON_ID).get()).size(), is(3));
        assertThat(locationsService.getHierarchy(locationsService.getLocation(LONDON_ID).get()).get(0).getName(), is("United Kingdom"));
        assertThat(locationsService.getHierarchy(locationsService.getLocation(LONDON_ID).get()).get(1).getName(), is("England"));
        assertThat(locationsService.getHierarchy(locationsService.getLocation(LONDON_ID).get()).get(2).getName(), is("London"));
        verifyZeroInteractions(bushfireApi);
    }

    private Locations stubLocations() {
        Locations locations = new Locations();
        Location ukLocation = new Location();
        ukLocation.setId(ROOT_ID);
        ukLocation.setName("United Kingdom");
        ukLocation.setLanding(true);
        ukLocation.setZoomOuts(emptyList());
        ukLocation.setZoomIns(singletonList(ENGLAND_ID));

        Location englandLocation = new Location();
        englandLocation.setId(ENGLAND_ID);
        englandLocation.setName("England");
        englandLocation.setLanding(true);
        englandLocation.setZoomOuts(singletonList(ROOT_ID));
        englandLocation.setZoomIns(singletonList(LONDON_ID));

        Location londonLocation = new Location();
        londonLocation.setId(LONDON_ID);
        londonLocation.setName("London");
        londonLocation.setSeoName("london");
        londonLocation.setLatitude(new BigDecimal("51.507248"));
        londonLocation.setLongitude(new BigDecimal("-0.129356"));
        londonLocation.setRadius(new BigDecimal("31.063000"));
        londonLocation.setLanding(true);
        londonLocation.setZoomOuts(singletonList(ENGLAND_ID));

        locations.setLocations(Arrays.asList(ukLocation, englandLocation, londonLocation));
        return locations;
    }

}