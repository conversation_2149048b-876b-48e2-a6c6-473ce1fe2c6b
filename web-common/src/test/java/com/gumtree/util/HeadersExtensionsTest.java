package com.gumtree.util;

import com.gumtree.web.abtest.growthbook.ClientExperiments;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class HeadersExtensionsTest {

    @Mock
    private HttpServletRequest mockRequest;

    @Before
    public void setUp() {
        // 初始化 mock 对象
    }

    /**
     * TC01: header 为 null
     * 预期结果: 返回空 experiments
     */
    @Test
    public void testGetClientExperiments_HeaderIsNull_ReturnsEmpty() {
        when(mockRequest.getHeader(HeadersExtensions.EXPERIMENTS_HEADER)).thenReturn(null);

        ClientExperiments result = HeadersExtensions.getClientExperiments(mockRequest);

        assertTrue(result.getExperiments().isEmpty());
    }

    /**
     * TC02: header 为空字符串
     * 预期结果: 返回空 experiments
     */
    @Test
    public void testGetClientExperiments_HeaderIsEmptyString_ReturnsEmpty() {
        when(mockRequest.getHeader(HeadersExtensions.EXPERIMENTS_HEADER)).thenReturn("");

        ClientExperiments result = HeadersExtensions.getClientExperiments(mockRequest);

        assertTrue(result.getExperiments().isEmpty());
    }

    /**
     * TC03: header 包含一个合法实验 GTNA-1.A
     * 预期结果: 成功解析为 GTNA_1 -> A
     */
    @Test
    public void testGetClientExperiments_ValidSingleExperiment_ReturnsCorrectMap() {
        when(mockRequest.getHeader(HeadersExtensions.EXPERIMENTS_HEADER)).thenReturn("GTNA-1.A");

        ClientExperiments result = HeadersExtensions.getClientExperiments(mockRequest);

        Map<ClientExperiments.ExperimentEnum, ClientExperiments.VariantEnum> expected = new HashMap<>();
        expected.put(ClientExperiments.ExperimentEnum.GTNA_1, ClientExperiments.VariantEnum.A);
        assertEquals(expected, result.getExperiments());
    }

    /**
     * TC04: header 包含多个合法实验
     * 预期结果: 所有实验都被正确解析
     */
    @Test
    public void testGetClientExperiments_MultipleValidExperiments_ReturnsAll() {
        when(mockRequest.getHeader(HeadersExtensions.EXPERIMENTS_HEADER)).thenReturn("GTNA-1.A,syi_cate_sug_flag.B");

        ClientExperiments result = HeadersExtensions.getClientExperiments(mockRequest);

        Map<ClientExperiments.ExperimentEnum, ClientExperiments.VariantEnum> expected = new HashMap<>();
        expected.put(ClientExperiments.ExperimentEnum.GTNA_1, ClientExperiments.VariantEnum.A);
        expected.put(ClientExperiments.ExperimentEnum.SYI_CATE_SUG_FLAG, ClientExperiments.VariantEnum.B);
        assertEquals(expected, result.getExperiments());
        assertEquals(true, result.isB(ClientExperiments.ExperimentEnum.SYI_CATE_SUG_FLAG));
        assertEquals(true, result.isA(ClientExperiments.ExperimentEnum.GTNA_1));
        assertEquals(false, result.isC(ClientExperiments.ExperimentEnum.GTNA_1));
        assertEquals(false, result.isD(ClientExperiments.ExperimentEnum.GTNA_1));
    }

    @Test
    public void testGetClientExperiments_MultipleLikeValidExperiments_ReturnsAll() {
        when(mockRequest.getHeader(HeadersExtensions.EXPERIMENTS_HEADER)).thenReturn("GTNA-1.A,syi_cate_sug_flag.A");

        ClientExperiments result = HeadersExtensions.getClientExperiments(mockRequest);

        Map<ClientExperiments.ExperimentEnum, ClientExperiments.VariantEnum> expected = new HashMap<>();
        expected.put(ClientExperiments.ExperimentEnum.GTNA_1, ClientExperiments.VariantEnum.A);
        expected.put(ClientExperiments.ExperimentEnum.SYI_CATE_SUG_FLAG, ClientExperiments.VariantEnum.A);
        assertEquals(expected, result.getExperiments());
        assertEquals(true, result.isA(ClientExperiments.ExperimentEnum.SYI_CATE_SUG_FLAG));
        assertEquals(true, result.isA(ClientExperiments.ExperimentEnum.GTNA_1));
        assertEquals(false, result.isC(ClientExperiments.ExperimentEnum.GTNA_1));
        assertEquals(false, result.isD(ClientExperiments.ExperimentEnum.GTNA_1));
    }

    @Test
    public void testGetClientExperiments_MultipleLikeBValidExperiments_ReturnsAll() {
        when(mockRequest.getHeader(HeadersExtensions.EXPERIMENTS_HEADER)).thenReturn("aaaaaaa.B,bbbbbbb.B");

        ClientExperiments result = HeadersExtensions.getClientExperiments(mockRequest);

        assertEquals(0, result.getExperiments().size());
    }




    /**
     * TC05: header 包含未知实验
     * 预期结果: UNKNOWN 实验被过滤掉
     */
    @Test
    public void testGetClientExperiments_UnknownExperiment_ReturnsEmpty() {
        when(mockRequest.getHeader(HeadersExtensions.EXPERIMENTS_HEADER)).thenReturn("unknown.C");

        ClientExperiments result = HeadersExtensions.getClientExperiments(mockRequest);

        assertTrue(result.getExperiments().isEmpty());
    }

    /**
     * TC06: header 包含非法 variant
     * 预期结果: variant 为 UNKNOWN
     */
    @Test
    public void testGetClientExperiments_InvalidVariant_ReturnsUnknownVariant() {
        when(mockRequest.getHeader(HeadersExtensions.EXPERIMENTS_HEADER)).thenReturn("GTNA-1.X");

        ClientExperiments result = HeadersExtensions.getClientExperiments(mockRequest);

        Map<ClientExperiments.ExperimentEnum, ClientExperiments.VariantEnum> expected = new HashMap<>();
        expected.put(ClientExperiments.ExperimentEnum.GTNA_1, ClientExperiments.VariantEnum.UNKNOWN);
        assertEquals(expected, result.getExperiments());
    }

    /**
     * TC07: header 包含混合的有效与无效实验
     * 预期结果: 只保留有效的实验
     */
    @Test
    public void testGetClientExperiments_MixedValidAndInvalid_ReturnsOnlyValid() {
        when(mockRequest.getHeader(HeadersExtensions.EXPERIMENTS_HEADER)).thenReturn("GTNA-1.A,unknown.B,syi_cate_sug_flag.C");

        ClientExperiments result = HeadersExtensions.getClientExperiments(mockRequest);

        Map<ClientExperiments.ExperimentEnum, ClientExperiments.VariantEnum> expected = new HashMap<>();
        expected.put(ClientExperiments.ExperimentEnum.GTNA_1, ClientExperiments.VariantEnum.A);
        expected.put(ClientExperiments.ExperimentEnum.SYI_CATE_SUG_FLAG, ClientExperiments.VariantEnum.C);
        assertEquals(expected, result.getExperiments());
    }
}
