package com.gumtree.util;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class XSSStripperUtilsTest {

    String testJSScript = "<script>alert(document.cookie)</script>";
    String testOnLoadEventScript = "onload(function{})=";
    String testValidString = "London & SouthHall";
    String testCodeTags = "<% =Replace(\"red blue red-blue redblue bluered\", \"blue\", \"purple\", 1, 3) %>";
    String testSvgObjectTag = "<svg/onload=alert('XSS')>";

    @Test
    public void shouldReturnEmptyStringOnScriptTag(){
        assertThat("").isEqualTo(XSSStripperUtils.stripXSS(testJSScript));
    }

    @Test
    public void shouldReturnEmptyStringOnLoadEventScript(){
        assertThat("").isEqualTo(XSSStripperUtils.stripXSS(testOnLoadEventScript));
    }

    @Test
    public void shouldReturnValidStringOnValidEntry(){
        assertThat("London & SouthHall").isEqualTo(XSSStripperUtils.stripXSS(testValidString));
    }

    @Test
    public void shouldReturnEmptyStringOnCodeTags(){
        assertThat("").isEqualTo(XSSStripperUtils.stripXSS(testCodeTags));
    }

    @Test
    public void shouldReturnEmptyStringOnSVGObjectTag(){
        assertThat("").isEqualTo(XSSStripperUtils.stripXSS(testSvgObjectTag));
    }

}

