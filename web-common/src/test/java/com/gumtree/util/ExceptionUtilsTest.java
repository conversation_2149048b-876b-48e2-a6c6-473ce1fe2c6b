package com.gumtree.util;

import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class ExceptionUtilsTest {

    @Test
    public void shouldGetMessageForGenericException() {
        // when
        String message = ExceptionUtils.toShortMessage(new IllegalArgumentException("id is required"));

        // then
        assertThat(message).isEqualTo("IllegalArgumentException: id is required");
    }

    @Test
    public void shouldGetMessageForExceptionWrappedInHystrixException() {
        // when
        String message = ExceptionUtils.toShortMessage(new HystrixRuntimeException(
                HystrixRuntimeException.FailureType.COMMAND_EXCEPTION,
                TestCommand.class,
                "message",
                new IllegalArgumentException("id is required"),
                null));

        // then
        assertThat(message).isEqualTo("HystrixRuntimeException > IllegalArgumentException: id is required");
    }

    @Test
    public void shouldGetMessageForHystrixExceptionWithNoCause() {
        // when
        String message = ExceptionUtils.toShortMessage(new HystrixRuntimeException(
                HystrixRuntimeException.FailureType.COMMAND_EXCEPTION,
                TestCommand.class,
                "hystrix error message",
                null,
                null));

        // then
        assertThat(message).isEqualTo("HystrixRuntimeException: hystrix error message");
    }

    public static class TestCommand extends HystrixCommand<Boolean> {
        public TestCommand(HystrixCommandGroupKey group) {
            super(group);
        }

        @Override
        protected Boolean run() throws Exception {
            return false;
        }
    }
}