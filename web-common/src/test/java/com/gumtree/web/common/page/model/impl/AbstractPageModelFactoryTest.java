package com.gumtree.web.common.page.model.impl;

import com.gumtree.api.category.domain.Category;
import com.gumtree.config.DefaultPropertyTestConfiguration;
import com.gumtree.domain.location.Location;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.Link;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.common.security.UserContextPageLinks;
import com.gumtree.web.common.security.UserContextPageLinksFactory;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = DefaultPropertyTestConfiguration.class)
public class AbstractPageModelFactoryTest {

    private AbstractPageModelFactory factory;

    private UrlScheme urlScheme;

    private UserContextPageLinksFactory pageLinksFactory;

    private UserContextPageLinks pageLinks;

    private LocationService locationService;

    private GumtreePageContext gumtreePageContext;

    private GumtreePage gumtreePage;

    private Location homepageLocation;

    private Location location;

    private Category category;

    @Before
    public void init() {
        factory = new AbstractPageModelFactory() {
        };

        urlScheme = mock(UrlScheme.class);
        pageLinksFactory = mock(UserContextPageLinksFactory.class);
        pageLinks = mock(UserContextPageLinks.class);
        locationService = mock(LocationService.class);
        gumtreePageContext = mock(GumtreePageContext.class);
        gumtreePage = mock(GumtreePage.class);
        homepageLocation = mock(Location.class);
        category = mock(Category.class);
        location = mock(Location.class);

        when(gumtreePageContext.getHomepageLocation()).thenReturn(homepageLocation);
        when(gumtreePageContext.getCategory()).thenReturn(category);
        when(gumtreePageContext.getLocation()).thenReturn(location);

        when(homepageLocation.getDisplayName()).thenReturn("Homepage Location");

        ReflectionTestUtils.setField(factory, "urlScheme", urlScheme);
        ReflectionTestUtils.setField(factory, "locationService", locationService);
        ReflectionTestUtils.setField(factory, "userContextPageLinksFactory", pageLinksFactory);

        when(urlScheme.urlFor(homepageLocation)).thenReturn("homepage.url");
        when(urlScheme.urlFor(Actions.UK_HOME)).thenReturn("uk.url");
        when(urlScheme.postAdUrlFor(homepageLocation, category)).thenReturn("postAd.url");

        when(pageLinksFactory.createPageLinks(homepageLocation, category)).thenReturn(pageLinks);
        when(pageLinks.getPostAdLink()).thenReturn(new SimpleLink("Post Ad", "postad.url"));
        when(pageLinks.getPostEventLink()).thenReturn(new SimpleLink("Post Event", "postEvent.url"));
        when(pageLinks.getManageAdsLink()).thenReturn(new SimpleLink("Manage Ads", "manageads.url"));
        when(pageLinks.getEditAccountLink()).thenReturn(new SimpleLink("Edit Account", "editaccount.url"));
        when(pageLinks.getLogoutLink()).thenReturn(new SimpleLink("Logout", "logout.url"));
        when(pageLinks.getLoginLink()).thenReturn(new SimpleLink("Login", "login.url"));
        when(pageLinks.getCreateAccountLink()).thenReturn(new SimpleLink("Create account", "createaccount.url"));
    }

    @Test
    public void createsCorrectHeaderModel() {
        CommonHeaderModel commonHeaderModel = (CommonHeaderModel) factory.createHeaderModel(gumtreePageContext, gumtreePage);
        Link homepageLocationLink = commonHeaderModel.getHomePageLink();
        assertThat(homepageLocationLink.getText(), equalTo("Homepage Location"));
        assertThat(homepageLocationLink.getUrl(), equalTo("homepage.url"));
        Link ukHomepageLink = commonHeaderModel.getUKHomePageLink();
        assertThat(ukHomepageLink.getText(), equalTo("United Kingdom"));
        assertThat(ukHomepageLink.getUrl(), equalTo("uk.url"));
        Link postAdLink = commonHeaderModel.getPostAdLink();
        assertThat(postAdLink.getText(), equalTo("Post Ad"));
        assertThat(postAdLink.getUrl(), equalTo("postad.url"));
        Link postEventLink = commonHeaderModel.getPostEventLink();
        assertThat(postEventLink.getText(), equalTo("Post Event"));
        assertThat(postEventLink.getUrl(), equalTo("postEvent.url"));
        Link manageAdsLink = commonHeaderModel.getManageAdsLink();
        assertThat(manageAdsLink.getText(), equalTo("Manage Ads"));
        assertThat(manageAdsLink.getUrl(), equalTo("manageads.url"));
        Link editAccountLink = commonHeaderModel.getEditAccountLink();
        assertThat(editAccountLink.getText(), equalTo("Edit Account"));
        assertThat(editAccountLink.getUrl(), equalTo("editaccount.url"));
        Link logoutLink = commonHeaderModel.getLogoutLink();
        assertThat(logoutLink.getText(), equalTo("Logout"));
        assertThat(logoutLink.getUrl(), equalTo("logout.url"));
        Link logintLink = commonHeaderModel.getLoginLink();
        assertThat(logintLink.getText(), equalTo("Login"));
        assertThat(logintLink.getUrl(), equalTo("login.url"));
        Link createAccountLink = commonHeaderModel.getCreateAccountLink();
        assertThat(createAccountLink.getText(), equalTo("Create account"));
        assertThat(createAccountLink.getUrl(), equalTo("createaccount.url"));
    }

    @Test
    public void createsFooterModelWhenPopularSearchesEnabled() {
        when(gumtreePage.populatePopularSearches()).thenReturn(true);
        CommonFooterModel commonFooterModel = (CommonFooterModel) factory.createFooterModel(gumtreePageContext, gumtreePage);
        assertThat(commonFooterModel.getLocation(), equalTo(location));
        assertThat(commonFooterModel.getCategory().getId(), equalTo(category.getId()));
    }

    @Test
    public void createsFooterModelWhenPopularSearchesDisabled() {
        when(gumtreePage.populatePopularSearches()).thenReturn(false);
        CommonFooterModel commonFooterModel = (CommonFooterModel) factory.createFooterModel(gumtreePageContext, gumtreePage);
        assertThat(commonFooterModel.getLocation(), equalTo(location));
        assertThat(commonFooterModel.getCategory().getId(), equalTo(category.getId()));
    }

}
