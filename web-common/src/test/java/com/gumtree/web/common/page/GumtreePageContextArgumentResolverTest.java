package com.gumtree.web.common.page;

import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.common.page.context.GumtreePageContextArgumentResolver;
import org.junit.Before;
import org.junit.Test;
import org.springframework.core.MethodParameter;
import org.springframework.test.util.ReflectionTestUtils;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class GumtreePageContextArgumentResolverTest {

    private GumtreePageContextArgumentResolver argumentResolver;

    private GumtreePageContext pageContext;

    @Before
    public void init() {
        pageContext = mock(GumtreePageContext.class);
        when(pageContext.isInitialised()).thenReturn(true);
        argumentResolver = new GumtreePageContextArgumentResolver();
        ReflectionTestUtils.setField(argumentResolver, "pageContext", pageContext);
    }

    @Test
    public void supportsGumtreePageContextMethodParameter() {
        MethodParameter parameter = mock(MethodParameter.class);
        when(parameter.getParameterType()).thenReturn((Class) GumtreePageContext.class);
        assertThat(argumentResolver.supportsParameter(parameter), equalTo(true));
    }

    @Test
    public void doesNotSupportMethodParametersThatAreNotOfTypeGumtreePageContext() {
        MethodParameter parameter = mock(MethodParameter.class);
        when(parameter.getParameterType()).thenReturn((Class) GumtreePage.class);
        assertThat(argumentResolver.supportsParameter(parameter), equalTo(false));
    }

    @Test
    public void resolveArgumentReturnsWiredInGumtreePageContext() throws Exception {
        assertThat(argumentResolver.resolveArgument(null, null, null, null), equalTo((Object) pageContext));
    }

    @Test(expected = IllegalStateException.class)
    public void throwsIllegalStateExceptionIfPageContextHasNotBeenInitialised() throws Exception {
        when(pageContext.isInitialised()).thenReturn(false);
        argumentResolver.resolveArgument(null, null, null, null);
    }
}
