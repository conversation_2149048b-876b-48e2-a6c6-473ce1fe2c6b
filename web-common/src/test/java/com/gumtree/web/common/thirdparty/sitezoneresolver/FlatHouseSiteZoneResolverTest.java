package com.gumtree.web.common.thirdparty.sitezoneresolver;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.web.common.thirdparty.LegacySiteNameResolver;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import org.junit.Before;
import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class FlatHouseSiteZoneResolverTest {

    private FlatHouseSiteZoneResolver resolver;
    private ThirdPartyRequestContext<?> requestContext;

    @Before
    public void setUp() {
        LegacySiteNameResolver legacySiteNameResolver = mock(LegacySiteNameResolver.class);
        resolver = new FlatHouseSiteZoneResolver(legacySiteNameResolver);
        requestContext = mock(ThirdPartyRequestContext.class);
        when(legacySiteNameResolver.resolveSiteName(anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                return invocation.getArguments()[0];
            }
        });
    }

    @Test
    public void testSiteAndZoneWhereCategoryIsL2BelowFlatsAndHouses() {
        Category flatsHouses = createCategory(10201, Categories.FLATS_AND_HOUSES.getSeoName());
        Category l2Category = createCategory(2, "propertyforsale");
        when(requestContext.getCategory()).thenReturn(l2Category);
        when(requestContext.getCategory(0)).thenReturn(flatsHouses);
        when(requestContext.getCategory(1)).thenReturn(l2Category);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("flathouseforsale"));
    }

    private Category createCategory(long id, String name) {
        Category category = mock(Category.class);
        when(category.getId()).thenReturn(id);
        when(category.getSeoName()).thenReturn(name);
        return category;
    }

}
