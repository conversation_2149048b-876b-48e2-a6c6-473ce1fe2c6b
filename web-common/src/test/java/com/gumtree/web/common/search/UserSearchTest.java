package com.gumtree.web.common.search;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.search.UserGeoLocation;
import com.gumtree.search.UserSearchKeywords;
import com.gumtree.search.UserSearchLocation;
import com.gumtree.search.UserSearchRefinement;
import com.gumtree.search.sorting.Sorting;
import com.gumtree.util.model.UserSearch;
import org.hamcrest.Matcher;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;
import static org.mockito.BDDMockito.given;

@RunWith(MockitoJUnitRunner.class)
public class UserSearchTest {
    @Mock private Category category;
    @Mock private Location location;

    /**
     * This is CRITICAL for caching!
     */
    @Test
    public void ensureThatEqualsIsOverwritten() {
        UserSearchKeywords keywords = new UserSearchKeywords("user input", new HashSet<String>(),
                new HashSet<Category>());
        UserSearchLocation locationSearch = new UserSearchLocation(location, Collections.<String>emptyList(),
                Collections.<String>emptyList(), "location stuff");
        UserSearchRefinement refinement = new UserSearchRefinement.Builder()
                .attributes(new ArrayList<Attribute>())
                .sorting(Sorting.DEFAULT)
                .urgentAdsOnly(true)
                .adsWithImagesOnly(false)
                .build();

        UserSearch userSearch = new UserSearch.Builder()
                .category(category)
                .keywordsSearch(keywords)
                .locationSearch(locationSearch)
                .refinement(refinement)
                .build();

        UserSearchLocation similarLocationSearch = new UserSearchLocation(location, Collections.<String>emptyList(),
                Collections.<String>emptyList(), "location stuff");

        UserSearch copy = new UserSearch.Builder(userSearch)
                .locationSearch(similarLocationSearch)
                .build();
        assertThat(userSearch, equalTo(copy));
        assertThat(userSearch.hashCode(), equalTo(copy.hashCode()));
    }

    @Test
    public void ensureThatEqualsOfRefinementIsOverwritten() {
        UserSearchRefinement refinement = new UserSearchRefinement.Builder()
                .attributes(Collections.<Attribute>emptySet())
                .sorting(Sorting.DEFAULT)
                .urgentAdsOnly(true)
                .adsWithImagesOnly(false)
                .searchInDescription(true)
                .build();
        UserSearchRefinement otherRefinement = new UserSearchRefinement.Builder()
                .attributes(Collections.<Attribute>emptyList())
                .sorting(Sorting.DEFAULT)
                .urgentAdsOnly(true)
                .adsWithImagesOnly(false)
                .searchInDescription(true)
                .build();
        assertThat(refinement, equalTo(otherRefinement));
        assertThat(refinement.hashCode(), equalTo(otherRefinement.hashCode()));
    }

    @Test
    public void getCategoryIdShouldReturnCategoryId() {
        given(category.getId()).willReturn(44L);

        UserSearch userSearch = new UserSearch.Builder().category(category).build();

        assertThat(userSearch.getCategoryId().get(), equalTo(44L));
    }

    @Test
    public void getCategoryIdShouldReturnEmptyResultWhenCategoryIsNull() {
        UserSearch userSearch = new UserSearch.Builder().build();

        assertThat(userSearch.getCategoryId(), isAbsentLong());
    }

    @Test
    public void getLocationIdShouldReturnLocationId() {
        given(location.getId()).willReturn(124);
        UserSearchLocation locationSearch = new UserSearchLocation(location);

        UserSearch userSearch = new UserSearch.Builder().locationSearch(locationSearch).build();

        assertThat(userSearch.getLocationId().get(), equalTo(124));
    }

    @Test
    public void getLocationIdShouldReturnEmptyResultWhenLocationIsNull() {
        UserSearch userSearch = new UserSearch.Builder().build();

        assertThat(userSearch.getLocationId(), isAbsent());
    }

    @Test
    public void getLocationIdShouldReturnEmptyResultWhenLocationSearchIsNull() {
        UserSearchLocation locationSearch = new UserSearchLocation(null);

        UserSearch userSearch = new UserSearch.Builder().locationSearch(locationSearch).build();

        assertThat(userSearch.getLocationId(), isAbsent());
    }

    @Test
    public void isRadialSearchShouldReturnFalseIfGeoLocationDataAreNotProvided() {
        // when
        UserSearch userSearch = new UserSearch.Builder().build();

        // then
        assertThat(userSearch.isPostcodeSearch(), is(false));
    }

    @Test
    public void isRadialSearchShouldReturnTrueIfGeoLocationDataAreProvided() {
        // given
        UserGeoLocation geoLocation = UserGeoLocation.builder().postcode("TW208DD")
                .latitude(BigDecimal.valueOf(1)).longitude(BigDecimal.valueOf(10)).location(location).build();

        // when
        UserSearch userSearch = new UserSearch.Builder().geoLocationSearch(Optional.of(geoLocation)).build();

        // then
        assertThat(userSearch.isPostcodeSearch(), is(true));
        assertThat(userSearch.getLocationSearch(), is(new UserSearchLocation(
                location,
                Collections.<String>emptyList(),
                Collections.<String>emptyList(),
                "TW208DD")));
    }

    private Matcher<Optional<Integer>> isAbsent() {
        return equalTo(Optional.<Integer>absent());
    }

    private Matcher<Optional<Long>> isAbsentLong() {
        return equalTo(Optional.<Long>absent());
    }
}
