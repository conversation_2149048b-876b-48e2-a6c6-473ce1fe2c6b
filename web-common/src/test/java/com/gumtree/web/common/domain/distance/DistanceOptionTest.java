package com.gumtree.web.common.domain.distance;

import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;

import static org.fest.assertions.api.Assertions.assertThat;

public class DistanceOptionTest {

    @Test
    public void generateOptionsSelectMatchingValue() throws Exception {
        List<DistanceOption> options = DistanceOption.generateOptions(10.1);

        assertThat(options).isEqualTo(Lists.newArrayList(
                new DistanceOption(0.0001, "Choose distance", false),
                new DistanceOption(1d, "+ 1 mile", false),
                new DistanceOption(3d, "+ 3 miles", false),
                new DistanceOption(5d, "+ 5 miles", false),
                new DistanceOption(10d, "+ 10 miles", true),
                new DistanceOption(15d, "+ 15 miles", false),
                new DistanceOption(30d, "+ 30 miles", false),
                new DistanceOption(50d, "+ 50 miles", false),
                new DistanceOption(75d, "+ 75 miles", false),
                new DistanceOption(100d, "+ 100 miles", false),
                new DistanceOption(1000d, "Nationwide", false)
        ));
    }

    @Test
    public void generateOptionsSelectFirstValueIfNoneMatchingFound() throws Exception {
        List<DistanceOption> options = DistanceOption.generateOptions(111.0);

        assertThat(options).isEqualTo(Lists.newArrayList(
                new DistanceOption(0.0001, "Choose distance", true),
                new DistanceOption(1d, "+ 1 mile", false),
                new DistanceOption(3d, "+ 3 miles", false),
                new DistanceOption(5d, "+ 5 miles", false),
                new DistanceOption(10d, "+ 10 miles", false),
                new DistanceOption(15d, "+ 15 miles", false),
                new DistanceOption(30d, "+ 30 miles", false),
                new DistanceOption(50d, "+ 50 miles", false),
                new DistanceOption(75d, "+ 75 miles", false),
                new DistanceOption(100d, "+ 100 miles", false),
                new DistanceOption(1000d, "Nationwide", false)
        ));
    }
}
