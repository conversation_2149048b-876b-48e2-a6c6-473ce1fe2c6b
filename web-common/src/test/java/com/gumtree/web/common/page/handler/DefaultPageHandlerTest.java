package com.gumtree.web.common.page.handler;

import com.gumtree.web.common.page.GumtreePage;
import org.junit.Test;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.Arrays;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.sameInstance;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class DefaultPageHandlerTest {

    private DefaultPageHandler pageHandler;

    @Test(expected = MissingGumtreePageAnnotationException.class)
    public void throwsExceptionWhenClassAnnotationsIsNullAndMethodAnnotationsIsNull() throws Exception {
        new DefaultPageHandler(null, null);
    }

    @Test(expected = MissingGumtreePageAnnotationException.class)
    public void throwsExceptionWhenMethodAnnotationsIsNullAndClassAnnotationsDoesNotContainGumtreePageAnnotation() throws Exception {
        new DefaultPageHandler(null, new ArrayList<Annotation>());
    }

    @Test(expected = MissingGumtreePageAnnotationException.class)
    public void throwsExceptionWhenClassAnnotationsIsNullAndMethodAnnotationsDoesNotContainGumtreePageAnnotation() throws Exception {
        new DefaultPageHandler(new ArrayList<Annotation>(), null);
    }

    @Test(expected = MissingGumtreePageAnnotationException.class)
    public void throwsExceptionWhenNoGumtreePageAnnotationInMethodOrClassAnnotations() throws Exception {
        new DefaultPageHandler(new ArrayList<Annotation>(), new ArrayList<Annotation>());
    }

    @Test
    public void whenClassAnnotationsContainsGumtreePageAnnotationItIsReturnedCorrectly() throws Exception {
        Annotation pageAnnotation = createAnnotation(GumtreePage.class);
        pageHandler = new DefaultPageHandler(null, Arrays.asList((Annotation) pageAnnotation));
        assertThat(pageHandler.getPageAnnotation(), sameInstance(pageAnnotation));
    }

    @Test
    public void whenMethodAnnotationsContainsGumtreePageAnnotationItIsReturnedCorrectly() throws Exception {
        Annotation pageAnnotation = createAnnotation(GumtreePage.class);
        pageHandler = new DefaultPageHandler(Arrays.asList((Annotation) pageAnnotation), null);
        assertThat(pageHandler.getPageAnnotation(), sameInstance(pageAnnotation));
    }

    @Test
    public void whenMethodAnnotationsAndClassAnnotationsContainsGumtreePageAnnotationTheMethodAnnotationTakesPriority() throws Exception {
        Annotation methodPageAnnotation = createAnnotation(GumtreePage.class);
        Annotation classPageAnnotation = createAnnotation(GumtreePage.class);
        pageHandler = new DefaultPageHandler(Arrays.asList((Annotation) methodPageAnnotation), Arrays.asList((Annotation) classPageAnnotation));
        assertThat(pageHandler.getPageAnnotation(), sameInstance(methodPageAnnotation));
    }

    @Test
    public void gumtreePageAnnotationIsNotReturnedInAdditionalAnnotationsWhenPresentInMethodAnnotations() throws Exception {
        Annotation methodPageAnnotation = createAnnotation(GumtreePage.class);
        pageHandler = new DefaultPageHandler(Arrays.asList((Annotation) methodPageAnnotation), null);
        assertThat(pageHandler.getAdditionalAnnotations().size(), equalTo(0));
    }

    @Test
    public void gumtreePageAnnotationIsNotReturnedInAdditionalAnnotationsWhenPresentInClassAnnotations() throws Exception {
        Annotation classPageAnnotation = createAnnotation(GumtreePage.class);
        pageHandler = new DefaultPageHandler(null, Arrays.asList((Annotation) classPageAnnotation));
        assertThat(pageHandler.getAdditionalAnnotations().size(), equalTo(0));
    }

    @Test
    public void gumtreePageAnnotationIsNotReturnedInAdditionalAnnotationsWhenPresentInMethodAndClassAnnotations() throws Exception {
        Annotation methodPageAnnotation = createAnnotation(GumtreePage.class);
        Annotation classPageAnnotation = createAnnotation(GumtreePage.class);
        pageHandler = new DefaultPageHandler(Arrays.asList((Annotation) methodPageAnnotation), Arrays.asList((Annotation) classPageAnnotation));
        assertThat(pageHandler.getAdditionalAnnotations().size(), equalTo(0));
    }

    @Test
    public void additionalAnnotationsFromMethodAreReturnedCorrectly() throws Exception {
        Annotation methodPageAnnotation = createAnnotation(GumtreePage.class);
        Annotation annotation1 = createAnnotation(Valid.class);
        Annotation annotation2 = createAnnotation(NotNull.class);
        pageHandler = new DefaultPageHandler(Arrays.asList(methodPageAnnotation, annotation1, annotation2), null);
        assertThat(pageHandler.getAdditionalAnnotations().size(), equalTo(2));
        assertThat(pageHandler.getAdditionalAnnotations().contains(annotation1), equalTo(true));
        assertThat(pageHandler.getAdditionalAnnotations().contains(annotation2), equalTo(true));
    }

    @Test
    public void additionalAnnotationsFromClassAreReturnedCorrectly() throws Exception {
        Annotation classPageAnnotation = createAnnotation(GumtreePage.class);
        Annotation annotation1 = createAnnotation(Valid.class);
        Annotation annotation2 = createAnnotation(NotNull.class);
        pageHandler = new DefaultPageHandler(null, Arrays.asList(classPageAnnotation, annotation1, annotation2));
        assertThat(pageHandler.getAdditionalAnnotations().size(), equalTo(2));
        assertThat(pageHandler.getAdditionalAnnotations().contains(annotation1), equalTo(true));
        assertThat(pageHandler.getAdditionalAnnotations().contains(annotation2), equalTo(true));
    }

    @Test
    public void additionalMethodAnnotationsOverrideAdditionalClassAnnotations() throws Exception {
        Annotation methodPageAnnotation = createAnnotation(GumtreePage.class);
        Annotation methodAnnotation1 = createAnnotation(Valid.class);
        Annotation methodAnnotation2 = createAnnotation(NotNull.class);
        Annotation methodAnnotation3 = createAnnotation(Min.class);
        Annotation classPageAnnotation = createAnnotation(GumtreePage.class);
        Annotation classAnnotation1 = createAnnotation(Valid.class);
        Annotation classAnnotation2 = createAnnotation(NotNull.class);
        Annotation classAnnotation3 = createAnnotation(Size.class);

        pageHandler = new DefaultPageHandler(
                Arrays.asList(methodPageAnnotation, methodAnnotation1, methodAnnotation2, methodAnnotation3),
                Arrays.asList(classPageAnnotation, classAnnotation1, classAnnotation2, classAnnotation3));

        assertThat(pageHandler.getPageAnnotation(), sameInstance(methodPageAnnotation));
        assertThat(pageHandler.getAdditionalAnnotations().size(), equalTo(4));
        assertThat(pageHandler.getAdditionalAnnotations().contains(methodAnnotation1), equalTo(true));
        assertThat(pageHandler.getAdditionalAnnotations().contains(methodAnnotation2), equalTo(true));
        assertThat(pageHandler.getAdditionalAnnotations().contains(methodAnnotation3), equalTo(true));
        assertThat(pageHandler.getAdditionalAnnotations().contains(classAnnotation3), equalTo(true));
    }

    private Annotation createAnnotation(Class<? extends Annotation> type) {
        Annotation annotation = mock(type);
        when(annotation.annotationType()).thenReturn((Class) type);
        return annotation;
    }
}
