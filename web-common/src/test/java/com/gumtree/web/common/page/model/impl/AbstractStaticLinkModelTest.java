package com.gumtree.web.common.page.model.impl;

import com.gumtree.util.model.Action;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.Link;
import com.gumtree.util.url.UrlScheme;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Matchers;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.*;

/**
 */
public class AbstractStaticLinkModelTest {

    private AbstractStaticLinkModel model;

    private UrlScheme urlScheme;

    @Before
    public void init() {
        urlScheme = mock(UrlScheme.class);
        model = new AbstractStaticLinkModel(urlScheme) {
        };
        when(urlScheme.urlFor(Matchers.<Action>any())).thenReturn("/test/url");
    }

    @Test
    public void testGetPostingRulesLink() throws Exception {
        Link link = model.getPostingRulesLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Posting rules"));
        verify(urlScheme).urlFor(Actions.POSTING_RULES_PAGE);
    }

    @Test
    public void testGetHelpLink() throws Exception {
        Link link = model.getHelpLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Help"));
        verify(urlScheme).urlFor(Actions.HELP_PAGE);
    }

    @Test
    public void testGetContactUsLink() throws Exception {
        Link link = model.getContactUsLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Help & Contact"));
        verify(urlScheme).urlFor(Actions.CONTACT_US);
    }

    @Test
    public void testGetStaySafeLink() throws Exception {
        Link link = model.getStaySafeLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Stay safe"));
        verify(urlScheme).urlFor(Actions.STAY_SAFE);
    }

    @Test
    public void testGetAboutGumtreeLink() throws Exception {
        Link link = model.getAboutGumtreeLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("About Gumtree"));
        verify(urlScheme).urlFor(Actions.ABOUT_GUMTREE);
    }

    @Test
    public void testGetBusinessAdvertisingLink() throws Exception {
        Link link = model.getBusinessAdvertisingLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Business advertising"));
        verify(urlScheme).urlFor(Actions.BUSINESS_ADVERTISING);
    }

    @Test
    public void testGetPrivacyPolicyLink() throws Exception {
        Link link = model.getPrivacyPolicyLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Privacy policy"));
        verify(urlScheme).urlFor(Actions.PRIVACY_POLICY);
    }

    @Test
    public void testGetInsuranceLink() throws Exception {
        Link link = model.getInsuranceLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Insurance"));
        verify(urlScheme).urlFor(Actions.INSURANCE);
    }

    @Test
    public void testGetTermsOfUseLink() throws Exception {
        Link link = model.getTermsOfUseLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Terms of use"));
        verify(urlScheme).urlFor(Actions.TERMS_OF_USE);
    }

    @Test
    public void testGetSiteMapLink() throws Exception {
        Link link = model.getSiteMapLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Site map"));
        verify(urlScheme).urlFor(Actions.SITE_MAP);
    }

    @Test
    public void testGetForumsLink() throws Exception {
        Link link = model.getForumsLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Forums"));
        verify(urlScheme).urlFor(Actions.FORUMS);
    }

    @Test
    public void testGetBlogLink() throws Exception {
        Link link = model.getBlogLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Blog"));
        verify(urlScheme).urlFor(Actions.BLOG);
    }

    @Test
    public void testGetFacebookLink() throws Exception {
        Link link = model.getFacebookLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo(""));
        verify(urlScheme).urlFor(Actions.FACEBOOK);
    }

    @Test
    public void testGetTwitterLink() throws Exception {
        Link link = model.getTwitterLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo(""));
        verify(urlScheme).urlFor(Actions.TWITTER);
    }

    @Test
    public void testGetSearchLink() throws Exception {
        Link link = model.getSearchLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("search"));
        verify(urlScheme).urlFor(Actions.SEARCH);
    }

    @Test
    public void testSavedAdsUrl() {
        Link link = model.getSavedAdsLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Favourites"));
        verify(urlScheme).urlFor(Actions.SAVED_ADS);
    }

    @Test
    public void testMobileRedirectUrl() {
        Link link = model.getMobileRedirectLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Back to mobile site"));
        verify(urlScheme).urlFor(Actions.MOBILE_SITE);
    }

    @Test
    public void testSavedSearchesUrl() {
        Link link = model.getSavedSearchesLink();
        assertThat(link.getUrl(), equalTo("/test/url"));
        assertThat(link.getText(), equalTo("Saved searches"));
        verify(urlScheme).urlFor(Actions.SAVED_SEARCHES);
    }

}
