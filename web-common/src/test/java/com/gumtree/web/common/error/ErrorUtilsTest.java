package com.gumtree.web.common.error;

import com.gumtree.common.util.error.SimpleError;
import org.junit.Test;
import org.mockito.Matchers;

import javax.validation.ConstraintViolation;
import javax.validation.Path;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class ErrorUtilsTest {

    @Test
    public void validateReturnsEmptyListWhenNoErrorsExist() {
        Validator validator = mock(Validator.class);
        when(validator.validate(Matchers.<Object>any())).thenReturn(new HashSet<ConstraintViolation<Object>>());
        List<SimpleError> errors = ErrorUtils.validate(new Object(), validator);
        assertThat(errors, equalTo((List<SimpleError>) new ArrayList<SimpleError>()));
    }

    @Test
    public void validateTranslatesObjectErrors() {

        Validator validator = mock(Validator.class);

        HashSet<ConstraintViolation<Object>> violationsSet = new LinkedHashSet<ConstraintViolation<Object>>(Arrays.asList(
                createViolation("error message 1", null, null),
                createViolation("error message 2", "", null)));

        when(validator.validate(Matchers.<Object>any())).thenReturn(violationsSet);

        List<SimpleError> errors = ErrorUtils.validate(new Object(), validator);

        assertThat(errors.size(), equalTo(2));

        boolean foundError1 = false;
        boolean foundError2 = false;

        for (SimpleError error : errors) {
            if (error.getMessageCode().equals("error message 1")) {
                foundError1 = true;
            } else if (error.getMessageCode().equals("error message 2")) {
                foundError2 = true;
            }
        }

        assertThat(foundError1, equalTo(true));
        assertThat(foundError2, equalTo(true));
    }

    @Test
    public void validateTranslatesPropertyErrors() {
        Validator validator = mock(Validator.class);

        HashSet<ConstraintViolation<Object>> violationsSet = new HashSet<ConstraintViolation<Object>>();
        violationsSet.add(createViolation("error message 1", "property.path.one", "invalid value 1"));
        violationsSet.add(createViolation("error message 2", "property.path.two", "invalid value 2"));
        violationsSet.add(createViolation("error message 3", "property.path.two", "invalid value 3"));

        when(validator.validate(Matchers.<Object>any())).thenReturn(violationsSet);

        List<SimpleError> errors = ErrorUtils.validate(new Object(), validator);

        assertThat(errors.size(), equalTo(3));

        boolean foundError1 = false;
        boolean foundError2 = false;
        boolean foundError3 = false;

        for (SimpleError error : errors) {
            if (error.getMessageCode().equals("error message 1")) {
                assertThat(error.getField(), equalTo("property.path.one"));
                foundError1 = true;
            } else if (error.getMessageCode().equals("error message 2")) {
                assertThat(error.getField(), equalTo("property.path.two"));
                foundError2 = true;
            } else if (error.getMessageCode().equals("error message 3")) {
                assertThat(error.getField(), equalTo("property.path.two"));
                foundError3 = true;
            }
        }

        assertThat(foundError1, equalTo(true));
        assertThat(foundError2, equalTo(true));
        assertThat(foundError3, equalTo(true));
    }

    private ConstraintViolation<Object> createViolation(String message, String path, String value) {
        ConstraintViolation<Object> violation = mock(ConstraintViolation.class);
        if (path == null) {
            when(violation.getPropertyPath()).thenReturn(null);
        } else {
            Path propertyPath = mock(Path.class);
            when(propertyPath.toString()).thenReturn(path);
            when(violation.getPropertyPath()).thenReturn(propertyPath);
        }
        if (value == null) {
            when(violation.getInvalidValue()).thenReturn(null);
        } else {
            when(violation.getInvalidValue()).thenReturn(value);
        }

        when(violation.getMessage()).thenReturn(message);
        return violation;
    }
}
