package com.gumtree.web.common.format;

import com.gumtree.api.Price;
import com.gumtree.common.format.PriceFormatterImpl;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeService;
import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.newattribute.DisplayAttribute;
import com.gumtree.domain.newattribute.internal.value.TextValue;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;

import static com.google.common.base.Optional.of;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.mock;

public class PriceFormatterTest {

    private PriceFormatterImpl priceFormatter;
    private AttributeService attributeService;

    @Before
    public void init() {
        priceFormatter = new PriceFormatterImpl();
        attributeService = mock(AttributeService.class);
        priceFormatter.setAttributeService(attributeService);
    }

    @Test
    public void testOneDecimalPointFormat() {
        Price price = new Price(20L);
        String displayPrice = priceFormatter.format(price, null);
        assertThat(displayPrice.endsWith("0.20") , equalTo(true));
    }

    @Test
    public void testOnePenceFormat() {
        Price price = new Price(1L);
        String displayPrice = priceFormatter.format(price, null);
        assertThat(displayPrice.endsWith("0.01") , equalTo(true));
    }

    @Test
    public void testCommaFormat() {
        Price price = new Price(10000010L);
        String displayPrice = priceFormatter.format(price, null);
        assertThat(displayPrice.endsWith("100,000.10") , equalTo(true));
    }

    @Test
    public void testMoreCommaFormat() {
        Price price = new Price(100000010L);
        String displayPrice = priceFormatter.format(price, null);
        assertThat(displayPrice.endsWith("1,000,000.10") , equalTo(true));
    }

    @Test
    public void testNoDecimalIfNoPenceFormat() {
        Price price = new Price(100000000L);
        String displayPrice = priceFormatter.format(price, null);
        assertThat(displayPrice.endsWith("1,000,000") , equalTo(true));
    }

    @Test
    public void formatingConvertingMonthlyToWeeklyShouldUseProvidedPriceWhenPeriodIsWeekly() {
        BigDecimal price = new BigDecimal(102);
        DisplayAttribute displayAttribute = mockDisplayAttribute("pw");
        given(attributeService.getDisplayAttribute((Attribute) anyObject(), anyLong())).willReturn(of(displayAttribute));

        String displayPrice = priceFormatter.formatConvertingMonthlyToWeekly(price, priceFrequency("pw"), 0L);

        assertThat(displayPrice.endsWith("102pw") , equalTo(true));
    }

    @Test
    public void formatingConvertingMonthlyToWeeklyShouldConvertProvidedPriceToWeeklyCeilWhenPeriodIsMonthly() {
        BigDecimal price = new BigDecimal(300);
        DisplayAttribute displayAttribute = mockDisplayAttribute("pm");
        given(attributeService.getDisplayAttribute((Attribute) anyObject(), anyLong())).willReturn(of(displayAttribute));

        String displayPrice = priceFormatter.formatConvertingMonthlyToWeekly(price, priceFrequency("pm"), 0L);

        assertThat(displayPrice.endsWith("70pw") , equalTo(true));
    }

    @Test
    public void formatingConvertingMonthlyToWeeklyShouldUseProvidedPriceWhenPeriodIsNotSpecified() {
        BigDecimal price = new BigDecimal(102);
        DisplayAttribute displayAttribute = mockDisplayAttribute("");
        given(attributeService.getDisplayAttribute((Attribute) anyObject(), anyLong())).willReturn(of(displayAttribute));

        String displayPrice = priceFormatter.formatConvertingMonthlyToWeekly(price, null, 0L);

        assertThat(displayPrice.endsWith("102") , equalTo(true));
    }

    private DisplayAttribute mockDisplayAttribute(String frequency) {
        DisplayAttribute displayAttribute = mock(DisplayAttribute.class);
        given(displayAttribute.getDisplayValue()).willReturn(frequency);
        return displayAttribute;
    }

    private Attribute priceFrequency(final String frequency) {
        return new Attribute() {
            private final String frequencyValue = frequency;

            @Override
            public String getType() {
                return "price_frequency";
            }

            @Override
            public AttributeValue getValue() {
                TextValue value = mock(TextValue.class);
                given(value.getName()).willReturn(frequencyValue);
                return value;
            }
        };
    }
}
