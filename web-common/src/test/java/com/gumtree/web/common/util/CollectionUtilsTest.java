package com.gumtree.web.common.util;

import org.hamcrest.Matchers;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertThat;

public class CollectionUtilsTest {

    @Test
    public void shouldTakeFirstElementsFromList() throws Exception {
        // given
        final List<Integer> list = Arrays.asList(1, 2, 3, 4);

        // when
        final List<Integer> sublist = CollectionUtils.take(list, 2);

        // then
        assertThat(sublist, Matchers.contains(1, 2));
    }

    @Test
    public void shouldTakeWholeListWhenSizeIsSmallerThanNumberOfElemens() throws Exception {
        // given
        final List<Integer> list = Arrays.asList(1, 2, 3, 4);
        int numberOfElements = list.size() + 1;

        // when
        final List<Integer> sublist = CollectionUtils.take(list, numberOfElements);

        // then
        assertThat(sublist, equalTo(list));
    }

    @Test
    public void shouldReturnEmptyList() throws Exception {
        // given
        final List<Integer> list = Collections.emptyList();

        // when
        final List<Integer> sublist = CollectionUtils.take(list, 1);

        // then
        assertThat(sublist, equalTo(list));
    }

    @Test
    public void shouldReturnNull() throws Exception {
        // given
        final List<Integer> list = null;

        // when
        final List<Integer> sublist = CollectionUtils.take(list, 1);

        // then
        assertThat(sublist, nullValue());
    }
}