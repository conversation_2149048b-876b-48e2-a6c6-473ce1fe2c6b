package com.gumtree.web.common;

import org.junit.Test;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class NameValuePairsTest {

    @Test
    public void shouldCreateNameValuePairsWithBapiReplyError() {
        List<NameValuePair> errors = new NameValuePairs().withBapiReplyError();
        assertThat(errors.get(0), equalTo(new NameValuePair("global", "reply.global.bapierror")));
    }

    @Test
    public void shouldCreateNameValuePairsWithInvalidAdvertError() {
        List<NameValuePair> errors = new NameValuePairs().withInvalidAdvertReplyError();
        assertThat(errors.get(0), equalTo(new NameValuePair("global", "reply.advertid.invalid")));
    }

    @Test
    public void shouldCreateNameValuePairsWithAlreadyAppliedReplyError() {
        List<NameValuePair> errors = new NameValuePairs().withAlreadyAppliedReplyError();
        assertThat(errors.get(0), equalTo(new NameValuePair("global", "reply.global.already_applied")));
    }

    @Test
    public void shouldCreateNameValuePairsWithInvalidJobIdentityError() {
        List<NameValuePair> errors = new NameValuePairs().withInvalidJobIdentityError();
        assertThat(errors.get(0), equalTo(new NameValuePair("global", "reply.global.invalid_job_id")));
    }

    @Test
    public void shouldCreateNameValuePairsWithInvalidEmailError() {
        List<NameValuePair> errors = new NameValuePairs().withInvalidEmailError();
        assertThat(errors.get(0), equalTo(new NameValuePair("global", "reply.global.invalid_email")));
    }

    @Test
    public void shouldCreateNameValuePairsWithBadCvError() {
        List<NameValuePair> errors = new NameValuePairs().withBadCvError();
        assertThat(errors.get(0), equalTo(new NameValuePair("global", "reply.global.cv_not_ok")));
    }

    @Test
    public void shouldCreateNameValuePairsWithCustomErrorMessage() {
        List<NameValuePair> errors = new NameValuePairs().add("Some error code");
        assertThat(errors.get(0), equalTo(new NameValuePair("message", "Some error code")));
    }

    @Test
    public void shouldCreateNameValuePairsWithCustomError() {
        List<NameValuePair> errors = new NameValuePairs().add("name", "value");
        assertThat(errors.get(0), equalTo(new NameValuePair("name", "value")));
    }
}