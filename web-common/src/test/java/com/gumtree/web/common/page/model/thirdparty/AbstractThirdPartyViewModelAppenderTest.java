package com.gumtree.web.common.page.model.thirdparty;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import javax.validation.Valid;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

/**
 */
public class AbstractThirdPartyViewModelAppenderTest {

    @Test
    public void registersItselfOnInitialisation() {
        ThirdPartyViewModelAppenderRegistry factory = mock(ThirdPartyViewModelAppenderRegistry.class);
        AbstractThirdPartyViewModelAppender<Valid> appender = new AbstractThirdPartyViewModelAppender<Valid>(Valid.class) {
            @Override
            public void append(Map<String, Object> model, Valid annotation, ThirdPartyRequestContext requestContext) {

            }
        };
        ReflectionTestUtils.setField(appender, "factory", factory);
        appender.init();
        verify(factory).registerAppender(appender);
        assertThat((Class<Valid>) appender.getSupportedAnnotationType(), equalTo(Valid.class));
    }
}
