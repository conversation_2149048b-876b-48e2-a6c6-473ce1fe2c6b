package com.gumtree.web.common.domain.converter;

import com.google.common.base.Optional;
import org.junit.Test;

import java.math.BigDecimal;

import static org.fest.assertions.api.Assertions.assertThat;

public class ConversionsTest {

    @Test
    public void convertPoundsToPence() {
        assertThat(Conversions.pounds2Pence("99.50")).isEqualTo(Optional.of(new BigDecimal(9950D).setScale(2)));
        assertThat(Conversions.pounds2Pence("99.50").transform(BigDecimal::longValue)).isEqualTo(Optional.of(9950L));
        assertThat(Conversions.pounds2Pence("0.50")).isEqualTo(Optional.of(new BigDecimal(50D).setScale(2)));
        assertThat(Conversions.pounds2Pence("55")).isEqualTo(Optional.of(new BigDecimal(5500D).setScale(2)));
        assertThat(Conversions.pounds2Pence("55.532222")).isEqualTo(Optional.of(new BigDecimal(5553D).setScale(2)));
        assertThat(Conversions.pounds2Pence("55.532222").transform(BigDecimal::longValue)).isEqualTo(Optional.of(5553L));
        assertThat(Conversions.pounds2Pence("aswewdsd")).isEqualTo(Optional.absent());
    }

    @Test
    public void convertAge2Year() {
        assertThat(Conversions.age2Year(new BigDecimal("0"), 2011)).isEqualTo(2011L);
        assertThat(Conversions.age2Year(new BigDecimal("1"), 2011)).isEqualTo(2011L);
        assertThat(Conversions.age2Year(new BigDecimal("2"), 2011)).isEqualTo(2010L);
    }

    @Test
    public void fromPence2PoundsWithZerosCollapsed() {
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("0")).isEqualTo(java.util.Optional.of("£0"));
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("100")).isEqualTo(java.util.Optional.of("£1"));
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("199")).isEqualTo(java.util.Optional.of("£1.99"));
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("10000")).isEqualTo(java.util.Optional.of("£100"));
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("100000")).isEqualTo(java.util.Optional.of("£1k"));
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("100090")).isEqualTo(java.util.Optional.of("£1,000.90"));
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("1000000")).isEqualTo(java.util.Optional.of("£10k"));
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("10000000")).isEqualTo(java.util.Optional.of("£100k"));
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("100000000")).isEqualTo(java.util.Optional.of("£1,000k"));
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("abc")).isEqualTo(java.util.Optional.empty());
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("  ")).isEqualTo(java.util.Optional.empty());
        assertThat(Conversions.fromPence2PoundsWithZerosCollapsed("")).isEqualTo(java.util.Optional.empty());
    }

    @Test
    public void fromPence2PoundsWithNoCollapsedZeros() {
        assertThat(Conversions.fromPence2PoundsWithNoCollapsedZeros(0)).isEqualTo("£0");
        assertThat(Conversions.fromPence2PoundsWithNoCollapsedZeros(100)).isEqualTo("£1");
        assertThat(Conversions.fromPence2PoundsWithNoCollapsedZeros(199)).isEqualTo("£1.99");
        assertThat(Conversions.fromPence2PoundsWithNoCollapsedZeros(10000)).isEqualTo("£100");
        assertThat(Conversions.fromPence2PoundsWithNoCollapsedZeros(100000)).isEqualTo("£1,000");
        assertThat(Conversions.fromPence2PoundsWithNoCollapsedZeros(100090)).isEqualTo("£1,000.90");
        assertThat(Conversions.fromPence2PoundsWithNoCollapsedZeros(1000000)).isEqualTo("£10,000");
        assertThat(Conversions.fromPence2PoundsWithNoCollapsedZeros(10000000)).isEqualTo("£100,000");
        assertThat(Conversions.fromPence2PoundsWithNoCollapsedZeros(100000000)).isEqualTo("£1,000,000");
    }
}