package com.gumtree.web.common.request.context.thirdparty;

import com.gumtree.api.category.domain.Category;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.common.thirdparty.DefaultThirdPartyRequestContextFactory;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContextAdapter;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.sameInstance;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class DefaultThirdPartyRequestContextFactoryTest {

    @Test
    public void createsThirdPartyRequestContextAdapter() {
        GumtreePageContext pageContext = mock(GumtreePageContext.class);
        when(pageContext.getCategory()).thenReturn(mock(Category.class));
        DefaultThirdPartyRequestContextFactory contextFactory = new DefaultThirdPartyRequestContextFactory();
        ThirdPartyRequestContextAdapter adapter =
                (ThirdPartyRequestContextAdapter) contextFactory.create(pageContext);
        assertThat(adapter.getPageContext(), sameInstance(pageContext));
    }
}
