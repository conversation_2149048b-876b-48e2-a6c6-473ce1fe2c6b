package com.gumtree.web.common.format;

import org.joda.time.DateTime;
import org.junit.Test;

import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.nullValue;

public class PostingTimeFormatterTest {

    /**
     * Originally posted x minutes ago (if posted less than one hour ago)
     *
     * @throws IOException never thrown
     */
    @Test
    public final void deliversPostingTimeAsMinutesAgo() throws IOException {
        Date postingDate = new Date(1308750576577L);
        Date now = new Date(postingDate.getTime() + (15 * 60 * 1000L));

        String value = PostingTimeFormatter.formatPostingTime(postingDate, now);
        assertThat(value, equalTo("15 mins ago"));
    }

    /**
     * Originally posted x hours ago (if posted more than 1 hour ago but at some point today)
     *
     * @throws IOException never thrown
     */
    @Test
    public final void deliversPostingTimeAsHoursAgo() throws IOException {
        Date postingDate = new Date(1308750576577L);
        Date now = new Date(postingDate.getTime() + (4 * 60 * 60 * 1000));
        String value = PostingTimeFormatter.formatPostingTime(postingDate, now);
        assertThat(value, equalTo("4 hours ago"));
    }

    /**
     * Originally posted yesterday (if posted yesterday)
     *
     * @throws ParseException never thrown
     * @throws IOException    never thrown
     */
    @Test
    public final void deliversYesterDayAsPostingTime() throws ParseException, IOException {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date postingDate = dateFormat.parse("2011-06-22 23:45:00");
        Date now = new Date(postingDate.getTime() + 20 * 60 * 1000);
        String value = PostingTimeFormatter.formatPostingTime(postingDate, now);
        assertThat(value, equalTo("yesterday"));
    }

    /**
     * Originally posted x days ago (if posted prior to yesterday but later than the current date of last month
     * e.g. if today is the 15th March, the cut-off date is 16th Feb inclusive)
     *
     * @throws IOException never thrown
     */
    @Test
    public final void deliversNumberOfDaysAgo() throws IOException {
        Date postingDate = new Date(1308750576577L);
        Date now = new Date(postingDate.getTime() + (3L * 24 * 60 * 60 * 1000));
        String value = PostingTimeFormatter.formatPostingTime(postingDate, now);
        assertThat(value, equalTo("3 days ago"));
    }

    /**
     * Originally posted 1 month ago (if posted on todays date but in the previous month but later
     * than the current date of the month which was 2 months ago)
     *
     * @throws IOException never thrown
     */
    @Test
    public final void deliversOneMonthAgo() throws IOException {
        Date postingDate = new Date(1308750576577L);
        Date now = new Date(postingDate.getTime() + (35L * 24 * 60 * 60 * 1000));
        String value = PostingTimeFormatter.formatPostingTime(postingDate, now);
        assertThat(value, equalTo("1 month ago"));
    }

    /**
     * Originally posted x months ago (if posted on todays date but x previous months ago but later than the
     * current date of the month which was x+1 months ago)
     *
     * @throws IOException never thrown
     */
    @Test
    public final void deliversNumberOfMonthsAgo() throws IOException {
        Date postingDate = new Date(1308750576577L);
        Date now = new Date(postingDate.getTime() + (70L * 24L * 60L * 60L * 1000L));
        String value = PostingTimeFormatter.formatPostingTime(postingDate, now);
        assertThat(value, equalTo("2 months ago"));
    }

    /**
     * Originally posted x years ago (if posted on todays date but x years ago but later than the current
     * date of the year which was x+1 years ago
     *
     * @throws IOException never thrown
     */
    @Test
    public final void deliversNumberOfYearsAgo() throws IOException {
        Calendar now = Calendar.getInstance();
        now.setTime(new Date(1308750576577L));

        Calendar postingDate = (Calendar) now.clone();
        postingDate.add(Calendar.YEAR, -3);
        postingDate.add(Calendar.DAY_OF_YEAR, -25);

        String value = PostingTimeFormatter.formatPostingTime(postingDate.getTime(), now.getTime());
        assertThat(value, equalTo("3 years ago"));
    }

    /**
     * Originally posted 1 year ago (if posted on todays date but 1 year ago but later than
     * the current date of the year which was 2 years ago
     *
     * @throws IOException never thrown
     */
    @Test
    public final void deliversOneYearAgo() throws IOException {
        Calendar now = Calendar.getInstance();
        now.setTime(new Date(1308750576577L));

        Calendar postingDate = (Calendar) now.clone();
        postingDate.add(Calendar.MONTH, -13);

        String value = PostingTimeFormatter.formatPostingTime(postingDate.getTime(), now.getTime());
        assertThat(value, equalTo("1 year ago"));
    }
    
    @Test
    public void expiryTimeReturnsAlreadyPurchasedWhenGivenNull() {
        String expiry = PostingTimeFormatter.formatFeatureExpiryTime(null, new DateTime());
        assertThat(expiry, equalTo("Already purchased"));
    }
    
    @Test
    public void expiryTimeReturnsNullWhenFeatureExpired() {
        DateTime now = new DateTime(2012, 1, 1, 12, 0, 0, 0);
        String expiry = PostingTimeFormatter.formatFeatureExpiryTime(now.minusDays(1), now);
        assertThat(expiry, nullValue());
    }

    @Test
    public void expiryTimeReturnsTodayWhenFeatureExpiresToday() {
        DateTime now = new DateTime(2012, 1, 1, 12, 0, 0, 0);
        String expiry = PostingTimeFormatter.formatFeatureExpiryTime(now.plusHours(5), now);
        assertThat(expiry, equalTo("Ends today"));
    }
    
    @Test
    public void expiryTimeReturns1DayWhenFeatureExpiresTomorrow() {
        DateTime now = new DateTime(2012, 1, 1, 12, 0, 0, 0);
        String expiry = PostingTimeFormatter.formatFeatureExpiryTime(now.plusHours(25), now);
        assertThat(expiry, equalTo("1 day left"));
    }
    
    @Test
    public void expiryTimeReturns7DaysWhenFeatureExpiresIn7days() {
        DateTime now = new DateTime(2012, 1, 1, 12, 0, 0, 0);
        String expiry = PostingTimeFormatter.formatFeatureExpiryTime(now.plusDays(7), now);
        assertThat(expiry, equalTo("7 days left"));
    }
}
