package com.gumtree.web.common.domain.order.converter;

import com.google.common.base.Optional;
import com.gumtree.api.Ad;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.domain.order.OrderItem;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultApiOrderToOrderEntityConverterTest {
    private static final Long ADVERT_ID = 1L;
    private static final Long CATEGORY_ID = 1L;
    private static final Category category = Category.newCategory().withId(CATEGORY_ID).build();

    @InjectMocks private DefaultApiOrderToOrderEntityConverter converter;
    @Mock private BushfireApi bushfireApi;
    @Mock private AdvertApi advertApi;
    @Mock private CategoryModel categoryModel;

    private Ad advert = new Ad();

    @Before
    public void setup() {
        advert.setCategoryId(CATEGORY_ID);

        when(bushfireApi.create(AdvertApi.class)).thenReturn(advertApi);
        when(advertApi.getAdvert(ADVERT_ID)).thenReturn(advert);
        when(categoryModel.getCategory(CATEGORY_ID)).thenReturn(Optional.of(category));
    }

    @Test
    public void testConvertsPricesCorrectly() {
        ApiOrder apiOrder = new ApiOrder();
        apiOrder.setTotalVat(1234L);
        apiOrder.setTotalIncVat(5678L);

        apiOrder.setItems(Collections.<ApiOrderItem>emptyList());

        Order order = converter.convert(apiOrder);
        assertThat(order.getTotalVat().toString(), equalTo("12.34"));
        assertThat(order.getTotalIncVat().toString(), equalTo("56.78"));
    }

    @Test
    public void testConvertsOrderItemsCorrectly() {
        ApiOrder apiOrder = new ApiOrder();
        ApiOrderItem item1 = new ApiOrderItem();
        ApiOrderItem item2 = new ApiOrderItem();
        ApiOrderItem item3 = new ApiOrderItem();
        item1.setPriceIncVat(123L);
        item1.setAdvertId(ADVERT_ID);

        item2.setPriceIncVat(234L);
        item2.setAdvertId(ADVERT_ID);

        item3.setPriceIncVat(345L);
        item3.setAdvertId(ADVERT_ID);

        apiOrder.setItems(Arrays.asList(item1, item2, item3));
        apiOrder.setTotalIncVat(702L);
        apiOrder.setTotalVat(117L);

        Order order = converter.convert(apiOrder);
        assertThat(order.getItems().size(), equalTo(3));
        OrderItem orderItem1 = order.getItems().get(0);
        assertThat(orderItem1.getPriceIncVat().toString(), equalTo("1.23"));
        assertThat(orderItem1.getAdvert(), equalTo(advert));
        assertThat(orderItem1.getCategory(), equalTo(category));
        OrderItem orderItem2 = order.getItems().get(1);
        assertThat(orderItem2.getPriceIncVat().toString(), equalTo("2.34"));
        OrderItem orderItem3 = order.getItems().get(2);
        assertThat(orderItem3.getPriceIncVat().toString(), equalTo("3.45"));

    }
}
