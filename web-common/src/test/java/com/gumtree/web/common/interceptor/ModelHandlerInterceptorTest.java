package com.gumtree.web.common.interceptor;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class ModelHandlerInterceptorTest {

    private static final String HEADER_NAME =  "x-gt-get-model";
    private static final String HEADER_SECRET = "gumtree";

    @Mock
    private HttpServletRequest request;

    @Mock
    private ModelAndView modelAndView;

    @Mock
    private HttpServletResponse response;

    private ModelHandlerInterceptor modelHandlerInterceptor  = new ModelHandlerInterceptor(HEADER_SECRET);

    @Test
    public void shouldNotRenderModelJsonViewIfHeaderIsMissing () {
        // given missing header

        // when
        modelHandlerInterceptor.postHandle(request, response, new Object(), modelAndView);

        // then
        verify(modelAndView, never()).setView(any(MappingJackson2JsonView.class));
    }

    @Test
    public void shouldNotRenderModelJsonViewIfWrongHeaderValue () {
        // given missing header
        when(request.getHeader(HEADER_NAME)).thenReturn("xyz");

        // when
        modelHandlerInterceptor.postHandle(request, response, new Object(), modelAndView);

        // then
        verify(modelAndView, never()).setView(any(MappingJackson2JsonView.class));
    }

    @Test
    public void shouldRenderModelJsonView () {
        // given missing header
        when(request.getHeader(HEADER_NAME)).thenReturn(HEADER_SECRET);

        // when
        modelHandlerInterceptor.postHandle(request, response, new Object(), modelAndView);

        // then
        verify(modelAndView).setView(any(MappingJackson2JsonView.class));
    }

}