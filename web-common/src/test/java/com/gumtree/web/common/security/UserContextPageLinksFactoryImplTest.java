package com.gumtree.web.common.security;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.Link;
import com.gumtree.util.url.UrlScheme;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static com.gumtree.web.common.security.UserContextPageLinksFactoryImpl.EDIT_MY_ACCOUNT;
import static com.gumtree.web.common.security.UserContextPageLinksFactoryImpl.LOGIN;
import static com.gumtree.web.common.security.UserContextPageLinksFactoryImpl.LOG_OUT;
import static com.gumtree.web.common.security.UserContextPageLinksFactoryImpl.MANAGE_MY_ADS;
import static com.gumtree.web.common.security.UserContextPageLinksFactoryImpl.POST_AD;
import static com.gumtree.web.common.security.UserContextPageLinksFactoryImpl.POST_EVENT;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class UserContextPageLinksFactoryImplTest {

    private UrlScheme urlScheme;

    private UserContextPageLinksFactoryImpl factory;

    private Location homepageLocation;

    private Category category;

    @Before
    public void init() {
        urlScheme = mock(UrlScheme.class);
        factory = new UserContextPageLinksFactoryImpl();
        ReflectionTestUtils.setField(factory, "urlScheme", urlScheme);
        homepageLocation = mock(Location.class);
        category = mock(Category.class);
    }

    @Test
    public void correctLinksAreGeneratedWhenBushfireSoftLoggedIn() {
        when(urlScheme.bushfirePostAdUrlFor(category)).thenReturn("bushfire.postad.url");
        when(urlScheme.bushfirePostEventUrl()).thenReturn("bushfire.postEvent.url");
        mockLinkUrls();
        UserContextPageLinks links = factory.createPageLinks(homepageLocation, category);
        assertLink(links.getPostAdLink(), POST_AD, "bushfire.postad.url");
        assertLink(links.getPostEventLink(), POST_EVENT, "bushfire.postEvent.url");
        assertLink(links.getManageAdsLink(), MANAGE_MY_ADS, "bushfire.manageads.url");
        assertLink(links.getEditAccountLink(), EDIT_MY_ACCOUNT, "bushfire.editaccount.url");
        assertLink(links.getLogoutLink(), LOG_OUT, "bushfire.logout.url");
        assertLink(links.getLoginLink(), LOGIN, "bushfire.login.url");
    }

    @Test
    public void createAccountLinkIsCorrectForLoggedInUser() {
        when(urlScheme.urlFor(Actions.CREATE_ACCOUNT)).thenReturn("logged.in.bushfire.create.account.url");
        mockLinkUrls();
        UserContextPageLinks links = factory.createPageLinks(homepageLocation, category);
        assertThat(links.getCreateAccountLink().getUrl(), equalTo("logged.in.bushfire.create.account.url"));
    }

    @Test
    public void createAccountLinkIsCorrectForNonLoggedInUser() {
        when(urlScheme.urlFor(Actions.CREATE_ACCOUNT)).thenReturn("logged.out.create.account.url");
        mockLinkUrls();
        UserContextPageLinks links = factory.createPageLinks(homepageLocation, category);
        assertThat(links.getCreateAccountLink().getUrl(), equalTo("logged.out.create.account.url"));
    }

    private void mockLinkUrls() {
        when(urlScheme.urlFor(Actions.LOGOUT)).thenReturn("legacy.logout.url");
        when(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)).thenReturn("bushfire.manageads.url");
        when(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ACCOUNT)).thenReturn("bushfire.editaccount.url");
        when(urlScheme.urlFor(Actions.BUSHFIRE_LOGOUT)).thenReturn("bushfire.logout.url");
        when(urlScheme.urlFor(Actions.BUSHFIRE_LOGIN)).thenReturn("bushfire.login.url");
    }

    private void assertLink(Link postAdLink, String text, String url) {
        assertThat(postAdLink.getText(), equalTo(text));
        assertThat(postAdLink.getUrl(), equalTo(url));
    }
}
