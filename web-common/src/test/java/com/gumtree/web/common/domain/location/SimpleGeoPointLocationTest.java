package com.gumtree.web.common.domain.location;

import org.junit.Test;

import java.math.BigDecimal;

import static org.fest.assertions.api.Assertions.assertThat;

public class SimpleGeoPointLocationTest {

    @Test
    public void showConstructObjectGivenValidLatLong() {

        BigDecimal latitude = BigDecimal.TEN;
        BigDecimal longitude = BigDecimal.ZERO;
        SimpleGeoPointLocation simpleGeoPointLocation = new SimpleGeoPointLocation(latitude, longitude);

        assertThat(simpleGeoPointLocation.getLatitude().isPresent()).isTrue();
        assertThat(simpleGeoPointLocation.getLongitude().isPresent()).isTrue();

    }

    @Test
    public void showConstructObjectGivenNullLatLong() {

        BigDecimal latitude = null;
        BigDecimal longitude = null;
        SimpleGeoPointLocation simpleGeoPointLocation = new SimpleGeoPointLocation(latitude, longitude);

        assertThat(simpleGeoPointLocation.getLatitude().isPresent()).isFalse();
        assertThat(simpleGeoPointLocation.getLongitude().isPresent()).isFalse();

    }

}
