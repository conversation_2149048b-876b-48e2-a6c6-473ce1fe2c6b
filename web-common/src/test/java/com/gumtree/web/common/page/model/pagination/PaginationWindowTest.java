package com.gumtree.web.common.page.model.pagination;


import static org.junit.Assert.assertEquals;

import com.gumtree.web.common.page.model.pagination.PaginationWindow;
import org.junit.Test;

public class PaginationWindowTest {
	
    @Test
    public void getPositionOfPaginationPanelForHighNumberOfResultPages(){
    	PaginationWindow window = new PaginationWindow(0, 9, 15);
    	assertEquals(1, window.getStart());
    	assertEquals(9, window.getEnd());
    	
    	window = new PaginationWindow(1, 9, 15);
    	assertEquals(1, window.getStart());
    	assertEquals(9, window.getEnd());
    	
    	window = new PaginationWindow(2, 9, 15);
    	assertEquals(1, window.getStart());
    	assertEquals(9, window.getEnd());
    	
    	window = new PaginationWindow(3, 9, 15);
    	assertEquals(1, window.getStart());
    	assertEquals(9, window.getEnd());
    	
    	window = new PaginationWindow(4, 9, 15);
    	assertEquals(1, window.getStart());
    	assertEquals(9, window.getEnd());
    	
    	window = new PaginationWindow(5, 9, 15);
    	assertEquals(1, window.getStart());
    	assertEquals(9, window.getEnd());
    	
    	window = new PaginationWindow(6, 9, 15);
    	assertEquals(2, window.getStart());
    	assertEquals(10, window.getEnd());
    	
    	window = new PaginationWindow(7, 9, 15);
    	assertEquals(3, window.getStart());
    	assertEquals(11, window.getEnd());
    	
    	window = new PaginationWindow(8, 9, 15);
    	assertEquals(4, window.getStart());
    	assertEquals(12, window.getEnd());
    	
    	window = new PaginationWindow(9, 9, 15);
    	assertEquals(5, window.getStart());
    	assertEquals(13, window.getEnd());
    	
    	window = new PaginationWindow(10, 9, 15);
    	assertEquals(6, window.getStart());
    	assertEquals(14, window.getEnd());
    	
    	window = new PaginationWindow(11, 9, 15);
    	assertEquals(7, window.getStart());
    	assertEquals(15, window.getEnd());
    	
    	window = new PaginationWindow(12, 9, 15);
    	assertEquals(7, window.getStart());
    	assertEquals(15, window.getEnd());
    	
    	window = new PaginationWindow(13, 9, 15);
    	assertEquals(7, window.getStart());
    	assertEquals(15, window.getEnd());
    	
    	window = new PaginationWindow(14, 9, 15);
    	assertEquals(7, window.getStart());
    	assertEquals(15, window.getEnd());
    	
    	window = new PaginationWindow(15, 9, 15);
    	assertEquals(7, window.getStart());
    	assertEquals(15, window.getEnd());
    	
    	window = new PaginationWindow(16, 9, 15);
    	assertEquals(7, window.getStart());
    	assertEquals(15, window.getEnd());
    }
    
    @Test
    public void getPositionOfPaginationPanelForSmallNumberOfResultPages(){
        PaginationWindow window = new PaginationWindow(0, 7, 7);
        assertEquals(1, window.getStart());
        assertEquals(7, window.getEnd());
       
        window = new PaginationWindow(1, 7, 7);
        assertEquals(1, window.getStart());
        assertEquals(7, window.getEnd());
       
        window = new PaginationWindow(2, 7, 7);
        assertEquals(1, window.getStart());
        assertEquals(7, window.getEnd());
       
        window = new PaginationWindow(3, 7, 7);
        assertEquals(1, window.getStart());
        assertEquals(7, window.getEnd());
       
        window = new PaginationWindow(4, 7, 7);
        assertEquals(1, window.getStart());
        assertEquals(7, window.getEnd());
       
        window = new PaginationWindow(5, 7, 7);
        assertEquals(1, window.getStart());
        assertEquals(7, window.getEnd());
       
        window = new PaginationWindow(6, 7, 7);
        assertEquals(1, window.getStart());
        assertEquals(7, window.getEnd());
       
        window = new PaginationWindow(7, 7, 7);
        assertEquals(1, window.getStart());
        assertEquals(7, window.getEnd());
       
        window = new PaginationWindow(8, 7, 7);
        assertEquals(1, window.getStart());
        assertEquals(7, window.getEnd());
       
    }
   
    @Test
    public void getPositionOfPaginationPanelFor1ResultPage(){
        PaginationWindow window = new PaginationWindow(0, 1, 1);
        assertEquals(1, window.getStart());
        assertEquals(1, window.getEnd());
       
        window = new PaginationWindow(1, 1, 1);
        assertEquals(1, window.getStart());
        assertEquals(1, window.getEnd());
       
        window = new PaginationWindow(2, 1, 1);
        assertEquals(1, window.getStart());
        assertEquals(1, window.getEnd());
       
    }
   
    @Test
    public void getPositionOfPaginationPanelFor2ResultPages(){
        PaginationWindow window = new PaginationWindow(0, 2, 2);
        assertEquals(1, window.getStart());
        assertEquals(2, window.getEnd());
       
        window = new PaginationWindow(1, 2, 2);
        assertEquals(1, window.getStart());
        assertEquals(2, window.getEnd());
       
        window = new PaginationWindow(2, 2, 2);
        assertEquals(1, window.getStart());
        assertEquals(2, window.getEnd());
       
        window = new PaginationWindow(3, 2, 2);
        assertEquals(1, window.getStart());
        assertEquals(2, window.getEnd());
       
    }
   
    @Test
    public void getPositionOfPaginationPanelForEvenNumberSizedPanel(){
        PaginationWindow window = new PaginationWindow(0, 8, 9);
        assertEquals(1, window.getStart());
        assertEquals(8, window.getEnd());
       
        window = new PaginationWindow(1, 8, 9);
        assertEquals(1, window.getStart());
        assertEquals(8, window.getEnd());
       
        window = new PaginationWindow(2, 8, 9);
        assertEquals(1, window.getStart());
        assertEquals(8, window.getEnd());
       
        window = new PaginationWindow(3, 8, 9);
        assertEquals(1, window.getStart());
        assertEquals(8, window.getEnd());
        
        window = new PaginationWindow(4, 8, 9);
        assertEquals(1, window.getStart());
        assertEquals(8, window.getEnd());
        
        window = new PaginationWindow(5, 8, 9);
        assertEquals(1, window.getStart());
        assertEquals(8, window.getEnd());
       
        window = new PaginationWindow(6, 8, 9);
        assertEquals(2, window.getStart());
        assertEquals(9, window.getEnd());
       
        window = new PaginationWindow(8, 8, 9);
        assertEquals(2, window.getStart());
        assertEquals(9, window.getEnd());
       
        window = new PaginationWindow(9, 8, 9);
        assertEquals(2, window.getStart());
        assertEquals(9, window.getEnd());
       
    }
    
    

}
