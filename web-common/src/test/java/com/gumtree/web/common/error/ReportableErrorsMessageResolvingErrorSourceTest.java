package com.gumtree.web.common.error;

import com.gumtree.common.util.error.ReportableErrors;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class ReportableErrorsMessageResolvingErrorSourceTest {

    private ReportableErrors errors;

    private ErrorMessageResolver messageResolver;

    private ReportableErrorsMessageResolvingErrorSource errorSource;

    @Before
    public void init() {
        errors = mock(ReportableErrors.class);
        messageResolver = mock(ErrorMessageResolver.class);
        errorSource = new ReportableErrorsMessageResolvingErrorSource(errors, messageResolver);
        verify(errors).report(errorSource);
    }

    @Test
    public void fieldErrorWithNoDefaultMessageOrArgsAddedCorrectly() {
        when(messageResolver.getMessage("some.message.code", null, null)).thenReturn("some error message");
        errorSource.fieldError("someField", "some.message.code");
        assertThat(errorSource.containsFieldError("someField"), equalTo(true));
        assertThat(errorSource.containsGlobalErrors(), equalTo(false));
        assertThat(errorSource.getResolvedFieldErrorMessages("someField").size(), equalTo(1));
        assertThat(errorSource.getResolvedFieldErrorMessages("someField").contains("some error message"), equalTo(true));
    }

    @Test
    public void fieldErrorWithDefaultMessageAndArgsAddedCorrectly() {
        when(messageResolver.getMessage("some.other.message.code", "some default message", "arg1", "arg2")).thenReturn("some other error message");
        errorSource.fieldError("someOtherField", "some.other.message.code", "some default message", "arg1", "arg2");
        assertThat(errorSource.containsFieldError("someOtherField"), equalTo(true));
        assertThat(errorSource.containsGlobalErrors(), equalTo(false));
        assertThat(errorSource.getResolvedFieldErrorMessages("someOtherField").size(), equalTo(1));
        assertThat(errorSource.getResolvedFieldErrorMessages("someOtherField").contains("some other error message"), equalTo(true));
    }

    @Test
    public void globalErrorWithNoDefaultMessageOrArgsAddedCorrectly() {
        when(messageResolver.getMessage("some.message.code", null, null)).thenReturn("some error message");
        errorSource.globalError("some.message.code");
        assertThat(errorSource.containsGlobalErrors(), equalTo(true));
        assertThat(errorSource.getResolvedGlobalErrorMessages().size(), equalTo(1));
        assertThat(errorSource.getResolvedGlobalErrorMessages().contains("some error message"), equalTo(true));
    }

    @Test
    public void globalErrorWithDefaultMessageAndArgsAddedCorrectly() {
        when(messageResolver.getMessage("some.other.message.code", "some default message", "arg1", "arg2")).thenReturn("some other error message");
        errorSource.globalError("some.other.message.code", "some default message", "arg1", "arg2");
        assertThat(errorSource.containsGlobalErrors(), equalTo(true));
        assertThat(errorSource.getResolvedGlobalErrorMessages().size(), equalTo(1));
        assertThat(errorSource.getResolvedGlobalErrorMessages().contains("some other error message"), equalTo(true));
    }

    @Test
    public void addsMultipleFieldAndGlobalMessagesAsExpected() {
        when(messageResolver.getMessage("some.field.message.code", null, null)).thenReturn("some field error message");
        when(messageResolver.getMessage("some.other.field.message.code", "some default field message", "arg1", "arg2")).thenReturn("some other field error message");
        when(messageResolver.getMessage("some.global.message.code", null, null)).thenReturn("some global error message");
        when(messageResolver.getMessage("some.other.global.message.code", "some default global message", "arg3", "arg4")).thenReturn("some other global error message");
        errorSource.fieldError("someField", "some.field.message.code");
        errorSource.fieldError("someOtherField", "some.other.field.message.code", "some default field message", "arg1", "arg2");
        errorSource.globalError("some.global.message.code");
        errorSource.globalError("some.other.global.message.code", "some default global message", "arg3", "arg4");

        assertThat(errorSource.containsGlobalErrors(), equalTo(true));
        assertThat(errorSource.getResolvedGlobalErrorMessages().size(), equalTo(2));
        assertThat(errorSource.getResolvedGlobalErrorMessages().contains("some global error message"), equalTo(true));
        assertThat(errorSource.getResolvedGlobalErrorMessages().contains("some other global error message"), equalTo(true));

        assertThat(errorSource.containsFieldError("someField"), equalTo(true));
        assertThat(errorSource.containsFieldError("someOtherField"), equalTo(true));
        assertThat(errorSource.getResolvedFieldErrorMessages("someField").size(), equalTo(1));
        assertThat(errorSource.getResolvedFieldErrorMessages("someOtherField").size(), equalTo(1));
        assertThat(errorSource.getResolvedFieldErrorMessages("someField").contains("some field error message"), equalTo(true));
        assertThat(errorSource.getResolvedFieldErrorMessages("someOtherField").contains("some other field error message"), equalTo(true));
    }

    @Test
    public void supportsMultipleMessagesForASingleField() {
        when(messageResolver.getMessage("some.field.message.code", null, null)).thenReturn("some field error message");
        when(messageResolver.getMessage("some.other.field.message.code", "some default field message", "arg1", "arg2")).thenReturn("some other field error message");
        errorSource.fieldError("someField", "some.field.message.code");
        errorSource.fieldError("someField", "some.other.field.message.code", "some default field message", "arg1", "arg2");

        assertThat(errorSource.containsFieldError("someField"), equalTo(true));
        assertThat(errorSource.getResolvedFieldErrorMessages("someField").size(), equalTo(2));
        assertThat(errorSource.getResolvedFieldErrorMessages("someField").contains("some field error message"), equalTo(true));
        assertThat(errorSource.getResolvedFieldErrorMessages("someField").contains("some other field error message"), equalTo(true));
    }
}
