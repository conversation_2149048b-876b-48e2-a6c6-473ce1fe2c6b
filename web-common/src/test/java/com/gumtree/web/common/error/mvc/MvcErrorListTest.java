package com.gumtree.web.common.error.mvc;

import com.gumtree.common.util.error.Error;
import org.junit.Before;
import org.junit.Test;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class MvcErrorListTest {

    private Errors errors;

    @Before
    public void init() {
        errors = mock(Errors.class);
    }

    @Test
    public void handlesNoErrors() {
        when(errors.getFieldErrors()).thenReturn(new ArrayList<FieldError>());
        when(errors.getGlobalErrors()).thenReturn(new ArrayList<ObjectError>());
        MvcErrorList list = new MvcErrorList(errors);
        assertThat(list.size(), equalTo(0));
    }

    @Test
    public void handlesNullErrors() {
        when(errors.getFieldErrors()).thenReturn(null);
        when(errors.getGlobalErrors()).thenReturn(null);
        MvcErrorList list = new MvcErrorList(errors);
        assertThat(list.size(), equalTo(0));
    }

    @Test
    public void addsFieldAndGlobalErrors() {
        populateTestErrors();
        MvcErrorList list = new MvcErrorList(errors);
        assertThat(list.size(), equalTo(4));
        assertThat(containsError(list, "field1", "code1", "msg1", "arg1", "arg2"), equalTo(true));
        assertThat(containsError(list, "field2", "code2", null, null), equalTo(true));
        assertThat(containsError(list, null, "code3", "msg3", "arg3", "arg4"), equalTo(true));
        assertThat(containsError(list, null, "code4", null, null), equalTo(true));
    }

    private boolean containsError(List<Error> errors, String field, String code, String defaultMessage, Object... args) {
        boolean found = false;
        for (Error error : errors) {
            try {
                assertThat(error.getField(), equalTo(field));
                assertThat(error.getMessageCode(), equalTo(code));
                assertThat(error.getDefaultMessage(), equalTo(defaultMessage));
                assertThat(error.getArgs(), equalTo(args));
            } catch (AssertionError ex) {
                continue;
            }
            found = true;
            break;
        }
        return found;
    }

    private void populateTestErrors() {
        FieldError fieldError1 = mockFieldError("field1", "code1", "msg1", "arg1", "arg2");
        FieldError fieldError2 = mockFieldError("field2", "code2", null, null);
        ObjectError objectError1 = mockObjectError("code3", "msg3", "arg3", "arg4");
        ObjectError objectError2 = mockObjectError("code4", null, null);
        when(errors.getFieldErrors()).thenReturn(Arrays.asList(fieldError1, fieldError2));
        when(errors.getGlobalErrors()).thenReturn(Arrays.asList(objectError1, objectError2));
    }

    private FieldError mockFieldError(String field, String code, String defaultMessage, String... args) {
        FieldError error = mock(FieldError.class);
        when(error.getField()).thenReturn(field);
        when(error.getCode()).thenReturn(code);
        when(error.getDefaultMessage()).thenReturn(defaultMessage);
        when(error.getArguments()).thenReturn(args);
        return error;
    }

    private ObjectError mockObjectError(String code, String defaultMessage, String... args) {
        ObjectError error = mock(ObjectError.class);
        when(error.getCode()).thenReturn(code);
        when(error.getDefaultMessage()).thenReturn(defaultMessage);
        when(error.getArguments()).thenReturn(args);
        return error;
    }
}
