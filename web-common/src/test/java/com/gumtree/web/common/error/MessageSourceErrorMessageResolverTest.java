package com.gumtree.web.common.error;

import org.junit.Before;
import org.junit.Test;
import org.springframework.context.MessageSource;
import org.springframework.context.support.ResourceBundleMessageSource;

import java.util.Locale;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class MessageSourceErrorMessageResolverTest {

    private MessageSource messageSource;

    private MessageSourceErrorMessageResolver messageResolver;

    @Before
    public void init() {
        messageSource = mock(MessageSource.class);
        messageResolver = new MessageSourceErrorMessageResolver(messageSource);
    }

    @Test
    public void returnsMessageResolvedFromMessageSource() {
        when(messageSource.getMessage("code", new String[]{"arg1"}, "default", Locale.getDefault())).thenReturn("msg1");
        String message = messageResolver.getMessage("code", "default", "arg1");
        assertThat(message, equalTo("msg1"));
    }

    @Test
    public void triesToResolveDefaultMessageWhenMessageCodeResolvesToNull() {
        when(messageSource.getMessage("code", new String[]{"arg1"}, "default", Locale.getDefault())).thenReturn(null);
        when(messageSource.getMessage("default", new String[]{"arg1"}, "default", Locale.getDefault()))
                .thenReturn("msg1");
        String message = messageResolver.getMessage("code", "default", "arg1");
        assertThat(message, equalTo("msg1"));
    }

    @Test
    public void triesToResolveDefaultMessageIfMessageCodeResolvesToDefaultMessage() {
        when(messageSource.getMessage("code", new String[]{"arg1"}, "default", Locale.getDefault()))
                .thenReturn("default");
        when(messageSource.getMessage("default", new String[]{"arg1"}, "default", Locale.getDefault()))
                .thenReturn("msg1");
        String message = messageResolver.getMessage("code", "default", "arg1");
        assertThat(message, equalTo("msg1"));
    }

    @Test
    public void returnsMessageCodeIfNeitherMessageCodeOrDefaultMessageResolve() {
        when(messageSource.getMessage("code", new String[]{"arg1"}, "default", Locale.getDefault())).thenReturn(null);
        when(messageSource.getMessage("default", new String[]{"arg1"}, "default", Locale.getDefault()))
                .thenReturn(null);
        String message = messageResolver.getMessage("code", "default", "arg1");
        assertThat(message, equalTo("code"));
    }

    @Test
    public void returnMessageFromIndexedPatter() {
        messageSource = new ResourceBundleMessageSource();
        messageResolver = new MessageSourceErrorMessageResolver(messageSource);
        //Took me a while to work this out but getMessage uses java.text.MessageFormat which requires escaping of single
        // quotes so if you want to put an argumant and a single quote in a message you will have to use a double quote
        // like "format''s argument {0}"
        String pattern = "If you do not receive the email in a few minutes then please check your junk email folder. If" +
                " you still don''t find the email then please check that you have spelt your email address correctly and" +
                " if it is correct we can [resend the email]({0}).";
        Object[] args = new Object[]{"<EMAIL>"};
        String message = messageResolver.getMessage(pattern, pattern, args);
        assertThat(message,
                equalTo("If you do not receive the email in a few minutes then please check your junk email folder. If " +
                        "you still don't find the email then please check that you have spelt your email address " +
                        "correctly and if it is correct we can [resend the email](<EMAIL>)."));
    }
}
