package com.gumtree.web.storage;

import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

import java.util.LinkedList;
import java.util.Queue;

public class FakeJedisPool extends Pool<Jedis> {

    private final Queue<Jedis> jedis;

    public FakeJedisPool(Jedis jedis) {
        this.jedis = new LinkedList<>();
        this.jedis.add(jedis);
    }

    @Override
    public Jedis getResource() {
        if (jedis.isEmpty()) {
            throw new RuntimeException("no pool resource available");
        }

        return jedis.poll();
    }

    @Override
    public void returnResource(Jedis resource) {
        jedis.add(resource);
    }

    public boolean isEmpty() {
        return jedis.isEmpty();
    }
}
