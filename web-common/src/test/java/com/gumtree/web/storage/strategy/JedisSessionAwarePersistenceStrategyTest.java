package com.gumtree.web.storage.strategy;

import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.storage.FakeJedis;
import com.gumtree.web.storage.FakeJedisPool;
import com.gumtree.web.storage.RedisTemplate;
import org.junit.Before;
import org.junit.Test;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;


import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class JedisSessionAwarePersistenceStrategyTest {
    public static final String SESSION_ID = "S3s51on-ID";

    private JedisPersistenceStrategy strategy;
    private RedisTemplate redisTemplate;
    private MeterRegistry meterRegistry;

    @Before
    public void setUp() throws Exception {
        UserSessionService userSessionService = mock(UserSessionService.class);
        when(userSessionService.getSessionCookieId()).thenReturn(SESSION_ID);
        meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());

        redisTemplate = new RedisTemplate(new FakeJedisPool(new FakeJedis()), null, null, meterRegistry);
        strategy = new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService);
    }

    @Test
    public void keysWrittenAlwaysPrefixedWithSessionId() {
        // when
        strategy.writeOperation(handler -> handler.set("somekey", "somevalue", 100));

        // than
        assertThat(redisTemplate.get("usersession_" + SESSION_ID + "_somekey"), equalTo("somevalue"));
        assertThat(redisTemplate.ttl("usersession_" + SESSION_ID + "_somekey"), equalTo(100L));

        assertThat(redisTemplate.get("somekey"), nullValue());
    }

    @Test
    public void readOperationUsesPrefix() {
        // given
        //usersession_%s_%s
        redisTemplate.set("usersession_" + SESSION_ID + "_somekey", "somevalue", 60);

        // when
        String value = strategy.readOperation("somekey");

        // then
        assertThat(value, equalTo("somevalue"));
    }
}

