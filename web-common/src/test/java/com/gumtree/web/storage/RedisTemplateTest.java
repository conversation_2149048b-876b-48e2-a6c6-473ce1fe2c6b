package com.gumtree.web.storage;

import com.google.common.util.concurrent.MoreExecutors;
import org.junit.Before;
import org.junit.Test;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import static org.fest.assertions.api.Assertions.assertThat;

public class RedisTemplateTest {

    private RedisTemplate redisTemplate;
    private FakeJedis primaryJedis;
    private FakeJedis secondaryJedis;
    private FakeJedisPool primaryPool;
    private FakeJedisPool secondaryPool;
    private MeterRegistry meterRegistry;

    @Before
    public void setUp() throws Exception {
        primaryJedis = new FakeJedis("primary-fails");
        secondaryJedis = new FakeJedis("secondary-fails");
        primaryPool = new FakeJedisPool(primaryJedis);
        secondaryPool = new FakeJedisPool(secondaryJedis);
        meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());
        redisTemplate = new RedisTemplate(primaryPool, secondaryPool, MoreExecutors.newDirectExecutorService(), meterRegistry);
    }

    @Test
    public void shouldSet() {
        // when
        redisTemplate.set("ok-key", "ok", 60);

        // then
        assertThat(primaryJedis.get("ok-key")).isEqualTo("ok");
    }

    @Test
    public void shouldGet() {
        redisTemplate.set("key", "ok", 60);

        // when + then
        assertThat(redisTemplate.get("key")).isEqualTo("ok");
    }

    @Test
    public void shouldDelete() {
        redisTemplate.set("key", "ok", 60);

        // when
        redisTemplate.del("key");

        // then
        assertThat(redisTemplate.get("key")).isNull();
    }

    @Test
    public void shouldReleasePoolResource() {
        // set
        redisTemplate.set("key", "value", 60);
        assertThat(primaryPool.isEmpty()).isFalse();

        // get
        redisTemplate.get("key");
        assertThat(primaryPool.isEmpty()).isFalse();

        // dell
        redisTemplate.del("key");
        assertThat(primaryPool.isEmpty()).isFalse();

        // ttl
        redisTemplate.ttl("key");
        assertThat(primaryPool.isEmpty()).isFalse();

        // expire
        redisTemplate.expire("key", 60);
        assertThat(primaryPool.isEmpty()).isFalse();
    }

    @Test
    public void shouldReleasePoolResourceOnErrorOnSet() {
        Exception exception = null;
        try {
            // when
            redisTemplate.set("primary-fails", "value", 60);
        } catch (Exception ex) {
            exception = ex;
        }

        // then
        assertThat(primaryPool.isEmpty()).isFalse();
        assertThat(exception).isNotNull();
    }

    @Test
    public void shouldReleasePoolResourceOnErrorOnGet() {
        Exception exception = null;
        try {
            // when
            redisTemplate.get("primary-fails");
        } catch (Exception ex) {
            exception = ex;
        }

        // then
        assertThat(primaryPool.isEmpty()).isFalse();
        assertThat(exception).isNotNull();
    }

    @Test
    public void shouldReleasePoolResourceOnErrorOnExpire() {
        Exception exception = null;
        try {
            // when
            redisTemplate.expire("primary-fails", 60);
        } catch (Exception ex) {
            exception = ex;
        }

        // then
        assertThat(primaryPool.isEmpty()).isFalse();
        assertThat(exception).isNotNull();
    }

    @Test
    public void shouldReleasePoolResourceOnErrorOnDel() {
        Exception exception = null;
        try {
            // when
            redisTemplate.del("primary-fails");
        } catch (Exception ex) {
            exception = ex;
        }

        // then
        assertThat(primaryPool.isEmpty()).isFalse();
        assertThat(exception).isNotNull();
    }

    @Test
    public void shouldWriteBothRedisClusters() throws InterruptedException {
        redisTemplate.set("ok-key", "ok", 60);

        // then
        assertThat(primaryJedis.get("ok-key")).isEqualTo("ok");
        assertThat(secondaryJedis.get("ok-key")).isEqualTo("ok");
    }

    @Test
    public void shouldWriteToPrimaryEvenIfSecondaryIsFailing() throws InterruptedException {
        // when
        redisTemplate.set("secondary-fails", "ok", 60);

        // then
        assertThat(primaryJedis.get("secondary-fails")).isEqualTo("ok");
        assertThat(primaryJedis.getData().get("secondary-fails")).isEqualTo("ok");
        assertThat(secondaryJedis.getData()).isEmpty();
    }

    @Test
    public void shouldWriteToSecondaryEvenIfPrimaryIsFailing() throws InterruptedException {
        Exception exception = null;
        // when
        try {
            redisTemplate.set("primary-fails", "ok", 60);
        } catch(Exception e) {
            exception = e;
        }

        // then
        assertThat(primaryJedis.getData()).isEmpty();
        assertThat(secondaryJedis.get("primary-fails")).isEqualTo("ok");
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(exception.getMessage()).isEqualTo("fake error caused by key: primary-fails");
    }

    @Test
    public void shouldDeleteFromBothRedisClusters() throws InterruptedException {
        // given
        redisTemplate.set("ok-key", "ok", 60);

        // when
        redisTemplate.del("ok-key");

        // then
        assertThat(primaryJedis.get("ok-key")).isNull();
        assertThat(secondaryJedis.get("ok-key")).isNull();
    }

    @Test
    public void shouldExpireInBothRedisClusters() throws InterruptedException {
        // given
        redisTemplate.set("ok-key", "ok", 60);

        // when
        redisTemplate.expire("ok-key", 55);

        // then
        assertThat(primaryJedis.ttl("ok-key")).isEqualTo(55L);
        assertThat(secondaryJedis.ttl("ok-key")).isEqualTo(55L);
    }
}