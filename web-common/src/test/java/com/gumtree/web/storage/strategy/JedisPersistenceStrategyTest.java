package com.gumtree.web.storage.strategy;

import com.gumtree.web.storage.FakeJedis;
import com.gumtree.web.storage.FakeJedisPool;
import com.gumtree.web.storage.RedisTemplate;
import io.micrometer.core.instrument.MeterRegistry;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

public class JedisPersistenceStrategyTest {
    private JedisPersistenceStrategy strategy;
    private RedisTemplate redisTemplate;
    private MeterRegistry meterRegistry;

    @Before
    public void setUp() throws Exception {
        meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());
        redisTemplate = new RedisTemplate(new FakeJedisPool(new FakeJedis()), null, null, meterRegistry);
        strategy = new JedisPersistenceStrategy(redisTemplate);
    }

    @Test
    public void valuesAreStored() {
        // when
        strategy.writeOperation(handler -> handler.set("somekey", "somevalue", 100));

        // than
        assertThat(redisTemplate.get("somekey"), equalTo("somevalue"));
        assertThat(redisTemplate.ttl("somekey"), equalTo(100L));
    }

    @Test
    public void readOperationWorks() {
        // given
        redisTemplate.set("somekey", "somevalue", 60);

        // when
        String value = strategy.readOperation("somekey");

        // then
        assertThat(value, equalTo("somevalue"));
    }
}
