package com.gumtree.web.storage;

import com.codahale.metrics.MetricRegistry;
import com.datastax.driver.core.Row;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.cassandraunit.CassandraCQLUnit;
import org.cassandraunit.dataset.CQLDataSet;
import org.junit.Before;
import org.junit.ClassRule;
import org.junit.Test;

import java.util.List;
import java.util.Optional;

import static org.fest.assertions.api.Assertions.assertThat;

public class CassandraKeyValueRepositoryTest {
    private CassandraKeyValueRepository repository;

    @ClassRule
    public static CassandraCQLUnit cassandraCQLUnit = new CassandraCQLUnit(new KeyValueCQLDataSet("seller", "test_table", "key", "value", "text"));

    @Before
    public void cleanTableAndSetupRepository() {
        cassandraCQLUnit.getSession().execute("truncate test_table");

        SimpleCassandraClient client = new SimpleCassandraClient(cassandraCQLUnit.getSession().getCluster(), "seller", new MetricRegistry());
        repository = new CassandraKeyValueRepository(client, "test_table", "key", "value", String.class);
    }

    @Test
    public void getShouldReturnNoValue() throws Exception {
        assertThat(repository.get("x")).isEqualTo(Optional.empty());
    }

    @Test
    public void getShouldReturnValue() throws Exception {
        // given
        cassandraCQLUnit.getSession().execute("insert into test_table (key, value) values ('x', 'y')");

        // then
        assertThat(repository.get("x")).isEqualTo(Optional.of("y"));
    }

    @Test
    public void getShouldReturnValueWithTTL() throws Exception {
        // given
        cassandraCQLUnit.getSession().execute("insert into test_table (key, value) values ('x', 'y') using ttl 10");

        Thread.sleep(1000);

        // then
        Optional<Pair<String, Integer>> valueWithTTL = repository.getWithTTL("x");
        assertThat(valueWithTTL.get().getLeft()).isEqualTo("y");
        assertThat(valueWithTTL.get().getRight()).isLessThanOrEqualTo(9);
    }

    @Test
    public void setShouldRespectTTL() throws Exception {
        // given
        repository.set("x", "y", 1);

        // when
        Thread.sleep(1000);

        // then
        assertThat(cassandraCQLUnit.getSession().execute("select * from test_table").one()).isNull();
    }

    @Test
    public void expireShouldRespectTTL() throws Exception {
        // when
        repository.set("x", "y", 1000);

        // then
        Row one = cassandraCQLUnit.getSession().execute("select * from test_table").one();
        assertThat(one.getString("key")).isEqualTo("x");
        assertThat(one.getString("value")).isEqualTo("y");

        // but when
        repository.expire("x", 1);
        Thread.sleep(1000);

        // then
        assertThat(cassandraCQLUnit.getSession().execute("select * from test_table").one()).isNull();
    }

    @Test
    public void deleteShouldDelete() throws Exception {
        // given
        cassandraCQLUnit.getSession().execute("insert into test_table (key, value) values ('x', 'y')");

        // when
        repository.delete("x");

        // then
        assertThat(cassandraCQLUnit.getSession().execute("select * from test_table").one()).isNull();
    }

    /**
     * Test utility class recreating keyspace and one key-value table
     */
    public static class KeyValueCQLDataSet implements CQLDataSet {
        private final String keyspace, table, keyColumn, valueColumn, valueType;

        public KeyValueCQLDataSet(String keyspace, String table, String keyColumn, String valueColumn, String valueType) {
            this.keyspace = keyspace;
            this.table = table;
            this.keyColumn = keyColumn;
            this.valueColumn = valueColumn;
            this.valueType = valueType;
        }

        @Override
        public List<String> getCQLStatements() {
            return Lists.newArrayList("CREATE TABLE IF NOT EXISTS " + keyspace + "." + table
                    + " (" + keyColumn + " text, " + valueColumn + " " + valueType + ", PRIMARY KEY(key));");
        }

        @Override
        public String getKeyspaceName() {
            return keyspace;
        }

        @Override
        public boolean isKeyspaceCreation() {
            return true;
        }

        @Override
        public boolean isKeyspaceDeletion() {
            return true;
        }
    }

}