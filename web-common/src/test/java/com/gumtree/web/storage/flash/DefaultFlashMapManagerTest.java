package com.gumtree.web.storage.flash;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.google.common.collect.Lists;
import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.GtEnvConfiguration;
import com.gumtree.web.storage.CassandraKeyValueRepository;
import com.gumtree.web.storage.RedisTemplate;
import com.gumtree.web.storage.exception.SessionDataAccessException;
import com.netflix.config.ConcurrentCompositeConfiguration;
import com.netflix.config.ConfigurationManager;
import org.codehaus.jackson.map.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.servlet.FlashMap;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Reason for hard-coding json: making sure persisted/retrieved json stays the same regardless of any refactoring
 */
public class DefaultFlashMapManagerTest {
    private static final String CASSANDRA_KEY = "session_xxx_id";
    private static final String REDIS_KEY = "flashMap_appName_session_xxx_id";

    private DefaultFlashMapManager manager;
    private HttpServletRequest request;
    private HttpServletResponse response;
    private ObjectMapper mapper;
    private MeterRegistry meterRegistry;

    private JedisPool jedisPool;
    private Jedis jedis;

    private CassandraKeyValueRepository cassandraRepository;

    private Counter failureCounter;

    @Before
    public void setup() {
        ConcurrentCompositeConfiguration finalConfig = new ConcurrentCompositeConfiguration();
        finalConfig.addConfiguration(new GtEnvConfiguration("appName", Env.PROD, "envName", "instanceName"), "envConfig");
        ConfigurationManager.loadPropertiesFromConfiguration(finalConfig);
        mapper = new ObjectMapper();
        request = mock(HttpServletRequest.class);
        response = mock(HttpServletResponse.class);
        jedis = mock(Jedis.class);
        HttpSession session = mock(HttpSession.class);
        meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());

        jedisPool = mock(JedisPool.class);
        when(jedisPool.getResource()).thenReturn(jedis);
        when(request.getSession()).thenReturn(session);
        when(session.getId()).thenReturn(CASSANDRA_KEY);

        MetricRegistry metricRegistry = mock(MetricRegistry.class);
        failureCounter = mock(Counter.class);
        when(metricRegistry.counter(any())).thenReturn(failureCounter);

        cassandraRepository = mock(CassandraKeyValueRepository.class);

        manager = new DefaultFlashMapManager(cassandraRepository, new RedisTemplate(jedisPool, null, null, meterRegistry), mapper, metricRegistry);
    }

    @Test
    public void retrieveHandlesNoResponse() {
        when(cassandraRepository.get(anyString())).thenReturn(Optional.empty());
        when(jedis.get(anyString())).thenReturn(null);

        assertThat(manager.retrieveFlashMaps(request).size(), equalTo(0));
    }

    @Test
    public void retrieveEmptyMap() {
        String flashMapJson = "{\"serializableFlashMaps\":[]}";

        when(cassandraRepository.get(anyString())).thenReturn(Optional.of(flashMapJson));

        assertThat(manager.retrieveFlashMaps(request).size(), equalTo(0));
    }

    @Test
    public void retrieveOneMap() throws Exception {
        final String flashMapJson = "{\"serializableFlashMaps\":[{\"flashValues\":{\"onekey\":\"oneval\"},\"requestPath\":\"/mypath\"" +
                ",\"requestParams\":{\"multiKey\":[\"multiVal\"]}}]}";

        when(jedis.get(eq(REDIS_KEY))).thenReturn(flashMapJson);
        when(cassandraRepository.get(eq(CASSANDRA_KEY))).thenReturn(Optional.of(flashMapJson));

        List<FlashMap> flashMaps = manager.retrieveFlashMaps(request);

        assertThat(flashMaps.size(), equalTo(1));

        FlashMap flashMap = flashMaps.get(0);
        assertThat(flashMap.size(), equalTo(1));
        assertThat(flashMap.get("onekey").toString(), equalTo("oneval"));
        assertThat(flashMap.getTargetRequestPath(), equalTo("/mypath"));
        assertThat(flashMap.getTargetRequestParams().size(), equalTo(1));

        MultiValueMap<String, String> multiValueMap = flashMap.getTargetRequestParams();
        assertThat(multiValueMap.size(), equalTo(1));
        assertThat(multiValueMap.getFirst("multiKey"), equalTo("multiVal"));

        // making sure that if cassandra returns value then there search on redis is not executed
        verify(cassandraRepository).get(eq(CASSANDRA_KEY));
        verify(jedis, times(0)).get(anyString());
    }

    @Test
    public void retrieveManyMaps() throws Exception {
        String json = "{\"serializableFlashMaps\":[{\"flashValues\":{\"onekey\":\"oneval\"},\"requestPath\":null,\"requestParams\":null}," +
                "{\"flashValues\":{\"twokey\":\"twoval\"},\"requestPath\":null,\"requestParams\":null}]}";
        when(jedis.get(anyString())).thenReturn(json);

        List<FlashMap> flashMaps = manager.retrieveFlashMaps(request);
        assertThat(flashMaps.size(), equalTo(2));

        FlashMap flashMap = flashMaps.get(0);
        assertThat(flashMap.size(), equalTo(1));
        assertThat(flashMap.get("onekey").toString(), equalTo("oneval"));

        flashMap = flashMaps.get(1);
        assertThat(flashMap.size(), equalTo(1));
        assertThat(flashMap.get("twokey").toString(), equalTo("twoval"));
    }

    @Test
    public void retrieveShouldAccessOnlyCassandraIfEntryIsAvailable() {
        when(cassandraRepository.get(anyString())).thenReturn(Optional.of("{\"serializableFlashMaps\":[]}"));

        manager.retrieveFlashMaps(request);

        verify(cassandraRepository).get(eq(CASSANDRA_KEY));
        verify(jedis, times(0)).get(anyString());
    }

    @Test
    public void retrieveShouldAccessBothRepositoriesIfEntryIsNotAvailable() {
        when(cassandraRepository.get(anyString())).thenReturn(Optional.empty());
        when(jedis.get(anyString())).thenReturn(null);

        manager.retrieveFlashMaps(request);

        verify(cassandraRepository).get(eq(CASSANDRA_KEY));
        verify(jedis).get(eq(REDIS_KEY));
    }

    @Test
    public void retrieveShouldSucceedWhenOnlyCassandraFailsToRead() {
        when(cassandraRepository.get(anyString())).thenThrow(new RuntimeException());
        when(jedis.get(anyString())).thenReturn(null);

        assertThat(manager.retrieveFlashMaps(request).size(), equalTo(0));
        verify(failureCounter).inc();
    }

    @Test(expected = SessionDataAccessException.class)
    public void retrieveShouldFailWhenBothRepositoriesFailToRead() {
        when(cassandraRepository.get(anyString())).thenThrow(new RuntimeException());
        when(jedis.get(anyString())).thenThrow(new RuntimeException());

        manager.retrieveFlashMaps(request);
    }

    @Test
    public void updateEmpty() throws IOException {
        manager.updateFlashMaps(Collections.emptyList(), request, response);

        String flashMapJson = "{\"serializableFlashMaps\":[]}";

        verify(jedis).set(eq(REDIS_KEY), eq(flashMapJson));
        verify(jedis).expire(eq(REDIS_KEY), eq(60));
        verify(cassandraRepository).set(eq(CASSANDRA_KEY), eq(flashMapJson), eq(60));
    }

    @Test
    public void updateWithOne() throws Exception {
        FlashMap map = new FlashMap();
        map.put("onekey", "oneval");
        LinkedMultiValueMap<String, String> reqParams = new LinkedMultiValueMap<>();
        reqParams.add("multiKey", "multiVal1");
        reqParams.add("multiKey", "multiVal2");

        map.setTargetRequestPath("/mypath");
        map.addTargetRequestParams(reqParams);

        manager.updateFlashMaps(Lists.newArrayList(map), request, response);

        final String flashMapJson = "{\"serializableFlashMaps\":[{\"flashValues\":{\"onekey\":\"oneval\"},\"requestPath\":\"/mypath\"" +
                ",\"requestParams\":{\"multiKey\":[\"multiVal1\",\"multiVal2\"]}}]}";

        verify(jedis).set(eq(REDIS_KEY), eq(flashMapJson) );
        verify(jedis).expire(eq(REDIS_KEY), eq(60));
        verify(cassandraRepository).set(eq(CASSANDRA_KEY), eq(flashMapJson), eq(60));
    }

    @Test
    public void updateShouldAccessBothRepositories() throws IOException {
        manager.updateFlashMaps(Collections.emptyList(), request, response);

        verify(jedis).set(anyString(), anyString());
        verify(jedis).expire(anyString(), anyInt());
        verify(cassandraRepository).set(anyString(), anyString(), anyInt());
    }

    @Test
    public void updateShouldSucceedWhenOnlyCassandraFailsToWrite() {
        doThrow(new RuntimeException()).when(cassandraRepository).set(anyString(), anyString(), anyInt());

        manager.updateFlashMaps(Lists.newArrayList(), request, response);

        String flashMapJson = "{\"serializableFlashMaps\":[]}";

        verify(jedis).set(eq(REDIS_KEY), eq(flashMapJson));
        verify(jedis).expire(eq(REDIS_KEY), eq(60));
        verify(cassandraRepository).set(eq(CASSANDRA_KEY), eq(flashMapJson), eq(60));
        verify(failureCounter).inc();
    }

    @Test(expected = SessionDataAccessException.class)
    public void updateShouldFailIfBothRepositoriesFailsToWrite() {
        doThrow(new RuntimeException()).when(cassandraRepository).set(anyString(), anyString(), anyInt());
        doThrow(new RuntimeException()).when(jedis).set(anyString(), anyString());

        manager.updateFlashMaps(Lists.newArrayList(), request, response);
    }


    @Test(expected = SessionDataAccessException.class)
    public void readShouldFailIfRedisFails() {
        when(cassandraRepository.get(anyString())).thenReturn(Optional.empty());
        when(jedis.get(anyString())).thenThrow(new IllegalStateException());

        manager.retrieveFlashMaps(request);
    }

    @Test(expected = SessionDataAccessException.class)
    public void updateShouldFailIfRedisFails() throws IOException {
        when(jedis.set(anyString(), anyString())).thenThrow(new IllegalStateException());

        manager.updateFlashMaps(Collections.emptyList(), request, response);
    }
}
