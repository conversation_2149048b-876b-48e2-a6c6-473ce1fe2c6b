package com.gumtree.web.storage.strategy;

import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.SystemClock;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class MapPersistenceStrategyTest {
    private MapPersistenceStrategy mapPersistenceStrategy;
    private Clock clock;

    private SessionPersistenceStrategy.WriteOperationCallback writeOperationCallback = handler -> {
        handler.set("somekey", "somevalue", 5); // Expire in 5 seconds
    };

    @Before
    public void setup() {
        clock = mock(Clock.class);

        when(clock.now()).thenReturn(1000L);

        mapPersistenceStrategy = new MapPersistenceStrategy(100000L, clock);
    }

    @Test
    public void nonExpiredKeysNotCleared() {
        mapPersistenceStrategy.writeOperation(writeOperationCallback);

        verify(clock).now();

        when(clock.now()).thenReturn(1500L);
        mapPersistenceStrategy.clearExpiredEntries();
        String value = mapPersistenceStrategy.readOperation("somekey");

        assertThat(value, equalTo("somevalue"));
    }

    @Test
    public void expiredKeysClearedAfterExpiryTaskRuns() {
        mapPersistenceStrategy.writeOperation(writeOperationCallback);

        verify(clock).now();

        when(clock.now()).thenReturn(6001L);
        mapPersistenceStrategy.clearExpiredEntries();
        String value = mapPersistenceStrategy.readOperation("somekey");

        assertThat(value, nullValue());
    }

    @Test
    public void notExpiredValuesAreReturnedByRead() throws Exception {
        mapPersistenceStrategy = new MapPersistenceStrategy(50L, new SystemClock());
        mapPersistenceStrategy.writeOperation( handler -> handler.set("somekey", "somevalue", 2));
        Thread.sleep(1200);

        String value = mapPersistenceStrategy.readOperation("somekey");
        assertThat(value, equalTo("somevalue"));
    }

    @Test
    public void expiredValuesAreNotReturnedByRead() throws Exception {
        mapPersistenceStrategy = new MapPersistenceStrategy(100000L, new SystemClock());
        mapPersistenceStrategy.writeOperation( handler -> handler.set("somekey", "somevalue", 1));
        Thread.sleep(1200);

        String value = mapPersistenceStrategy.readOperation("somekey");
        assertThat(value, nullValue());
    }


}
