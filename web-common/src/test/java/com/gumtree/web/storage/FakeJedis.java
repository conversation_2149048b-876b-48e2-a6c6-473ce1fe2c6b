package com.gumtree.web.storage;

import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.Map;

public class FakeJedis extends Jedis {
    public final String ERROR_TROWING_KEY = "error-throwing-key";

    private Map<String, String> data = new HashMap<String, String>();
    private Map<String, Long> ttls = new HashMap<String, Long>();

    private final String errorThrowingKey;

    public FakeJedis() {
        super("nothing");
        this.errorThrowingKey = ERROR_TROWING_KEY;
    }

    public FakeJedis(String errorThrowingKey) {
        super("nothing");
        this.errorThrowingKey = errorThrowingKey;
    }

    @Override
    public String set(String key, String value) {
        throwErrorIfRequired(key);
        data.put(key, value);
        return "ok";
    }

    @Override
    public String get(String key) {
        throwErrorIfRequired(key);
        return data.get(key);
    }

    @Override
    public Long expire(String key, int seconds) {
        throwErrorIfRequired(key);
        ttls.put(key, Integer.valueOf(seconds).longValue());
        return 0L;
    }

    @Override
    public Long del(String key) {
        throwErrorIfRequired(key);
        data.remove(key);
        return 0L;
    }

    @Override
    public Long del(String... keys) {
        throwErrorIfRequired(keys[0]);
        data.remove(keys);
        return 0L;
    }

    @Override
    public Long ttl(String key) {
        throwErrorIfRequired(key);
        return ttls.get(key);
    }

    public Map<String, String> getData() {
        return data;
    }

    private void throwErrorIfRequired(String key) {
        if (errorThrowingKey.equals(key)) {
            throw new RuntimeException("fake error caused by key: " + key);
        }
    }
}
