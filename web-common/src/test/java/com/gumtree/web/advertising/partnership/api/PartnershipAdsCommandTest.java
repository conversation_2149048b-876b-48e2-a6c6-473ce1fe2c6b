package com.gumtree.web.advertising.partnership.api;

import com.gumtree.common.properties.GtPropManager;
import com.gumtree.web.advertising.partnership.Partnership;
import com.gumtree.web.advertising.partnership.PartnershipsParameters;
import com.gumtree.web.advertising.partnership.api.HystrixEnabledPartnershipsApi.PartnershipAdsCommand;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static com.gumtree.config.MwebProperty.PARTAPI_CONNECTION_POOL_MAX;
import static com.gumtree.domain.page.Page.HOME_RESPONSIVE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PartnershipAdsCommandTest {

    static {
        GtPropManager.setProperty(PARTAPI_CONNECTION_POOL_MAX.getPropertyName(), "10");
    }

    @Mock
    PartnershipsApi partnershipsApi;

    int timeoutMs = 1000;

    @Test
    public void shouldCacheResponsesHits() {

        PartnershipsParameters partnershipsParameters = PartnershipsParameters.builder().withPage(HOME_RESPONSIVE).build();
        Partnership partnership = new Partnership("id", "title");
        List<Partnership> partnershipAds = Collections.singletonList(partnership);
        when(partnershipsApi.getContent(partnershipsParameters)).thenReturn(partnershipAds);

        HystrixRequestContext context = HystrixRequestContext.initializeContext();
        try {
            PartnershipAdsCommand homepageResponsiveCommandA = new PartnershipAdsCommand(partnershipsApi, partnershipsParameters, timeoutMs);
            PartnershipAdsCommand homepageResponsiveCommandB = new PartnershipAdsCommand(partnershipsApi, partnershipsParameters, timeoutMs);

            assertEquals(partnershipAds, homepageResponsiveCommandA.execute());
            assertFalse(homepageResponsiveCommandA.isResponseFromCache());

            assertEquals(partnershipAds, homepageResponsiveCommandB.execute());
            assertTrue(homepageResponsiveCommandB.isResponseFromCache());
        } finally {
            context.shutdown();
        }

        // start a new request context
        context = HystrixRequestContext.initializeContext();
        try {
            PartnershipAdsCommand command3b = new PartnershipAdsCommand(partnershipsApi, partnershipsParameters, timeoutMs);
            assertEquals(partnershipAds, command3b.execute());
            assertFalse(command3b.isResponseFromCache());
        } finally {
            context.shutdown();
        }
    }

    @Test
    public void shouldFallbackToEmptyListWhenErrorWithPartnershipAPI() {
        HystrixRequestContext.initializeContext();

        PartnershipsParameters partnershipsParameters = PartnershipsParameters.builder().withPage(HOME_RESPONSIVE).build();

        PartnershipAdsCommand homepageResponsiveCommandA = new PartnershipAdsCommand(partnershipsApi, partnershipsParameters, timeoutMs);

        when(partnershipsApi.getContent(partnershipsParameters)).thenThrow(new RuntimeException("eekk..."));

        assertEquals(Collections.emptyList(), homepageResponsiveCommandA.execute());
    }

}
