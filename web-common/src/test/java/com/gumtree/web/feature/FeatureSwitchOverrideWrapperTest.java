package com.gumtree.web.feature;

import com.gumtree.web.cookie.cutters.feature.FeatureSwitchOverrideCookie;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import static org.fest.assertions.api.Assertions.assertThat;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FeatureSwitchOverrideWrapperTest {

    @Mock
    private HttpServletRequest request;

    @Mock
    private FeatureSwitchManager baseFeatureSwitchManager;

    @Mock
    private FeatureSwitch featureSwitch;

    @Test
    public void shouldReturnBaseResponseIfNoCookie() {
        // given
        when(featureSwitch.name()).thenReturn("SOME_FEATURE");
        when(featureSwitch.getPercentage()).thenReturn(100);
        when(baseFeatureSwitchManager.getFeatureState(featureSwitch)).thenReturn(FeatureSwitchConfig.State.ON);

        // when & then
        assertThat(getTestInstance().getFeatureState(featureSwitch)).isEqualTo(FeatureSwitchConfig.State.ON);

        // and
        verify(baseFeatureSwitchManager).getFeatureState(featureSwitch);
    }

    @Test
    public void shouldReturnCookieValue() {
        // given
        Cookie cookie = new Cookie(FeatureSwitchOverrideCookie.NAME, "SOME_FEATURE:T04=");
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});

        // and
        when(featureSwitch.name()).thenReturn("SOME_FEATURE");
        when(featureSwitch.getPercentage()).thenReturn(100);
        when(baseFeatureSwitchManager.getFeatureState(featureSwitch)).thenReturn(FeatureSwitchConfig.State.OFF);

        // when & then
        assertThat(getTestInstance().getFeatureState(featureSwitch)).isEqualTo(FeatureSwitchConfig.State.ON);

        // and
        verify(baseFeatureSwitchManager, never()).getFeatureState(featureSwitch);
    }

    private FeatureSwitchOverrideWrapper getTestInstance() {
        return new FeatureSwitchOverrideWrapper("", baseFeatureSwitchManager, request);
    }

}