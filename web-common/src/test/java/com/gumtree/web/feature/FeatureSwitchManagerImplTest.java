package com.gumtree.web.feature;

import com.gumtree.labs.api.LuckyNumberGenerator;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.abtest.AbExperimentsCookie;
import org.fest.assertions.data.Offset;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FeatureSwitchManagerImplTest {

    @Mock
    private CookieResolver cookieResolver;

    @Mock
    private HttpServletRequest request;

    @Mock
    private FeatureSwitch featureSwitch;

    @InjectMocks
    private FeatureSwitchManagerImpl featureSwitchManager;

    @Test
    public void shouldReturnProperlyForEnabledFeatureSwitch() {
        // given
        when(featureSwitch.name()).thenReturn("SOME_FEATURE");
        when(featureSwitch.getPercentage()).thenReturn(100);

        // when & then
        assertThat(featureSwitchManager.isOn(featureSwitch)).isTrue();
        assertThat(featureSwitchManager.isOff(featureSwitch)).isFalse();

        // and
        verify(cookieResolver, never()).resolve(request, AbExperimentsCookie.class);
    }

    @Test
    public void shouldReturnProperlyForDisabledFeatureSwitch() {
        // given
        when(featureSwitch.name()).thenReturn("SOME_FEATURE");
        when(featureSwitch.getPercentage()).thenReturn(0);

        // when & then
        assertThat(featureSwitchManager.isOn(featureSwitch)).isFalse();
        assertThat(featureSwitchManager.isOff(featureSwitch)).isTrue();

        // and
        verify(cookieResolver, never()).resolve(request, AbExperimentsCookie.class);
    }

    @Test
    public void shouldReturn25pOn() {
        int onCount = 0;
        int numberOfIterations = 10000;
        for (int i = 1; i <= numberOfIterations; i++) {
            reset(cookieResolver);
            reset(request);
            reset(featureSwitch);
            when(featureSwitch.name()).thenReturn("SOME_FEATURE");
            when(featureSwitch.getPercentage()).thenReturn(25);
            AbExperimentsCookie abExperimentsCookie = mock(AbExperimentsCookie.class);
            when(cookieResolver.resolve(request, AbExperimentsCookie.class)).thenReturn(abExperimentsCookie);
            when(abExperimentsCookie.getLuckyNumber()).thenReturn(LuckyNumberGenerator.luckyNumberAsBase36());
            boolean isOn = featureSwitchManager.isOn(featureSwitch);
            assertThat(featureSwitchManager.isOff(featureSwitch)).isEqualTo(!isOn);

            if (isOn) {
                onCount++;
            }
        }

        assertThat(100 * onCount / (double)numberOfIterations).isEqualTo(25, Offset.offset(2.0));
    }

}