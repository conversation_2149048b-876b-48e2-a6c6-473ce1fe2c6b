package com.gumtree.web.abtest;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class ExperimentsBotFilterWrapperTest {

    @Mock
    private ExperimentsProvider baseExperimentsProvider;

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private ExperimentsBotFilterWrapper experimentsBotFilterWrapper;

    private Map<String, String> expectedExperiments = new HashMap<String, String>(){
        {
            put("TEST", "A");
        }
    };

    @Before
    public void setup() {
        when(baseExperimentsProvider.get()).thenReturn(new Experiments(expectedExperiments));
    }

    @Test
    public void shouldNotFilterIfSomeUserAgent() {
        // given
        when(request.getHeader("User-Agent")).thenReturn("Some user agent");

        // when
        Experiments experiments = experimentsBotFilterWrapper.get();

        // then
        assertThat(experiments.getExperiments()).isEqualTo(expectedExperiments);

        // and
        verify(baseExperimentsProvider).get();
    }

    @Test
    public void shouldNotFilterIfMissingUserAgent() {
        // given
        when(request.getHeader("User-Agent")).thenReturn(null);

        // when
        Experiments experiments = experimentsBotFilterWrapper.get();

        // then
        assertThat(experiments.getExperiments()).isEqualTo(expectedExperiments);

        // and
        verify(baseExperimentsProvider).get();
    }

    @Test
    public void shouldFilterIfUserAgentIsGoogleBot() {
        // given
        when(request.getHeader("User-Agent")).thenReturn("Some googlebot here");

        // when
        Experiments experiments = experimentsBotFilterWrapper.get();

        // then
        assertThat(experiments.getExperiments()).isEqualTo(Collections.emptyMap());

        // and
        verify(baseExperimentsProvider, never()).get();
    }

    @Test
    public void shouldFilterIfUserAgentIsBingBot() {
        // given
        when(request.getHeader("User-Agent")).thenReturn("Some bingbot here");

        // when
        Experiments experiments = experimentsBotFilterWrapper.get();

        // then
        assertThat(experiments.getExperiments()).isEqualTo(Collections.emptyMap());

        // and
        verify(baseExperimentsProvider, never()).get();
    }

}