package com.gumtree.web.browse;

import com.google.common.base.Optional;
import com.gumtree.api.bapi.BapiLocationsResolvingService;
import com.gumtree.web.common.domain.location.Location;
import com.gumtree.web.common.domain.location.ResolvedLocation;
import com.gumtree.web.common.domain.location.ResolvedNamedLocation;
import com.gumtree.web.cookie.cutters.userpreferences.UserPreferencesCookie;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BrowseCategoriesLocationResolvingServiceTest {

    @Mock
    private BapiLocationsResolvingService bapiLocationsResolvingService;
    @Mock
    private UserPreferencesCookie userPreferencesCookie;

    private BrowseCategoriesLocationResolvingService resolvingService;

    @Before
    public void setUp(){
        resolvingService = new BrowseCategoriesLocationResolvingService(bapiLocationsResolvingService);
    }

    @Test
    public void returnsPostcodeLocationWithoutResolving() {
        when(userPreferencesCookie.getLocation()).thenReturn(Optional.of("TW91DH"));

        ResolvedLocation resolvedLocation = resolvingService.locallyResolveLocation(userPreferencesCookie);

        assertThat(resolvedLocation.getIdName(), is("TW91DH"));
        verifyZeroInteractions(bapiLocationsResolvingService);
    }

    @Test
    public void returnsOutcodeLocationWithoutResolving() {
        when(userPreferencesCookie.getLocation()).thenReturn(Optional.of("TW9"));

        ResolvedLocation resolvedLocation = resolvingService.locallyResolveLocation(userPreferencesCookie);

        assertThat(resolvedLocation.getIdName(), is("TW9"));
        verifyZeroInteractions(bapiLocationsResolvingService);
    }

    @Test
    public void returnsLocationByResolvingWithInMemoryCache(){
        Location location = new Location(1234L, "richmond", "Richmond");
        when(bapiLocationsResolvingService.getLocation("Richmond")).thenReturn(Optional.of(location));
        when(bapiLocationsResolvingService.resolveLocation(1234L)).thenReturn(new ResolvedNamedLocation(location));
        when(userPreferencesCookie.getLocation()).thenReturn(Optional.of("Richmond"));

        ResolvedLocation resolvedLocation = resolvingService.locallyResolveLocation(userPreferencesCookie);

        assertThat(resolvedLocation.getIdName(), is("richmond"));
    }

    @Test
    public void returnsUnresolvedLocationWhenCannotResolveInMemory(){
        when(bapiLocationsResolvingService.getLocation("Invalid")).thenReturn(Optional.absent());
        when(userPreferencesCookie.getLocation()).thenReturn(Optional.of("Invalid"));

        ResolvedLocation resolvedLocation = resolvingService.locallyResolveLocation(userPreferencesCookie);

        assertThat(resolvedLocation.getIdName(), is("Invalid"));
    }

    @Test
    public void returnsUnresolvedLocationWhenUserPreferenceCookieIsNull(){
        ResolvedLocation resolvedLocation = resolvingService.locallyResolveLocation(null);

        assertThat(resolvedLocation.getIdName(), is(""));
        verifyZeroInteractions(bapiLocationsResolvingService);
    }

}