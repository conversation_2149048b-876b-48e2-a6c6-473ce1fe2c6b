package com.gumtree.web.util.urlrewrite;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;

@RunWith(MockitoJUnitRunner.class)
public class ConfigureHttpsProxyTest {
    private static HttpsProxyConfigurer httpsProxyConfigurer = new HttpsProxyConfigurer();

    @Mock private HttpServletRequest request;
    @Mock private HttpServletResponse response;

    private String proxyHost;
    private String proxyPort;

    @Before
    public void beforeEach() {
        proxyHost = System.getProperty("https.proxyHost", "");
        proxyPort = System.getProperty("https.proxyPort", "");
    }

    @After
    public void afterEach() {
        System.setProperty("https.proxyHost", proxyHost);
        System.setProperty("https.proxyPort", proxyPort);
    }

    @Test
    public void shouldSetUseProxyReqAttributeWhenHttpsSystemPropertiesAreSet() {
        // given
        System.setProperty("https.proxyHost", "host");
        System.setProperty("https.proxyPort", "port");

        // when
        httpsProxyConfigurer.run(request, response);

        // then
        verify(request).setAttribute("use-proxy", "host:port");
        verifyZeroInteractions(response);
    }

    @Test
    public void shouldNotConfigureHttpProxyIfProxyHostIsNotSet() {
        // given
        System.setProperty("https.proxyHost", "");
        System.setProperty("https.proxyPort", "port");

        // when
        httpsProxyConfigurer.run(request, response);

        // then
        verifyZeroInteractions(request);
        verifyZeroInteractions(response);
    }

    @Test
    public void shouldNotConfigureHttpProxyIfProxyPortIsNotSet() {
        // given
        System.setProperty("https.proxyHost", "host");
        System.setProperty("https.proxyPort", "");

        // when
        httpsProxyConfigurer.run(request, response);

        // then
        verifyZeroInteractions(request);
        verifyZeroInteractions(response);
    }
}
