package com.gumtree.web.device;

import com.gumtree.web.common.device.DefaultDevice;
import com.gumtree.web.common.device.DefaultDeviceResolver;
import com.gumtree.web.common.device.DeviceOS;
import org.junit.Before;
import org.junit.Test;
import org.springframework.mobile.device.DeviceResolver;
import org.springframework.mock.web.MockHttpServletRequest;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

public class DefaultDeviceResolverTest {

    private DeviceResolver deviceResolver;

    @Before
    public void before() {
        deviceResolver = new DefaultDeviceResolver();
    }

    @Test
    public void resolveIOS() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("user-agent", UserAgent.IOS.getUserAgent());

        DefaultDevice device = (DefaultDevice) deviceResolver.resolveDevice(request);

        assertThat(device.getOs(), is(DeviceOS.IOS));
    }

    @Test
    public void resolveAndroid() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("user-agent", UserAgent.ANDROID.getUserAgent());

        DefaultDevice device = (DefaultDevice) deviceResolver.resolveDevice(request);

        assertThat(device.getOs(), is(DeviceOS.ANDROID));
    }

    @Test
    public void resolveUnknown() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("user-agent", UserAgent.WINDOWS.getUserAgent());

        DefaultDevice device = (DefaultDevice) deviceResolver.resolveDevice(request);

        assertThat(device.getOs(), is(DeviceOS.UNKNOWN));
    }

    @Test
    public void resolveMissingUserAgent() {
        MockHttpServletRequest request = new MockHttpServletRequest();

        DefaultDevice device = (DefaultDevice) deviceResolver.resolveDevice(request);

        assertThat(device.getOs(), is(DeviceOS.UNKNOWN));
    }

    enum UserAgent {

        IOS("mozilla/5.0 (iphone; u; cpu iphone os 4_3_2 like mac os x; en-us) applewebkit/533.17.9 (khtml, like gecko) version/5.0.2 mobile/8h7 safari/6533.18.5"),
        ANDROID("mozilla/5.0 (linux; android 4.1.1; nexus 7 build/jro03d) applewebkit/535.19 (khtml, like gecko) chrome/18.0.1025.166  safari/535.19"),
        WINDOWS("mozilla/5.0 (compatible; msie 9.0; windows phone os 7.5; trident/5.0; iemobile/9.0; nokia; lumia 800)");

        private String userAgent;

        private UserAgent(String userAgent) {
            this.userAgent = userAgent;
        }

        public String getUserAgent() {
            return userAgent;
        }
    }
}
