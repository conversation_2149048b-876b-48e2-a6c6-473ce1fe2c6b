package com.gumtree.web.cookie.cutters.userpreferences;

import com.google.common.collect.Lists;
import org.junit.Test;

import javax.servlet.http.Cookie;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.gumtree.web.cookie.cutters.CookieTestUtil.base64Encode;
import static com.gumtree.web.cookie.cutters.userpreferences.UserPreferencesCookie.addSearchKeywordToTheHead;
import static java.net.URLEncoder.encode;
import static org.fest.assertions.api.Assertions.assertThat;

public class UserPreferencesCookieTest {

    @Test
    public void getSearchKeywordsReturnsValidList() throws Exception {
        assertThat(new UserPreferencesCookie("localhost", -1, "/", new Cookie("gt_userPref", "invalid value")).getSearchKeywords())
                .as("invalid cookie")
                .isEmpty();
        assertThat(new UserPreferencesCookie("localhost", -1, "/", new <PERSON>ie("gt_userPref", "lfsk:")).getSearchKeywords())
                .as("valid cookie with searches")
                .isEmpty();
        assertThat(new UserPreferencesCookie("localhost", -1, "/", new Cookie("gt_userPref", "lfsk:" + base64Encode(encode("abC,", "UTF-8") + "," + encode(" p:l&,", "UTF-8")))).getSearchKeywords())
                .as("valid cookie with searches")
                .containsExactly("abC,", " p:l&,");
    }

    @Test
    public void encodeAndDecodeSearchKeywordsGevesTheSameInput() {
        // given
        List<String> in = Lists.newArrayList("", ",lskjdf, ", "7*^&%ATlslUY@", "8^.,?`±!&&&");

        // when
        String enc = UserPreferencesCookie.encodeSearchKeywords(in);
        List<String> dec = UserPreferencesCookie.decodeSearchKeywords(enc);

        // then
        assertThat(in).isEqualTo(dec);
    }

    @Test
    public void addSearchKeywordToTheHeadAdsUniqueEntryAtBegginningOfList() {
        assertThat(addSearchKeywordToTheHead(Collections.emptyList(), "Abc"))
                .as("empty list")
                .containsExactly("Abc");
        assertThat(addSearchKeywordToTheHead(Collections.singletonList("ABC"), "abc"))
                .as("no duplicates")
                .containsExactly("abc");
        assertThat(addSearchKeywordToTheHead(Arrays.asList("aBc", "efG"), "EFG"))
                .as("latests one in the front")
                .containsExactly("EFG", "aBc");
        assertThat(addSearchKeywordToTheHead(Arrays.asList("1", "2", "3", "4", "5"), "6"))
                .as("no more than 5 items")
                .containsExactly("6", "1", "2", "3", "4");
    }
}