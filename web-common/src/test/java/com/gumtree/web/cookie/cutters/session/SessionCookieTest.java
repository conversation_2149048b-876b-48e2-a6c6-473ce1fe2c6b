package com.gumtree.web.cookie.cutters.session;

import com.google.common.base.Optional;
import com.gumtree.web.cookie.CookieUtils;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;

import javax.servlet.http.Cookie;

import static org.fest.assertions.api.Assertions.assertThat;

public class SessionCookieTest {

    @Test
    public void getSearchCategoryIfPresentAndValidNumber() {
        // given
        SessionCookie cookie = new SessionCookie("gumtree.com", 0, "/", new Cookie("gt_s", "sc:" + Base64.encodeBase64String("1".getBytes())));

        // when
        Optional<Long> searchCategory = cookie.getSearchCategory();

        // then
        assertThat(searchCategory).isEqualTo(Optional.of(1L));
    }

    @Test
    public void getSearchCategoryIfPresentButInvalidNumber() {
        // given
        SessionCookie cookie = new SessionCookie("gumtree.com", 0, "/", new <PERSON>ie("gt_s", "sc:" + Base64.encodeBase64String("xyz".getBytes())));

        // when
        Optional<Long> searchCategory = cookie.getSearchCategory();

        // then
        assertThat(searchCategory).isEqualTo(Optional.<Long>absent());
    }

    @Test
    public void getSearchKeywordIfPresent() {
        // given
        SessionCookie cookie = new SessionCookie("gumtree.com", 0, "/", new Cookie("gt_s", "sk:" + Base64.encodeBase64String("audi".getBytes())));

        // when
        Optional<String> searchKeyword = cookie.getSearchKeyword();

        // then
        assertThat(searchKeyword).isEqualTo(Optional.of("audi"));
    }

    @Test
    public void getSearchOnlyAdsPostedPriorToTimeIfPresentAndValid() {
        // given
        SessionCookie cookie = new SessionCookie("gumtree.com", 0, "/", new Cookie("gt_s", "st:" + Base64.encodeBase64String("123456".getBytes())));

        // when
        Optional<Long> searchStartTime = cookie.getSearchOnlyAdsPostedPriorToTime();

        // then
        assertThat(searchStartTime).isEqualTo(Optional.of(123456L));
    }

    @Test
    public void getSearchOnlyAdsPostedPriorToTimeIfNotPresent() {
        // given
        SessionCookie cookie = new SessionCookie("gumtree.com", 0, "/", new Cookie("gt_s", ""));

        // when
        Optional<Long> searchStartTime = cookie.getSearchOnlyAdsPostedPriorToTime();

        // then
        assertThat(searchStartTime).isEqualTo(Optional.absent());
    }

    @Test
    public void getSearchOnlyAdsPostedPriorToTimeIfPresentAndInvalid() {
        // given
        SessionCookie cookie = new SessionCookie("gumtree.com", 0, "/", new Cookie("gt_s", "st:" + Base64.encodeBase64String("abc".getBytes())));

        // when
        Optional<Long> searchStartTime = cookie.getSearchOnlyAdsPostedPriorToTime();

        // then
        assertThat(searchStartTime).isEqualTo(Optional.absent());
    }

    @Test
    public void getBingClientIdIfPresent() {
        // given
        SessionCookie cookie = new SessionCookie("gumtree.com", 0, "/", new Cookie("gt_s", "bci:" + Base64.encodeBase64String("1".getBytes())));

        // when
        java.util.Optional<String> bingClientId = cookie.getBingClientId();

        // then
        assertThat(bingClientId).isEqualTo(java.util.Optional.of("1"));
    }

    @Test
    public void getBingClientIdIfNotPresent() {
        // given
        SessionCookie cookie = new SessionCookie("gumtree.com", 0, "/", new Cookie("gt_s", ""));

        // when
        java.util.Optional<String> bingClientId = cookie.getBingClientId();

        // then
        assertThat(bingClientId).isEqualTo(java.util.Optional.empty());
    }

    @Test
    public void setBingClientId() {
        // given
        SessionCookie cookie = new SessionCookie("gumtree.com", 0, "/", new Cookie("gt_s", ""));

        // when
        cookie.setBingClientId("bing_id");

        // then
        Cookie newSessionCookie = CookieUtils.createHttpCookie("gt_s", cookie, false);
        assertThat(newSessionCookie.getValue()).isEqualTo("bci:YmluZ19pZA==");
    }
}