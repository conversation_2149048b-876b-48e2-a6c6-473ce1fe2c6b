package com.gumtree.web.cookie.cutters.capi;

import org.apache.shiro.codec.Base64;
import org.junit.Before;
import org.junit.Test;

import javax.servlet.http.Cookie;
import java.util.Optional;

import static org.fest.assertions.api.Assertions.assertThat;

public class CapiAuthenticationCookieCutterTest {

    private CapiAuthenticationCookieCutter cutter;

    @Before
    public void beforeEach() {
        cutter = new CapiAuthenticationCookieCutter("gumtree.com");
    }

    @Test
    public void shouldCutNewCookieAsSessionCookie() {
        // when
        CapiAuthenticationCookie cookie = cutter.cutNew();

        // then
        assertThat(cookie.getMaxAge()).isEqualTo(-1);
        assertThat(cookie.getValueOpt()).isEqualTo(Optional.empty());
    }

    @Test
    public void shouldCutExistingCookieAsSessionCookie() {
        // given
        Cookie existing = new Cookie("gt_capi_auth", Base64.encodeToString("cookie-value".getBytes()));

        // when
        CapiAuthenticationCookie cookie = cutter.cutExisting(existing);

        // then
        assertThat(cookie.getMaxAge()).isEqualTo(-1);
        assertThat(cookie.getValueOpt()).isEqualTo(Optional.of("cookie-value"));
    }
}
