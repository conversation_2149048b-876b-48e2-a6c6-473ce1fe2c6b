package com.gumtree.web.cookie.cutters.appbanner;

import com.gumtree.web.cookie.CookieResolver;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;

import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper.Action.REPLY_SUCCESS;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.initMocks;

@RunWith(MockitoJUnitRunner.class)
public class AppBannerCookieHelperTest {

    private AppBannerCookieHelper appBannerCookieHelper;

    @Mock
    private CookieResolver cookieResolver;
    @Mock
    private HttpServletRequest request;

    @Before
    public void setUp() throws Exception {
        initMocks(this);
        appBannerCookieHelper = new AppBannerCookieHelper(cookieResolver);
    }

    @Test
    public void whenAppBannerVisibleAndUserActionThenCampaignTypeChanges() {

        AppBannerCookie appBannerCookie = mock(AppBannerCookie.class);

        when(cookieResolver.resolve(request, AppBannerCookie.class)).thenReturn(appBannerCookie);
        when(appBannerCookie.isVisible()).thenReturn(true);

        // When: perform reply action and app banner visible
        appBannerCookieHelper.updateAppBannerCookie(REPLY_SUCCESS, request);

        // Then: campaign type is updated to reply success
        verify(appBannerCookie, times(1)).isVisible();
        verify(appBannerCookie, times(1)).setActionTriggered(REPLY_SUCCESS.getCampaignType());
    }

    @Test
    public void whenAppBannerNotVisibleAndUserActionThenCampaignTypeUnchanged() {

        AppBannerCookie appBannerCookie = mock(AppBannerCookie.class);

        // Given: original cookie action was post ad complete
        when(cookieResolver.resolve(request, AppBannerCookie.class)).thenReturn(appBannerCookie);
        when(appBannerCookie.isVisible()).thenReturn(false);

        // When: perform reply action and app banner visible
        appBannerCookieHelper.updateAppBannerCookie(REPLY_SUCCESS, request);

        // Then: campaign type is updated to reply success
        verify(appBannerCookie, times(1)).getAppBannerVisible();
        verify(appBannerCookie, times(1)).isVisible();
        verifyNoMoreInteractions(appBannerCookie);
    }

}