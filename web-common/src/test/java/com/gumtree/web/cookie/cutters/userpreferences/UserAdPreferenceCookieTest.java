package com.gumtree.web.cookie.cutters.userpreferences;

import com.google.common.collect.Maps;
import com.gumtree.web.cookie.CookieSerializer;
import org.junit.Test;
import static org.fest.assertions.api.Assertions.assertThat;
import java.util.Map;

public class UserAdPreferenceCookieTest {

    @Test
    public void resetConsentModificationFlag() {
        assertThat(resetConsentMod(cookie(false, false)).isConsentModified())
                .as("none is mod -> consent")
                .isFalse();
        assertThat(resetConsentMod(cookie(false, false)).isConsentStringModified())
                .as("none is mod - string consent")
                .isFalse();

        assertThat(resetConsentMod(cookie(true, false)).isConsentModified())
                .as("consent is mod, string is not mod -> consent")
                .isFalse();
        assertThat(resetConsentMod(cookie(true, false)).isConsentStringModified())
                .as("consent is mod, string is not mod-> str consent")
                .isFalse();

        assertThat(resetConsentMod(cookie(false, true)).isConsentModified())
                .as("consent is not mod, string is mod -> consent")
                .isFalse();
        assertThat(resetConsentMod(cookie(false, true)).isConsentStringModified())
                .as("consent is not mod, string is mod -> str consent")
                .isTrue();

        assertThat(resetConsentMod(cookie(true, true)).isConsentModified())
                .as("consent is mod, string is mod -> consent")
                .isFalse();
        assertThat(resetConsentMod(cookie(true, true)).isConsentStringModified())
                .as("consent is mod, string is mod -> str consent")
                .isTrue();
    }

    @Test
    public void resetStringConsentModificationFlag() {
        assertThat(resetStringConsentMod(cookie(false, false)).isConsentModified())
                .as("none is mod -> consent")
                .isFalse();
        assertThat(resetStringConsentMod(cookie(false, false)).isConsentStringModified())
                .as("none is mod - string consent")
                .isFalse();

        assertThat(resetStringConsentMod(cookie(true, false)).isConsentModified())
                .as("consent is mod, string is not mod -> consent")
                .isTrue();
        assertThat(resetStringConsentMod(cookie(true, false)).isConsentStringModified())
                .as("consent is mod, string is not mod-> str consent")
                .isFalse();

        assertThat(resetStringConsentMod(cookie(false, true)).isConsentModified())
                .as("consent is not mod, string is mod -> consent")
                .isFalse();
        assertThat(resetStringConsentMod(cookie(false, true)).isConsentStringModified())
                .as("consent is not mod, string is mod -> str consent")
                .isFalse();

        assertThat(resetStringConsentMod(cookie(true, true)).isConsentModified())
                .as("consent is mod, string is mod -> consent")
                .isTrue();
        assertThat(resetStringConsentMod(cookie(true, true)).isConsentStringModified())
                .as("consent is mod, string is mod -> str consent")
                .isFalse();
    }



    /*
        utilities
     */

    private static UserAdPreferenceCookie resetConsentMod(UserAdPreferenceCookie cookie) {
        cookie.resetConsentModificationFlag();
        return cookie;
    }
    private static UserAdPreferenceCookie resetStringConsentMod(UserAdPreferenceCookie cookie) {
        cookie.resetStringConsentModificationFlag();
        return cookie;
    }

    private static UserAdPreferenceCookie cookie(boolean isConsentMod, boolean isStringConsentMod) {
        Map<String, String> vals = Maps.newHashMap();
        vals.put("state", "1");

        if (!isConsentMod && !isStringConsentMod) {
            vals.put("mod", "0");
        } else if (isConsentMod && !isStringConsentMod) {
            vals.put("mod", "1");
        } else if (!isConsentMod) {
            vals.put("mod", "2");
        } else {
            vals.put("mod", "3");
        }
        return new UserAdPreferenceCookie("wwww.gumtree.com", -1, "/", CookieSerializer.serializeCookieMap(vals));
    }
}