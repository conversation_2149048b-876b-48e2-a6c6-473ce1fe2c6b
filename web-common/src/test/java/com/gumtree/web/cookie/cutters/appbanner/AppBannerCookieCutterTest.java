package com.gumtree.web.cookie.cutters.appbanner;

import com.gumtree.web.cookie.BaseCookie;
import com.gumtree.web.cookie.CookieUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.Cookie;

import java.time.ZonedDateTime;

import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieCutter.MAX_AGE_COOKIE;
import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieCutter.PATH;
import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper.Action.REPLY_SUCCESS;
import static java.time.format.DateTimeFormatter.RFC_1123_DATE_TIME;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.Matchers.*;
import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.hamcrest.core.IsNull.nullValue;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AppBannerCookieCutterTest {

    private String domain = "dev.gumtree.com";

    @Test
    public void baseName() {
        AppBannerCookieCutter appBannerCookieCutter = new AppBannerCookieCutter("domain");
        assertThat(appBannerCookieCutter.getBaseName(), equalTo(AppBannerCookie.NAME));
    }

    @Test
    public void getSupportedCookieType() {
        assertThat(new AppBannerCookieCutter("domain").getSupportedCookieType(), equalTo(AppBannerCookie.class));
    }

    @Test
    public void cutNew() {
        AppBannerCookieCutter appBannerCookieCutter = new AppBannerCookieCutter(domain);
        AppBannerCookie appBannerCookie = appBannerCookieCutter.cutNew();
        assertThat(appBannerCookie.getDomain(), equalTo(domain));
        assertThat(appBannerCookie.getActionTriggered(), nullValue());
        assertThat(appBannerCookie.getName(), equalTo(AppBannerCookie.NAME));
        assertThat(appBannerCookie.getMaxAge(), equalTo(MAX_AGE_COOKIE));
        assertThat(appBannerCookie.isVisible(), is(false));
        assertThat(appBannerCookie.getExpiryDate(), nullValue());
    }

    @Test
    public void whenCutExistingAndCookieHasExpiryDateThenMaxAgeShouldReduceAndExpiryDateRemainsUnchanged() {

        // Given
        AppBannerCookieCutter appBannerCookieCutter = new AppBannerCookieCutter(domain);

        AppBannerCookie existingCookie = new AppBannerCookie(domain, MAX_AGE_COOKIE, PATH);
        existingCookie.setActionTriggered(REPLY_SUCCESS.getCampaignType());
        Cookie httpCookie = CookieUtils.createHttpCookie(AppBannerCookie.NAME, existingCookie, false);


        await().atMost(2, SECONDS)
                .until(() -> {
                    //  When
                    AppBannerCookie appBannerCookie = appBannerCookieCutter.cutExisting(httpCookie);

                    // Then
                    assertThat(appBannerCookie.isVisible(), is(true));
                    assertThat(appBannerCookie.getActionTriggered(), equalTo(REPLY_SUCCESS.getCampaignType()));
                    assertThat(appBannerCookie.getExpiryDate(), equalTo(existingCookie.getExpiryDate()));
                    assertThat(appBannerCookie.getMaxAge(), lessThan(existingCookie.getMaxAge()));
                    assertThat(appBannerCookie.getMaxAge(), greaterThan(existingCookie.getMaxAge() - 3 ));
                });
    }

    @Test
    public void whenCutExistingAndCookieHasNoExpiryDateThenDefaultCookieCut() {

        // Given
        AppBannerCookieCutter appBannerCookieCutter = new AppBannerCookieCutter(domain);

        BaseCookie existingCookie = mock(BaseCookie.class);
        when(existingCookie.getDomain()).thenReturn(domain);
        when(existingCookie.getMaxAge()).thenReturn(MAX_AGE_COOKIE);
        when(existingCookie.getPath()).thenReturn(PATH);
        when(existingCookie.isHttpOnly()).thenReturn(false);

        Cookie httpCookie = CookieUtils.createHttpCookie(AppBannerCookie.NAME, existingCookie, false);

        //  When
        AppBannerCookie appBannerCookie = appBannerCookieCutter.cutExisting(httpCookie);

        // Then
        assertThat(appBannerCookie.isVisible(), is(false));
        assertThat(appBannerCookie.getActionTriggered(), nullValue());

        assertThat(appBannerCookie.getMaxAge(), lessThanOrEqualTo(existingCookie.getMaxAge()));
    }

    private ZonedDateTime parseDate(String date) {
        return ZonedDateTime.parse(date, RFC_1123_DATE_TIME);
    }

}