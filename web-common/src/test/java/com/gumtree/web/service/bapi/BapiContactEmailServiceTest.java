package com.gumtree.web.service.bapi;

import com.google.common.base.Function;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.gumtree.api.ApiContactEmail;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.EmailStatus;
import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BapiContactEmailServiceTest {
    private static final BushfireApiKey BUSHFIRE_API_KEY = new BushfireApiKey("access-key", "private-key");

    @InjectMocks private BapiContactEmailService service;
    @Mock private BushfireApi bushfireApi;
    @Mock private UserApi userApi;

    private HystrixRequestContext context;

    @Before
    public void beforeEach() {
        context = HystrixRequestContext.initializeContext();
        when(bushfireApi.create(UserApi.class, BUSHFIRE_API_KEY)).thenReturn(userApi);
    }

    @After
    public void afterEach() {
        context.shutdown();
    }

    @Test
    public void shouldGetEditableContactEmailOrdered() {
        // given
        User user = User.builder().withId(1L).withApiKey(BUSHFIRE_API_KEY).build();
        ArrayList<ApiContactEmail> unorderedContactEmails = Lists.newArrayList(
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFICATION_SENT).withPreferred(false).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFICATION_SENT).withPreferred(false).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFIED).withPreferred(false).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFIED).withPreferred(true).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFIED).withPreferred(false).build()
        );

        // and
        when(userApi.getContactEmails(1l, Lists.newArrayList(EmailStatus.VERIFICATION_SENT, EmailStatus.VERIFIED))).thenReturn(unorderedContactEmails);

        // when
        Iterable<ApiContactEmail> contactEmails = service.getEditable(user);

        // then
        Iterable<String> emails = Iterables.transform(contactEmails, new Function<ApiContactEmail, String>() {
            @Override
            public String apply(ApiContactEmail input) {
                return input.getEmail();
            }
        });
        assertThat(Lists.newArrayList(emails)).isEqualTo(Lists.newArrayList(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        ));
    }

    @Test
    public void shouldGetEmailsForPostingOrdered() {
        // given
        User user = User.builder().withId(1L).withApiKey(BUSHFIRE_API_KEY).build();
        ArrayList<ApiContactEmail> unorderedContactEmails = Lists.newArrayList(
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFICATION_SENT).withPreferred(false).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFICATION_SENT).withPreferred(false).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFIED).withPreferred(false).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFIED).withPreferred(true).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFIED).withPreferred(false).build()
        );

        // and
        when(userApi.getContactEmails(1l, Lists.newArrayList(EmailStatus.VERIFIED, EmailStatus.VERIFICATION_SENT))).thenReturn(unorderedContactEmails);

        // when
        Iterable<String> emailsForPosting = service.getForPosting(user);

        // then
        assertThat(Lists.newArrayList(emailsForPosting)).isEqualTo(Lists.newArrayList(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        ));
    }

    @Test
    public void shouldGetEmailsForReplyOrdered() {
        // given
        User user = User.builder().withId(1L).withApiKey(BUSHFIRE_API_KEY).build();
        ArrayList<ApiContactEmail> unorderedContactEmails = Lists.newArrayList(
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFICATION_SENT).withPreferred(false).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFICATION_SENT).withPreferred(false).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFIED).withPreferred(false).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFIED).withPreferred(true).build(),
                ApiContactEmail.builder().withEmail("<EMAIL>").withStatus(EmailStatus.VERIFIED).withPreferred(false).build()
        );

        // and
        when(userApi.getContactEmails(1l, Lists.newArrayList(EmailStatus.VERIFIED, EmailStatus.VERIFICATION_SENT))).thenReturn(unorderedContactEmails);

        // when
        Iterable<String> emailsForPosting = service.getForReply(user);

        // then
        assertThat(Lists.newArrayList(emailsForPosting)).isEqualTo(Lists.newArrayList(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        ));
    }
}