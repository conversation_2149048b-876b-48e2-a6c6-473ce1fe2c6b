package com.gumtree.config.session;

import com.google.common.collect.Lists;
import org.junit.Test;

import java.net.InetSocketAddress;

import static org.fest.assertions.api.Assertions.assertThat;

public class CassandraConfigTest {

    @Test
    public void convertServersConfigToContactPointsNoPort() {
        InetSocketAddress address = new InetSocketAddress("host", 9042);
        assertThat(CassandraConfig.convertServersConfigToContactPoints(" host")).isEqualTo(Lists.newArrayList(address));
    }

    @Test
    public void convertServersConfigToContactPointsWithPort() {
        InetSocketAddress address = new InetSocketAddress("host", 1234);
        assertThat(CassandraConfig.convertServersConfigToContactPoints("host:1234 ")).isEqualTo(Lists.newArrayList(address));
    }

    @Test
    public void convertServersConfigToContactPointsMixedPortConfig() {
        InetSocketAddress address1 = new InetSocketAddress("host1", 9042);
        InetSocketAddress address2 = new InetSocketAddress("host2", 1234);
        assertThat(CassandraConfig.convertServersConfigToContactPoints(" host1, host2:1234 ")).isEqualTo(
                Lists.newArrayList(address1, address2));
    }

    @Test(expected = IllegalArgumentException.class)
    public void convertServersConfigToContactPointsIncorrectConfig() {
        CassandraConfig.convertServersConfigToContactPoints("host:1234:12");
    }
}