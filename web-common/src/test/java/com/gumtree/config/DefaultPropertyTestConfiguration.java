package com.gumtree.config;

import com.netflix.config.ConfigurationManager;
import java.util.Properties;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;

// Config file which configures dynamic properties for unit tests
public class DefaultPropertyTestConfiguration implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        Properties properties = new Properties();
        properties.setProperty("gumtree.messagecentre.enabled", "true");
        properties.setProperty("gumtree.url.seller.base_uri", "https://my.gumtree.com");
        properties.setProperty("gumtree.messagecentre.link.enabled", "true");
        ConfigurationManager.loadProperties(properties);
    }
}
