package com.gumtree.mobile.web.category;

import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.fest.assertions.api.Assertions.assertThat;

public class BrowseCategoryTest {


    @Test
    public void testConstructor() {
        Category carsCategory = new Category(CategoryConstants.CARS_ID, "cars", "Cars");
        Category bmwCategory = new Category(10303L, "bmw", "Cars");
        bmwCategory.setHidden(true);
        Category audiCategory = new Category(10306L, "audi", "Cars");
        audiCategory.setHidden(true);

        carsCategory.addChildren(audiCategory, bmwCategory);

        List<BrowseCategory> childrenItems = new ArrayList<>();
        childrenItems.add(new BrowseCategory(bmwCategory, null, false));
        childrenItems.add(new BrowseCategory(audiCategory, null, false));

        boolean selected = true;
        BrowseCategory browseCategory = new BrowseCategory(carsCategory, childrenItems, selected);
        assertThat(browseCategory.getChildren()).isFalse();

        assertThat(browseCategory.getId()).isEqualTo(carsCategory.getId());
        assertThat(browseCategory.getName()).isEqualTo(carsCategory.getName());
        assertThat(browseCategory.getSeoName()).isEqualTo(carsCategory.getSeoName());
    }


}
