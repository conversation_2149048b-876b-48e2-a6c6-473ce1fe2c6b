package com.gumtree.mobile.web.seo.metadata;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.List;
import java.util.Set;

import static com.gumtree.mobile.web.seo.metadata.SEOMetadata.SEO_DESCRIPTION;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadata.SEO_H1;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadata.SEO_TITLE;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.CATEGORY;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.LOCATION;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.SEARCH_TERM;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultSeoMetadataFactoryTest {
    private static final Category ALL = Category.newCategory().withId(1L).withSeoName("all").withSeoDisplayName("all").build();

    @InjectMocks private DefaultSeoMetadataFactory factory;

    @Mock private CategoryModel categoryModel;
    @Mock private CategorySeoConfig categorySeoConfig;

    @Test
    public void nonRootLocationPlaceholderShouldBeReplacedWithLocationDisplayName() {
        // given
        when(categoryModel.getFullPath(ALL.getId())).thenReturn(Lists.newArrayList(ALL));

        List<String> categoryPath = Lists.newArrayList(ALL.getSeoName());
        Set<String> pageId = Sets.newHashSet(LOCATION.getKey(), CATEGORY.getKey());
        when(categorySeoConfig.getPageConfig(categoryPath, pageId))
                .thenReturn(ImmutableMap.of(SEO_TITLE, "Ads [Location]", SEO_DESCRIPTION, "Ads [Location]. #1 Site"));

        // when
        SeoMetadataProps props = new SeoMetadataProps(ALL).withLocation("London");
        SEOMetadata metadata = factory.newInstance(props);

        // then
        assertThat(metadata.getTitle()).isEqualTo("Ads London");
        assertThat(metadata.getDescription()).isEqualTo("Ads London. #1 Site");
    }

    @Test
    public void nonRootInLocationPlaceholderShouldBeReplacedWithLocationDisplayName() {
        // given
        when(categoryModel.getFullPath(ALL.getId())).thenReturn(Lists.newArrayList(ALL));

        List<String> categoryPath = Lists.newArrayList(ALL.getSeoName());
        Set<String> pageId = Sets.newHashSet(LOCATION.getKey(), CATEGORY.getKey());
        when(categorySeoConfig.getPageConfig(categoryPath, pageId))
                .thenReturn(ImmutableMap.of(SEO_TITLE, "Ads [In-Location]", SEO_DESCRIPTION, "Ads [In-Location]. #1 Site"));

        // when
        SeoMetadataProps props = new SeoMetadataProps(ALL).withLocation("London");
        SEOMetadata metadata = factory.newInstance(props);

        // then
        assertThat(metadata.getTitle()).isEqualTo("Ads in London");
        assertThat(metadata.getDescription()).isEqualTo("Ads in London. #1 Site");
    }

    @Test
    public void unitedKingdomLocationPlaceholdersShouldBeReplacedWithBlank() {
        // given
        when(categoryModel.getFullPath(ALL.getId())).thenReturn(Lists.newArrayList(ALL));

        List<String> categoryPath = Lists.newArrayList(ALL.getSeoName());
        Set<String> pageId = Sets.newHashSet(LOCATION.getKey(), CATEGORY.getKey());
        when(categorySeoConfig.getPageConfig(categoryPath, pageId))
                .thenReturn(ImmutableMap.of(SEO_TITLE, "Ads [Location]", SEO_DESCRIPTION, "Ads [In-Location]. #1 Site"));

        // when
        SeoMetadataProps props = new SeoMetadataProps(ALL).withLocation("United Kingdom");
        SEOMetadata metadata = factory.newInstance(props);

        // then
        assertThat(metadata.getTitle()).isEqualTo("Ads");
        assertThat(metadata.getDescription()).isEqualTo("Ads . #1 Site");
    }

    @Test
    public void shouldNotHtmlEscapeSearchKeyword() {
        // given
        when(categoryModel.getFullPath(ALL.getId())).thenReturn(Lists.newArrayList(ALL));

        List<String> categoryPath = Lists.newArrayList(ALL.getSeoName());
        Set<String> pageId = Sets.newHashSet(LOCATION.getKey(), SEARCH_TERM.getKey(), CATEGORY.getKey());
        when(categorySeoConfig.getPageConfig(categoryPath, pageId))
                .thenReturn(ImmutableMap.of(SEO_H1, "[Ad-count] ads for [Search-term] in [Category] [In-Location]", SEO_TITLE, "", SEO_DESCRIPTION, ""));

        // when
        SeoMetadataProps props = new SeoMetadataProps(ALL)
                .withAdCount(100)
                .withLocation("United Kingdom")
                .withSearchTerm("¯\\_(ツ)_/¯");

        SEOMetadata metadata = factory.newInstance(props);

        // then
        assertThat(metadata.getH1()).isEqualTo("100 ads for ¯\\_(ツ)_/¯ in all");
    }

    @Test
    public void shouldNotHtmlEscapeSearchLocation() {
        // given
        when(categoryModel.getFullPath(ALL.getId())).thenReturn(Lists.newArrayList(ALL));

        List<String> categoryPath = Lists.newArrayList(ALL.getSeoName());
        Set<String> pageId = Sets.newHashSet(LOCATION.getKey(), CATEGORY.getKey());
        when(categorySeoConfig.getPageConfig(categoryPath, pageId))
                .thenReturn(ImmutableMap.of(SEO_H1, "[Ad-count] ads for Sale in [Category] [In-Location]", SEO_TITLE, "", SEO_DESCRIPTION, ""));

        // when
        SeoMetadataProps props = new SeoMetadataProps(ALL)
                .withAdCount(100)
                .withLocation("¯\\_(ツ)_/¯");

        SEOMetadata metadata = factory.newInstance(props);

        // then
        assertThat(metadata.getH1()).isEqualTo("100 ads for Sale in all in ¯\\_(ツ)_/¯");
    }
}