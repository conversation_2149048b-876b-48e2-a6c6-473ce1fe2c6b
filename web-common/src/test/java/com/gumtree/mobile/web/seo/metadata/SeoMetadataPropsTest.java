package com.gumtree.mobile.web.seo.metadata;

import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.Category;
import org.junit.Test;

import static com.google.common.collect.ImmutableMap.of;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.IN_LOCATION;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.LOCATION;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.TOP_CAR_MAKES;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.TOP_CAR_MODELS;
import static com.gumtree.mobile.web.seo.metadata.SeoMetadataProps.buildTopMakes;
import static com.gumtree.mobile.web.seo.metadata.SeoMetadataProps.buildTopModels;
import static org.fest.assertions.api.Assertions.assertThat;

public class SeoMetadataPropsTest {
    private static final Category ALL = Category.newCategory().withId(1L).withSeoName("all").build();

    @Test
    public void locationPropSupportsTwoPlaceholders() {
        // when
        SeoMetadataProps props = new SeoMetadataProps(ALL).withLocation("London");

        // then
        assertThat(props.getProperties()).isEqualTo(of(LOCATION, "London", IN_LOCATION, "in London", TOP_CAR_MAKES, "", TOP_CAR_MODELS, ""));
    }

    @Test
    public void unitedKingdomLocationShouldCreateBlankReplacements() {
        // when
        SeoMetadataProps props = new SeoMetadataProps(ALL).withLocation("United Kingdom");

        // then
        assertThat(props.getProperties()).isEqualTo(of(LOCATION, "", IN_LOCATION, "", TOP_CAR_MAKES, "", TOP_CAR_MODELS, ""));
    }

    @Test
    public void searchTermPropSupportsTwoPlaceholders() {
        // when
        SeoMetadataProps props = new SeoMetadataProps(ALL).withSearchTerm("cheap taBle");

        // then
        assertThat(props.getProperties()).isEqualTo(of(SEOMetadataProperty.SEARCH_TERM, "cheap taBle", SEOMetadataProperty.SEARCH_TERM_CAPITALIZED, "Cheap taBle", TOP_CAR_MAKES, "", TOP_CAR_MODELS, ""));
    }

    @Test
    public void buildTopMakesProperty() {
        assertThat(buildTopMakes(null)).isEqualTo(null);
        assertThat(buildTopMakes(Lists.newArrayList()));
        assertThat(buildTopMakes(Lists.newArrayList("Audi"))).isEqualTo(" Search by used car makes Audi and more.");
        assertThat(buildTopMakes(Lists.newArrayList("Audi", "BMW"))).isEqualTo(" Search by used car makes Audi, BMW and more.");
    }

    @Test
    public void buildTopModelsProperty() {
        assertThat(buildTopModels(null)).isEqualTo(null);
        assertThat(buildTopModels(Lists.newArrayList())).isEqualTo(null);
        assertThat(buildTopModels(Lists.newArrayList("A3"))).isEqualTo(" A3");
        assertThat(buildTopModels(Lists.newArrayList("A3", "A4"))).isEqualTo(" A3, A4");
    }
}