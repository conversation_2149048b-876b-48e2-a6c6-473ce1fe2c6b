package com.gumtree.mobile.web.vip;

import org.junit.Test;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

public class VIPPathTest {

    @Test
    public void validVIPPath() {
        VIPPath vipPath = new VIPPath("cars", "this is the ad & it has sóme cr@zy chars! £99.00", 123L);

        assertThat(vipPath.getPath(), is("/p/cars/this-is-the-ad-it-has-s-me-cr-zy-chars-£99.00/123"));
        assertThat(vipPath.getPathSegment(), is("/p/cars/this-is-the-ad-it-has-s-me-cr-zy-chars-£99.00/123"));
    }

    @Test
    public void validVIPPathWithSpecialLetters() {
        VIPPath vipPath = new VIPPath("cars", "Cafe & Crêperie leasehold for sale.", 123L);

        assertThat(vipPath.getPath(), is("/p/cars/cafe-cr-perie-leasehold-for-sale./123"));
    }

    @Test
    public void validVIPPathWithSpashInTitle() {
        VIPPath vipPath = new VIPPath("cars", "House cleaning for £4 p/h", 123L);

        assertThat(vipPath.getPath(), is("/p/cars/house-cleaning-for-£4-p-h/123"));
    }

    @Test
    public void validVIPPathWithReportAdSuccessParamEqualTrue() {
        VIPPath vipPath = new VIPPath("cars", "title", 123L).addReportAdSuccessParam(true);

        assertThat(vipPath.getPath(), is("/p/cars/title/123?reportAdSuccess=true"));
        assertThat(vipPath.getPathSegment(), is("/p/cars/title/123"));
    }

    @Test
    public void validVIPPathWithReportAdSuccessParamEqualFalse() {
        VIPPath vipPath = new VIPPath("cars", "title", 123L).addReportAdSuccessParam(false);

        assertThat(vipPath.getPath(), is("/p/cars/title/123?reportAdSuccess=false"));
    }

    @Test
    public void validVIPPathWithMakeOfferSuccessParamEqualTrue() {
        VIPPath vipPath = new VIPPath("cars", "title", 123L).addMakeOfferSuccessParam(true);

        assertThat(vipPath.getPath(), is("/p/cars/title/123?makeOfferSuccess=true"));
    }

    @Test
    public void validVIPPathWithMakeOfferSuccessParamEqualFalse() {
        VIPPath vipPath = new VIPPath("cars", "title", 123L).addMakeOfferSuccessParam(false);

        assertThat(vipPath.getPath(), is("/p/cars/title/123?makeOfferSuccess=false"));
    }

    @Test
    public void validVIPPathWithMakeOfferSuccessParamEqualNull() {
        VIPPath vipPath = new VIPPath("cars", "title", 123L).addMakeOfferSuccessParam(null);

        assertThat(vipPath.getPath(), is("/p/cars/title/123"));
    }

}
