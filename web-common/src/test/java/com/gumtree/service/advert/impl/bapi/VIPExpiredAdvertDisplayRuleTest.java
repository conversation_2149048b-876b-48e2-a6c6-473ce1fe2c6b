package com.gumtree.service.advert.impl.bapi;

import com.gumtree.domain.advert.AdvertStatus;
import com.gumtree.domain.advert.entity.AdvertEntity;
import org.junit.Test;

import java.util.Date;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

public class VIPExpiredAdvertDisplayRuleTest {

    private VIPExpiredAdvertDisplayRule vipAdDisplayRule = new VIPExpiredAdvertDisplayRule();

    @Test
    public void shouldAllowDisplayWhenAdvertIsFullExpiredAndHasLiveDate() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setStatus(AdvertStatus.EXPIRED);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(true));
    }

    @Test
    public void shouldNotAllowDisplayWhenExpiredAdvertDoesNotHaveLiveDate() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setStatus(AdvertStatus.EXPIRED);
        advert.setLiveDate(null);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(false));
    }

    @Test
    public void shouldNotAllowDisplayWhenAdvertIsLive() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setStatus(AdvertStatus.LIVE);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(false));
    }

    @Test
    public void shouldNotAllowDisplayWhenAdvertIsPrePublished() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setStatus(AdvertStatus.PRE_PUBLISHED);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(false));
    }

    private AdvertEntity fullExpiredAdvert() {
        AdvertEntity advertEntity = new AdvertEntity();
        advertEntity.setStatus(AdvertStatus.EXPIRED);
        advertEntity.setTitle("advert title");
        advertEntity.setDescription("advert description");
        advertEntity.setId(12345L);
        advertEntity.setCategoryId(12345L);
        advertEntity.setLocationId(12345);
        advertEntity.setLiveDate(new Date());
        return advertEntity;
    }

    @Test
    public void shouldNotAllowDisplayIfAdvertDoesNotHaveATitle() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setTitle(null);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(false));
    }

    @Test
    public void shouldNotAllowDisplayIfAdvertDoesNotHaveAnId() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setId(null);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(false));
    }

    @Test
    public void shouldNotAllowDisplayIfAdvertDoesNotHaveADescription() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setDescription(null);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(false));
    }

    @Test
    public void shouldNotAllowDisplayIfAdvertDoesNotHaveAStatus() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setStatus(null);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(false));
    }

    @Test
    public void shouldNotAllowDisplayIfAdvertDoesNotHaveACategoryId() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setCategoryId(null);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(false));
    }

    @Test
    public void shouldNotAllowDisplayIfAdvertDoesNotHaveALocationId() {
        AdvertEntity advert = fullExpiredAdvert();
        advert.setLocationId(null);
        assertThat(vipAdDisplayRule.shouldDisplay(advert), is(false));
    }
}
