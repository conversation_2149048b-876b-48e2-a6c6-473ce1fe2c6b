package com.gumtree.service.advert.impl.bapi;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.client.StubBushfireApi;
import com.gumtree.domain.advert.BasicAdvert;
import com.gumtree.service.advert.ExpiredAdvertNotFoundException;
import com.gumtree.util.stub.api.StubAdvertApi;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;

import static com.gumtree.api.ApiAdBuilder.advert;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

public class BAPIExpiredAdvertServiceTest {

    private BAPIExpiredAdvertService bapiExpiredAdvertService;
    private StubAdvertApi stubAdvertApi;
    private ApiAdToBasicAdvertConverter apiAdToAdvertConverter;
    private VIPExpiredAdvertDisplayRule vipAdDisplayRule;

    @Before
    public void setUp() throws Exception {
        stubAdvertApi = new StubAdvertApi();
        StubBushfireApi stubBushfireApi = new StubBushfireApi();
        stubBushfireApi.setAdvertApi(stubAdvertApi);
        apiAdToAdvertConverter = new ApiAdToBasicAdvertConverter(new ApiAdvertStatusToAdvertStatusConverter());
        vipAdDisplayRule = new VIPExpiredAdvertDisplayRule();
        bapiExpiredAdvertService = new BAPIExpiredAdvertService(stubBushfireApi, apiAdToAdvertConverter, vipAdDisplayRule);

    }

    @Test
    public void shouldReturnAnAdFromBapi() {
        //given
        Ad advertFromBapi = advert().with().id(12345L).description("ad description").title("ad title").status(AdStatus.EXPIRED).build();
        given(bapiReturns(advertFromBapi));
        BasicAdvert retrievedAdvert = bapiExpiredAdvertService.getAdvert(12345L);
        assertThat(retrievedAdvert.getId(), equalTo(advertFromBapi.getId()));
        assertThat(retrievedAdvert.getDescription(), equalTo(advertFromBapi.getDescription()));
        assertThat(retrievedAdvert.getTitle(), equalTo(advertFromBapi.getTitle()));
        assertThat(retrievedAdvert.getStatus().toString(), equalTo(advertFromBapi.getStatus().toString()));
    }

    @Test(expected = ExpiredAdvertNotFoundException.class)
    public void shouldThrowExceptionIfAdvertDoesNotExistInBapi() {
        //given
        bapiExpiredAdvertService.getAdvert(12L);
    }


    @Test(expected = ExpiredAdvertNotFoundException.class)
    public void shouldReturnAnArchivedAdFromBapi() {
        //given
        Ad advertFromBapi = advert().with().id(12345L)
                .description("ad description").title("ad title")
                .status(AdStatus.EXPIRED)
                .expiredDate(new DateTime().minusDays(1))
                .build();
        given(bapiReturns(advertFromBapi));
        bapiExpiredAdvertService.getAdvert(12345L);
    }

    private boolean bapiReturns(Ad advert) {
        stubAdvertApi.addAdvert(advert);
        return false;
    }

    private void given(boolean ignore) {

    }

}
