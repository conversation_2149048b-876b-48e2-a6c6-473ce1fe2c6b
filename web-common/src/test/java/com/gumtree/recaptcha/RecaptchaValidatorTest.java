package com.gumtree.recaptcha;

import com.gumtree.common.properties.GtPropManager;
import com.gumtree.common.util.http.GumtreeHttpClient;
import com.gumtree.common.util.http.HttpClientFactory;
import com.gumtree.common.util.http.JsonHttpClient;
import com.gumtree.config.CommonProperty;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.HttpPost;
import org.codehaus.jackson.map.ObjectMapper;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

@RunWith(MockitoJUnitRunner.class)
public class RecaptchaValidatorTest {

    @Mock
    GumtreeHttpClient mockClient;

    @Before
    public void setUp() {
        Mockito.when(mockClient.post(Mockito.any(),
                Mockito.eq(RecaptchaApiResponse.class))).thenAnswer(
                r -> {
                    HttpEntity entity = ((HttpPost)r.getArguments()[0]).getEntity();
                    String request = IOUtils.toString(entity.getContent());
                    RecaptchaApiResponse response = new RecaptchaApiResponse();
                    if (request.contains("errorResponse")) {
                        response.setSuccess(false);
                        response.setErrorCodes(new String[] { "invalid-input-secret" });
                        return response;
                    } else if (request.contains("invalidResponse")) {
                        response.setSuccess(false);
                        response.setErrorCodes(new String[]{"invalid-input-response"});
                        return response;
                    } else if (request.contains("alreadyVerifiedResponse")) {
                        response.setSuccess(false);
                        response.setErrorCodes(new String[] { "timeout-or-duplicate" });
                        return response;
                    } else if (request.contains("throwError")) {
                        throw new RuntimeException("Exception thrown");
                    } else {
                        response.setSuccess(true);
                        return response;
                    }
                }
        );
    }

    @Ignore
    @Test
    public void testConnectionWithRealSerivce() {

        //given
        GtPropManager.setProperty(CommonProperty.RECAPTCHA_SECRET.getPropertyName(), "6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe");
        JsonHttpClient httpClient = new JsonHttpClient(new HttpClientFactory().create(1000, 1000), new ObjectMapper());

        //when
        RecaptchaValidator recaptchaValidator = new RecaptchaValidator(httpClient);

        //then
        assertThat(
                recaptchaValidator.validateResponse("myResponse", "127.0.0.1"),
                equalTo(RecaptchaValidationResult.INVALID)
        );
    }


    @Test
    public void shouldReturnErrorInCaseOfConnectionProblems() {
        //given
        GtPropManager.setProperty(CommonProperty.RECAPTCHA_SECRET.getPropertyName(), "mySecret");

        //when
        RecaptchaValidator recaptchaValidator = new RecaptchaValidator(mockClient);

        //then
        assertThat(
                recaptchaValidator.validateResponse("throwError", "127.0.0.1"),
                equalTo(RecaptchaValidationResult.ERROR)
        );
    }

    @Test
    public void shouldReturnErrorInCaseOfErrorInResponse() {
        //given
        GtPropManager.setProperty(CommonProperty.RECAPTCHA_SECRET.getPropertyName(), "mySecret");

        //when
        RecaptchaValidator recaptchaValidator = new RecaptchaValidator(mockClient);

        //then
        assertThat(
                recaptchaValidator.validateResponse("errorResponse", "127.0.0.1"),
                equalTo(RecaptchaValidationResult.ERROR)
        );
    }

    @Test
    public void shouldReturnInvalidInCaseValidationDidntSucceed() {
        //given
        GtPropManager.setProperty(CommonProperty.RECAPTCHA_SECRET.getPropertyName(), "6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe");

        //when
        RecaptchaValidator recaptchaValidator = new RecaptchaValidator(mockClient);

        //then
        assertThat(
                recaptchaValidator.validateResponse("invalidResponse", "127.0.0.1"),
                equalTo(RecaptchaValidationResult.INVALID)
        );
    }

    @Test
    public void shouldReturnInvalidInCaseResponseHasAlreadyBeenVerified() {
        //given
        GtPropManager.setProperty(CommonProperty.RECAPTCHA_SECRET.getPropertyName(), "6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe");

        //when
        RecaptchaValidator recaptchaValidator = new RecaptchaValidator(mockClient);

        //then
        assertThat(
                recaptchaValidator.validateResponse("alreadyVerifiedResponse", "127.0.0.1"),
                equalTo(RecaptchaValidationResult.INVALID)
        );
    }

    @Test
    public void shouldReturnValidInCaseValidationSucceeded() {
        //given
        GtPropManager.setProperty(CommonProperty.RECAPTCHA_SECRET.getPropertyName(), "6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe");

        //when
        RecaptchaValidator recaptchaValidator = new RecaptchaValidator(mockClient);

        //then
        assertThat(
                recaptchaValidator.validateResponse("myResponse", "127.0.0.1"),
                equalTo(RecaptchaValidationResult.VALID)
        );
    }

}