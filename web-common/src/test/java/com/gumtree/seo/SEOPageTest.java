package com.gumtree.seo;

import com.google.common.collect.ImmutableMap;
import com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty;
import org.junit.Test;

import static com.gumtree.seo.SEOPage.Builder.replacePlaceHolders;
import static org.fest.assertions.api.Assertions.assertThat;

public class SEOPageTest {

    @Test
    public void shouldReplacePlaceholder() {
        // given
        ImmutableMap<String, String> placeholderValues = ImmutableMap.<String, String>builder()
                .put(SEOMetadataProperty.PROPERTY_TYPE.getPlaceHolder(), "Studio")
                .put(SEOMetadataProperty.IN_LOCATION.getPlaceHolder(), "in London")
                .build();

        // when
        String processedText = replacePlaceHolders("[property_type] to Rent [In-Location] - Gumtree", placeholderValues);

        // then
        assertThat(processedText).isEqualTo("Studio to Rent in London - Gumtree");
    }

    @Test
    public void shouldRemovePlaceholdersWithoutValue() {
        // given
        ImmutableMap<String, String> placeholderValues = ImmutableMap.<String, String>builder()
                .put(SEOMetadataProperty.PROPERTY_TYPE.getPlaceHolder(), "Studio")
                .put(SEOMetadataProperty.IN_LOCATION.getPlaceHolder(), "in London")
                .build();

        // when
        String processedText = replacePlaceHolders("[property_number_beds] [property_type] to Rent [In-Location] - Gumtree", placeholderValues);

        // then
        assertThat(processedText).isEqualTo("Studio to Rent in London - Gumtree");
    }
}