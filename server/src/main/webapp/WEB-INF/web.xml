<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">

    <display-name>Gumtree Seller Website</display-name>

    <context-param>
        <param-name>sessionCookieName</param-name>
        <param-value>GTSELLERSESSIONID</param-value>
    </context-param>
    <!-- Configure ContextLoaderListener to use AnnotationConfigWebApplicationContext
           instead of the default XmlWebApplicationContext -->
    <context-param>
        <param-name>contextClass</param-name>
        <param-value>
            org.springframework.web.context.support.AnnotationConfigWebApplicationContext
        </param-value>
    </context-param>

    <listener>
        <listener-class>com.gumtree.config.session.SessionConfigListener</listener-class>
    </listener>

    <!-- Configuration locations must consist of one or more comma- or space-delimited
       fully-qualified @Configuration classes. Fully-qualified packages may also be
       specified for component-scanning -->
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>com.gumtree.config.SellerConfig</param-value>
    </context-param>

    <!-- Bootstrap the root application context as usual using ContextLoaderListener -->
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>

    <servlet>
        <description>Hystrix Stream servlet</description>
        <display-name>HystrixMetricsStreamServlet</display-name>
        <servlet-name>HystrixMetricsStreamServlet</servlet-name>
        <servlet-class>com.netflix.hystrix.contrib.metrics.eventstream.HystrixMetricsStreamServlet</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>HystrixMetricsStreamServlet</servlet-name>
        <url-pattern>/internal/hystrix.stream</url-pattern>
    </servlet-mapping>

    <context-param>
        <param-name>contextInitializerClasses</param-name>
        <param-value>com.gumtree.config.SellerApplicationContextInitializer</param-value>
    </context-param>

    <filter>
        <filter-name>requestTimingFilter</filter-name>
        <filter-class>com.gumtree.config.filters.RequestTimingFilter</filter-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>com.gumtree.config.servlet.SellerServletConfig</param-value>
        </init-param>
    </filter>

    <filter-mapping>
        <filter-name>requestTimingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!--Make Sure ALL Requests have UTF-8 encoding - MUST BE FIRST FILTER ALWAYS-->
    <filter>
        <filter-name>charsetFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>

    <filter-mapping>
        <filter-name>charsetFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>clickjackingFilter</filter-name>
        <filter-class>com.gumtree.web.filter.ClickjackingFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>clickjackingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <display-name>HystrixRequestContextServletFilter</display-name>
        <filter-name>HystrixRequestContextServletFilter</filter-name>
        <filter-class>com.gumtree.config.filters.HystrixRequestContextFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>HystrixRequestContextServletFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>instrumentedFilter</filter-name>
        <filter-class>com.gumtree.metrics.filter.GumtreeInstrumentedFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>instrumentedFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>globalErrorHandler</filter-name>
        <filter-class>com.gumtree.web.filter.GlobalErrorHandlerFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>globalErrorHandler</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- This is required to allow the PayPalIPNFilter to access request-scoped beans -->
    <filter>
        <filter-name>requestContextFilter</filter-name>
        <filter-class>org.springframework.web.filter.RequestContextFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>requestContextFilter</filter-name>
        <!-- The /* in the url-pattern ensures that trailing slashes are handled correctly by the IPN filter -->
        <url-pattern>/payment/ipn/*</url-pattern>
    </filter-mapping>

    <!-- Grab the request body for PayPal IPN callbacks -->
    <!-- THIS MUST HAPPEN BEFORE ANYTHING ELSE READS THE REQUEST INPUT STREAM -->
    <filter>
        <filter-name>payPalIPNFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>payPalIPNFilter</filter-name>
        <!-- The /* in the url-pattern ensures that trailing slashes are handled correctly by the IPN filter -->
        <url-pattern>/payment/ipn/*</url-pattern>
    </filter-mapping>

    <!-- Grab the request body for PostAd callbacks -->
    <!-- THIS MUST HAPPEN BEFORE ANYTHING ELSE READS THE REQUEST INPUT STREAM -->
    <filter>
        <filter-name>sellerCorsFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>sellerCorsFilter</filter-name>
        <!-- The /* in the url-pattern ensures that trailing slashes are handled correctly by the IPN filter -->

        <url-pattern>/postad/*</url-pattern>
        <url-pattern>/ajax/category/children</url-pattern>
        <url-pattern>/ajax/vrn</url-pattern>
        <url-pattern>/api/category/suggest</url-pattern>
        <url-pattern>/api/skill/*</url-pattern>
        <url-pattern>/my-account/jobs/cv</url-pattern>
        <url-pattern>/my-account/jobs/cv/upload</url-pattern>
        <url-pattern>/my-account/jobs/cv/delete</url-pattern>
        <url-pattern>/my-account/jobs/cv/download</url-pattern>
        <url-pattern>/manage-account/update</url-pattern>
        <url-pattern>/manage-account/deactivate</url-pattern>
        <url-pattern>/manage-account/change-password</url-pattern>
        <url-pattern>/manage-account/subscribe</url-pattern>
        <url-pattern>/manage-account/contact-email/*</url-pattern>
        <url-pattern>/api/titleAndDesc/AIsuggest</url-pattern>
        <url-pattern>/api/title/AIsuggest</url-pattern>
        <url-pattern>/api/categoryAttributes/AIsuggest</url-pattern>
        <url-pattern>/api/category/AIsuggest</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>UrlRewriteFilter</filter-name>
        <filter-class>org.tuckey.web.filters.urlrewrite.UrlRewriteFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>UrlRewriteFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>requestTokenFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>requestTokenFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>shiroFilter</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
        <init-param>
            <param-name>targetFilterLifecycle</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>

    <filter-mapping>
        <filter-name>shiroFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <servlet>
        <servlet-name>website</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextClass</param-name>
            <param-value>
                org.springframework.web.context.support.AnnotationConfigWebApplicationContext
            </param-value>
        </init-param>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>com.gumtree.config.servlet.SellerServletConfig</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <!-- Mappings for static assets that are not images, js or css-->
    <servlet> 
        <servlet-name>default</servlet-name>
        <servlet-class>org.eclipse.jetty.servlet.DefaultServlet</servlet-class>
        <load-on-startup>0</load-on-startup> 
        <init-param>
            <param-name>dirAllowed</param-name>
            <param-value>false</param-value>
        </init-param>
    </servlet>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>/pdf/noticeofinfringement.pdf</url-pattern>
        <url-pattern>/responsive/*</url-pattern>
        <url-pattern>/robots.txt</url-pattern>
        <url-pattern>/.well-known/assetlinks.json</url-pattern>
        <url-pattern>/apple-app-site-association</url-pattern>
        <url-pattern>/.well-known/apple-app-site-association</url-pattern>
        <url-pattern>/favicon.ico</url-pattern>
    </servlet-mapping>

    // TODO: Check to see if image is now broken on error page, if it is, reference it to /responsive

    <servlet-mapping>
        <servlet-name>website</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>metrics</servlet-name>
        <servlet-class>io.prometheus.client.exporter.MetricsServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>metrics</servlet-name>
        <url-pattern>/internal/metrics</url-pattern>
    </servlet-mapping>

    <session-config>
        <session-timeout>30</session-timeout>
        <cookie-config>
            <http-only>true</http-only>
            <secure>true</secure>
            <max-age>3600</max-age>
        </cookie-config>
        <tracking-mode>COOKIE</tracking-mode>
    </session-config>

    <servlet>
        <servlet-name>internal</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextClass</param-name>
            <param-value>
                org.springframework.web.context.support.AnnotationConfigWebApplicationContext
            </param-value>
        </init-param>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>com.gumtree.config.servlet.InternalServletConfig</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>internal</servlet-name>
        <url-pattern>/internal/*</url-pattern>
    </servlet-mapping>

    <context-param>
      <param-name>resteasy.providers</param-name>
      <param-value>com.gumtree.service.messagecentre.MessageCentreObjectMapperProvider</param-value>
    </context-param>
    <listener>
      <listener-class>org.jboss.resteasy.plugins.server.servlet.ResteasyBootstrap</listener-class>
    </listener>
</web-app>
