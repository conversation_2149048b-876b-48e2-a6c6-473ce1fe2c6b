<?xml version="1.0" encoding="utf-8"?>

<!DOCTYPE urlrewrite
        PUBLIC "-//tuckey.org//DTD UrlRewrite 4.0//EN" "http://www.tuckey.org/res/dtds/urlrewrite4.0.dtd">

<urlrewrite>

    <outbound-rule encodefirst="true">
        <note>Remove jsessionid from embedded urls - for urls WITH query parameters</note>
        <from>^/(.*);jsessionid=.*[?](.*)$</from>
        <to encode="false">/$1?$2</to>
    </outbound-rule>

    <outbound-rule encodefirst="true">
        <note>Remove jsessionid from embedded urls - for urls WITHOUT query parameters</note>
        <from>^/(.*);jsessionid=.*[^?]$</from>
        <to encode="false">/$1</to>
    </outbound-rule>

    <outbound-rule encodefirst="true">
        <note>Remove gtsellersessionid from embedded urls - for urls WITH query parameters</note>
        <from>^/(.*);gtsellersessionid=.*[?](.*)$</from>
        <to encode="false">/$1?$2</to>
    </outbound-rule>

    <outbound-rule encodefirst="true">
        <note>Remove gtsellersessionid from embedded urls - for urls WITHOUT query parameters</note>
        <from>^/(.*);gtsellersessionid=.*[^?]$</from>
        <to encode="false">/$1</to>
    </outbound-rule>

    <rule>
        <condition type="method">GET</condition>
        <from>/mysavedsearches</from>
        <to type="permanent-redirect">https://www.gumtree.com/my-account/saved-searches</to>
    </rule>
    <rule>
        <condition type="method">GET</condition>
        <from>/reply/([0-9]+)/form</from>
        <to type="permanent-redirect">https://www.gumtree.com/reply/$1</to>
    </rule>

</urlrewrite>