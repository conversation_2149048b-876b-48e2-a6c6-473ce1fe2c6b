<configuration>

    <define name="isDev" class="ch.qos.logback.core.property.FileExistsPropertyDefiner">
        <path>target</path>
    </define>

    <if condition='isDefined("SENTRY_DSN")'>
        <then>
            <appender name="APPLOG_SENTRY" class="net.kencochrane.raven.logback.SentryAppender">
                <dsn>${SENTRY_DSN}?raven.async.queuesize=50</dsn>
                <tags>env:${gumtree.envName},instance:${gumtree.instanceName}</tags>
                <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                    <level>WARN</level>
                </filter>
            </appender>

            <root>
                <appender-ref ref="APPLOG_SENTRY"/>
            </root>
        </then>
    </if>

    <root>
        <appender-ref ref="APPLOG_SENTRY"/>
    </root>

    <!--
        Anytime we change encryption key used to encrypt rememberMe cookie the manager tries to decrypt old cookie with the new key and
        obliviously fails. This failure is logged as WARNING but it's expecting failure so we don't want it to be logged at all.
        That why I have increased log level to ERROR to not log those warning and to not spam our logs (as it create a few thousands entries
        per hour). All warning produce by the manager are related to rememberMe cookie so I believe we are not hiding anything serious
        by doing this.
    -->
    <logger name="org.apache.shiro.mgt.DefaultSecurityManager" level="ERROR" />

    <!--
        This one spam our log with messages that should be logged on debug level like:
        INFO  o.e.jetty.servlet.DefaultServlet - content=ResourceHttpContent@c5e0f2b{r=file:///tmp/webapp/.well-known/apple-app-site-association,gz=false}

        It pretty much duplicate our access log as it just log that it served that static file
    -->
    <logger name="org.eclipse.jetty.servlet.DefaultServlet" level="ERROR" />

    <!--
        We handle some exception on there which we don't want to get reported into sentry but it's nice to have them in app.log for
        manual check analyses.
    -->
    <logger name="com.gumtree.web.filter.GlobalErrorHandlerFilter" level="INFO" />

    <!--
        App log is getting spammed by warning like this one (more than 1k a day sometimes even +7k, check out
        https://sentry.gt.ecg.so/sentry/mobile-web/issues/6599/):

        10-11-2016 09:56:10.159 [ThreadPool-75] WARN  org.eclipse.jetty.http.HttpParser - bad HTTP parsed: 400 Unknown Version
            for HttpChannelOverHttp@30db3c97{r=0,c=false,a=IDLE,uri=null}
        org.eclipse.jetty.http.BadMessageException: 400: Unknown Version
	        at org.eclipse.jetty.http.HttpParser.parseLine(HttpParser.java:784)

	    This is caused by http request not providing valid HTTP version, you can reproduce it like this:

	        $ telnet localhost 8181
              GET null null null

                HTTP/1.1 400 Unknown Version
                Content-Length: 0
                Connection: close

         As y can see if y don't provided valid HTTP version y will get this error and your request will be ignored. Response code is 400.
         If think it's safe to ignore this warning and remove it from the logs as this is internet facing app so we are getting hit / tested
         by various bots, hackers, etc which may not always play fare and provided valid request.

         Only problem I noticed it that despite we return 400 we log it to our acces log as 200 which is lay but that is bug in jetty
         itself as it doesn't update response status correctly.

         0:0:0:0:0:0:0:1:8181 0:0:0:0:0:0:0:1 - - [10/Nov/2016:09:57:18 +0000] "null null null" 200 0 68440T - "-" "-"
    -->
    <logger name="org.eclipse.jetty.http.HttpParser" level="ERROR" />
    <appender name="APPLOG" class="ch.qos.logback.core.ConsoleAppender">
        <if condition='${isDev}'>
            <then>
                <encoder>
                    <charset>UTF-8</charset>
                    <pattern>%date{"yyyy-MM-dd'T'HH:mm:ss.SSSZ", UTC} %5p %t %c{5}:%L %m</pattern>
                </encoder>
            </then>
            <else>
                <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
                    <layout class="com.google.cloud.spring.logging.StackdriverJsonLayout">
                        <includeMessage>true</includeMessage>
                        <includeException>true</includeException>
                    </layout>
                </encoder>
            </else>
        </if>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <root level="INFO">
        <appender-ref ref="APPLOG"/>
    </root>

</configuration>
