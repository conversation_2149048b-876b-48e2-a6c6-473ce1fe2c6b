<?xml version="1.0" encoding="UTF-8"?>
<ehcache>

    <defaultCache
            maxElementsInMemory="10000"
            eternal="false"
            timeToIdleSeconds="120"
            timeToLiveSeconds="120"
            overflowToDisk="false"
            diskPersistent="false"
            diskExpiryThreadIntervalSeconds="120"/>

    <cache name="categoryModel"
           maxElementsInMemory="10000"
           eternal="true"
           overflowToDisk="false"/>

    <cache name="locationModel"
           maxElementsInMemory="10000"
           eternal="true"
           overflowToDisk="false"/>

    <cache name="locationTrees"
           maxElementsInMemory="10000"
           eternal="true"
           overflowToDisk="false"/>

    <cache name="statsStubData"
           maxElementsInMemory="1"
           eternal="true"
           overflowToDisk="false"/>

</ehcache>