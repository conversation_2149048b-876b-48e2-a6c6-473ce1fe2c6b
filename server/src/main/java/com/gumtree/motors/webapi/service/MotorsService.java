package com.gumtree.motors.webapi.service;

import com.google.common.collect.Lists;
import com.gumtree.api.Ad;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.error.ApiErrors;
import com.gumtree.motors.webapi.model.MotorsAd;
import com.gumtree.web.common.sapi.AdvertAttributeNames;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardisedVehicleDataResponse;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.VehicleAttribute;
import com.gumtree.web.api.WebApiError;
import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.ajax.vrm.MotorsApiClient;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.postad.model.PostAdvertBeanBuilder;
import com.gumtree.web.seller.page.postad.model.SingleAdvertShoppingCart;
import com.gumtree.web.seller.service.CheckoutContainer;
import javaslang.Tuple;
import javaslang.Tuple2;
import javaslang.control.Either;
import org.jboss.resteasy.client.ClientResponseFailure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import rx.Single;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.gumtree.util.StreamUtils.streamOptional;

@Service
public class MotorsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MotorsService.class);

    private final MotorsApiClient motorsApiClient;

    private final CategoryModel categoryModel;

    private final BushfireApi bushfireApi;

    private final CheckoutContainer checkoutContainer;

    private final UserSession userSession;

    @Autowired
    public MotorsService(MotorsApiClient motorsApiClient, CategoryModel categoryModel, BushfireApi bushfireApi, CheckoutContainer
            checkoutContainer, UserSession userSession) {
        this.motorsApiClient = motorsApiClient;
        this.categoryModel = categoryModel;
        this.bushfireApi = bushfireApi;
        this.checkoutContainer = checkoutContainer;
        this.userSession = userSession;
    }


    public Tuple2<List<AttributeMetadata>, Map<String, String>> getVrmAttributes(@NotNull String vrm, @NotNull final Long categoryId) {
        Assert.notNull(vrm);
        Assert.notNull(categoryId);

        Optional<StandardisedVehicleDataResponse> standardisedVehicleDataResponse = motorsApiClient.lookupVehicleData(vrm, categoryId);

        Map<String, String> vehiclesValues = standardisedVehicleDataResponse
                .orElse(emptyAttributes()).getAttributes()
                .stream()
                .collect(Collectors.toMap(VehicleAttribute::getName, VehicleAttribute::getValue));

        List<AttributeMetadata> attributeMetadatas = categoryModel.getCategoryAttributes(categoryId)
                .or(Lists.newArrayList())
                .stream()
                .filter(attributeMetadata -> !attributeMetadata.isExcludedFromSyi() && attributeMetadata.isDisplayLabel())
                .collect(Collectors.toList());

        return Tuple.of(attributeMetadatas, vehiclesValues);
    }

    public Single<Checkout> createMotorAd(MotorsAd motorsAd, String ipAddress, PermanentCookie permanentCookie,
                                          String threatmetrixSessionId) {
        Map<String, String> attributes = motorsApiClient.standardiseVehicleData(motorsAd.getCategoryId(), motorsAd.getAttributes());
        attributes.put(CategoryConstants.Attribute.PRICE.getName(), convertToPrice(motorsAd.getPrice()));
        attributes.put(AdvertAttributeNames.SELLER_TYPE.getName(), "private");
        Optional<Long> associatedCategory = extractAssociatedCategoryFromAttributes(attributes, motorsAd.getCategoryId());
        User user = userSession.getUser();

        PostAdvertBean postAdvertBean = buildPostAdvertBean(advertBeanBuilder -> {
            advertBeanBuilder.description(motorsAd.getDescription());
            advertBeanBuilder.postcode(motorsAd.getPostcode());
            advertBeanBuilder.visibleOnMap(motorsAd.getShowMapOnAd());
            advertBeanBuilder.title(motorsAd.getTitle());
            advertBeanBuilder.categoryId(associatedCategory.orElse(motorsAd.getCategoryId()));

            advertBeanBuilder.contactEmail(user.getEmail());
            advertBeanBuilder.contactName(user.getFirstName());
            advertBeanBuilder.accountId(userSession.getSelectedAccountId());
            advertBeanBuilder.ipAddress(ipAddress);
            advertBeanBuilder.threatmetrixSessionId(threatmetrixSessionId);
            advertBeanBuilder.postcode(motorsAd.getPostcode());
            advertBeanBuilder.cookie(permanentCookie.getId());
            advertBeanBuilder.imageIds(motorsAd.getImageIds());
            attributes.entrySet().stream()
                    .forEachOrdered(entry ->
                            advertBeanBuilder.attribute(entry.getKey(), entry.getValue(),
                                    categoryModel.getAttributeType(entry.getKey()).orNull()));
        });

        Single<Checkout> checkout = validateAd(postAdvertBean)
                .onErrorResumeNext(MotorsService::handleInternalError)
                .flatMap(isValid -> isValid.fold(
                        apiErrors -> handleValidation(apiErrors),
                        advertBean -> createAd(advertBean)))
                .flatMap(ad -> createOrder(ad));

        return checkout;
    }

    private static <A> Single<A> handleInternalError(Throwable throwable) {
        LOGGER.error("Unexpected internal error while creating a motors as : ", throwable);
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, HttpStatus
                .INTERNAL_SERVER_ERROR.toString(), "Internal error while trying to post a car ad: " + throwable.getMessage());
        return Single.error(new WebApiErrorException(webApiErrorResponse));
    }

    private static <A> Single<A> handleValidation(ApiErrors apiErrors) {
        List<WebApiError> webApiErrors = apiErrors.getErrors().stream().map(error -> new WebApiError(
                error.getMessageCode(), Optional.ofNullable(error.getDefaultMessage()).orElse("Incorrect value"),
                error.getField())).collect(Collectors.toList());
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.BAD_REQUEST,
                apiErrors.getErrorCode().asString(), "Validation failed for 1 or more fields", webApiErrors);
        WebApiErrorException webApiErrorException = new WebApiErrorException(webApiErrorResponse);
        return Single.error(webApiErrorException);
    }

    private Single<Either<ApiErrors, PostAdvertBean>> validateAd(PostAdvertBean postAdvertBean) {
        return Single.fromCallable(() -> {
            try {
                bushfireApi.advertApi().validateAd(postAdvertBean);
                return Either.right(postAdvertBean);
            } catch (ClientResponseFailure clientResponseFailure) {
                ApiErrors apiErrors = (ApiErrors) clientResponseFailure.getResponse().getEntity(ApiErrors.class);
                return Either.left(apiErrors);
            }
        });
    }

    private Single<Ad> createAd(PostAdvertBean postAdvertBean) {
        return Single.fromCallable(() -> bushfireApi.create(AdvertApi.class, userSession.getApiKey()).postAd(postAdvertBean))
                .onErrorResumeNext(MotorsService::handleInternalError);
    }

    private Single<Checkout> createOrder(Ad ad) {
        return Single.fromCallable(() -> {
            SingleAdvertShoppingCart cart = new SingleAdvertShoppingCart(ad.getId(), userSession.getSelectedAccountId());
            cart.addProduct(ProductName.INSERTION);
            CreateOrderBean createOrderBean = cart.toOrderBean();
            ApiOrder order = bushfireApi.create(OrderApi.class, userSession.getApiKey()).createOrder(createOrderBean);
            return checkoutContainer.createCheckout(order, ad, true);
        }).onErrorResumeNext(MotorsService::handleInternalError);
    }

    private static StandardisedVehicleDataResponse emptyAttributes() {
        StandardisedVehicleDataResponse vehicleDataResponse = new StandardisedVehicleDataResponse();
        vehicleDataResponse.setAttributes(Lists.newArrayList());
        return vehicleDataResponse;
    }

    private Optional<Long> extractAssociatedCategoryFromAttributes(Map<String, String> attributes, Long parentCategoryId) {
        return attributes.keySet()
                .stream()
                .flatMap(attributeName -> streamOptional(categoryModel.findAttributesByNameForGivenCategory(attributeName,
                        parentCategoryId)))
                .flatMap(attributeMetadata -> {
                    String attributeValue = attributes.get(attributeMetadata.getName());
                    return streamOptional(attributeMetadata.getAssociatedCategoryId(attributeValue));
                })
                .findFirst();
    }

    private String convertToPrice(Long value) {
        try {
            return new BigDecimal(value).divide(new BigDecimal(100)).toString();
        } catch (Exception ex) {
            return "";
        }
    }

    private PostAdvertBean buildPostAdvertBean(Consumer<PostAdvertBeanBuilder> enhancerFunc) {
        PostAdvertBeanBuilder postAdvertBeanBuilder = new PostAdvertBeanBuilder();
        enhancerFunc.accept(postAdvertBeanBuilder);
        return postAdvertBeanBuilder.build();
    }
}
