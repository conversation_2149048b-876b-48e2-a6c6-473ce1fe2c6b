package com.gumtree.motors.webapi.converter;

import com.gumtree.api.Image;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.motors.webapi.model.MotorsAd;
import com.gumtree.motorssyi.client.model.CreateMotorAdRequest;
import com.gumtree.motorssyi.client.model.ErrorItem;
import com.gumtree.motorssyi.client.model.InternalError;
import com.gumtree.motorssyi.client.model.MotorAdFail;
import com.gumtree.motorssyi.client.model.MotorAdSuccess;
import com.gumtree.motorssyi.client.model.MotorUploadImageFail;
import com.gumtree.motorssyi.client.model.MotorUploadImageSuccess;
import com.gumtree.motorssyi.client.model.PostCodeLookUpApiResponse;
import com.gumtree.motorssyi.client.model.VrmAttribute;
import com.gumtree.motorssyi.client.model.VrmAttributeDropDownValue;
import com.gumtree.motorssyi.client.model.VrmDisplayAttribute;
import com.gumtree.motorssyi.client.model.VrmLookupApiResponse;
import com.gumtree.seller.domain.image.entity.ImageSize;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.gumtree.motors.webapi.model.MotorsAd.buildMotorsAd;
import static com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState.POSTCODE_RECOGNISED;

public final class MotorsSYIConverters {

    private static final Logger LOGGER = LoggerFactory.getLogger(MotorsSYIConverters.class);

    private static final String CLOUDFLARE_URL_PREFIX = "imagedelivery.net";
    public static final String FWD_SLASH_CHAR = "/";

    private MotorsSYIConverters() {

    }

    public static PostCodeLookUpApiResponse convertToPostCodeLookUpApiResponse(@NotNull PostcodeLookupResponse postcodeLookupResponse) {
        Assert.notNull(postcodeLookupResponse);

        return buildPostCodeLookUpApiResponse(postCodeLookUpApiResponse -> {
            postCodeLookUpApiResponse.setPostcode(postcodeLookupResponse.getPostcode());
            if (postcodeLookupResponse.getState().equals(POSTCODE_RECOGNISED)) {
                postCodeLookUpApiResponse.setIsValid(true);
            } else {
                postCodeLookUpApiResponse.setIsValid(false);
            }
        });
    }

    public static VrmLookupApiResponse convertToVrmLookupApiResponse(@NotNull List<AttributeMetadata> attributeMetadatas,
                                                                     @NotNull Map<String, String> vehicleValues) {
        Assert.notNull(attributeMetadatas);
        Assert.notNull(vehicleValues);

        List<VrmDisplayAttribute> vrmDisplayAttributes = attributeMetadatas
                .stream()
                .map(attributeMetadata -> buildVrmDisplayAttribute(vrmDisplayAttribute -> {
                    vrmDisplayAttribute.setName(attributeMetadata.getName());
                    vrmDisplayAttribute.setLabel(attributeMetadata.getLabel());
                    vrmDisplayAttribute.isRequired(attributeMetadata.isRequired());
                    vrmDisplayAttribute.setType(attributeMetadata.getType().name());
                    vrmDisplayAttribute.setValue(vehicleValues.get(attributeMetadata.getName()));
                    if (attributeMetadata.getType().equals(AttributeType.ENUM)) {
                        vrmDisplayAttribute.setOptions(getVrmAttributeDropDownValues(attributeMetadata, vehicleValues));
                    }
                }))
                .collect(Collectors.toList());

        return buildVrmLookupApiResponse(vrmLookupApiResponse -> {
            vrmLookupApiResponse.setAttributes(vrmDisplayAttributes);
        });
    }

    public static MotorsAd convertToMotorsAd(@NotNull CreateMotorAdRequest createMotorAdRequest) {
        Assert.notNull(createMotorAdRequest);

        Map<String, String> vrmAttributes = createMotorAdRequest.getVrmAttributes()
                .stream()
                .collect(Collectors.toMap(
                        VrmAttribute::getName,
                        vrmAttribute -> StringUtils.trimToEmpty(vrmAttribute.getValue()),
                        (vrmAttribute1, vrmAttribute2) -> {
                            LOGGER.warn("Duplicate key found in attributes, using latest value : " + vrmAttribute2);
                            return vrmAttribute2;
                        }));

        return buildMotorsAd(motorsAd -> {
            motorsAd.setTitle(createMotorAdRequest.getTitle());
            motorsAd.setDescription(createMotorAdRequest.getDescription());
            motorsAd.setCategoryId(createMotorAdRequest.getCategoryId());
            motorsAd.setPostcode(createMotorAdRequest.getPostcode());
            motorsAd.setPrice(createMotorAdRequest.getPrice());
            motorsAd.setShowMapOnAd(createMotorAdRequest.getShowMapOnAd());
            motorsAd.setImageIds(createMotorAdRequest.getImages());
            motorsAd.setAttributes(vrmAttributes);
        });
    }

    public static MotorAdSuccess convertToMotorAdSuccess(String redirect) {
        return buildMotorAdSuccess(motorAdSuccess -> motorAdSuccess.setRedirect(redirect));
    }

    public static Function<WebApiErrorResponse, MotorAdFail> converterToMotorAdFail() {
        return errorResponse ->
                buildMotorAdFail(motorAdFail -> {
                    motorAdFail.setCode(errorResponse.getCode());
                    motorAdFail.setMessage(errorResponse.getMessage());

                    List<ErrorItem> errorItems = errorResponse.getErrors().stream().map(webApiError -> {
                        ErrorItem errorItem = new ErrorItem();
                        errorItem.setPath(Collections.singletonList(webApiError.getField()));
                        errorItem.setError(webApiError.getMessage());
                        return errorItem;
                    }).collect(Collectors.toList());

                    motorAdFail.setErrors(errorItems);
                });
    }

    public static MotorUploadImageSuccess convertToMotorUploadImageSuccess(String originalName, Image image) {
        return buildMotorUploadImageSuccess(motorUploadImageSuccess -> {
            motorUploadImageSuccess.setFileName(originalName);
            motorUploadImageSuccess.setId(image.getId());
            motorUploadImageSuccess.setSize(image.getSize());
            motorUploadImageSuccess.setUrl(image.getUrl());
            motorUploadImageSuccess.setThumbnailUrl(getSecureImageUrl(image, ImageSize.MINITHUMB.getId()));
        });
    }

    private static String getSecureImageUrl(Image image, int sizeId){
        String imageUrl = image.getUrl();
        if (imageUrl.contains(CLOUDFLARE_URL_PREFIX)) {
            return imageUrl.substring(0, imageUrl.lastIndexOf(FWD_SLASH_CHAR) + 1) + sizeId;
        } else {
            return image.getThumbnailUrl();
        }
    }

    public static Function<WebApiErrorResponse, MotorUploadImageFail> converterToMotorUploadImageFail() {
        return errorResponse ->
                buildMotorUploadImageFail(motorUploadImageFail -> {
                    motorUploadImageFail.setCode(errorResponse.getCode());
                    motorUploadImageFail.setMessage(errorResponse.getMessage());
                });
    }

    public static InternalError convertToInternalError(WebApiErrorResponse errorResponse) {
        return buildInternalError(internalError -> {
            internalError.setErrorCode(errorResponse.getCode());
            internalError.setErrorDetail(errorResponse.getMessage());
            internalError.setUserMessage("Unexpected error");
        });
    }

    private static List<VrmAttributeDropDownValue> getVrmAttributeDropDownValues(AttributeMetadata attributeMetadata, Map<String, String>
            vehicleValues) {
        return attributeMetadata.getValues()
                .stream()
                .map(attributeValueMetadata -> buildVrmAttributeDropDownValue(vrmAttributeDropDownValue -> {
                    vrmAttributeDropDownValue.setName(attributeValueMetadata.getValue());
                    vrmAttributeDropDownValue.setLabel(attributeValueMetadata.getLabel());
                    boolean isValueSelected = vehicleValues
                            .getOrDefault(attributeMetadata.getName(), "")
                            .equals(attributeValueMetadata.getValue());

                    vrmAttributeDropDownValue.setIsSelected(isValueSelected);
                })).collect(Collectors.toList());
    }

    public static PostCodeLookUpApiResponse buildPostCodeLookUpApiResponse(Consumer<PostCodeLookUpApiResponse> enhancerFunc) {
        PostCodeLookUpApiResponse postCodeLookUpApiResponse = new PostCodeLookUpApiResponse();
        enhancerFunc.accept(postCodeLookUpApiResponse);
        return postCodeLookUpApiResponse;
    }

    public static VrmLookupApiResponse buildVrmLookupApiResponse(Consumer<VrmLookupApiResponse> enhancerFunc) {
        VrmLookupApiResponse vrmLookupApiResponse = new VrmLookupApiResponse();
        enhancerFunc.accept(vrmLookupApiResponse);
        return vrmLookupApiResponse;
    }

    public static VrmDisplayAttribute buildVrmDisplayAttribute(Consumer<VrmDisplayAttribute> enhancerFunc) {
        VrmDisplayAttribute vrmDisplayAttribute = new VrmDisplayAttribute();
        enhancerFunc.accept(vrmDisplayAttribute);
        return vrmDisplayAttribute;
    }

    public static VrmAttributeDropDownValue buildVrmAttributeDropDownValue(Consumer<VrmAttributeDropDownValue> enhancerFunc) {
        VrmAttributeDropDownValue vrmAttributeDropDownValue = new VrmAttributeDropDownValue();
        enhancerFunc.accept(vrmAttributeDropDownValue);
        return vrmAttributeDropDownValue;
    }

    public static MotorAdSuccess buildMotorAdSuccess(Consumer<MotorAdSuccess> enhancerFunc) {
        MotorAdSuccess motorAdSuccess = new MotorAdSuccess();
        enhancerFunc.accept(motorAdSuccess);
        return motorAdSuccess;
    }

    public static MotorAdFail buildMotorAdFail(Consumer<MotorAdFail> enhancerFunc) {
        MotorAdFail motorAdFail = new MotorAdFail();
        enhancerFunc.accept(motorAdFail);
        return motorAdFail;
    }

    public static MotorUploadImageFail buildMotorUploadImageFail(Consumer<MotorUploadImageFail> enhancerFunc) {
        MotorUploadImageFail motorUploadImageFail = new MotorUploadImageFail();
        enhancerFunc.accept(motorUploadImageFail);
        return motorUploadImageFail;
    }

    public static MotorUploadImageSuccess buildMotorUploadImageSuccess(Consumer<MotorUploadImageSuccess> enhancerFunc) {
        MotorUploadImageSuccess motorUploadImageSuccess = new MotorUploadImageSuccess();
        enhancerFunc.accept(motorUploadImageSuccess);
        return motorUploadImageSuccess;
    }

    public static InternalError buildInternalError(Consumer<InternalError> enhancerFunc) {
        InternalError internalError = new InternalError();
        enhancerFunc.accept(internalError);
        return internalError;
    }
}
