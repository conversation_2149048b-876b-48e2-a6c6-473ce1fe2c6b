package com.gumtree.motors.webapi.model;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

public final class MotorsAd {

    private String title;
    private String description;
    private Map<String, String> attributes;
    private String postcode;
    private Boolean showMapOnAd;
    private Long price;
    private Long categoryId;
    private List<Long> imageIds;

    private MotorsAd() {
        imageIds = Collections.emptyList();
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, String> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<String, String> attributes) {
        this.attributes = attributes;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public Boolean getShowMapOnAd() {
        return showMapOnAd;
    }

    public void setShowMapOnAd(Boolean showMapOnAd) {
        this.showMapOnAd = showMapOnAd;
    }

    public Long getPrice() {
        return price;
    }

    public void setPrice(Long price) {
        this.price = price;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public List<Long> getImageIds() {
        return imageIds;
    }

    public void setImageIds(List<Long> imageIds) {
        this.imageIds = imageIds;
    }

    public static MotorsAd buildMotorsAd(Consumer<MotorsAd> enhancerFunc) {
        MotorsAd motorsAd = new MotorsAd();
        enhancerFunc.accept(motorsAd);
        return motorsAd;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MotorsAd motorsAd = (MotorsAd) o;
        return Objects.equals(title, motorsAd.title) &&
                Objects.equals(description, motorsAd.description) &&
                Objects.equals(attributes, motorsAd.attributes) &&
                Objects.equals(postcode, motorsAd.postcode) &&
                Objects.equals(showMapOnAd, motorsAd.showMapOnAd) &&
                Objects.equals(price, motorsAd.price) &&
                Objects.equals(categoryId, motorsAd.categoryId) &&
                Objects.equals(imageIds, motorsAd.imageIds);
    }

    @Override
    public int hashCode() {

        return Objects.hash(title, description, attributes, postcode, showMapOnAd, price, categoryId, imageIds);
    }
}
