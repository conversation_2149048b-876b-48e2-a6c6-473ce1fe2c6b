package com.gumtree.config;

import com.gumtree.common.properties.GtProps;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.seller.security.apiauthentication.CSRFTokenService;
import com.gumtree.web.seller.security.apiauthentication.JsonWebTokenProperties;
import com.gumtree.web.seller.security.apiauthentication.JsonWebTokenService;
import com.nimbusds.jose.JOSEException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SellerApiAuthenticationConfig {

    private final String sellerUrl = GtProps.getStr(SellerProperty.SELLER_BASE_URL);
    private final String jwtPrivateKey = GtProps.getStr(SellerProperty.API_JWT_PRIVATE_KEY);
    private final int tokenNotValidBeforeSeconds = GtProps.getInt(SellerProperty.API_JWT_VALID_NOT_BEFORE_SECONDS);
    private final int tokenNotValidAfterSeconds = GtProps.getInt(SellerProperty.API_JWT_VALID_NOT_AFTER_SECONDS);
    private final String jwtSecret = GtProps.getStr(MwebProperty.JWT_SECRET);

    @Bean
    public JsonWebTokenService jsonWebTokenService() {
        JsonWebTokenProperties properties =
                new JsonWebTokenProperties(sellerUrl, jwtPrivateKey, tokenNotValidBeforeSeconds, tokenNotValidAfterSeconds, jwtSecret);
        return new JsonWebTokenService(properties);
    }

    @Bean
    public CSRFTokenService csrfTokenService(ParameterEncryption parameterEncryption) throws JOSEException {
        JsonWebTokenProperties properties =
                new JsonWebTokenProperties(sellerUrl, jwtPrivateKey, tokenNotValidBeforeSeconds, tokenNotValidAfterSeconds, jwtSecret);
        return new CSRFTokenService(properties, parameterEncryption);
    }
}
