package com.gumtree.config.models;

import com.gumtree.api.util.ObjectMapperFactory;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Models configuration.
 */
@Configuration
@ComponentScan(basePackages = {
        "com.gumtree.service.category",
        "com.gumtree.service.location" })
@Import({ ProductionModelsConfig.class, StubModelsConfig.class })
public class ModelsConfig {

    /**
     * Object mapper bean.
     * @return the bean
     */
    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapperFactory().create();
    }
}
