package com.gumtree.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.gumtree.api.OkHttpClientMetricsInterceptor;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.util.StringUtils;
import com.gumtree.common.util.http.GumtreeHttpClient;
import com.gumtree.common.util.http.HttpClientFactory;
import com.gumtree.common.util.http.JsonHttpClient;
import com.gumtree.common.util.json.SnakeCaseObjectMapperFactory;
import com.gumtree.gas.OIDCUserStore;
import com.gumtree.gas.OpenIdConnectService;
import com.gumtree.recaptcha.RecaptchaValidator;
import com.gumtree.shared.client.CommonApiClient;
import com.gumtree.user.service.ThirdPartyConsentService;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.api.AccountDeactivationApi;
import com.gumtree.user.service.api.MarketingPreferenceApi;
import com.gumtree.user.service.api.ThirdPartyMarketingConsentApi;
import com.gumtree.user.service.api.UserInformationApi;
import com.gumtree.user.service.api.UserManagementApi;
import com.gumtree.user.service.support.exception.UserManagementServerErrorDecoder;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.MadgexCookieHelper;
import com.gumtree.web.cookie.MessageCentreCookieHelper;
import com.gumtree.web.cookie.cutters.messagecentre.MessageCentreCookieCutter;
import com.gumtree.web.security.SecurityHelper;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.config.CommonSecurityConfig;
import com.gumtree.web.security.config.FilterChainBuilder;
import com.gumtree.web.security.config.FilterDefinitionBuilder;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.security.shiro.GumtreeAppTokenRealm;
import com.gumtree.web.security.shiro.GumtreeFormAuthenticationFilter;
import com.gumtree.web.security.shiro.GumtreeRealm;
import com.gumtree.web.security.shiro.GumtreeSoftLoginFilter;
import com.gumtree.web.security.shiro.NewUserLoginFilter;
import com.gumtree.web.security.shiro.ShiroSubjectUserSession;
import com.gumtree.web.security.shiro.StartPostAdFlowFilter;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import feign.Logger;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.http.client.HttpClient;
import org.apache.shiro.authc.credential.AllowAllCredentialsMatcher;
import org.apache.shiro.authc.pam.AtLeastOneSuccessfulStrategy;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.HashMap;
import java.util.Map;

import static com.gumtree.config.SellerProperty.PARAMETER_ENCRYPTION_KEY;
import static com.gumtree.user.service.support.UserServiceClient.userServiceObjectMapper;

/**
 * Configuration for Seller security.
 */
@Configuration

public class SellerSecurityConfig extends CommonSecurityConfig {

    public static final String ANON_FILTER = "anon";

    public static final String AUTHC_FILTER = "gtauthc";

    public static final String SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK = "user";
    public static final String SOFT_FILTER_WITHOUT_ACCESS_TOKEN_CHECK = "soft_without_access_token_check";

    public static final String NEW_USER_LOGIN_FILTER = "newuser";

    public static final String START_POST_AD_FLOW_FILTER = "startPostAdFlowFilter";
    private static final String USER_API_COMMAND_GROUP_NAME = "UserApi";

    private String clientId = GtProps.getStr(SellerProperty.SALESFORCE_CLIENT_ID);

    private String clientSecret = GtProps.getStr(SellerProperty.SALESFORCE_CLIENT_SECRET);

    private String redirectUri = GtProps.getStr(SellerProperty.SALESFORCE_REDIRECT_URI);

    private String jwtSecret = GtProps.getStr(SellerProperty.SALESFORCE_JWT_SECRET);

    @Autowired
    private BushfireApi bushfireApi;

    @Autowired
    private SellerSessionDataService sessionDataService;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private UserSecurityManager userSecurityManager;

    @Autowired
    private OIDCUserStore userStore;

    @Autowired
    private RecaptchaValidator recaptchaValidator;

    @Autowired
    private MessageCentreCookieCutter messageCentreCookieCutter;

    @Autowired
    private CookieResolver cookieResolver;

    @Autowired
    private MeterRegistry meterRegistry;

    private String sellerSecureBaseUri = GtProps.getStr(SellerProperty.SELLER_SECURE_BASE_URL);

    @Bean
    public UserServiceFacade userServiceFacade() {
        return new UserServiceFacade(userManagementApi(), marketingPreferenceApi(), accountDeactivationApi(), userInformationApi());
    }

    private UserManagementApi userManagementApi() {
        String baseUrl = GtProps.getStr(UserApiPropNames.BASE_URL);
        int connectionTimeout = GtProps.getInt(UserApiPropNames.CONNECTION_TIMEOUT);
        int socketTimeout = GtProps.getInt(UserApiPropNames.SOCKET_TIMEOUT);
        ObjectMapper objectMapper = userServiceObjectMapper();
        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(baseUrl,
                connectionTimeout,
                socketTimeout,
                UserManagementApi.class.getName(),
                10,
                objectMapper,
                client)
                .builder(UserManagementApi.class)
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withErrorDecoder(new UserManagementServerErrorDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + socketTimeout)
                .withCircuitBreaker(false)
                .buildClient();
    }

    private UserInformationApi userInformationApi() {
        String baseUrl = GtProps.getStr(UserApiPropNames.BASE_URL);
        int connectionTimeout = GtProps.getInt(UserApiPropNames.CONNECTION_TIMEOUT);
        int socketTimeout = GtProps.getInt(UserApiPropNames.SOCKET_TIMEOUT);
        ObjectMapper objectMapper = userServiceObjectMapper();
        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(baseUrl,
                connectionTimeout,
                socketTimeout,
                UserInformationApi.class.getName(),
                10,
                objectMapper,
                client)
                .builder(UserInformationApi.class)
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withErrorDecoder(new UserManagementServerErrorDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + socketTimeout)
                .withCircuitBreaker(false)
                .buildClient();
    }

    private MarketingPreferenceApi marketingPreferenceApi() {
        String baseUrl = GtProps.getStr(UserApiPropNames.BASE_URL);
        int connectionTimeout = GtProps.getInt(UserApiPropNames.CONNECTION_TIMEOUT);
        int socketTimeout = GtProps.getInt(UserApiPropNames.SOCKET_TIMEOUT);
        ObjectMapper objectMapper = userServiceObjectMapper();
        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(baseUrl,
                connectionTimeout,
                socketTimeout,
                UserManagementApi.class.getName(),
                10,
                objectMapper, client)
                .builder(MarketingPreferenceApi.class)
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withErrorDecoder(new UserManagementServerErrorDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + socketTimeout)
                .withCircuitBreaker(false)
                .buildClient();
    }

    private AccountDeactivationApi accountDeactivationApi() {
        String baseUrl = GtProps.getStr(UserApiPropNames.BASE_URL);
        int connectionTimeout = GtProps.getInt(UserApiPropNames.CONNECTION_TIMEOUT);
        int socketTimeout = GtProps.getInt(UserApiPropNames.SOCKET_TIMEOUT);
        ObjectMapper objectMapper = userServiceObjectMapper();
        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(baseUrl,
                connectionTimeout,
                socketTimeout,
                UserManagementApi.class.getName(),
                10,
                objectMapper,
                client)
                .builder(AccountDeactivationApi.class)
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withErrorDecoder(new UserManagementServerErrorDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + socketTimeout)
                .withCircuitBreaker(false)
                .buildClient();
    }

    @Bean
    public SecurityHelper securityHelper(UserServiceFacade userServiceFacade, MeterRegistry meterRegistry) {
        return new SecurityHelper(userServiceFacade, meterRegistry);
    }

    /**
     * @return the user session
     * @throws Exception if there were problems
     */
    @Bean
    public UserSession authenticatedUserSession(UserServiceFacade userServiceFacade) throws Exception {
        return new ShiroSubjectUserSession(userSecurityManager, loginUtils, sessionDataService, userServiceFacade);
    }

    @Bean
    public OpenIdConnectService getOIDCService() throws Exception {
        return OpenIdConnectService.apply(clientId, clientSecret, redirectUri, jwtSecret, userStore);
    }

    /**
     * Shiro filter factory bean.
     *
     * @return the factory bean
     * @throws Exception if something went wrong
     */
    @Autowired
    @Bean(name = "shiroFilter")
    public FactoryBean<Filter> shiroFilterFactoryBean(UrlScheme urlScheme, UserSession userSession,
                                                      UserServiceFacade userServiceFacade) throws Exception {
        ShiroFilterFactoryBean factory = new ShiroFilterFactoryBean();
        factory.setSecurityManager(securityManager());

        // Setup special GT filter that permanently enables remember me functionality
        Map<String, Filter> filters = new HashMap<>();
        GumtreeFormAuthenticationFilter gumtreeFormAuthenticationFilter =
                new GumtreeFormAuthenticationFilter(urlScheme, bushfireApi, loginUtils, userSession, messageCentreCookieHelper(),
                        cookieResolver, meterRegistry);
        filters.put(AUTHC_FILTER, gumtreeFormAuthenticationFilter);

        // Add filter which grants access as long as user is at least "remembered"
        SecurityHelper securityHelper = securityHelper(userServiceFacade, meterRegistry);
        GumtreeSoftLoginFilter softLoginFilter = new GumtreeSoftLoginFilter(securityHelper, true);
        softLoginFilter.setLoginUrl(StringUtils.concat(sellerSecureBaseUri, "/login"));
        filters.put(SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK, softLoginFilter);

        GumtreeSoftLoginFilter softFilterWithoutTokenCheck = new GumtreeSoftLoginFilter(securityHelper, false);
        softFilterWithoutTokenCheck.setLoginUrl(StringUtils.concat(sellerSecureBaseUri, "/login"));
        filters.put(SOFT_FILTER_WITHOUT_ACCESS_TOKEN_CHECK, softFilterWithoutTokenCheck);

        // Add new user login filters
        filters.put(NEW_USER_LOGIN_FILTER, new NewUserLoginFilter(loginUtils, securityHelper));
        StartPostAdFlowFilter startPostAdFlowFilter = new StartPostAdFlowFilter(loginUtils, securityHelper);
        startPostAdFlowFilter.setLoginUrl(StringUtils.concat(sellerSecureBaseUri, "/login/postad"));
        filters.put(START_POST_AD_FLOW_FILTER, startPostAdFlowFilter);

        factory.setFilters(filters);
        // Filter chains
        factory.setFilterChainDefinitions(new FilterChainBuilder().containing(
                new FilterDefinitionBuilder().forUrl("/manage/**").withFilter(SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK),
                new FilterDefinitionBuilder().forUrl("/conversations/unread").withFilter(SOFT_FILTER_WITHOUT_ACCESS_TOKEN_CHECK),
                new FilterDefinitionBuilder().forUrl("/conversations/**").withFilter(SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK),
                new FilterDefinitionBuilder().forUrl("/manage-account/**").withFilter(SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK),
                new FilterDefinitionBuilder().forUrl("/my-account/jobs/**").withFilter(SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK),
                new FilterDefinitionBuilder().forUrl("/checkout/**").withFilter(NEW_USER_LOGIN_FILTER),
                new FilterDefinitionBuilder().forUrl("/postad").withFilter(START_POST_AD_FLOW_FILTER),
                new FilterDefinitionBuilder().forUrl("/postad/**").withFilter(NEW_USER_LOGIN_FILTER),
                new FilterDefinitionBuilder().forUrl("/api/motors/syi/**").withFilter(NEW_USER_LOGIN_FILTER),
                new FilterDefinitionBuilder().forUrl("/api/skill/**").withFilter(NEW_USER_LOGIN_FILTER),
                new FilterDefinitionBuilder().forUrl("/user/savedsearch/**").withFilter(NEW_USER_LOGIN_FILTER),
                new FilterDefinitionBuilder().forUrl("/user/activateemailalert/**").withFilter(SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK),
                new FilterDefinitionBuilder().forUrl("/mysavedsearches").withFilter(SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK),
                new FilterDefinitionBuilder().forUrl("/add_posting.html/**").withFilter(NEW_USER_LOGIN_FILTER),
                new FilterDefinitionBuilder().forUrl("/cgi-bin/add_posting.pl/**").withFilter(NEW_USER_LOGIN_FILTER),
                new FilterDefinitionBuilder().forUrl("/login").withFilter(AUTHC_FILTER),
                new FilterDefinitionBuilder().forUrl("/oidc/authorise").withFilter(SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK),
                new FilterDefinitionBuilder().forUrl("/api/authenticate").withFilter(SOFT_LOGIN_FILTER_WITH_ACCESS_TOKEN_CHECK),
                new FilterDefinitionBuilder().forUrl("/**").withFilter(ANON_FILTER)).build());

        // TODO: We ought to be using the UrlScheme for this but it is not currently available in this context
        factory.setLoginUrl(StringUtils.concat(sellerSecureBaseUri, "/login"));
        factory.setSuccessUrl(StringUtils.concat(sellerSecureBaseUri, "/manage/ads"));
        return factory;
    }

    @Bean
    public MessageCentreCookieHelper messageCentreCookieHelper() {
        return new MessageCentreCookieHelper(messageCentreCookieCutter, cookieResolver);
    }

    @Bean
    public MadgexCookieHelper madgexCookieHelper() {
        return new MadgexCookieHelper();
    }

    @Bean
    public org.apache.shiro.mgt.SecurityManager securityManager(UserServiceFacade userServiceFacade) throws Exception {
        GumtreeRealm realm = new GumtreeRealm(userServiceFacade, recaptchaValidator);
        realm.setCredentialsMatcher(new AllowAllCredentialsMatcher());
        realm.setCachingEnabled(false);

        GumtreeAppTokenRealm appTokenRealm = new GumtreeAppTokenRealm(userServiceFacade);
        appTokenRealm.setCredentialsMatcher(new AllowAllCredentialsMatcher());
        appTokenRealm.setCachingEnabled(false);

        CustomModularRealmAuthenticator authenticator = new CustomModularRealmAuthenticator();
        authenticator.setAuthenticationStrategy(new AtLeastOneSuccessfulStrategy());
        authenticator.setRealms(Lists.newArrayList(realm,appTokenRealm));

        DefaultWebSecurityManager manager = (DefaultWebSecurityManager) super.securityManager();
        manager.setAuthenticator(authenticator);

        return manager;
    }

    @Bean(name = "recaptchaValidatorHttpClient")
    public GumtreeHttpClient recaptchaValidatorGumtreeHttpClient() {
        org.codehaus.jackson.map.ObjectMapper mapper = new SnakeCaseObjectMapperFactory().create();
        HttpClient httpClient = new HttpClientFactory().instrumentedClient(
                GtProps.getInt(MwebProperty.RECAPTCHA_HTTPCLIENT_SOCKET_TIMEOUT),
                GtProps.getInt(MwebProperty.RECAPTCHA_HTTPCLIENT_CONNECTION_TIMEOUT),
                GtProps.getInt(MwebProperty.RECAPTCHA_HTTPCLIENT_RETRY_COUNT));
        return new JsonHttpClient(httpClient, mapper);
    }

    @Bean
    public RecaptchaValidator recaptchaValidator(@Qualifier("recaptchaValidatorHttpClient") GumtreeHttpClient gumtreeHttpClient) {
        return new RecaptchaValidator(gumtreeHttpClient);
    }

    @Bean
    public ParameterEncryption parameterEncryption() throws Exception {
        return new ParameterEncryption(GtProps.getStr(PARAMETER_ENCRYPTION_KEY));
    }

    @Bean
    public ThirdPartyConsentService thirdPartyConsentService() {
        return new ThirdPartyConsentService(thirdPartyMarketingConsentApi());
    }

    private ThirdPartyMarketingConsentApi thirdPartyMarketingConsentApi() {
        String baseUrl = GtProps.getStr(UserApiPropNames.BASE_URL);
        int connectionTimeout = GtProps.getInt(UserApiPropNames.CONNECTION_TIMEOUT);
        int socketTimeout = GtProps.getInt(UserApiPropNames.SOCKET_TIMEOUT);
        ObjectMapper objectMapper = userServiceObjectMapper();
        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(baseUrl,
                connectionTimeout,
                socketTimeout,
                UserManagementApi.class.getName(),
                10,
                objectMapper,
                client)
                .builder(ThirdPartyMarketingConsentApi.class)
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withErrorDecoder(new UserManagementServerErrorDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + socketTimeout)
                .withCircuitBreaker(false)
                .buildClient(Logger.Level.FULL);
    }

}
