package com.gumtree.config.exception;

import com.gumtree.security.phone.number.authenticator.model.ApiError;


public class PhoneNumberAuthenticatorServerException extends RuntimeException {

    private ApiError apiError;

    public PhoneNumberAuthenticatorServerException(String message) {
        super(message);
    }

    public PhoneNumberAuthenticatorServerException(ApiError apiError) {
        super(apiError.toString());
        this.apiError = apiError;
    }

    public ApiError getApiError() {
        return apiError;
    }

    public void setApiError(ApiError apiError) {
        this.apiError = apiError;
    }

    @Override
    public String toString() {
        return "PhoneNumberAuthenticatorServerException{" +
                "apiError=" + apiError +
                '}';
    }
}
