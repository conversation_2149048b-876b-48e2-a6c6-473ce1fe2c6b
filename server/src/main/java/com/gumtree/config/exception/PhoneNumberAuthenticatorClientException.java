package com.gumtree.config.exception;

import com.gumtree.security.phone.number.authenticator.model.ApiError;
import com.netflix.hystrix.exception.ExceptionNotWrappedByHystrix;
import com.netflix.hystrix.exception.HystrixBadRequestException;

public class PhoneNumberAuthenticatorClientException extends HystrixBadRequestException
        implements ExceptionNotWrappedByHystrix {

    private ApiError apiError;

    public PhoneNumberAuthenticatorClientException(String message) {
        super(message);
    }

    public PhoneNumberAuthenticatorClientException(ApiError apiError) {
        super(apiError.toString());
        this.apiError = apiError;
    }

    public ApiError getApiError() {
        return apiError;
    }

    public void setApiError(ApiError apiError) {
        this.apiError = apiError;
    }

    @Override
    public String toString() {
        return "PhoneNumberAuthenticatorClientException{" +
                "apiError=" + apiError +
                '}';
    }
}
