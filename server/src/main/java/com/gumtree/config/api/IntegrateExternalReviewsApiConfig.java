package com.gumtree.config.api;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.shared.client.CommonApiClient;
import com.gumtree.tns.api.ReviewConnectionsManagementApi;
import com.gumtree.tns.api.ReviewDisplayApi;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.gumtree.shared.client.DefaultObjectMapper.defaultObjectMapper;

@Configuration
public class IntegrateExternalReviewsApiConfig {
    public static final String GROUP_COMMAND = "integrate-external-reviews";
    public static final int POOL_SIZE = 10;

    @Bean
    public ReviewConnectionsManagementApi reviewConnectionsManagementApi() {

        String baseUrl = GtProps.getStr(SellerProperty.INTEGRATE_EXTERNAL_REVIEWS_HOST);
        int connectionTimeout = GtProps.getInt(SellerProperty.INTEGRATE_EXTERNAL_REVIEWS_CONNECTION_TIMEOUT_MILLIS);
        int socketTimeout = GtProps.getInt(SellerProperty.INTEGRATE_EXTERNAL_REVIEWS_READ_TIMEOUT_MILLIS);
        return new CommonApiClient(
                baseUrl,
                connectionTimeout,
                socketTimeout,
                GROUP_COMMAND,
                POOL_SIZE,
                defaultObjectMapper())
                .builder(ReviewConnectionsManagementApi.class)
                .withEncoder(new JacksonEncoder())
                .withDecoder(new JacksonDecoder(defaultObjectMapper()))
                .withExecutionTimeout(connectionTimeout + socketTimeout)
                .withDecode404()
                .buildClient();

    }

    @Bean
    public ReviewDisplayApi reviewDisplayApi() {

        String baseUrl = GtProps.getStr(SellerProperty.INTEGRATE_EXTERNAL_REVIEWS_HOST);
        int connectionTimeout = GtProps.getInt(SellerProperty.INTEGRATE_EXTERNAL_REVIEWS_CONNECTION_TIMEOUT_MILLIS);
        int socketTimeout = GtProps.getInt(SellerProperty.INTEGRATE_EXTERNAL_REVIEWS_READ_TIMEOUT_MILLIS);
        return new CommonApiClient(
                baseUrl,
                connectionTimeout,
                socketTimeout,
                GROUP_COMMAND,
                POOL_SIZE,
                defaultObjectMapper())
                .builder(ReviewDisplayApi.class)
                .withEncoder(new JacksonEncoder())
                .withDecoder(new JacksonDecoder(defaultObjectMapper()))
                .withDecode404()
                .withExecutionTimeout(connectionTimeout + socketTimeout)
                .buildClient();
    }
}