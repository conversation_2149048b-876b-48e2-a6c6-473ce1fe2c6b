package com.gumtree.config.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.config.BushfireApiConfig;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.config.profiles.CommonConfigProfiles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;

/**
 * Class to handle BAPI configuration
 */
@Configuration
@Profile(CommonConfigProfiles.BUSHFIRE_API)
@Import({BushfireApiConfig.class})
public class BushfireApiProfileConfig {

    @Autowired
    private BushfireApi bushfireApi;

    @Bean
    public AccountApi accountApi() throws Exception {
        return bushfireApi.accountApi();
    }
}
