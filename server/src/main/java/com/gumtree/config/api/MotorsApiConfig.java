package com.gumtree.config.api;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.ApiClient;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.VehicleDataApi;
import com.gumtree.web.seller.page.ajax.vrm.MotorsApiClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MotorsApiConfig {

    @Bean
    public MotorsApiClient motorsApiClient(final VehicleDataApi vehicleDataApi) {
        return new MotorsApiClient(vehicleDataApi);
    }

    @Bean
    public VehicleDataApi vehicleDataApi() {
        String host = GtProps.getStr(SellerProperty.MOTORS_API_HOST);
        Integer port = GtProps.getInt(SellerProperty.MOTORS_API_PORT);
        Integer connectionTimeout = GtProps.getInt(SellerProperty.MOTORS_API_CONNECTION_TIMEOUT);
        Integer readTimeout = GtProps.getInt(SellerProperty.MOTORS_API_READ_TIMEOUT);
        ApiClient apiClient = new ApiClient().setBasePath(String.format("http://%s:%d", host, port));
        apiClient.getHttpClient().setConnectTimeout(connectionTimeout);
        apiClient.getHttpClient().setReadTimeout(readTimeout);
        return new VehicleDataApi(apiClient);
    }
}
