package com.gumtree.config.api;

import com.google.common.collect.Maps;
import com.gumtree.api.category.UnfilteredCategoryModel;
import com.gumtree.api.config.CachedCategoryModelConfigBase;
import com.gumtree.api.config.CategoryApiConfig;
import com.gumtree.api.config.CategoryModelFactory;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.config.profiles.CommonConfigProfiles;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;

import java.util.Map;


@Profile(CommonConfigProfiles.CATEGORY_API)
@Configuration
@Import(CategoryApiConfig.class)
public class CategoryReadApiConfig extends CachedCategoryModelConfigBase {

    @Override
    public Map<String, Object> filterOut() {
        Map<String, Object> filterOut = Maps.newHashMap();
        filterOut.put("enabled", false);
        filterOut.put("read_only", true);
        return filterOut;
    }

    @Bean
    public UnfilteredCategoryModel unfilteredCategoryModel() {
        Long cacheCheckInterval = GtProps.getLong(SellerProperty.CATSAPI_CACHE_RELOAD_INTERVAL);
        CategoryModelFactory factory = new CategoryModelFactory(categoryReadApi(), cacheCheckInterval);
        UnfilteredCategoryModel ucm = factory.createWithoutFilters();
        return ucm;
    }
}
