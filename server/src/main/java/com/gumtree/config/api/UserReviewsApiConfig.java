package com.gumtree.config.api;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.userreviewsservice.client.ApiClient;
import com.gumtree.userreviewsservice.client.ReviewsReadApi;
import com.gumtree.userreviewsservice.client.ReviewsWriteApi;
import com.gumtree.userreviewsservice.client.UserReviewsServiceErrorDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UserReviewsApiConfig {
    public static final String COMMAND_GROUP_NAME = "URS";
    public static final String CLIENT_ID = "seller";

    @Bean
    public ApiClient defaultReviewApiClient() {
        return new ApiClient(GtProps.getStr(SellerProperty.USER_REVIEWS_API_HOST),
                CLIENT_ID,
                GtProps.getInt(SellerProperty.USER_REVIEWS_API_CONNECTION_TIMEOUT),
                GtProps.getInt(SellerProperty.USER_REVIEWS_API_READ_TIMEOUT),
                COMMAND_GROUP_NAME,
                3);
    }

    @Bean
    public ReviewsReadApi reviewsReadApi(ApiClient apiClient) {
        UserReviewsServiceErrorDecoder errorDecoder = new UserReviewsServiceErrorDecoder(apiClient.getObjectMapper());
        return apiClient.buildClient(ReviewsReadApi.class, errorDecoder);
    }

    @Bean
    public ReviewsWriteApi reviewsWriteApi(ApiClient apiClient) {
        UserReviewsServiceErrorDecoder errorDecoder = new UserReviewsServiceErrorDecoder(apiClient.getObjectMapper());
        return apiClient.buildClient(ReviewsWriteApi.class, errorDecoder);
    }
}
