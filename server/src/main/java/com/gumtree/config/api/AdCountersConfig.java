package com.gumtree.config.api;

import com.gumtree.adcounters.CountersApi;
import com.gumtree.api.OkHttpClientMetricsInterceptor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.MwebProperty;
import com.gumtree.google.authservice.GoogleAuthService;
import com.gumtree.shared.client.CommonApiClient;
import com.gumtree.util.UuidProvider;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounterService;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounterServiceImpl;
import feign.Logger;
import feign.okhttp.OkHttpClient;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.gumtree.shared.client.DefaultObjectMapper.defaultObjectMapper;

@Configuration
public class AdCountersConfig {

    private static final String AD_COUNTER_COMMAND_NAME = "cdata_ad_counter";
    private static final int AD_COUNTER_POOL_SIZE = 5;

    @Autowired
    private MeterRegistry meterRegistry;


    @Bean
    public AdCounterService adCounterService(
            CountersApi countersApi,
            @Qualifier("adCountersGoogleAuthService") GoogleAuthService adCountersGoogleAuthService,
            UuidProvider uuidProvider
    ) {
        return new AdCounterServiceImpl(countersApi, adCountersGoogleAuthService, uuidProvider);
    }

    @Bean
    public CountersApi gtTsaAdCountersApi() {

        String tsaAdCountersBaseUrl = GtProps.getStr(MwebProperty.TSA_ADCOUNTERS_API_BASE_URL);
        int readTimeout = GtProps.getInt(MwebProperty.TSA_AD_COUNTERS_API_READ_TIMEOUT);
        int connectionTimeout = GtProps.getInt(MwebProperty.TSA_AD_COUNTERS_API_CONNECTION_TIMEOUT);
        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(tsaAdCountersBaseUrl,
                connectionTimeout,
                readTimeout,
                AD_COUNTER_COMMAND_NAME,
                AD_COUNTER_POOL_SIZE,
                defaultObjectMapper(),
                client)
                .builder(CountersApi.class)
                .withCircuitBreaker(false)
                .withDefaultGumtreeDecoder()
                .withExecutionTimeout(connectionTimeout + readTimeout)
                .buildClient(Logger.Level.FULL);
    }
}
