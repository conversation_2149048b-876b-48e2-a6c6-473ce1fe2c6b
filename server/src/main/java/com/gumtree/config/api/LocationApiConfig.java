package com.gumtree.config.api;

import com.gumtree.api.CommonApiClient;
import com.gumtree.api.OkHttpClientMetricsInterceptor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.CommonProperty;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.infrastructure.driven.locations.api.LocationsApi;
import feign.okhttp.OkHttpClient;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class LocationApiConfig {
    public static final String COMMAND_GROUP_NAME = "LOCATIONS";
    public static final String CLIENT_ID = "seller";
    public static final int CONNECTION_TIMEOUT = 5000;
    public static final int READ_TIMEOUT = 1000;
    public static final int POOL_SIZE = 10;

    @Autowired
    private MeterRegistry meterRegistry;

    @Bean
    public LocationsApi locationsApi() {
        String baseUrl = GtProps.getStr(SellerProperty.LOCATIONS_URL);
        String ingressUser = GtProps.getStr(CommonProperty.K8S_INGRESS_USER);
        String ingressPassword = GtProps.getStr(CommonProperty.K8S_INGRESS_PASSWORD);

        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(baseUrl, CLIENT_ID, CONNECTION_TIMEOUT, READ_TIMEOUT, COMMAND_GROUP_NAME, POOL_SIZE, client)
                .builder(LocationsApi.class)
                .withBasicAuthentication(ingressUser, ingressPassword)
                .withDecode404()
                .buildClient();
    }
}
