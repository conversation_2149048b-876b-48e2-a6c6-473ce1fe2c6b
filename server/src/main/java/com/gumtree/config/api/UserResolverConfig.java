package com.gumtree.config.api;

import com.gumtree.api.UserResolver;
import com.gumtree.web.seller.service.user.BapiUserService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * BAPI User Api wrapper configuration
 */
@Configuration
public class UserResolverConfig {

    @Bean
    public UserResolver bapiUserResolver() {
        return new BapiUserService();
    }

}
