package com.gumtree.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;

//CHECKSTYLE:OFF:hideutilityclassconstructor
@Configuration
public class SellerPropertiesConfig {

    /**
     * Placeholder configurer is required for @Value annotations to work.
     *
     * Properties are loaded into the environment in {@link SellerApplicationContextInitializer}
     *
     * @return the placeholder configurer
     */
    @Bean
    public static PropertySourcesPlaceholderConfigurer propertyConfigurer() {
        PropertySourcesPlaceholderConfigurer configurer = new PropertySourcesPlaceholderConfigurer();
        configurer.setIgnoreUnresolvablePlaceholders(false);
        return configurer;
    }

    @Bean
    public SellerProperty.RecaptchaEnabled recaptchaEnabled() {
        return new SellerProperty.RecaptchaEnabled();
    }
}
//CHECKSTYLE:ON
