package com.gumtree.config;

import com.gumtree.common.properties.spring.GumtreeContextInitializer;
import com.gumtree.config.profiles.CommonConfigProfiles;
import com.gumtree.config.profiles.activator.ProfileActivator;
import com.gumtree.config.profiles.activator.ProfileActivatorImpl;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.ConfigurableEnvironment;

import java.util.ArrayList;
import java.util.List;

public class SellerApplicationContextInitializer extends GumtreeContextInitializer {

    private static final String APP_NAME = "seller-server";
    private static final Logger LOGGER = LoggerFactory.getLogger(SellerApplicationContextInitializer.class);
    private static final String WITH_FORWARD_PROXY = "withForwardProxy";

    @Override
    protected String getAppName() {
        if (System.getenv(WITH_FORWARD_PROXY) != null) {
            return APP_NAME + "-" + System.getenv(WITH_FORWARD_PROXY) + "-proxy";
        }

        return APP_NAME;
    }

    protected void activateProfiles(ConfigurableEnvironment environment) {
        List<ProfileActivator> profileActivators = getProfileActivators(environment);
        String[] activeProfiles = environment.getActiveProfiles();
        if (ArrayUtils.isEmpty(activeProfiles)) {
            List<String> profiles = new ArrayList<>();
            profiles.add(CommonConfigProfiles.BUSHFIRE_API);
            profiles.add(CommonConfigProfiles.CATEGORY_API);
            profiles.add(CommonConfigProfiles.PRODUCTION_MODELS);
            profiles.add(CommonConfigProfiles.ELASTIC_SEARCH_API);
            for (ProfileActivator profile : profileActivators) {
                profiles.add(profile.getActiveProfile());
            }

            environment.setActiveProfiles(profiles.toArray(new String[profiles.size()]));
        } else {
            List<String> missingProfiles = new ArrayList<>();
            for (ProfileActivator profileActivator : profileActivators) {
                if (!ArrayUtils.contains(activeProfiles, profileActivator.getOnProfile())
                        && !ArrayUtils.contains(activeProfiles, profileActivator.getOffProfile())) {
                    missingProfiles.add(profileActivator.getActiveProfile());
                }
            }

            if (!missingProfiles.isEmpty()) {
                environment.setActiveProfiles(ArrayUtils.addAll(activeProfiles,
                        missingProfiles.toArray(new String[missingProfiles.size()])));
            }
        }

        LOGGER.debug("Starting application with the following active profiles: " + environment.getActiveProfiles());
    }

    private List<ProfileActivator> getProfileActivators(ConfigurableEnvironment environment) {
        List<ProfileActivator> profileActivators = new ArrayList<>();
        profileActivators.add(new ProfileActivatorImpl(environment, "gumtree.redis.enabled",
                CommonConfigProfiles.SESSION_PERSISTENCE_REDIS, CommonConfigProfiles.SESSION_PERSISTENCE_STUB, true));
        return profileActivators;
    }

}
