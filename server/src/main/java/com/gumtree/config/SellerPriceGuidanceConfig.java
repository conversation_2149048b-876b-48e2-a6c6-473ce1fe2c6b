package com.gumtree.config;

import com.gumtree.common.format.PriceFormatter;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.api.PriceGuidanceApi;
import com.gumtree.web.seller.converter.FlatAdConverterUtils;
import com.gumtree.web.seller.service.postad.priceguidance.AdToPriceGuidanceAdConverter;
import com.gumtree.web.seller.service.postad.priceguidance.PriceGuidanceService;
import com.gumtree.web.seller.service.postad.priceguidance.PriceGuidanceServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SellerPriceGuidanceConfig {

    @Autowired
    private FlatAdConverterUtils flatAdConverterUtils;

    @Autowired
    private PriceFormatter priceFormatter;

    @Autowired
    private PriceGuidanceApi priceGuidanceApi;

    @Bean
    public PriceGuidanceService priceGuidanceService() {
        AdToPriceGuidanceAdConverter converter = new AdToPriceGuidanceAdConverter(flatAdConverterUtils, priceFormatter);
        return new PriceGuidanceServiceImpl(priceGuidanceApi, converter);
    }

}
