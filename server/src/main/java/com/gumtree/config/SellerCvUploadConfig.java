package com.gumtree.config;

import com.gumtree.cvstore.spec.CvStoreClient;
import com.gumtree.service.jobs.CvService;
import com.gumtree.service.jobs.CvServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.web.context.WebApplicationContext;


@Configuration
public class SellerCvUploadConfig {

    @Bean
    @Autowired
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.INTERFACES)
    public CvService sellerCvService(CvStoreClient cvStoreClient) {
        return new CvServiceImpl(cvStoreClient);
    }
}
