package com.gumtree.config;

import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.GtProps;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.service.convertr.ConvertrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ConvertrConfig {

    @Autowired
    private CategoryService categoryService;

    @Bean
    public ConvertrService convertrService() {
        boolean isProduction = "prod".equals(GtProps.getStr(Env.Properties.ENV_NAME)); // env on QA is set to "production", using envName
        return new ConvertrService(isProduction, categoryService);
    }

}
