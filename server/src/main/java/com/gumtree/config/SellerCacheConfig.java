package com.gumtree.config;

import net.sf.ehcache.CacheManager;
import net.sf.ehcache.management.ManagementService;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.cache.ehcache.EhCacheManagerFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jmx.export.MBeanExporter;
import org.springframework.jmx.support.MBeanServerFactoryBean;

import javax.management.MBeanServer;

/**
 *
 */
@Configuration
@ImportResource("classpath:META-INF/config/spring/ehcache-config.xml")
public class SellerCacheConfig {

    /**
     *
     * @return Bean
     * @throws Exception -
     */
    @Bean
    public MBeanExporter mBeanExporter() throws Exception {
        MBeanExporter exporter = new MBeanExporter();
        exporter.setServer(mbeanServer());
        return exporter;
    }

    /**
     *
     * @return FactoryBean
     */
    @Bean(name = "cacheManager")
    public FactoryBean<CacheManager> cacheManagerFactoryBean() {
        EhCacheManagerFactoryBean factory = new EhCacheManagerFactoryBean();
        factory.setConfigLocation(new ClassPathResource("META-INF/ehcache.xml"));
        factory.setShared(true);
        return factory;
    }

    /**
     *
     * @return FactoryBean
     */
    @Bean(name = "mbeanServer")
    public FactoryBean<MBeanServer> mbeanServerFactoryBean() {
        MBeanServerFactoryBean factory = new MBeanServerFactoryBean();
        factory.setLocateExistingServerIfPossible(true);
        return factory;
    }

    /**
     *
     * @return Object
     * @throws Exception thrown
     */
    @Bean(name = "registerMBeans")
    public Object registerMBeans() throws Exception {
        ManagementService.registerMBeans(cacheManager(), mbeanServer(), true, true, true, true);
        return new Object();
    }

    /**
     *
     * @return CacheManager
     * @throws Exception on Object retrieval
     */
    public CacheManager cacheManager() throws Exception {
        try {
            CacheManager cacheManager = cacheManagerFactoryBean().getObject();
            return cacheManager;
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     *
     * @return Bean Server
     * @throws Exception t
     */
    public MBeanServer mbeanServer() throws Exception {
        try {
            return mbeanServerFactoryBean().getObject();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }
}
