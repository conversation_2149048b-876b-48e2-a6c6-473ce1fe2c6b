package com.gumtree.config.session;

import com.gumtree.api.UserResolver;
import com.gumtree.common.util.time.Clock;
import com.gumtree.config.profiles.CommonConfigProfiles;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.DefaultUserSessionService;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.storage.ratelimit.RateCheckResult;
import com.gumtree.web.storage.ratelimit.RateLimiterPersister;
import com.gumtree.web.storage.strategy.MapPersistenceStrategy;
import com.gumtree.web.storage.strategy.MapSessionAwarePersistenceStrategy;
import com.gumtree.web.storage.strategy.SessionPersistenceStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.servlet.FlashMapManager;
import org.springframework.web.servlet.support.SessionFlashMapManager;

import javax.servlet.http.HttpServletRequest;

@Configuration
@Profile(CommonConfigProfiles.SESSION_PERSISTENCE_STUB)
public class StubSessionPersistenceConfig {

    @Value("${gumtree.session.map.expiry.checkms}")
    private Long mapClearExpiredCheckMs;

    @Autowired
    private Clock clock;

    @Autowired
    private UserSessionService userSessionService;

    @Bean
    public FlashMapManager flashMapManager() {
        return new SessionFlashMapManager();
    }

    @Bean(name = "syiPersistenceStrategy")
    public SessionPersistenceStrategy syiPersistenceStrategy() {
        return new MapSessionAwarePersistenceStrategy(userSessionService, mapClearExpiredCheckMs, clock);
    }

    @Bean(name = "syiPersistenceSimpleStrategy")
    public SessionPersistenceStrategy syiPersistenceSimpleStrategy() {
        return new MapSessionAwarePersistenceStrategy(userSessionService, mapClearExpiredCheckMs, clock);
    }

    @Bean(name = "checkoutPersistenceStrategy")
    public SessionPersistenceStrategy checkoutPersistenceStrategy() {
        return new MapSessionAwarePersistenceStrategy(userSessionService, mapClearExpiredCheckMs, clock);
    }

    @Bean(name = "checkoutPersistenceSimpleStrategy")
    public SessionPersistenceStrategy checkoutPersistenceSimpleStrategy() {
        return new MapSessionAwarePersistenceStrategy(userSessionService, mapClearExpiredCheckMs, clock);
    }

    @Bean(name = "userPersistenceStrategy")
    public SessionPersistenceStrategy userPersistenceStrategy() {
        return new MapSessionAwarePersistenceStrategy(userSessionService, mapClearExpiredCheckMs, clock);
    }

    @Bean(name = "madFilterPersistenceStrategy")
    public SessionPersistenceStrategy madFilterPersistenceStrategy() {
        return new MapSessionAwarePersistenceStrategy(userSessionService, mapClearExpiredCheckMs, clock);
    }

    @Bean(name = "secureTokenPersistenceStrategy")
    public SessionPersistenceStrategy secureTokenPersistenceStrategy() {
        return new MapSessionAwarePersistenceStrategy(userSessionService, mapClearExpiredCheckMs, clock);
    }

    @Bean(name = "openIDPersistenceStrategy")
    public SessionPersistenceStrategy openIDPersistenceStrategy() {
        return new MapPersistenceStrategy(mapClearExpiredCheckMs, clock);
    }

    @Bean
    @Autowired
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.INTERFACES)
    public UserSessionService cookieIdProvider(HttpServletRequest request, UserResolver userResolver, CookieResolver cookieResolver) {
        return new DefaultUserSessionService(cookieResolver, request, userResolver);
    }

    @Bean
    public RateLimiterPersister rateLimiterPersister() {
        return new RateLimiterPersister() {
            @Override
            public RateCheckResult checkRate(int limitPerInterval, String key) {
                return RateCheckResult.EMPTY;
            }

            @Override
            public void incRateCounter(int intervalSeconds, String key) {

            }
        };
    }
}
