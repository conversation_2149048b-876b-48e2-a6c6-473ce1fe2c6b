package com.gumtree.config.session;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.SessionCookieConfig;
import javax.servlet.annotation.WebListener;

@WebListener
public class SessionConfigListener implements ServletContextListener {

    public static final String SESSION_COOKIE_PATH = "/";
    public static final String DOMAIN_PROD_PLACEHOLDER = "gumtree.com";
    public static final String SESSION_COOKIE_NAME = "sessionCookieName";
    public static final String GUMTREE_LOGIN_COOKIE_ENV_NAME = "gumtree.login.cookie.domain";

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        String sessionCookieName = sce.getServletContext().getInitParameter(SESSION_COOKIE_NAME);
        String domain = getDomainFromEnv();

        SessionCookieConfig sessionCookieConfig = sce.getServletContext().getSessionCookieConfig();
        sessionCookieConfig.setName(sessionCookieName);
        sessionCookieConfig.setDomain(domain != null ? domain : DOMAIN_PROD_PLACEHOLDER);
        sessionCookieConfig.setPath(SESSION_COOKIE_PATH);
        sessionCookieConfig.setHttpOnly(true);
        sessionCookieConfig.setSecure(true);
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
    }

    protected String getDomainFromEnv() {
        return System.getenv(GUMTREE_LOGIN_COOKIE_ENV_NAME);
    }
}
