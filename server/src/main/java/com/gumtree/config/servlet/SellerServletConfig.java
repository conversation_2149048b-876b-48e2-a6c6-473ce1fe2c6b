package com.gumtree.config.servlet;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.config.SellerApiAuthenticationConfig;
import com.gumtree.config.SellerPriceGuidanceConfig;
import com.gumtree.config.SellerPropertiesConfig;
import com.gumtree.config.SellerUserReviewsConfig;
import com.gumtree.config.profiles.SellerProfilesConfig;
import com.gumtree.config.thirdparty.SellerThirdPartyConfig;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.web.common.page.context.RequestScopedGumtreePageContext;
import com.gumtree.web.common.propertyeditor.GumtreePropertyEditorRegistrar;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.EmailViewDecider;
import com.gumtree.web.seller.page.ViewDecider;
import com.gumtree.web.seller.page.context.GumtreeSellerPageContext;
import com.gumtree.web.seller.page.manageads.model.ManageAdsWorkspace;
import com.gumtree.web.seller.page.manageads.model.ManageAdsWorkspaceImpl;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.seller.service.CheckoutContainerImpl;
import com.gumtree.web.seller.service.postad.UserPostcodeLookupService;
import com.gumtree.web.seller.service.postad.UserPostcodeLookupServiceImpl;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationMetadata;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import com.gumtree.web.seller.service.presentation.config.MappingBasedAttributePresentationService;
import com.gumtree.web.seller.service.skill.SkillAttributeMetadataService;
import com.gumtree.web.seller.service.skill.SkillAttributeMetadataServiceImpl;
import com.gumtree.web.seller.service.skill.model.SkillAttributeMetadata;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import com.gumtree.zeno.core.config.ZenoConfig;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.bind.support.ConfigurableWebBindingInitializer;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import javax.validation.Validator;
import java.io.IOException;

@Configuration
@EnableWebMvc
@ComponentScan(basePackages = {
        "com.gumtree.web.seller.page",
        "com.gumtree.web.seller.reporting",
        "com.gumtree.web.common.page",
        "com.gumtree.web.common.thirdparty",
        "com.gumtree.web.common.util",
        "com.gumtree.web.common.functions",
        "com.gumtree.web.common.popularsearch",
        "com.gumtree.web.common.error",
        "com.gumtree.web.common.security",
        "com.gumtree.web.common.format",
        "com.gumtree.common.format",
        "com.gumtree.web.reporting",
        "com.gumtree.web.util",
        "com.gumtree.web.seller.service",
        "com.gumtree.domain.newattribute",
        "com.gumtree.service.category",
        "com.gumtree.web.security.login",
        "com.gumtree.web.common.template",
        "com.gumtree.legacy.impl",
        "com.gumtree.web.zeno",
        "com.gumtree.zeno.core.controller",
        "com.gumtree.web.common.device",
        "com.gumtree.web.seller.page.messagecentre",
        "com.gumtree.web.seller.healthcheck",
        "com.gumtree.motors",
        "com.gumtree.web.seller.security.apiauthentication.controller",
        "com.gumtree.web.seller.security.capi.controller",
})
@Import({
        SellerPropertiesConfig.class,
        SellerWebsiteInterceptorsConfig.class,
        SellerWebsiteArgumentResolversConfig.class,
        SellerWebsiteMessageSourceConfig.class,
        SellerWebsiteFreemarkerConfig.class,
        SellerProfilesConfig.class,
        SellerThirdPartyConfig.class,
        ZenoConfig.class,
        SellerUserReviewsConfig.class,
        SellerPriceGuidanceConfig.class,
        SellerApiAuthenticationConfig.class,
        AdSearchApiConfig.class
})
@ImportResource({
        "classpath:META-INF/config/spring/ehcache-config.xml"
})
public class SellerServletConfig implements InitializingBean {

    @Autowired
    private RequestMappingHandlerAdapter requestMappingHandlerAdapter;

    @Autowired
    private SellerSessionDataService sessionDataService;

    @Autowired
    private UserSession userSession;

    @Value("${gumtree.images.maxUploadSize}")
    private long maxImageUploadSize;

    @Autowired
    private FullAdsSearchApi fullAdsSearchApi;

    /**
     * {@inheritDoc}
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        ConfigurableWebBindingInitializer initializer =
                (ConfigurableWebBindingInitializer) requestMappingHandlerAdapter.getWebBindingInitializer();
        initializer.setPropertyEditorRegistrar(new GumtreePropertyEditorRegistrar());
    }

    /**
     * Request-scoped common page context.
     *
     * @return the context
     */
    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.INTERFACES)
    public RequestScopedGumtreePageContext gumtreePageContext() {
        return new GumtreeSellerPageContext();
    }

    /**
     * Local validator factory bean.
     *
     * @return the bean
     */
    @Bean
    public Validator validator() {
        return new LocalValidatorFactoryBean();
    }

    /**
     * Email view decider
     *
     * @return the bean
     */
    @Bean(name = "emailViewDecider")
    public ViewDecider emailViewDecider() {
        return new EmailViewDecider();
    }

    /**
     * Order session bean.
     *
     * @return the bean
     */
    @Bean
    public CheckoutContainer checkoutContainer() {
        return new CheckoutContainerImpl(sessionDataService);
    }

    /**
     * Attribute presentation service.
     *
     * @return the service
     * @throws IOException if the metadata could not be read
     */
    @Bean
    public AttributePresentationService attributePresentationService(CategoryModel categoryModel) throws IOException {

        ClassPathResource attributeMetadataResource =
                new ClassPathResource("META-INF/config/attributes/attribute-presentation-metadata.json");

        ObjectMapper objectMapper = new ObjectMapper();

        AttributePresentationMetadata metadata = objectMapper.readValue(
                attributeMetadataResource.getInputStream(), AttributePresentationMetadata.class);

        return new MappingBasedAttributePresentationService(categoryModel, metadata.getMetadata());
    }

    /**
     * Manage ads session bean.
     *
     * @return the bean
     */
    @Bean
    public ManageAdsWorkspace manageAdsWorkspace() {
        return new ManageAdsWorkspaceImpl(sessionDataService, userSession);
    }

    /**
     * Multipart resolver bean.
     *
     * @return the bean
     */
    @Bean
    public MultipartResolver multipartResolver() {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
        multipartResolver.setMaxUploadSize(maxImageUploadSize);
        return multipartResolver;
    }

    @Bean
    public UserPostcodeLookupService userPostcodeLookupService() {
        return new UserPostcodeLookupServiceImpl(fullAdsSearchApi);
    }

    @Bean
    public SkillAttributeMetadataService skillAttributeMetadataService() throws IOException {
        ClassPathResource skillMetadataResource =
                new ClassPathResource("META-INF/config/attributes/seller-skills-attributes.json");

        ObjectMapper objectMapper = new ObjectMapper();

        SkillAttributeMetadata metadata = objectMapper.readValue(
                skillMetadataResource.getInputStream(), SkillAttributeMetadata.class);

        return new SkillAttributeMetadataServiceImpl(metadata.getCategories());
    }
}
