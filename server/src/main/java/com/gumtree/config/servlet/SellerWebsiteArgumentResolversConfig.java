package com.gumtree.config.servlet;

import com.gumtree.web.common.ip.RemoteIPArgumentResolver;
import com.gumtree.web.common.page.context.GumtreePageContextArgumentResolver;
import com.gumtree.web.security.login.LoginFailureArgumentResolver;
import com.gumtree.web.security.shiro.SubjectArgumentResolver;
import com.gumtree.web.seller.resolver.AccountArgumentResolver;
import com.gumtree.web.seller.resolver.UserArgumentResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.List;

/**
 * Configuration for MVC Argument Resolvers.
 */
@Configuration
public class SellerWebsiteArgumentResolversConfig extends WebMvcConfigurerAdapter {

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(gumtreePageContextArgumentResolver());
        argumentResolvers.add(remoteIpArgumentResolver());
        argumentResolvers.add(loginFailureArgumentResolver());
        argumentResolvers.add(subjectArgumentResolver());
        argumentResolvers.add(accountArgumentResolver());
        argumentResolvers.add(userArgumentResolver());
    }

    /**
     * Bean for resolving page context arguments.
     *
     * @return a resolver
     */
    @Bean
    public HandlerMethodArgumentResolver gumtreePageContextArgumentResolver() {
        return new GumtreePageContextArgumentResolver();
    }

    /**
     * Bean for resolving the remote IP address.
     *
     * @return a resolver
     */
    @Bean
    public HandlerMethodArgumentResolver remoteIpArgumentResolver() {
        return new RemoteIPArgumentResolver();
    }

    /**
     * Bean for resolving login failure arguments
     *
     * @return a resolver
     */
    @Bean
    public HandlerMethodArgumentResolver loginFailureArgumentResolver() {
        return new LoginFailureArgumentResolver();
    }

    /**
     * Bean for resolving teh subject argument.
     *
     * @return a resolver
     */
    @Bean
    public HandlerMethodArgumentResolver subjectArgumentResolver() {
        return new SubjectArgumentResolver();
    }

    /**
     * account resolver
     * @return a resolver
     */
    @Bean
    public HandlerMethodArgumentResolver accountArgumentResolver() {
        return new AccountArgumentResolver();
    }

    /**
     * user resolver
     * @return a resolver
     */
    @Bean
    public HandlerMethodArgumentResolver userArgumentResolver() {
        return new UserArgumentResolver();
    }
}
