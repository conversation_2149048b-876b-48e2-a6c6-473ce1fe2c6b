package com.gumtree.config;

import com.gumtree.common.properties.GtProps;
import com.gumtree.web.service.images.secure.DefaultSecureImageService;
import com.gumtree.web.service.images.secure.SecureImageService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SellerImageServiceConfig {

    @Bean
    public SecureImageService secureImageService() {
        return new DefaultSecureImageService(GtProps.getStr(CommonProperty.SECURE_IMAGE_DOMAIN));
    }
}
