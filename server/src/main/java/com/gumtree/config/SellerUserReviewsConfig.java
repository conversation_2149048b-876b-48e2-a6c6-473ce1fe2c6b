package com.gumtree.config;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.tns.api.ReviewConnectionsManagementApi;
import com.gumtree.tns.api.ReviewDisplayApi;
import com.gumtree.userreviewsservice.client.ReviewsReadApi;
import com.gumtree.web.seller.page.reviews.service.ExternalReviewsService;
import com.gumtree.web.seller.page.reviews.service.UserReviewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SellerUserReviewsConfig {
    @Autowired
    private ReviewsReadApi reviewsReadApi;

    @Autowired
    private ReviewConnectionsManagementApi externalReviewConnectionsManagementApi;

    @Autowired
    private ReviewDisplayApi externalReviewDisplayApi;

    @Autowired
    private FullAdsSearchApi fullAdsSearchApi;

    @Autowired
    private CategoryModel categoryModel;

    @Bean
    public UserReviewsService userReviewsService() {
        return new UserReviewsService(reviewsReadApi);
    }

    @Bean
    public ExternalReviewsService externalReviewsService() {
        return new ExternalReviewsService(externalReviewConnectionsManagementApi, externalReviewDisplayApi, fullAdsSearchApi, categoryModel);
    }
}
