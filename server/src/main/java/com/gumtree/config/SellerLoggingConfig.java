package com.gumtree.config;

import com.gumtree.common.logging.service.requesttoken.RequestTokenService;
import com.gumtree.common.logging.service.requesttoken.ThreadLocalRequestTokenService;
import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.SimpleClock;
import com.gumtree.web.filter.RequestTokenLoggingFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;

/**
 *
 */
@Configuration
public class SellerLoggingConfig {

    /**
     *
     * @return   RequestTokenService
     */
    @Bean
    public RequestTokenService requestTokenService() {
        return new ThreadLocalRequestTokenService();
    }

    /**
     *
     * @return Clock
     */
    @Bean
    public Clock timingClock() {
        return new SimpleClock();
    }

    /**
     *
     * @return Filter
     */
    @Bean
    public Filter requestTokenFilter(RequestTokenService requestTokenService) {
        return new RequestTokenLoggingFilter(requestTokenService);
    }
}
