package com.gumtree.web.seller.page.manageads.mycompany.command;

import com.gumtree.api.Account;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.api.domain.account.DefaultImageBean;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

/**
 * Command for removing default image from an account.
 */
public final class ChangeDefaultImageCommand extends AuthenticatedApiCall<Account> {

    private Long accountId;

    private Long imageId;

    /**
     * Constructor.
     *
     * @param accountId      the account id
     * @param imageId        the image id to save
     * @param apiKeyProvider the api key provider
     */
    public ChangeDefaultImageCommand(Long accountId, Long imageId, ApiKeyProvider apiKeyProvider) {
        super(apiKeyProvider);
        this.accountId = accountId;
        this.imageId = imageId;
    }

    @Override
    public Account execute(BushfireApi api) {
        DefaultImageBean imageBean = new DefaultImageBean();
        imageBean.setImageId(imageId);
        return api.create(AccountApi.class, getApiKey()).setDefaultImage(accountId, imageBean);
    }
}
