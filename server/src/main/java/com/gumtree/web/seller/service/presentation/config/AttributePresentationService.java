package com.gumtree.web.seller.service.presentation.config;

import com.gumtree.web.seller.page.postad.model.CategorySpecificPostAdFormPanels;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeGroup;

import java.util.List;
import java.util.Map;

/**
 * Service for generating attribute presentation metadata for a given category.
 */
public interface AttributePresentationService {

    /**
     * Get attribute group metadata for the given category.
     *
     * @param categoryId the category id.
     * @param formAttributes the attributes values entered by the user to the SYI form
     * @return attribute group metadata for the given category.
     */
    List<PostAdAttributeGroup> loadAttributeGroups(Long categoryId, Map<String, String> formAttributes);

    /**
     * Get attribute groups metadata for the given category grouped by group priority
     *
     * @param categoryId the category id of the category to get groups for
     * @param formAttributes the attributes values entered by the user to the SYI form
     *
     * @return attribute group metadata for the given category
     */
    CategorySpecificPostAdFormPanels loadPrioritisedCategorySpecificFormPanels(Long categoryId, Map<String, String> formAttributes);
}
