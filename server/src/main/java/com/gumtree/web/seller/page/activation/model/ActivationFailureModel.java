package com.gumtree.web.seller.page.activation.model;

import com.gumtree.web.seller.page.activation.ActivationResendBean;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.springframework.web.servlet.ModelAndView;

public class ActivationFailureModel extends CommonModel {

    private ActivationResendBean activationResendBean;

    public ActivationFailureModel() {
        // required for spring mapping
    }

    private ActivationFailureModel(CoreModel core, Builder builder) {
        super(core);
        this.activationResendBean = builder.activationResendBean;
    }

    public ActivationResendBean getForm() {
        return activationResendBean;
    }

    public void setForm(ActivationResendBean activationResendBean) {
        this.activationResendBean = activationResendBean;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private ActivationResendBean activationResendBean;

        public Builder withActivationResendBean(ActivationResendBean activationResendBean) {
            this.activationResendBean = activationResendBean;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.ActivationFailure;
            coreModelBuilder.withTitle("Activation Failure | My Gumtree - Gumtree");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new ActivationFailureModel(coreModelBuilder.build(page), this));
        }
    }
}
