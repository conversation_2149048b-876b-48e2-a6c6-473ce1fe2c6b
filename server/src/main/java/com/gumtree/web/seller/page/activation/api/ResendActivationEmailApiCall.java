package com.gumtree.web.seller.page.activation.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.domain.user.beans.UserStatusBean;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.api.client.executor.ApiCall;

/**
 * Implementation of {@link ApiCall} to resend an activation email for a user.
 * @deprecated should be replace with one from bapi-client
 */
public final class ResendActivationEmailApiCall implements ApiCall<Void> {

    private String username;

    /**
     * Constructor.
     *
     * @param username the username to send email for
     */
    public ResendActivationEmailApiCall(String username) {
        this.username = username;
    }

    /**
     * This call actually forces a activation email as well as changes the user status to Awaiting Activation,
     * not obvious, but hey-ho.
     *
     * @param api
     * @return
     */
    @Override
    public Void execute(BushfireApi api) {
        UserStatusBean userStatusBean = new UserStatusBean();
        userStatusBean.setUsername(username);
        userStatusBean.setStatus(UserStatus.AWAITING_ACTIVATION);
        api.create(UserApi.class).newStatus(username, userStatusBean);
        return null;
    }
}
