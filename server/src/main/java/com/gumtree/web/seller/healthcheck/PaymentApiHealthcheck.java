package com.gumtree.web.seller.healthcheck;

import com.gumtree.healthcheck.core.Health;
import com.gumtree.healthcheck.core.HealthCheck;
import com.gumtree.healthcheck.core.HealthCheckRegistrator;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;

import static java.lang.String.format;

public class PaymentApiHealthcheck implements HealthCheck {

    private final String paymentApiHost;
    private final Integer paymentApiPort;
    private final Integer connectionTimeout;
    private final Integer readTimeout;

    public PaymentApiHealthcheck(String paymentApiHost, Integer paymentApiPort, Integer connectionTimeout, Integer readTimeout) {
        this.paymentApiHost = paymentApiHost;
        this.paymentApiPort = paymentApiPort;
        this.connectionTimeout = connectionTimeout;
        this.readTimeout = readTimeout;
    }

    @PostConstruct
    public void postConstruct() {
        HealthCheckRegistrator.register(this);
    }

    @Override
    public String getId() {
        return "payment-api";
    }

    @Override
    public String getRemote() {
        return format("http://%s:%d", paymentApiHost, paymentApiPort);
    }

    @Override
    public Health getHealth() {
        try {
            URLConnection connection = new URL(getRemote()).openConnection();
            connection.setConnectTimeout(connectionTimeout);
            connection.setReadTimeout(readTimeout);
            connection.connect();
            return Health.healthy();
        } catch (IOException e) {
            return Health.unhealthy(format("Error connecting to Payment API: %s", getRemote()));
        }
    }
}
