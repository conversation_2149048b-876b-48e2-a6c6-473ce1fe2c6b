package com.gumtree.web.seller.security.apiauthentication;

public class JsonWebTokenProperties {

    private String issuer;
    private String privateKey;
    private int tokenNotValidBeforeSeconds;
    private int tokenNotValidAfterSeconds;
    private String jwtSecret;

    public JsonWebTokenProperties(String issuer, String privateKey, int tokenNotValidBeforeSeconds,
                                  int tokenNotValidAfterSeconds,String jwtSecret) {
        this.issuer = issuer;
        this.privateKey = privateKey;
        this.tokenNotValidBeforeSeconds = tokenNotValidBeforeSeconds;
        this.tokenNotValidAfterSeconds = tokenNotValidAfterSeconds;
        this.jwtSecret = jwtSecret;
    }

    public String getIssuer() {
        return issuer;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public int getTokenNotValidBeforeSeconds() {
        return tokenNotValidBeforeSeconds;
    }

    public int getTokenNotValidAfterSeconds() {
        return tokenNotValidAfterSeconds;
    }

    public String getJwtSecret() {
        return jwtSecret;
    }
}
