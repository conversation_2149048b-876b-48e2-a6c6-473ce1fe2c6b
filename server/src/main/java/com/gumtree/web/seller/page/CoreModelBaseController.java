package com.gumtree.web.seller.page;

import com.google.common.base.Optional;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.common.model.CoreModel;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;

/**
 * A base controller with some utility methods.
 * // todo - delete this controller - use core model factory directly no point to use inheritance to share code
 */
public abstract class CoreModelBaseController extends NoDependenciesController {
    @Autowired
    private CoreModel.BuilderFactory coreModelBuilderFactory;

    protected CookieResolver cookieResolver;

    protected CategoryModel categoryModel;

    private UserSessionService userSessionService;

    @Autowired
    public CoreModelBaseController(UserSessionService userSessionService,
                                   CategoryModel categoryModel,
                                   CookieResolver cookieResolver) {
        this.userSessionService = userSessionService;
        this.categoryModel = categoryModel;
        this.cookieResolver = cookieResolver;
    }

    protected Optional<User> getLoggedInUser(){
        return userSessionService.getUser();
    }

    protected CoreModel.Builder getCoreModelBuilder(HttpServletRequest request) {
        return coreModelBuilderFactory.create(request);
    }

    protected CoreModel.Builder getCoreModelBuilder(HttpServletRequest request, Long categoryId) {
        Optional<Category> categoryOpt = categoryModel.getCategory(categoryId);
        Category category = categoryOpt.isPresent() ? categoryOpt.get() : categoryModel.getRootCategory();
        return getCoreModelBuilder(request, category);
    }

    protected CoreModel.Builder getCoreModelBuilder(HttpServletRequest request, Category category) {
        return coreModelBuilderFactory.create(request, category);
    }

    void setCoreModelBuilderFactory(CoreModel.BuilderFactory coreModelBuilderFactory) {
        this.coreModelBuilderFactory = coreModelBuilderFactory;
    }
}
