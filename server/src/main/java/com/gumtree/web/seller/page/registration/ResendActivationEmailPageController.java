package com.gumtree.web.seller.page.registration;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.UserResponse;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.activation.api.ResendActivationEmailApiCall;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.login.controller.LoginController;
import com.gumtree.web.seller.page.registration.model.ConfirmationModel;
import com.gumtree.web.seller.page.registration.model.ResendActivationModel;
import com.gumtree.web.seller.service.user.forgotpassword.PasswordResetService;
import com.gumtree.web.zeno.userregistration.UserActivationBeginZenoEvent;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserActivationResend;
import com.gumtree.zeno.core.service.ZenoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.Map;

import static com.gumtree.api.error.ApiErrorCode.ALREADY_ACTIVATED;

@Controller
@GumtreePage(PageType.UserActivationResend)
@GoogleAnalytics
public class ResendActivationEmailPageController extends BaseSellerController {

    public static final String PAGE_PATH = "/resend-activation";
    private static final String RESULT_PATH = "/resend-activation-result";
    public static final String EXPERIMENT_PAGE_PATH = "/new-resend-activation";

    private static final String EMAIL_PARAM = "resend_email";

    private final UserSession authenticatedUserSession;

    private final BushfireApi bushfireApi;
    private final PasswordResetService passwordResetService;
    private final UserServiceFacade userServiceFacade;

    private static final Logger LOGGER = LoggerFactory.getLogger(ResendActivationEmailPageController.class);

    @Autowired
    public ResendActivationEmailPageController(CookieResolver cookieResolver,
                                               CategoryModel categoryModel,
                                               ApiCallExecutor apiCallExecutor,
                                               ErrorMessageResolver messageResolver,
                                               UrlScheme urlScheme,
                                               UserSession authenticatedUserSession,
                                               UserSessionService userSessionService,
                                               BushfireApi bushfireApi,
                                               ZenoService zenoService,
                                               PasswordResetService passwordResetService,
                                               UserServiceFacade userServiceFacade) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.authenticatedUserSession = authenticatedUserSession;
        this.bushfireApi = bushfireApi;
        this.passwordResetService = passwordResetService;
        this.zenoService = zenoService;
        this.userServiceFacade = userServiceFacade;
    }

    /**
     * @return the login form action
     */
    @ModelAttribute("loginFormAction")
    public final String loginFormAction() {
        return LoginController.PAGE_PATH;
    }

    /**
     * Resend the activation email.
     */
    @RequestMapping(value = PAGE_PATH, method = RequestMethod.GET)
    public final ModelAndView resendActivation(Model model, RedirectAttributes redirectAttributes) {
        Long userId = authenticatedUserSession.getUser().getId();
        return resendActivation(userId, model, redirectAttributes);
    }

    @RequestMapping(value = PAGE_PATH + "/{id}", method = RequestMethod.GET)
    public final ModelAndView resendActivation(@PathVariable("id") Long userId,
                                               Model model, RedirectAttributes redirectAttributes) {

        ApiResponse<UserResponse> userResponse = userServiceFacade.getUserById(userId);
        if (!userResponse.isDefined()) {
            LOGGER.info("User not found for id, resend activation failed.");
            return redirect(LoginController.PAGE_PATH);
        }
        String email = userResponse.get().getUserEmail();
        ApiCallResponse<Void> apiResponse = execute(new ResendActivationEmailApiCall(email));

        if (apiResponse.isErrorResponse()) {
            if (ALREADY_ACTIVATED == apiResponse.getErrorCode()) {
                passwordResetService.resetPassword(email);
            } else {
                populateErrors(model, apiResponse);
                return redirect(LoginController.PAGE_PATH);
            }
        }

        redirectAttributes.addFlashAttribute(EMAIL_PARAM, email);
        return redirect(RESULT_PATH);
    }

    @RequestMapping(value = RESULT_PATH, method = RequestMethod.GET)
    public final ModelAndView resendActivationResult(HttpServletRequest request, Model model) {

        String email = (String) model.asMap().get(EMAIL_PARAM);

        if (StringUtils.isEmpty(email)) {
            LOGGER.info("Email missing, resend activation failed.");
            return redirect(LoginController.PAGE_PATH);
        }

        User user = bushfireApi.userApi().getUser(email);
        String resendPath = PAGE_PATH + "/" + user.getId() + "/";

        CoreModel.Builder coreModel = getCoreModelBuilder(request)
                .withGaEvents(Lists.newArrayList(UserActivationResend.class.getSimpleName()));
        ConfirmationModel.Builder confirmationModel = ConfirmationModel.builder()
                .withEmailAddress(email)
                .withResendPath(resendPath)
                .withResendFlow(true);


        if (user.getStatus() == UserStatus.AWAITING_ACTIVATION) {
            zenoService.logEvent(new UserActivationBeginZenoEvent(user));
        }

        return confirmationModel.build(coreModel);
    }

    @RequestMapping(value = EXPERIMENT_PAGE_PATH + "/{id}", method = RequestMethod.GET)
    public ResponseEntity<ResendActivationModel> newResendActivation(@PathVariable("id") Long userId) {
        if (userId == null) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        User user = bushfireApi.userApi().getUser(userId);
        String email = user.getEmail();

        ApiCallResponse<Void> apiResponse = execute(new ResendActivationEmailApiCall(email));
        if (apiResponse.isErrorResponse()) {
            ApiErrorCode apiErrorCode = apiResponse.getErrorCode();

            if (ALREADY_ACTIVATED == apiErrorCode) {
                passwordResetService.resetPassword(email);
            }

            return new ResponseEntity<>(
                    buildResendActivationModel(true, populateError(apiResponse), userId),
                    apiErrorCode.getStatus()
            );
        }

        if (user.getStatus() == UserStatus.AWAITING_ACTIVATION) {
            zenoService.logEvent(new UserActivationBeginZenoEvent(user));
        }

        return new ResponseEntity<>(
                buildResendActivationModel(false, null, userId),
                HttpStatus.OK
        );
    }

    private ResendActivationModel buildResendActivationModel(boolean withError,
                                                             Map<String, List<String>> errors,
                                                             Long userId){
        return ResendActivationModel.builder()
                .resendPath(buildResendPath(userId))
                .withError(withError)
                .errors(errors)
                .build();
    }

    private Map<String, List<String>> populateError(ApiCallResponse<Void> apiResponse) {
        Map<String, List<String>> errors = Maps.newHashMap();
        ReportableErrorsMessageResolvingErrorSource reportableError = populateErrors(apiResponse);
        errors.putAll(reportableError.getAllResolvedFieldErrorMessages());
        errors.put("global", reportableError.getResolvedGlobalErrorMessages());
        return errors;
    }

    private String buildResendPath(Long userId) {
        return EXPERIMENT_PAGE_PATH + "/" + userId;
    }

}
