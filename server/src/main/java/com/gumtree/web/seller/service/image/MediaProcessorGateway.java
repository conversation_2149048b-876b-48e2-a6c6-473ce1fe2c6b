package com.gumtree.web.seller.service.image;

import com.gumtree.mediaprocessor.api.ImageApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
public class MediaProcessorGateway {

    private final ImageApi imageApi;

    @Autowired
    public MediaProcessorGateway(@Qualifier("mediaProcessorImageApi") ImageApi imageApi) {
        this.imageApi = imageApi;
    }

    public Long post(MultipartFile file) {
        return imageApi.post(file).toBlocking().value().getId();
    }

}
