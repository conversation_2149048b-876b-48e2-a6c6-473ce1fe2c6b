package com.gumtree.web.seller.service.adstats;

import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
class AdvertStatisticDataFactoryOfFactory {

    private UrlScheme urlScheme;

    @Autowired
    AdvertStatisticDataFactoryOfFactory(UrlScheme urlScheme) {
        this.urlScheme = urlScheme;
    }

    AdvertStatisticDataFactory createFactoryWithStats(Map<Long, AdCounters> adCountersMap) {
        return new AdvertStatisticDataFactory(adCountersMap, urlScheme);
    }
}
