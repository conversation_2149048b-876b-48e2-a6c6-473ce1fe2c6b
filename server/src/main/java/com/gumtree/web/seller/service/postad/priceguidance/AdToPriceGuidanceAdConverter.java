package com.gumtree.web.seller.service.postad.priceguidance;

import com.gumtree.common.format.PriceFormatter;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.entity.ImageEntity;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.Ad;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.Category;
import com.gumtree.web.seller.converter.FlatAdConverterUtils;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceAd;
import org.springframework.core.convert.converter.Converter;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

public class AdToPriceGuidanceAdConverter implements Converter<Ad, PriceGuidanceAd> {

    private final FlatAdConverterUtils flatAdConverterUtils;
    private final PriceFormatter priceFormatter;

    public AdToPriceGuidanceAdConverter(FlatAdConverterUtils flatAdConverterUtils,
                                        PriceFormatter priceFormatter) {
        this.flatAdConverterUtils = flatAdConverterUtils;
        this.priceFormatter = priceFormatter;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final PriceGuidanceAd convert(Ad ad) {
        Long primaryCategoryId = getPrimaryCategoryId(ad);
        Map<String, Attribute> adAttributeMap = flatAdConverterUtils.getAdAttributeMap(primaryCategoryId, ad.getAttribute());

        String sellerType = flatAdConverterUtils.getSellerType(primaryCategoryId, adAttributeMap);
        String primaryImage = Optional.ofNullable(getPrimaryImage(ad)).map(Image::getBaseUrl).orElse(null);
        Optional<String> vehicleMileage = flatAdConverterUtils.getVehicleMileage(primaryCategoryId, adAttributeMap);

        BigDecimal price = flatAdConverterUtils.getPrice(adAttributeMap);
        String formattedPrice = priceFormatter.format(price, null, primaryCategoryId);

        return new PriceGuidanceAd(
                ad.getId(),
                ad.getTitle(),
                formattedPrice,
                primaryImage,
                sellerType,
                vehicleMileage.orElse(null));
    }

    private Image getPrimaryImage(Ad advert) {
        if (advert.getPrimaryImageUrl() != null) {
            ImageEntity image = new ImageEntity();
            image.setBaseUrl(advert.getPrimaryImageUrl());
            return image;
        }
        return null;
    }

    private Long getPrimaryCategoryId(Ad ad) throws IllegalArgumentException {
        //noinspection ConstantConditions
        return Optional.ofNullable(ad.getCategories())
                .map(Collection::stream)
                .flatMap(s -> s.filter(c -> c.getPrimary() != null).filter(Category::getPrimary).findFirst())
                .orElseThrow(() -> new IllegalArgumentException("Missing category id of ad: " + ad.getId()))
                .getId();
    }

}
