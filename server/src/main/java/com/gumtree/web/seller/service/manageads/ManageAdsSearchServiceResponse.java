package com.gumtree.web.seller.service.manageads;

import com.gumtree.api.Ad;

import java.util.List;

/**
 * A response that holds adverts and display variables for the manage ads page
 */
public class ManageAdsSearchServiceResponse {
    private List<Ad> adverts;
    private Integer totalResults;
    private String searchTerms;
    private Boolean isEmptySearch;

    public Boolean getEmptySearch() {
        return isEmptySearch;
    }

    public void setEmptySearch(Boolean emptySearch) {
        isEmptySearch = emptySearch;
    }

    public List<Ad> getAdverts() {
        return adverts;
    }

    public void setAdverts(List<Ad> adverts) {
        this.adverts = adverts;
    }

    public String getSearchTerms() {
        return searchTerms;
    }

    public void setSearchTerms(String searchTerms) {
        this.searchTerms = searchTerms;
    }

    public Integer getTotalResults() {
        return totalResults;
    }

    public void setTotalResults(Integer totalResults) {
        this.totalResults = totalResults;
    }

}
