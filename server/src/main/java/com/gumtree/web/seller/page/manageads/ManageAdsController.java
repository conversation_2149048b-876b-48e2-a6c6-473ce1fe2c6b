package com.gumtree.web.seller.page.manageads;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.primitives.Ints;
import com.gumtree.api.Account;
import com.gumtree.api.AccountStatus;
import com.gumtree.api.Ad;
import com.gumtree.api.AdFeature;
import com.gumtree.api.AdStatus;
import com.gumtree.api.ApiProductPrice;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.spec.PriceApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.domain.order.CreateOrderItemBean;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.bapi.ListingCapApi;
import com.gumtree.common.util.time.Clock;
import com.gumtree.domain.advert.AdvertStatus;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.manageads.AdvertFeatureMatrix;
import com.gumtree.web.common.page.model.pagination.Pagination;
import com.gumtree.web.common.page.model.pagination.PaginationService;
import com.gumtree.web.common.pagination.AppendingPaginationUrlGenerator;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.testing.SocialLoginUtil;
import com.gumtree.web.seller.converter.AdToAdPreviewConverter;
import com.gumtree.web.seller.dto.ShortAdDTO;
import com.gumtree.web.seller.model.AdPreview;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.PageNotFoundException;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.manageads.api.CreateOrderApiCall;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.metric.Metric;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;
import com.gumtree.web.seller.page.manageads.model.ManageAdsFeatureFormBean;
import com.gumtree.web.seller.page.manageads.model.ManageAdsFilterFormBean;
import com.gumtree.web.seller.page.manageads.model.ManageAdsModel;
import com.gumtree.web.seller.page.manageads.model.ManageAdsWorkspace;
import com.gumtree.web.seller.page.manageads.reporting.ManageAdsGoogleAnalyticsConfigurer;
import com.gumtree.web.seller.page.payment.controller.PaymentCheckoutController;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.page.reviews.service.ExternalReviewsService;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.seller.service.CheckoutMetaInjector;
import com.gumtree.web.seller.service.adstats.AdvertStatisticData;
import com.gumtree.web.seller.service.adstats.AdvertStatsService;
import com.gumtree.web.seller.service.manageads.ManageAdsSearchService;
import com.gumtree.web.seller.service.manageads.ManageAdsSearchServiceResponse;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.RMADAccess;
import com.gumtree.zeno.core.service.ZenoService;
import org.apache.commons.lang.StringUtils;
import org.jboss.resteasy.client.exception.ResteasyIOException;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;
import rx.Single;
import rx.schedulers.Schedulers;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.gumtree.web.seller.page.manageads.ManageAdsController.PAGE_PATH;

@Controller
@GumtreePage(PageType.MyAds)
@GoogleAnalytics(configurer = ManageAdsGoogleAnalyticsConfigurer.class)
@RequestMapping(PAGE_PATH)
public final class ManageAdsController extends BaseSellerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManageAdsController.class);
    public static final String PAGE_PATH = "/manage/ads";
    public static final String VIEW_NAME = "manage-ads";
    public static final String BUY_TOP_AD = "BUY_TOP_AD";
    public static final String BUY_FEATURES = "/buy-features/";

    public static final int PAGE_SIZE = 10;
    public static final int MAX_FEATURES_SIZE = PAGE_SIZE * 3;
    public static final int PAGINATION_WINDOW_SIZE = 9;

    private static final List<String> PROMOTABLE_AD_STATUSES = Arrays.asList(
            AdvertStatus.LIVE.toString(),
            AdvertStatus.AWAITING_CS_REVIEW.toString(), AdvertStatus.AWAITING_SCREENING.toString()
    );

    private BushfireApi bushfireApi;
    private AdToAdPreviewConverter converter;
    private PaginationService paginationService;
    private AdvertStatsService advertStatsService;
    private Clock clock;
    private ManageAdsWorkspace manageAdsWorkspace;
    private CheckoutContainer checkoutContainer;
    private ManageAdsSearchService manageAdsSearchService;
    private ManageAdsHelper manageAdsHelper;
    private UserSession userSession;
    private ZenoService zenoService;
    private CheckoutMetaInjector checkoutMetaInjector;
    private CustomMetricRegistry metrics;
    private ListingCapApi listingCapApi;
    private ExternalReviewsService externalReviewsService;

    @Value("${gumtree.bumpup.restriction.minutes}")
    private Integer bumpupRestrictionMinutes;

    @Autowired
    public ManageAdsController(CookieResolver cookieResolver, CategoryModel categoryModel,
                               ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                               UrlScheme urlScheme, UserSession userSession,
                               BushfireApi bushfireApi, AdToAdPreviewConverter converter,
                               PaginationService paginationService, AdvertStatsService advertStatsService,
                               Clock clock, ManageAdsWorkspace manageAdsWorkspace,
                               CheckoutContainer checkoutContainer, ManageAdsSearchService manageAdsSearchService,
                               ManageAdsHelper manageAdsHelper, ZenoService zenoService, UserSessionService userSessionService,
                               CheckoutMetaInjector checkoutMetaInjector, CustomMetricRegistry customMetricRegistry,
                               ListingCapApi listingCapApi, ExternalReviewsService externalReviewsService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.bushfireApi = bushfireApi;
        this.converter = converter;
        this.paginationService = paginationService;
        this.advertStatsService = advertStatsService;
        this.clock = clock;
        this.manageAdsWorkspace = manageAdsWorkspace;
        this.checkoutContainer = checkoutContainer;
        this.manageAdsSearchService = manageAdsSearchService;
        this.manageAdsHelper = manageAdsHelper;
        this.userSession = userSession;
        this.zenoService = zenoService;
        this.checkoutMetaInjector = checkoutMetaInjector;
        this.metrics = customMetricRegistry;
        this.listingCapApi = listingCapApi;
        this.externalReviewsService = externalReviewsService;
    }

    /**
     * Show manage ads page
     *
     * @param errorCode error code
     * @param request   http request
     * @return model/view
     */
    // TODO errorCode probably can be removed because according to original comment this parameter was set only
    // while redirecting from com.gumtree.web.seller.page.payment.controller.BumpUpController and this controller was removed.
    @RequestMapping(method = RequestMethod.GET)
    public ModelAndView showManageAdsPageOne(@ModelAttribute("errorCode") String errorCode, HttpServletRequest request) {
        zenoService.logEvent(new Object[]{"none", "main"}, VIEW_NAME, RMADAccess.class);

        ManageAdsFilterFormBean filterFormBean = metrics.madPageTimer("getFilterForm").record(() -> manageAdsWorkspace.getFilterForm());
        filterFormBean.setStatus(ManageAdStatus.ACTIVE_ADS);
        manageAdsWorkspace.setFilterForm(filterFormBean);

        ManageAdsModel.Builder builder = showManageAdsPage(1, request);

        Optional<String> errorCodeModel = getErrorModelString(ApiErrorCode.fromStringSafe(errorCode));
        if (errorCodeModel.isPresent()) {
            builder.withError(errorCodeModel.get());
        }
        return builder.build();
    }

    /**
     * Show paged search result
     *
     * @param page    page number string eg 'page5'
     * @param request http request
     * @return model/view
     */
    @RequestMapping(value = "/{page}", method = RequestMethod.GET)
    public ModelAndView showPagedSearchResult(@PathVariable("page") String page, HttpServletRequest request) {
        int pageNumber = Optional.fromNullable(Ints.tryParse(page.replace("page", ""))).or(1);
        int normalizedPageNumber = Math.max(1, pageNumber);
        zenoService.logEvent(new Object[]{"page", String.valueOf(normalizedPageNumber)}, VIEW_NAME, RMADAccess.class);
        return showManageAdsPage(normalizedPageNumber, request).build();
    }

    /**
     * Show the given advert
     *
     * @param advertId candidate advert id
     * @param request  http request
     * @return model/view
     */
    @RequestMapping(method = RequestMethod.GET, params = "advertId")
    public ModelAndView showAdvertById(@RequestParam("advertId") Long advertId, HttpServletRequest request) {
        zenoService.logEvent(new Object[]{"showbyId", String.valueOf(advertId)}, VIEW_NAME, RMADAccess.class);

        ManageAdsSearchServiceResponse searchResponse = manageAdsSearchService.search(Collections.<String>emptyList(),
                String.valueOf(advertId), userSession.getSelectedAccountId(), 1, PAGE_SIZE);

        ManageAdsModel.Builder builder = buildManageAdsModel(searchResponse, 1, request);
        builder.withShowDeleteAdBalloon(true);
        return builder.build();
    }

    /**
     * Show the given advert, provided it has one of the following statuses :-  <br />
     * - LIVE <br />
     * - AWAITING_CS_REVIEW <br />
     * - AWAITING_SCREENING <br />
     *
     * @param advertId
     * @param request
     * @return
     */
    @GumtreePage(PageType.MyAds)
    @RequestMapping(value = "/advert/{advertId}", method = RequestMethod.GET)
    public ModelAndView showAdvertByIdInAllowedStatusList(@PathVariable("advertId") Long advertId, HttpServletRequest request) {
        ManageAdsSearchServiceResponse searchResponse = manageAdsSearchService.search(PROMOTABLE_AD_STATUSES,
                String.valueOf(advertId), userSession.getSelectedAccountId(), 1, PAGE_SIZE);

        ManageAdsModel.Builder builder = buildManageAdsModel(searchResponse, 1, request);
        return builder.build();
    }

    @RequestMapping(value = "/recently-expired", method = RequestMethod.GET)
    public ResponseEntity<List<ShortAdDTO>> recentlyExpiredAdverts(@RequestParam(defaultValue = "30") Integer withinDays, HttpServletRequest request) {
        final LocalDate expiryCutoff = LocalDate.now().minusDays(withinDays);
        final Long accountId = userSession.getSelectedAccountId();

        try {
            ManageAdsSearchServiceResponse searchResponse = manageAdsSearchService.search(
                    Collections.singletonList(AdvertStatus.EXPIRED.toString()), null, accountId, 1, PAGE_SIZE);

            List<ShortAdDTO> filteredAdverts = searchResponse.getAdverts().stream()
                    .filter(advert -> advert.getExpiryDate() != null && advert.getExpiryDate().toLocalDate().isAfter(expiryCutoff))
                    .map(ShortAdDTO::new)
                    .collect(Collectors.toList());

            return new ResponseEntity<>(filteredAdverts, HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("Error occurred during fetching recently expired adverts for account: {}", accountId, e);
            return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Filter adverts based on status and search terms
     *
     * @param manageAdStatus status to filter on
     * @param searchTerms    user search terms
     * @param request        http request
     * @return model/view
     */
    @GumtreePage(PageType.MyAds)
    @RequestMapping(method = RequestMethod.GET, params = "mad-filter-search")
    public ModelAndView filterAdverts(@ModelAttribute("status") ManageAdStatus manageAdStatus,
                                      @ModelAttribute("search") String searchTerms,
                                      @RequestParam(defaultValue = "1") Integer page,
                                      HttpServletRequest request) {
        ManageAdsFilterFormBean filterFormBean = new ManageAdsFilterFormBean();
        filterFormBean.setStatus(manageAdStatus);
        filterFormBean.setSearchTerms(searchTerms);
        manageAdsWorkspace.setFilterForm(filterFormBean);

        return showManageAdsPage(page, request).build();
    }

    /**
     * Select a specific account
     *
     * @param selectedAccountId account selected by the user
     * @param request           http request
     * @return model/view
     */
    @GumtreePage(PageType.MyAds)
    @RequestMapping(method = RequestMethod.GET, params = "mad-account-select")
    public ModelAndView selectAccount(@RequestParam("selectedAccountId") Long selectedAccountId, HttpServletRequest request) {
        if (!userHasAccount(selectedAccountId)) {
            zenoService.logEvent(new Object[]{"mad-account-select", "page-not-found"}, VIEW_NAME, RMADAccess.class);
            throw new PageNotFoundException("/manage/ads");
        }
        userSession.setSelectedAccountId(selectedAccountId);
        ManageAdsFilterFormBean filterFormBean = manageAdsWorkspace.getFilterForm();
        filterFormBean.setSearchTerms(null);
        manageAdsWorkspace.setFilterForm(filterFormBean);

        zenoService.logEvent(new Object[]{"mad-account-select", String.valueOf(selectedAccountId)}, VIEW_NAME, RMADAccess.class);

        return showManageAdsPage(1, request).build();
    }

    /**
     * Apply features to adverts for pro user
     *
     * @param advertFeatureMatrix advert/feature matrix
     * @param request             http request
     * @return model/view
     */
    @GumtreePage(PageType.MyAds)
    @RequestMapping(method = RequestMethod.POST)
    public ModelAndView featureAdvertsForProUser(@ModelAttribute("advertFeatureMatrix") AdvertFeatureMatrix advertFeatureMatrix,
                                                 HttpServletRequest request) {

        Account account = bushfireApi.accountApi().getAccount(userSession.getSelectedAccountId());
        User user = userSession.getUser();
        if (isUserAllowedToBuyFeature(account, user)) {
            // TODO: We could introduce a new request that takes a list of advert ids
            // TODO: and returns the list filtered by account, this would save on data transfer
            List<String> allAdvertIds = new ArrayList<>(advertFeatureMatrix.getMatrix().keySet());
            List<String> advertIds =
                    (allAdvertIds.size() > MAX_FEATURES_SIZE) ? allAdvertIds.subList(0, MAX_FEATURES_SIZE - 1) : allAdvertIds;
            List<Long> advertIdsLong = Lists.transform(advertIds, new Function<String, Long>() {
                @Nullable
                @Override
                public Long apply(String s) {
                    return new Long(s);
                }
            });
            List<Ad> adverts = filterByAccount(userSession.getSelectedAccountId(), bushfireApi.advertApi().getAdverts(advertIdsLong));

            CreateOrderBean createOrderBean = advertFeatureMatrix.createOrderBean(userSession.getSelectedAccountId(), adverts);

            zenoService.logEvent(new Object[]{"apply-features", StringUtils.join(advertIds, ",")}, VIEW_NAME, RMADAccess.class);
            return processOrderBean(createOrderBean, request, adverts);

        } else {

            zenoService.logEvent(new Object[]{"apply-features", "redirect"}, ManageAdsController.PAGE_PATH, RMADAccess.class);
            return new ModelAndView(createRedirect(ManageAdsController.PAGE_PATH));
        }
    }

    /**
     * Apply features to adverts for standard user
     *
     * @param request http request
     * @return model & view in case of error, redirect to payment otherwise
     */
    @GumtreePage(PageType.MyAds)
    @RequestMapping(value = BUY_FEATURES, method = RequestMethod.POST)
    public ModelAndView featureAdvertsForStandardUser(HttpServletRequest request) {

        Account account = bushfireApi.accountApi().getAccount(userSession.getSelectedAccountId());
        User user = userSession.getUser();
        if (isUserAllowedToBuyFeature(account, user)) {
            Map<String, String[]> requestParams = request.getParameterMap();
            List<Long> advertIds = Lists.newArrayList();
            for (String key : requestParams.keySet()) {
                if (StringUtils.isNumeric(key)) {
                    advertIds.add(Long.parseLong(key));
                }
            }

            // filter by ads we own
            List<Ad> adverts = filterByAccount(userSession.getSelectedAccountId(), bushfireApi.advertApi().getAdverts(advertIds));
            CreateOrderBean createOrderBean = buildCreateOrderBean(requestParams, adverts);

            zenoService.logEvent(new Object[]{"buy-features", StringUtils.join(advertIds, ",")}, VIEW_NAME, RMADAccess.class);

            return processOrderBean(createOrderBean, request, adverts);

        } else {

            zenoService.logEvent(new Object[]{"buy-features", "redirect"}, ManageAdsController.PAGE_PATH, RMADAccess.class);
            return new ModelAndView(createRedirect(ManageAdsController.PAGE_PATH));
        }
    }

    private boolean isUserAllowedToBuyFeature(Account account, User user) {
        return AccountStatus.ACTIVE.equals(account.getStatus()) && !user.isEbayMotorsUser();
    }

    private List<Ad> extractAdsFromOrderBean(CreateOrderBean createOrderBean, List<Ad> adverts) {
        List<Long> advertIdsInOrder = createOrderBean.getItems().stream().map(x -> x.getAdvertId()).collect(Collectors.toList());
        return adverts.stream().filter(advert -> advertIdsInOrder.contains(advert.getId())).collect(Collectors.toList());
    }

    protected CreateOrderBean buildCreateOrderBean(Map<String, String[]> requestParams, List<Ad> adverts) {
        CreateOrderBean createOrderBean = new CreateOrderBean();
        createOrderBean.setAccountId(userSession.getSelectedAccountId());
        createOrderBean.setItems(Lists.<CreateOrderItemBean>newArrayList());

        for (Ad advert : adverts) {
            List<String> purchases = Lists.newArrayList(requestParams.get(String.valueOf(advert.getId())));
            boolean featurePurchased = purchases.contains(BUY_TOP_AD);
            for (String productName : purchases) {
                if (!advertHasProduct(advert, productName)) {
                    // due to the select, we always will have a FEATURE_X_DAYS selected, we have to ensure we want it
                    // by testing if the checkbox was selected
                    boolean isFeature = productName.equals(ProductName.FEATURE_3_DAY.toString()) ||
                            productName.equals(ProductName.FEATURE_7_DAY.toString()) ||
                            productName.equals(ProductName.FEATURE_14_DAY.toString());

                    // exclude top_ad flag, ensure that if it is a feature it has been purchased
                    if (!BUY_TOP_AD.equals(productName) && purchases.contains(productName) && (!isFeature || featurePurchased)) {
                        CreateOrderItemBean createOrderItemBean = new CreateOrderItemBean();
                        createOrderItemBean.setAdvertId(advert.getId());
                        createOrderItemBean.setProductName(ProductName.valueOf(productName));
                        createOrderBean.getItems().add(createOrderItemBean);
                        metrics.metricCounter(Metric.FEATURE.name(), productName);
                    }
                }
            }
        }
        return createOrderBean;
    }

    private ModelAndView processOrderBean(CreateOrderBean createOrderBean, HttpServletRequest request, List<Ad> adverts) {
        ApiCallResponse<ApiOrder> response = execute(new CreateOrderApiCall(createOrderBean, userSession));

        if (response.isErrorResponse()) {
            return showManageAdsPage(1, request)
                    .withError(getErrorModelString(Optional.of(response.getErrorCode())).get())
                    .withErrors(populateErrors(response)) // doesn't seem to be handled properly
                    .build();
        }

        List<Ad> list = extractAdsFromOrderBean(createOrderBean, adverts);
        java.util.Optional<Ad> adMaybe = list.stream().findFirst();

        boolean isMultipleAds = ((list != null) && (list.size() > 1)) ? true : false;

        Checkout checkout = checkoutContainer.createCheckout(response.getResponseObject());
        Checkout finalCheckout = adMaybe.map(ad -> (
                checkoutMetaInjector.injectTrackingForManageAdsUpdate(checkout, isMultipleAds, ad))
        ).orElse(checkout);

        return getRedirectForCheckout(PaymentCheckoutController.PAGE_PATH, finalCheckout);
    }

    private boolean userHasAccount(Long selectedAccountId) {
        for (Account account : userSession.getSelectableAccounts()) {
            if (account.getId().equals(selectedAccountId)) {
                return true;
            }
        }
        return false;
    }

    private List<Ad> filterByAccount(final Long accountId, List<Ad> adverts) {
        List<Ad> filteredAds = Lists.newArrayList();
        for (Ad advert : adverts) {
            if (advert != null && accountId.equals(advert.getAccountId())) {
                filteredAds.add(advert);
            }
        }
        return filteredAds;
    }

    /**
     * Filters ads on status and search terms and generates model for pro/standard user
     *
     * @param pageNumber candidate page number
     * @param request    http request
     * @return model builder
     */
    private ManageAdsModel.Builder showManageAdsPage(int pageNumber, HttpServletRequest request) {
        return metrics.madPageTimer("showManageAdsPage").record(() -> {
            ManageAdsFilterFormBean filterFormBean = metrics.madPageTimer("getFilterForm").record(() ->
                    manageAdsWorkspace.getFilterForm());

            List<String> statuses = Lists.newArrayList();
            for (AdStatus singleStatus : filterFormBean.getStatus().getStatuses()) {
                statuses.add(singleStatus.name());
            }

            Long selectedAccountId = metrics.madPageTimer("getSelectedAccountId").record(() -> userSession.getSelectedAccountId());
            ManageAdsSearchServiceResponse searchResponse =
                    manageAdsSearchService.search(statuses, filterFormBean.getSearchTerms(), selectedAccountId, pageNumber, PAGE_SIZE);

            ManageAdsModel.Builder builder = buildManageAdsModel(searchResponse, pageNumber, request);

            return builder;
        });
    }

    /**
     * Generates model for pro/standard user based on search results (either by status/search terms or by ad id)
     *
     * @param searchResponse manage ads search response
     * @param pageNumber     candidate page number
     * @param request        http request
     * @return model builder
     */
    private ManageAdsModel.Builder buildManageAdsModel(
            ManageAdsSearchServiceResponse searchResponse,
            int pageNumber,
            HttpServletRequest request) {
        return metrics.madPageTimer("buildManageAdsModel").record(() -> {
            long start = System.currentTimeMillis();
            List<Long> advertIds = Lists.newArrayList();
            for (Ad advert : searchResponse.getAdverts()) {
                advertIds.add(advert.getId());
            }
            ManageAdsFeatureFormBean manageAdsFeatureFormBean = new ManageAdsFeatureFormBean();
            for (Ad advert : searchResponse.getAdverts()) {
                AdPreview preview = converter.convert(advert);
                if (preview != null) {
                    manageAdsFeatureFormBean.getPreviews().add(preview);
                }
            }
            metrics.madPageTimer("convertToAdPreview").record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);

            manageAdsFeatureFormBean.getAdvertFeatureMatrix().add(searchResponse.getAdverts());
            Account account = metrics.madPageTimer("getSelectedAccountId").record(() ->
                    bushfireApi.accountApi().getAccount(userSession.getSelectedAccountId()));

            Pagination pagination = paginationService.getPagination(new AppendingPaginationUrlGenerator(PAGE_PATH), pageNumber,
                    searchResponse.getTotalResults(), 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);

            boolean enableMarketingOptIn = SocialLoginUtil
                    .isRequiringMarketingOptIn(request, Optional.of(userSession.getUser()), true);

            ManageAdsFilterFormBean filterForm = metrics.madPageTimer("getFilterForm").record(() -> manageAdsWorkspace.getFilterForm());
            CoreModel.Builder coreModelBuilder = metrics.madPageTimer("getCoreModelBuilder").record(() -> getCoreModelBuilder(request));

            ManageAdsModel.Builder builder = new ManageAdsModel.Builder(
                    coreModelBuilder.withUserType(userSession.isProUser()),
                    userSession.isProUser(),
                    userSession.getUser().isEbayMotorsUser())
                    .withManageAdsFeatureFormBean(manageAdsFeatureFormBean)
                    .withAccount(account)
                    .withFeatureForm(manageAdsFeatureFormBean)
                    .withFilterForm(filterForm)
                    .withAccountSelectionForm(manageAdsHelper.getAccountSelectionForm(userSession))
                    .withCurrentPageNumber(pagination.getCurrentPageNumber())
                    .withTotalNumberOfPages(pagination.getTotalNumberOfPages())
                    .withPaginationSections(pagination.getPaginationSections())
                    .withDecadicPagination(pagination.getDecadicPagination())
                    .withShowDeleteAdBalloon(false)
                    .withSearch(searchResponse.getSearchTerms())
                    .withAdvertCount(searchResponse.getTotalResults())
                    .withEmptySearch(searchResponse.getEmptySearch())
                    .withStatus(filterForm.getStatus())
                    .withClock(clock.getDate())
                    .withBumpupRestrictionMinutes(bumpupRestrictionMinutes)
                    .withEnableMarketingOptIn(enableMarketingOptIn)
                    .withShowManageJobsOnMadgexButton(userSession.getUser().isJobsUser())
                    .withManageAdsUrls(manageAdsHelper, getUrlScheme())
                    .withListingCapApi(listingCapApi);

            Single.zip(
                            getStatisticsAsync(searchResponse),
                            getProductPricesAsync(advertIds),
                            getGoogleReviewSwitchAsync(request, userSession.getSelectedAccountId()),
                            (statisticData, longMapMap, isGoogleReviewOnData) -> new Object() {
                                final List<AdvertStatisticData> stats = statisticData;
                                final Map<Long, Map<String, ProductPrice>> prices = longMapMap;
                                final boolean isGoogleReviewOn = isGoogleReviewOnData;
                            }
                    )
                    .flatMap(data -> {
                        builder.withStatisticForAdvert(data.stats);
                        builder.withProductPrices(data.prices);
                        builder.withGoogleReviewSwitch(data.isGoogleReviewOn);

                        return Single.just(new Object());
                    })
                    .toBlocking()
                    .value();

            if (!AccountStatus.ACTIVE.equals(account.getStatus())) {
                builder.withAccountSuspendedNotice(resolveMessage("account.notice." + account.getStatus().name().toLowerCase()))
                        .withAccountSuspendedTitle(resolveMessage("account.title." + account.getStatus().name().toLowerCase()));
            }

            return builder;
        });
    }

    private Single<Boolean> getGoogleReviewSwitchAsync(HttpServletRequest request, Long selectedAccountId) {
        return externalReviewsService.getGoogleReviewSwitchAsync(selectedAccountId);
    }

    private Single<List<AdvertStatisticData>> getStatisticsAsync(ManageAdsSearchServiceResponse searchResponse) {
        return Single.fromCallable(() -> getAdStatistics(searchResponse.getAdverts())).subscribeOn(Schedulers.io());
    }

    private Single<Map<Long, Map<String, ProductPrice>>> getProductPricesAsync(List<Long> advertIds) {
        BushfireApiKey apiKey = userSession.getApiKey();

        return Single.fromCallable(() -> getProductPrices(advertIds, apiKey)).subscribeOn(Schedulers.io());
    }

    private Optional<String> getErrorModelString(Optional<ApiErrorCode> apiErrorCodeOption) {
        if (apiErrorCodeOption.isPresent()) {
            ApiErrorCode apiErrorCode = apiErrorCodeOption.get();
            switch (apiErrorCode) {
                case READ_ONLY_CATEGORY:
                    return Optional.of("Invalid-Category:Read-Only");
                case DISABLED_CATEGORY:
                    return Optional.of("Invalid-Category:Disabled");
                case MISSING_CATEGORY:
                    return Optional.of("Invalid-Category:Missing");
                case UNSUPPORTED_OPERATION:
                    return Optional.of("Unsupported-Operation");
                default:
                    return Optional.of("Error:" + apiErrorCode.asString());
            }
        }
        return Optional.absent();
    }

    private List<AdvertStatisticData> getAdStatistics(List<Ad> adverts) {
        return metrics.madPageTimer("getAdStatistics").record(() -> {
            try {
                return advertStatsService.getStatisticForAdvertList(adverts);
            } catch (ResteasyIOException ioe) {
                // in majority of case this will be caused by stats api or bapi restarts
                // logging as debug to avoid hammering Sentry with exceptions that are expected
                LOGGER.debug("Resteasy Error,could not retrieve ad stats data {}", ioe.getMessage());
            } catch (Exception e) {
                LOGGER.error("Could not retrieve ad stats data {}", e.getMessage());
            }

            return Collections.emptyList();
        });
    }

    private Map<Long, Map<String, ProductPrice>> getProductPrices(List<Long> advertIds, BushfireApiKey apiKey) {
        return metrics.madPageTimer("getProductPrices").record(() -> {
        if (!advertIds.isEmpty()) {
            PriceApi priceApi = bushfireApi.create(PriceApi.class, apiKey);
            Map<Long, List<ApiProductPrice>> productPrices = priceApi.getProductPricesForAdverts(StringUtils.join(advertIds, ","));
            Map<Long, Map<String, ProductPrice>> incVatPrices = Maps.newHashMap();
            for (Long adId : productPrices.keySet()) {
                incVatPrices.put(adId, ProductType.productPriceMap(productPrices.get(adId)));
            }
            return incVatPrices;
        }
        return Collections.emptyMap();
        });
    }

    private boolean advertHasProduct(Ad advert, String productName) {
        for (AdFeature adFeature : advert.getFeatures()) {
            if (adFeature.getProductName().toString().equals(productName)) {
                return true;
            }
        }
        return false;
    }


}
