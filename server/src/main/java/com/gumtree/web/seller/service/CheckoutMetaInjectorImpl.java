package com.gumtree.web.seller.service;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.util.MetaDataGenerator;
import com.gumtree.web.seller.page.postad.model.meta.MetaPathInfo;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Created by mdi<PERSON><PERSON><PERSON> on 6/23/17.
 */
@Component
public class CheckoutMetaInjectorImpl implements CheckoutMetaInjector {

    private final SellerSessionDataService sessionDataService;

    private final MetaDataGenerator metaDataGenerator;

    @Autowired
    public CheckoutMetaInjectorImpl(SellerSessionDataService sessionDataService, MetaDataGenerator metaDataGenerator) {
        this.sessionDataService = sessionDataService;
        this.metaDataGenerator = metaDataGenerator;
    }

    public Checkout injectTrackingForManageAdsUpdate(Checkout checkout, boolean isMultipleAds, Ad sampleAdForActiveOrInActive) {
        injectMetaInformation(checkout, sampleAdForActiveOrInActive, Optional.empty(), isMultipleAds);
        sessionDataService.setCheckout(checkout.getKey(), checkout);
        return checkout;
    }

    public Checkout injectTrackingForEditorUpdate(Checkout checkout, Ad ad, AdStatus originalStatus) {
        injectMetaInformation(checkout, ad, Optional.of(originalStatus), false);
        sessionDataService.setCheckout(checkout.getKey(), checkout);
        return checkout;
    }

    public Checkout injectTrackingForPost(Checkout checkout, Ad ad) {
        injectMetaInformation(checkout, ad, Optional.empty(), false);
        sessionDataService.setCheckout(checkout.getKey(), checkout);
        return checkout;
    }

    private void injectMetaInformation(Checkout checkout, Ad ad, Optional<AdStatus> maybeOriginalStatus, boolean isMultipleAds) {
        MetaPathInfo metaPathInfo = new MetaPathInfo(metaDataGenerator.getPageFeatureTypes(checkout),
                metaDataGenerator.getPageAction(checkout, maybeOriginalStatus.orElse(ad.getStatus())),
                metaDataGenerator.getPaymentTypes(checkout), isMultipleAds);
        checkout.setMetaPathInfo(metaPathInfo);
    }
}
