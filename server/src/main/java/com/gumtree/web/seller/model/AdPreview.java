package com.gumtree.web.seller.model;

import com.gumtree.api.AdStatus;
import com.gumtree.domain.Identifiable;
import com.gumtree.domain.media.Image;

import java.util.Date;

/**
 * A specialized version of an advert for being displayed in an overview.
 */
public interface AdPreview extends Identifiable<Long> {

    /**
     * @return the id
     */
    Long getId();

    /**
     * @return the title
     */
    String getTitle();

    /**
     * @return the description
     */
    String getDescription();

    /**
     * @return the advertUrl
     */
    String getAdvertUrl();

    /**
     * @return the url for editing the advert.
     */
    String getEditAdvertUrl();

    /**
     * @return the url for restoring the advert from deleted status.
     */
    String getRestoreAdvertUrl();

    /**
     * @return the displayPrice
     */
    String getDisplayPrice();

    /**
     * @return the displayLocationText
     */
    String getDisplayLocationText();

    /**
     * @return the displayDate
     */
    String getDisplayDate();

    /**
     * @return the seoTimeStamp
     */
    String getSeoTimeStamp();

    /**
     * @return the main image
     */
    Image getMainImage();

    /**
     * @return the isUrgent
     */
    boolean isUrgent();

    /**
     * @return as display text
     */
    String getPostedTime();

    /**
     * @return as date
     */
    Date getPostedTimeAsDate();

    /**
     * @return is featured
     */
    boolean isFeatured();

    /**
     * get the main image preview url
     *
     * @return url as string
     */
    String getPreviewUrl();

    /**
     * @param previewUrl as string
     */
    void setPreviewUrl(String previewUrl);

    /**
     * get the main image thumb url
     *
     * @return url as string
     */
    String getThumbnailUrl();

    /**
     * @param thumbnailUrl as string
     */
    void setThumbnailUrl(String thumbnailUrl);

    /**
     * @param isFeatured true or false
     */
    void setFeatured(boolean isFeatured);

    /**
     * Returns concatenated String of title and displayLocationText or just
     * title if there is no displayLocationText used for image alt tags.
     *
     * @return the alt tag
     */
    String getAltTag();

    /**
     * Get the ad contact email for manage ads (this may need to be ad.getUserEmail())
     *
     * @return the value of the entered email address
     */
    String getContactEmail();

    /**
     * Get the date/time the advert was first created
     *
     * @return as display text
     */
    String getCreatedTime();

    /**
     * @return as date
     */
    Date getCreatedTimeAsDate();

    /**
     * Get the date/time the advert was last modified
     *
     * @return as display text
     */
    String getLastModifiedTime();

    /**
     * @return as date
     */
    Date getLastModifiedTimeAsDate();

    /**
     * @return the advert status
     */
    AdStatus getStatus();

    /**
     * @return the seller type
     */
    String getSellerType();

    /**
     *
     * @return the distance from the search location (postcode)
     */
    String getDistance();

    void setPublishedRecently(boolean v);

    boolean getPublishedRecently();

    /**
     * @return alternative search by date value (e.g. for search by event date or trip date)
     */
    String getCustomDateText();

    /**
     * @return the number of images for this ad.
     */
    int getNumberOfImages();

    /**
     * @return the number of videos for this ad.
     */
    int getNumberOfVideos();

    /**
     * Text to display if an ad has been Successful
     * @return
     */
    String getMarkAsLabel();

    /**
     * @return the report ad base url without reason
     */
    String getReportAdUrl();

    // we need this get for the JSP in pro-MAD, which somehow doesn't recognise neither is or has prefixes...
    Boolean getCategoryReadOnly();

    Long getCategoryId();
    /**
     * @return the l1 category id
     */
    Long getL1CategoryId();

    Long getL2CategoryId();

    Long getL3CategoryId();

    /**
     * @return remaining part of advert description if the advert description is too long to be displayed.
     * Returns null if advert description is not very long (and it can be fully displayed on SRP).
     */
    String getDescriptionExtensionOrNull();

    /**
     * @return the isSold
     */
    boolean isMarkedAsSold();
}
