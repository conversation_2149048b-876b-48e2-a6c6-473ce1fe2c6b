package com.gumtree.web.seller.page.manageads.mycompany.model;

import com.gumtree.api.PackageUsage;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.List;

/**
 * Display model for package usage page.
 */
public final class DisplayCreditPackageUsage {

    private String appliedDate;

    private Long advertId;

    private String advertTitle;

    private String appliedBy;

    private List<String> contactDetails;

    private String packageType;

    public String getAppliedDate() {
        return appliedDate;
    }

    public Long getAdvertId() {
        return advertId;
    }

    public String getAdvertTitle() {
        return advertTitle;
    }

    public String getAppliedBy() {
        return appliedBy;
    }

    public List<String> getContactDetails() {
        return contactDetails;
    }

    public String getPackageType() {
        return packageType;
    }

    /**
     * Builder for {@link DisplayCreditPackageUsage}
     */
    public static final class Builder {

        private static final DateTimeFormatter DATE_FORMAT = DateTimeFormat.forPattern("dd/MM/yyyy");

        private String appliedDate;

        private Long advertId;

        private String advertTitle;

        private String appliedBy;

        private List<String> contactDetails = new ArrayList<String>();

        private String packageType;

        /**
         * Build using an API model.
         *
         * @param packageUsage the api model
         * @return this builder
         */
        public Builder creditPackageUsage(PackageUsage packageUsage) {
            appliedDate = DATE_FORMAT.print(packageUsage.getAppliedDate());
            advertId = packageUsage.getAdvertId();
            advertTitle = packageUsage.getAdvertTitle();
            appliedBy = packageUsage.getAppliedBy();
            contactDetails.add(packageUsage.getAppliedBy());
            packageType = packageUsage.getPackageType();
            if (packageUsage.getContactTelephone() != null) {
                contactDetails.add(packageUsage.getContactTelephone());
            }
            return this;
        }

        /**
         * @return a {@link DisplayCreditPackage}
         */
        public DisplayCreditPackageUsage build() {
            DisplayCreditPackageUsage usage = new DisplayCreditPackageUsage();
            usage.appliedDate = appliedDate;
            usage.advertId = advertId;
            usage.advertTitle = advertTitle;
            usage.appliedBy = appliedBy;
            usage.contactDetails = contactDetails;
            usage.packageType = packageType;
            return usage;
        }
    }
}
