package com.gumtree.web.seller.page.manageads.mydetails.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.seller.domain.deactivation.entity.DeactivationReason;
import com.gumtree.api.client.executor.ApiCall;

/**
 *
 */
public class DeactivateMyAccountApiCall implements ApiCall<Void> {

    private String username;

    private DeactivationReason reason;

    /**
     * Default deactivate api constructor
     *
     * @param username username used to fetch id and set status
     * @param reason   deactivation reason for tracking
     */
    public DeactivateMyAccountApiCall(String username, DeactivationReason reason) {
        this.username = username;
        this.reason = reason;
    }

    @Override
    public final Void execute(BushfireApi api) {
        UserApi userApi = api.create(UserApi.class);
        //do deactivation
        Long id = userApi.getUser(username).getId();
        userApi.deactivateUserAccount(id, reason);
        return null;
    }
}
