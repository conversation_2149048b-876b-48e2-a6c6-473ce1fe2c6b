package com.gumtree.web.seller.page;

import java.io.Serializable;

/**
 * Interface that helps select a view to render based on some criteria
 */
public interface ViewDecider extends Serializable {

    /**
     * Decides on the name of the view to render according to the input received
     * @param originalView name of the original view over which we are deciding
     * @return view to render
     */
    String decideView(String originalView);
}
