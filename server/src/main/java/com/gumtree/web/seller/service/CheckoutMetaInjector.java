package com.gumtree.web.seller.service;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.web.seller.page.manageads.model.Checkout;

/**
 * Created by mdivilioglu on 6/23/17.
 */

public interface CheckoutMetaInjector {
    /*
    * This is for injection extra information for
    * updates on Manage Ads page.
    * On Manage Ads Page, there are 2 types of filtering: Active or Inactive. The sampleAd is to get
    * the type of ad(s) selected so if it is DELETED_USER or EXPIRED, it does not matter, as it will
    * be resulting to a RELIST on the URL. So on inactive it is either EXPIRED or DELETED which both will
    * lead to RELIST. Taking one of them will be enough to put its status into the logic and it does not
    * matter which one is taken as they belong to INACTIVE category.
    * */

    Checkout injectTrackingForManageAdsUpdate(Checkout checkout, boolean isMultipleAds, Ad sampleAdForActiveOrInactive);

    /*
    * This is for injecting extra information from Editor in case the Update is not done via an
    * Edit Page(rather than box tick Manage Ads page)
    * */
    Checkout injectTrackingForEditorUpdate(Checkout checkout, Ad ad, AdStatus originalStatus);

    /* This is for injection extra information for new posts */
    Checkout injectTrackingForPost(Checkout checkout, Ad ad);
}
