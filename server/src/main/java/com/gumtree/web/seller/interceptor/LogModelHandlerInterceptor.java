package com.gumtree.web.seller.interceptor;

import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.SerializationConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public class LogModelHandlerInterceptor extends Hand<PERSON>InterceptorAdapter {

    @Value("${gumtree.model.log.enabled}")
    private boolean enableModelLog;

    private static final Logger LOG = LoggerFactory.getLogger(LogModelHandlerInterceptor.class);

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {
        if (enableModelLog) {

            LOG.info("------------------------ MODEL OUTPUT START ------------------------");

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(SerializationConfig.Feature.FAIL_ON_EMPTY_BEANS, false);
            objectMapper.configure(SerializationConfig.Feature.INDENT_OUTPUT, true);

            Map<String, Object> model = modelAndView.getModel();
            for (String key : model.keySet()) {
                try {
                    if (!key.contains("BindingResult")) {
                        LOG.info(key + " -> " + objectMapper.writeValueAsString(model.get(key)));
                    } else {
                        LOG.info("Ignoring key " + key);
                    }
                } catch (Exception e) {
                    LOG.error("Failed to output key: " + key, e);
                }
            }

            LOG.info("------------------------ MODEL OUTPUT FINISH ------------------------");
        }
    }
}
