package com.gumtree.web.seller.security.apiauthentication;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

public final class CsrfTokenMetricsUtils {
    private static final String CSRF_TOKEN_COUNTER_NAME = "csrf_token";
    private static final String ACTION = "user_logout";
    private static final String ACTION_TAG = "action";
    private static final String RESULT_TAG = "result";
    private static final String VALID = "valid";
    private static final String INVALID = "invalid";

    public static void recordValidCsrfTokenMetric(MeterRegistry meterRegistry) {
        Counter.builder(CSRF_TOKEN_COUNTER_NAME)
                .tag(ACTION_TAG, ACTION)
                .tag(RESULT_TAG, VALID)
                .register(meterRegistry)
                .increment();
    }

    public static void recordInvalidCsrfTokenMetric(MeterRegistry meterRegistry) {
        Counter.builder(CSRF_TOKEN_COUNTER_NAME)
                .tag(ACTION_TAG, ACTION)
                .tag(RESULT_TAG, INVALID)
                .register(meterRegistry)
                .increment();
    }

    private CsrfTokenMetricsUtils() {
    }
}
