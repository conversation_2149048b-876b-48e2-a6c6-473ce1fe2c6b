package com.gumtree.web.seller.page.activation.controller;

import com.google.common.collect.Lists;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.command.GetUserApiCall;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.error.mvc.ConstraintViolationErrorList;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.activation.ActivationResendBean;
import com.gumtree.web.seller.page.activation.api.ResendActivationEmailApiCall;
import com.gumtree.web.seller.page.activation.model.ActivationFailureModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.login.controller.LoginController;
import com.gumtree.web.seller.page.registration.ConfirmationPageController;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.zeno.userregistration.UserActivationFailureZenoEvent;
import com.gumtree.web.zeno.userregistration.UserActivationResendZenoEvent;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserActivationFail;
import com.gumtree.zeno.core.service.ZenoService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.gumtree.api.error.ApiErrorCode.ALREADY_ACTIVATED;
import static com.gumtree.web.seller.page.activation.controller.ActivationFailureController.PAGE_PATH;
import static com.gumtree.web.seller.page.activation.controller.ActivationPageController.USER_ID_PARAM;

@Controller
@GumtreePage(PageType.UserActivationFail)
@RequestMapping(PAGE_PATH)
@GoogleAnalytics
public final class ActivationFailureController extends BaseSellerController {

    private final Logger logger = LoggerFactory.getLogger(ActivationFailureController.class);

    public static final String PAGE_PATH = "/activation-failed";

    private final Validator validator;

    private final ZenoService zenoService;

    private final ParameterEncryption parameterEncryption;

    static final String PARAMETER_NAME = "gt_d";

    @Autowired
    public ActivationFailureController(CookieResolver cookieResolver,
                                       CategoryModel categoryModel,
                                       ApiCallExecutor apiCallExecutor,
                                       ErrorMessageResolver messageResolver,
                                       UserSessionService userSessionService,
                                       UrlScheme urlScheme,
                                       Validator validator,
                                       ZenoService zenoService,
                                       ParameterEncryption parameterEncryption) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.validator = validator;
        this.zenoService = zenoService;
        this.parameterEncryption = parameterEncryption;
    }

    /**
     * Get form action for failed activation.
     *
     * @return the page path
     */
    @ModelAttribute("activationFailedFormAction")
    public String activationFailedFormAction() {
        return PAGE_PATH;
    }

    /**
     * Get the view name for failed activation.
     *
     * @return the view name
     */
    @RequestMapping(method = RequestMethod.GET)
    public ModelAndView viewActivationFailedPage(HttpServletRequest request) {

        ActivationResendBean activationResendBean = new ActivationResendBean();

        String encryptedParameterMap = request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME);
        if(StringUtils.isNotBlank(encryptedParameterMap)) {
            Map<String,String> data = parameterEncryption.decryptUrlEncodedParameterMap(encryptedParameterMap);
            String email = data.get(USER_ID_PARAM);
            if(email != null) {
                zenoService.logEvent(new UserActivationFailureZenoEvent(email));
                activationResendBean.setUsername(email);
            }
        }
        return activationFailureModelAndView(request, activationResendBean);
    }

    @RequestMapping(method = RequestMethod.POST)
    public ModelAndView resendActivation(@ModelAttribute("model") ActivationFailureModel incomingModel,
                                         HttpServletRequest request) {

        ActivationResendBean activationResendBean = incomingModel.getForm();

        Set<ConstraintViolation> violations = new HashSet<>(validator.validate(activationResendBean));
        if (violations.isEmpty()) {
            ApiCallResponse<Void> apiResponse = execute(new ResendActivationEmailApiCall(activationResendBean.getUsername()));
            if (apiResponse.isErrorResponse()) {
                return handleResendActivationEmailFailureResponse(request, activationResendBean, apiResponse);
            }
        } else {
            return handleFormValidationErrors(request, activationResendBean, violations);
        }

        ApiCallResponse<User> userResponse = getApiCallExecutor().call(new GetUserApiCall(activationResendBean.getUsername()));
        User user = userResponse.getResponseObject();

        Map<String, String> data = new HashMap<>();

        if (user != null) {
            data.put("emailAddress", user.getEmail());
            data.put("userId", user.getId().toString());
        } else {
            logger.warn("activation failure.. how could this be? remove null check if it's not in the logs");
        }

        String value = parameterEncryption.encryptMapAndUrlEncode(data);

        zenoService.logEvent(new UserActivationResendZenoEvent(user));
        return redirectWithParameters(ConfirmationPageController.PAGE_PATH, PARAMETER_NAME, value);
    }

    private ModelAndView handleFormValidationErrors(HttpServletRequest request,
                                                    ActivationResendBean activationResendBean,
                                                    Set<ConstraintViolation> violations) {
        populateErrors(activationResendBean, new ConstraintViolationErrorList(violations));
        return activationFailureModelAndView(request, activationResendBean);
    }

    private ModelAndView handleResendActivationEmailFailureResponse(HttpServletRequest request,
                                                                    ActivationResendBean activationResendBean,
                                                                    ApiCallResponse<Void> apiResponse) {

        logger.info("Resend Activation Failure : {}", apiResponse);

        if(ApiErrorCode.NOT_FOUND == apiResponse.getErrorCode() || ALREADY_ACTIVATED == apiResponse.getErrorCode()) {
            return redirect(LoginController.PAGE_PATH);
        } else {
            return activationFailureModelAndView(request, activationResendBean);
        }

    }

    private  ModelAndView activationFailureModelAndView(HttpServletRequest request,
                                                        ActivationResendBean activationResendBean) {

        ActivationFailureModel.Builder activationFailureModel = ActivationFailureModel.builder()
                .withActivationResendBean(activationResendBean);

        CoreModel.Builder coreModel = getCoreModelBuilder(request)
                .withGaEvents(Lists.newArrayList(UserActivationFail.class.getSimpleName()));

        return activationFailureModel.build(coreModel);
    }

}
