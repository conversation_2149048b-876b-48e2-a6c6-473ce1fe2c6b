package com.gumtree.web.seller.service.image.error;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.io.CharStreams;
import com.gumtree.bapi.model.ApiErrorCode;
import com.gumtree.bapi.model.ApiErrors;
import feign.Response;
import feign.codec.ErrorDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Reader;
import java.util.Optional;

public class BapiErrorDecoder implements ErrorDecoder {

    private static final Logger LOGGER = LoggerFactory.getLogger(BapiErrorDecoder.class);

    private final ObjectMapper objectMapper;
    private final ApiErrors defaultApiErrors;

    public BapiErrorDecoder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.defaultApiErrors = getUnknownApiErrors();
    }

    @Override
    public Exception decode(String s, Response response) {
        Optional<ApiErrors> bapiError =
                getResponseBody(response, ApiErrors.class);
        int status = response.status();

        return bapiError.map(apiErrors -> new BapiException(s, status, apiErrors))
                .orElseGet(() -> new BapiException(s, status, defaultApiErrors));

    }

    private <T> Optional<T> getResponseBody(Response response, Class<T> klass) {
        if(response.body() == null) {
            return Optional.empty();
        }
        try (Reader reader = response.body().asReader()){
            String bodyJson = CharStreams.toString(reader);
            return Optional.ofNullable(objectMapper.readValue(bodyJson, klass));
        } catch (IOException e) {
            LOGGER.error("Unable to parse response body to JSON {}, {}", e.getMessage(), response);
            return Optional.empty();
        }
    }

    private ApiErrors getUnknownApiErrors() {
        ApiErrors apiErrors = new ApiErrors();
        apiErrors.setErrorCode(ApiErrorCode._2F);
        return apiErrors;
    }

}

