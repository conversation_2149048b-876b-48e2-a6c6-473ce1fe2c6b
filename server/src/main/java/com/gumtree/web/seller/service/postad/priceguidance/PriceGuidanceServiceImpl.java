package com.gumtree.web.seller.service.postad.priceguidance;

import com.gumtree.seller.infrastructure.driven.motors.price.guidance.api.PriceGuidanceApi;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.Ad;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.PriceGuidanceResponse;
import com.gumtree.web.seller.page.postad.model.PriceGuidance;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceAd;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceContext;
import org.apache.commons.lang3.Validate;
import org.springframework.core.convert.converter.Converter;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class PriceGuidanceServiceImpl implements PriceGuidanceService {
    private static final Integer PAGE_SIZE = 20;

    private final PriceGuidanceApi priceGuidanceApi;

    private final Converter<Ad, PriceGuidanceAd> flatAdConverter;

    public PriceGuidanceServiceImpl(PriceGuidanceApi priceGuidanceApi, Converter<Ad, PriceGuidanceAd> flatAdConverter) {
        this.priceGuidanceApi = priceGuidanceApi;
        this.flatAdConverter = flatAdConverter;
    }

    @SuppressWarnings("UnstableApiUsage")
    @Override
    public Optional<PriceGuidanceContext> getForCarAd(@Nonnull String vrn, Long categoryId) {
        Validate.notBlank(vrn);

        return priceGuidanceApi
                .getPriceGuidance(vrn, Optional.of(PAGE_SIZE), Optional.ofNullable(categoryId).map(Long::intValue))
                .map(Optional::ofNullable)
                .map(maybeResponse ->  maybeResponse
                        .map(r -> new PriceGuidanceContext(convertToPriceGuidance(r), r.getAttributes())))
                .toBlocking().value();
    }

    private PriceGuidance convertToPriceGuidance(PriceGuidanceResponse result) {
        List<PriceGuidanceAd> ads = result.getAds().stream()
                .map(flatAdConverter::convert).collect(Collectors.toList());
        return new PriceGuidance(result.getAds().size(), ads);
    }

}
