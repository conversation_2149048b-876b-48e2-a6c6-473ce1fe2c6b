package com.gumtree.web.seller.service.category.model;

import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class CategoryTextMatcher {
    public final Map<String, String> normalizationCache = new ConcurrentHashMap<>();
    private final String recallType = "recall_type";

    // Trie Node class
    class TrieNode {
        Map<Character, TrieNode> children;
        boolean isEndOfWord; // Indicates if a category ends at this node
        String fullCategoryWord; // Store the full category string if it ends here

        public TrieNode() {
            children = new HashMap<>();
            isEndOfWord = false;
            fullCategoryWord = null;
        }
    }

    class Trie {
        private TrieNode root;

        public Trie() {
            root = new TrieNode();
        }

        // Inserts individual words from categories into the trie
        public void insertWord(String word) {
            TrieNode current = root;
            for (char ch : word.toCharArray()) {
                current.children.putIfAbsent(ch, new TrieNode());
                current = current.children.get(ch);
            }
            current.isEndOfWord = true;
            current.fullCategoryWord = word; // Store the word itself
        }

        // Searches for any category word that has the given prefix
        public List<String> findWordsWithPrefix(String prefix) {
            List<String> matches = new ArrayList<>();
            // Add a minimum length limit (e.g., at least 3 characters)
            if (prefix.length() < 2) {
                return matches;
            }
            TrieNode current = root;

            // Traverse the trie based on the query prefix
            for (char ch : prefix.toCharArray()) {
                if (current.children.containsKey(ch)) {
                    current = current.children.get(ch);
                } else {
                    return matches; // No words start with this prefix
                }
            }

            // Now, from the current node, find all words that end here or further down
            findAllWordsFromNode(current, matches);
            return matches;
        }

        // Helper to collect all words under a given TrieNode
        private void findAllWordsFromNode(TrieNode node, List<String> foundWords) {
            if (node.isEndOfWord) {
                foundWords.add(node.fullCategoryWord);
            }
            for (TrieNode child : node.children.values()) {
                findAllWordsFromNode(child, foundWords);
            }
        }
    }

    private final Trie trie;
    private final List<CategorySuggesterScoreModel> categories;
    private final Map<Long, CategorySuggesterScoreModel> categoriesMap;
    private final String query;
    private final List<String> queryTokens;
    private final double fuzzyThreshold;
    private final int editThreshold;
    private final double cosineThreshold;
    private final int maxCategorySize;

    public CategoryTextMatcher(String query, List<CategorySuggesterScoreModel> categories,
                               int maxCategorySize, double fuzzyThreshold, int editThreshold, double cosineThreshold) {

        this.query = query;
        this.queryTokens = processInput(query);
        this.categories = new ArrayList<>();
        this.categoriesMap = new HashMap<>();
        this.trie = new Trie();
        this.fuzzyThreshold = fuzzyThreshold;
        this.editThreshold = editThreshold;
        this.cosineThreshold = cosineThreshold;
        this.maxCategorySize = maxCategorySize;

        for (CategorySuggesterScoreModel cat : categories) {
            List<String> tokens = processInput(cat.getDisplayName());
            cat.setDisplayNameTokens(tokens);
            cat.setTreeTokens(processInput(cat.getTree()));
            this.categories.add(cat);
            this.categoriesMap.put(cat.getId(), cat);

            for (String word : tokens) {
                this.trie.insertWord(word);
            }

            String compoundForm = String.join("", tokens);
            this.trie.insertWord(compoundForm);

            if (tokens.size() > 1) {
                String keyCombo = tokens.get(0) + tokens.get(1);
                this.trie.insertWord(keyCombo);
            }
        }
    }

    /**
     * Normalizes a single word using lemmatization and falls back to stemming if needed.
     * In Java, we use Stanford CoreNLP for robust lemmatization.
     * Note: Stanford CoreNLP's lemmatizer is quite good and often negates the need for a separate stemmer fallback
     * for common English words. However, the logic is kept similar to your Python code.
     *
     * @param word The word to normalize.
     * @return The normalized (lemmatized or stemmed) word.
     */
    public String normalizeWord(String word) {
        if (StringUtils.isBlank(word)) {
            return "";
        }
        return normalizationCache.computeIfAbsent(word, k -> word.toLowerCase());
    }

    /**
     * Processes input text to tokenize and normalize words.
     *
     * @param text The input text.
     * @return A list of normalized tokens.
     */
    public List<String> processInput(String text) {
        List<String> normalizedTokens = new ArrayList<>();
        // Word tokenizer
        Pattern pattern = Pattern.compile("\\b\\w+\\b");
        Matcher matcher = pattern.matcher(text.toLowerCase());

        while (matcher.find()) {
            String lemma = normalizeWord(matcher.group());
            if (!StringUtils.isBlank(lemma) && 1 < lemma.length()) {
                normalizedTokens.add(lemma);
            }
        }
        return normalizedTokens;
    }

    /**
     * Performs an exact match of input tokens against preprocessed category tokens.
     *
     * @return A list of categories that exactly match.
     */
    public List<Long> exactMatch() {
        return categories.stream()
                .filter(cat -> queryTokens.equals(cat.getDisplayNameTokens()))
                .map(CategorySuggesterScoreModel::getId)
                .collect(Collectors.toList());
    }

    /**
     * Calculates the Jaccard similarity between two lists of tokens.
     *
     * @param tokens1 The first list of tokens.
     * @param tokens2 The second list of tokens.
     * @return The Jaccard similarity score.
     */
    public double jaccardSim(List<String> tokens1, List<String> tokens2) {
        Set<String> set1 = new HashSet<>(tokens1);
        Set<String> set2 = new HashSet<>(tokens2);

        int intersection = 0;
        for (String s : set1) {
            if (set2.contains(s)) {
                ++intersection;
            }
        }

        int union = set1.size() + set2.size() - intersection;
        return union == 0 ? 0.0 : (double) intersection / union;
    }

    /**
     * Performs a fuzzy match using Jaccard similarity.
     *
     * @param threshold The minimum similarity score for a match.
     * @return A list of (category, similarity_score) pairs, sorted by similarity in descending order.
     */
    public List<Long> fuzzyMatch(double threshold) {
        return categories.stream()
                .map(cat -> new AbstractMap.SimpleEntry<>(cat, jaccardSim(queryTokens, cat.getDisplayNameTokens())))
                .filter(entry -> entry.getValue() >= threshold)
                .sorted(Comparator.<Map.Entry<CategorySuggesterScoreModel, Double>>comparingDouble(Map.Entry::getValue).reversed())
                .map(entry -> entry.getKey().getId())
                .collect(Collectors.toList());
    }

    /**
     * Calculate the Levenshtein edit distance
     *
     * @param s1 string query
     * @param s2 string category
     * @return distance between s1 and s2
     */
    public int calculateLevenshteinDistance(String s1, String s2) {
        int m = s1.length();
        int n = s2.length();
        int[][] dp = new int[m + 1][n + 1];

        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }

        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                int cost = s1.charAt(i - 1) == s2.charAt(j - 1) ? 0 : 1;
                dp[i][j] = Math.min(Math.min(
                                dp[i - 1][j] + 1,
                                dp[i][j - 1] + 1),
                        dp[i - 1][j - 1] + cost);
            }
        }
        return dp[m][n];
    }

    /**
     * Match query and category using the Edit Distance.
     *
     * @param threshold The threshold
     * @return The list of matching categories
     */
    public List<Long> editMatch(int threshold) {
        String queryJoined = String.join("", queryTokens);
        List<Map.Entry<Long, Integer>> scores = new ArrayList<>();
        for (CategorySuggesterScoreModel cat : categories) {
            int dist = calculateLevenshteinDistance(queryJoined, String.join("", cat.getDisplayNameTokens()));
            if (dist < threshold) {
                scores.add(new HashMap.SimpleEntry<>(cat.getId(), dist));
            }
        }
        // Sort by similarity in descending order
        scores.sort(Map.Entry.comparingByValue(Comparator.naturalOrder()));

        List<Long> result = new ArrayList<>();
        String categoryLevelOne = "";
        for (Map.Entry<Long, Integer> entry : scores) {
            Long categoryId = entry.getKey();
            CategorySuggesterScoreModel cat = categoriesMap.get(categoryId);
            if (cat != null) {
                List<String> levels = Arrays.asList(cat.getTree().split(";"));
                if (1 < levels.size()) {
                    String levelOne = cat.getTree().split(";")[1];
                    if (categoryLevelOne.isEmpty()) {
                        categoryLevelOne = levelOne;
                    } else if (!levelOne.equals(categoryLevelOne)) {
                        break;
                    }
                    result.add(cat.getId());
                }
            }
        }
        return result;
    }

    public List<Long> prefixMatch() {
        // Use a Set to avoid duplicate category matches
        Set<Long> matchedCategoryIds = new HashSet<>();

        for (String qWord : queryTokens) {
            List<String> matchingWords = trie.findWordsWithPrefix(qWord);

            for (String matchedWord : matchingWords) {
                for (CategorySuggesterScoreModel cat : categories) {
                    List<String> catTokens = cat.getDisplayNameTokens();
                    String compoundForm = String.join("", catTokens);

                    // check the match type
                    boolean isDirectTokenMatch = catTokens.contains(matchedWord);
                    boolean isCompoundMatch = matchedWord.equals(compoundForm);
                    boolean isKeyComboMatch = catTokens.size() > 1 &&
                            matchedWord.equals(catTokens.get(0) + catTokens.get(1));

                    if (isDirectTokenMatch || isCompoundMatch || isKeyComboMatch) {
                        matchedCategoryIds.add(cat.getId());
                    }
                }
            }
        }
        List<Map.Entry<Long, Integer>> scores = new ArrayList<>();
        for (Long categoryId : matchedCategoryIds) {
            CategorySuggesterScoreModel cat = categoriesMap.get(categoryId);
            if (null != cat) {
                int score = 10 * String.join("", cat.getDisplayNameTokens()).length()
                        + String.join("", cat.getTreeTokens()).length();
                scores.add(new AbstractMap.SimpleEntry<>(categoryId, score));
            }
        }
        scores.sort(Map.Entry.comparingByValue(Comparator.naturalOrder()));
        return scores.stream().map(Map.Entry::getKey).collect(Collectors.toList());
    }

    /**
     * Calculate cosine similarity (optimized version)
     *
     * @param s1 query
     * @param s2 category name
     * @return similarity score within 0.0 ~ 1.0
     */
    public static double calculateCosineSimilarity(String s1, String s2) {
        Map<Character, Integer> vec1 = new HashMap<>();
        Map<Character, Integer> vec2 = new HashMap<>();

        s1 = s1.toLowerCase();
        s2 = s2.toLowerCase();

        for (char c : s1.toCharArray()) vec1.put(c, vec1.getOrDefault(c, 0) + 1);
        for (char c : s2.toCharArray()) vec2.put(c, vec2.getOrDefault(c, 0) + 1);

        double dotProduct = 0;
        for (char c : vec1.keySet()) {
            if (vec2.containsKey(c)) {
                dotProduct += vec1.get(c) * vec2.get(c);
            }
        }

        double mag1 = Math.sqrt(vec1.values().stream().mapToInt(v -> v * v).sum());
        double mag2 = Math.sqrt(vec2.values().stream().mapToInt(v -> v * v).sum());

        return mag1 > 0 && mag2 > 0 ? dotProduct / (mag1 * mag2) : 0;
    }

    public List<Long> cosineMatch(double threshold) {
        String queryStr = String.join("", queryTokens);
        List<CategorySuggesterScoreModel> toSort = new ArrayList<>();
        for (CategorySuggesterScoreModel cat : categories) {
            String catStr = String.join("", cat.getDisplayNameTokens());
            cat.setScore(calculateCosineSimilarity(queryStr, catStr));
            if (cat.getScore() >= threshold) {
                toSort.add(cat);
            }
        }
        toSort.sort(Comparator.comparing(CategorySuggesterScoreModel::getScore).reversed());
        List<Long> list = new ArrayList<>();
        for (CategorySuggesterScoreModel cat : toSort) {
            Long id = cat.getId();
            list.add(id);
        }
        return list;
    }

    public List<CategorySuggesterScoreModel> sortCategories(List<CategorySuggesterScoreModel> categories) {
        List<IndexedCategory> indexedList = new ArrayList<>();
        for (int i = 0; i < categories.size(); i++) {
            indexedList.add(new IndexedCategory(i, categories.get(i)));
        }

        Collections.sort(indexedList, (ic1, ic2) -> {
            String level1 = extractFirstLevel(ic1.category.getTree());
            String level2 = extractFirstLevel(ic2.category.getTree());
            int priority1 = getPriority(level1);
            int priority2 = getPriority(level2);
            if (priority1 != priority2) {
                return Integer.compare(priority1, priority2);
            }
            return Integer.compare(ic1.originalIndex, ic2.originalIndex);
        });

        return indexedList.stream()
                .map(ic -> ic.category)
                .collect(Collectors.toList());
    }

    public String extractFirstLevel(String tree) {
        if (tree == null || tree.isEmpty()) return "";
        int idx = tree.indexOf(';');
        return (idx > 0) ? tree.substring(0, idx) : tree;
    }

    public int getPriority(String firstLevel) {
        // Highest priority
        if ("For Sale".equals(firstLevel)) return 1;
        // Second highest priority
        if ("Services".equals(firstLevel)) return 2;
        // Other categories have the lowest priority
        return 3;
    }

    class IndexedCategory {
        int originalIndex;
        CategorySuggesterScoreModel category;

        IndexedCategory(int originalIndex, CategorySuggesterScoreModel category) {
            this.originalIndex = originalIndex;
            this.category = category;
        }
    }

    public List<CategorySuggesterScoreModel> doTextMatch() {
        if (StringUtils.isBlank(query)) {
            return Collections.emptyList();
        }
        Set<Long> matchedIds = new LinkedHashSet<>();
        List<CategorySuggesterScoreModel> results = new ArrayList<>();

        // Create a list of matching stages (type + ID list)
        List<AbstractMap.SimpleEntry<String, List<Long>>> matchingStages = new ArrayList<>();
        matchingStages.add(new AbstractMap.SimpleEntry<>("exact_match", exactMatch()));
        matchingStages.add(new AbstractMap.SimpleEntry<>("prefix_match", prefixMatch()));
        matchingStages.add(new AbstractMap.SimpleEntry<>("fuzzy_match", fuzzyMatch(fuzzyThreshold)));
        matchingStages.add(new AbstractMap.SimpleEntry<>("edit_match", editMatch(editThreshold)));
        matchingStages.add(new AbstractMap.SimpleEntry<>("cosine_match", cosineMatch(cosineThreshold)));

        // Process each matching phase in sequence
        for (AbstractMap.SimpleEntry<String, List<Long>> stage : matchingStages) {
            String matchType = stage.getKey();
            List<Long> stageIds = stage.getValue();

            for (Long id : stageIds) {
                if (matchedIds.size() >= maxCategorySize) break;
                if (matchedIds.contains(id)) continue;

                CategorySuggesterScoreModel cat = categoriesMap.get(id);
                if (cat != null) {
                    Map<String, String> categoryCrumb = new HashMap<>();
                    categoryCrumb.put(recallType, matchType);
                    cat.setCategoryCrumb(categoryCrumb);
                    results.add(cat);
                    matchedIds.add(id);
                }
            }
        }

        if ("sofa".equals(query)) {
			results = sortCategories(results);
        }

        // Result sorting: non-"other" has priority
        results.addAll(categories.stream().filter(
                cat -> !matchedIds.contains(cat.getId())).collect(Collectors.toList()));

        List<CategorySuggesterScoreModel> finalResults =
                results.stream()
                        .filter(cat ->
                                cat.getDisplayName() == null || !cat.getDisplayName().toLowerCase().contains("other"))
                        .collect(Collectors.toList());

        finalResults.addAll(results.stream()
                .filter(cat ->
                        cat.getDisplayName() != null && cat.getDisplayName().toLowerCase().contains("other"))
                .collect(Collectors.toList()));
        return finalResults.stream().limit(maxCategorySize).collect(Collectors.toList());
    }
}