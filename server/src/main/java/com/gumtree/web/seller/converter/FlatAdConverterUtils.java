package com.gumtree.web.seller.converter;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.advert.entity.PriceEntity;
import com.gumtree.domain.location.entity.LocationCentroidEntity;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.Video;
import com.gumtree.domain.media.entity.ImageEntity;
import com.gumtree.domain.media.entity.VideoEntity;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeService;
import com.gumtree.domain.newattribute.DisplayAttribute;
import com.gumtree.domain.user.UserType;
import com.gumtree.domain.user.entity.UserEntity;
import com.gumtree.liveadsearch.model.Account;
import com.gumtree.liveadsearch.model.Feature;
import com.gumtree.liveadsearch.model.FlatAd;
import com.gumtree.liveadsearch.model.GeoLocation;
import com.gumtree.liveadsearch.model.Location;
import com.gumtree.liveadsearch.model.User;
import com.gumtree.location.LocationDisplayUtils;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.category.CategoryService;
import org.apache.commons.lang.StringUtils;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Currency;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gumtree.api.category.domain.AttributeType.DATETIME;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.PRICE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.SELLER_TYPE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_MILEAGE;


/**
 * Utility methods for flat ad convertion
 */
@Component
public class FlatAdConverterUtils {

    private DateTimeFormatter dateTimeFormatter = ISODateTimeFormat.dateTime();

    private final AttributeService attributeService;

    private final CategoryService categoryService;

    @Autowired
    public FlatAdConverterUtils(AttributeService service, CategoryService categoryService) {
        this.attributeService = service;
        this.categoryService = categoryService;
    }


    public BigDecimal getPrice(Map<String, Attribute> adAttributeMap) {
        Attribute priceAsStr = adAttributeMap.get(PRICE.getName());
        if (priceAsStr != null) {
            Double priceAsDouble = Double.valueOf(priceAsStr.getValue().as(String.class));
            return BigDecimal.valueOf(getPriceInPounds(priceAsDouble));
        }
        return null;
    }

    private double getPriceInPounds(Double priceAsDouble) {
        return priceAsDouble / 100;
    }

    public String getSellerType(Long primaryCategoryId, Map<String, Attribute> adAttributeMap) {
        if (attributeService.isSupportedByCategory(SELLER_TYPE.getName(), primaryCategoryId)) {
            Attribute sellerTypeAttribute = adAttributeMap.get(SELLER_TYPE.getName());
            if (sellerTypeAttribute != null) {
                return attributeService.getAttributeValueDisplayName(sellerTypeAttribute, primaryCategoryId);
            }
        }

        return "";
    }

    public java.util.Optional<String> getVehicleMileage(Long primaryCategoryId, Map<String, Attribute> adAttributeMap) {
        if (attributeService.isSupportedByCategory(VEHICLE_MILEAGE.getName(), primaryCategoryId)) {
            Attribute attribute = adAttributeMap.get(VEHICLE_MILEAGE.getName());
            if (attribute != null) {
                return java.util.Optional.ofNullable(attributeService.getAttributeValueDisplayName(attribute, primaryCategoryId));
            }
        }

        return java.util.Optional.empty();
    }

    public Image getPrimaryImage(FlatAd advert) {
        if (advert.getPrimaryImageUrl() != null) {
            ImageEntity image = new ImageEntity();
            image.setBaseUrl(advert.getPrimaryImageUrl());
            return image;
        }
        return null;
    }

    public boolean isUrgent(FlatAd advert) {
        List<Feature> features = advert.getFeatures();
        if (features != null && !features.isEmpty()) {
            for (Feature feature : features) {
                ProductName productName = ProductName.valueOf(feature.getProduct());
                if (ProductName.URGENT == productName) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isFeatured(FlatAd advert) {
        List<Feature> features = advert.getFeatures();
        if (features != null && !features.isEmpty()) {
            for (Feature feature : features) {
                ProductName productName = ProductName.valueOf(feature.getProduct());
                if (ProductName.FEATURE_3_DAY == productName
                        || ProductName.FEATURE_7_DAY == productName
                        || ProductName.FEATURE_14_DAY == productName) {
                    return true;
                }
            }
        }
        return false;
    }

    public String getWebsiteUrl(FlatAd advert) {
        List<Feature> features = advert.getFeatures();
        if (features != null && !features.isEmpty()) {
            for (Feature feature : features) {
                if (feature.getUrl()!=null) {
                    return feature.getUrl();
                }
            }
        }
        return "";
    }

    public String getSeoTimeStamp(FlatAd advert) {
        Long liveDate = advert.getPublishedDate();
        if (liveDate == null) {
            return null;
        }

        return dateTimeFormatter.print(liveDate);
    }

    public String getDisplayLocationText(FlatAd advert) {
        String locationText = advert.getLocalArea();
        if (StringUtils.isNotEmpty(locationText)) {
            return LocationDisplayUtils.getCleanedLocationText(locationText);
        }

        if (getPrimaryLocation(advert) != null) {
            return getPrimaryLocation(advert).getDisplayName();
        }

        return "";
    }

    public Location getPrimaryLocation(FlatAd advert) {
        java.util.Optional<Location> optionalLocation = java.util.Optional.empty();
                if(advert.getLocations()!=null) {
                    optionalLocation=   advert.getLocations()
                            .stream()
                            .filter(Location::getPrimary)
                            .findFirst();
                }
        return optionalLocation.orElse(null);
    }

    public com.gumtree.liveadsearch.model.Category getPrimaryCategory(FlatAd advert) {
        java.util.Optional<com.gumtree.liveadsearch.model.Category> optionalCategory = java.util.Optional.empty();
               if(advert.getCategories()!=null) {
                   optionalCategory = advert.getCategories()
                           .stream()
                           .filter(com.gumtree.liveadsearch.model.Category::getPrimary)
                           .findFirst();
               }
        return optionalCategory.orElse(null);
    }

    public String getDateLabel(Map<String, Attribute> adAttributeMap, Long categoryId) {
        Optional<Category> categoryOption = categoryService.getById(categoryId);
        if (categoryOption.isPresent()) {
            return getDateLabel(adAttributeMap, categoryOption.get());
        }
        return null;
    }

    public Map<String, Attribute> getAdAttributeMap(FlatAd ad) {
        Map<String, Object> adAttributes = ad.getAttribute();
        if (adAttributes != null && !adAttributes.isEmpty()) {
            Optional<Category> categoryById
                    = categoryService.getById(getPrimaryCategory(ad).getId());
            if (categoryById.isPresent()) {
                List<AttributeMetadata> categoryMetadata = categoryById.get().getAttributeMetadata();
                return getAttributesMappingForAttributesMetadata(adAttributes, categoryMetadata);
            }
        }
        return new HashMap<String, Attribute>();
    }

    public Map<String, Attribute> getAdAttributeMap(Long categoryId, Map<String, Object> adAttributes) {
        if (adAttributes != null && !adAttributes.isEmpty()) {
            Optional<Category> categoryById = categoryService.getById(categoryId);
            if (categoryById.isPresent()) {
                List<AttributeMetadata> categoryMetadata = categoryById.get().getAttributeMetadata();
                return getAttributesMappingForAttributesMetadata(adAttributes, categoryMetadata);
            }
        }
        return new HashMap<String, Attribute>();
    }

    private Map<String, Attribute> getAttributesMappingForAttributesMetadata(
            Map<String, Object> adAttributes, List<AttributeMetadata> attributesMetadata) {
        Map<String, Attribute> adAttrMap = new HashMap<String, Attribute>();
        for (Map.Entry<String, Object> attrEntry : adAttributes.entrySet()) {
            Optional<AttributeMetadata> attrMetadata
                    = getAttributeMetadataMatchingName(attributesMetadata, attrEntry.getKey());
            if (attrMetadata.isPresent()) {
                Optional<Attribute> attribute = createAttribute(attrEntry.getValue(), attrMetadata.get());
                if (attribute.isPresent()) {
                    adAttrMap.put(attrMetadata.get().getName(), attribute.get());
                }
            }
        }
        return adAttrMap;
    }

    private Optional<AttributeMetadata> getAttributeMetadataMatchingName(
            List<AttributeMetadata> attributeMetadataList, String key) {
        for (AttributeMetadata attributeMetadata: attributeMetadataList) {
            if (attributeMetadata.getName().equals(key)) {
                return Optional.fromNullable(attributeMetadata);
            }
        }
        return Optional.absent();
    }

    public Date toDateNullSafe(Long millis) {
        if (millis != null) {
            return new Date(millis);
        }

        return null;
    }

    public Optional<Attribute> createAttribute(Object value, AttributeMetadata attrType) {
        return Optional.fromNullable(attributeService.createAttribute(attrType, value));
    }

    public List<Video> getVideos(FlatAd flatAd) {
        String youtubeUrl = flatAd.getYoutubeUrl();
        return Lists.newArrayList(((Video) new VideoEntity(youtubeUrl)));
    }

    public PriceEntity getPriceEntity(Map<String, Attribute> attributeMap) {
        BigDecimal decimal = getPrice(attributeMap);
        if (decimal != null) {
            PriceEntity priceEntity = new PriceEntity();
            priceEntity.setAmount(decimal);
            priceEntity.setCurrency(Currency.getInstance("GBP"));
            return priceEntity;
        }
        return null;
    }

    public UserEntity getUserEntity(FlatAd flatAd) {
        User createdBy = flatAd.getCreatedBy();
        UserEntity userEntity = new UserEntity();
        Account account = flatAd.getAccount();
        if (account != null) {
            userEntity.setFirstPostingDate(toDateNullSafe(account.getPostingSince()));
        }
        if (createdBy != null) {
            userEntity.setId(createdBy.getId().toString());
        }
        userEntity.setContactUrl(flatAd.getContactUrl());
        userEntity.setContactName(flatAd.getContactName());
        userEntity.setContactTelephone(flatAd.getContactTelephone());
        userEntity.setEmailAddress(flatAd.getContactEmail());
        userEntity.setType(getUserType(flatAd));
        return userEntity;
    }

    private UserType getUserType(FlatAd flatAd) {
        Account account = flatAd.getAccount();
        if (account != null && account.getPro()) {
            return UserType.TRADE;
        } else {
            return UserType.PRIVATE;
        }
    }


    public LocationCentroidEntity getPoint(FlatAd flatAd) {
        GeoLocation centroid = flatAd.getCentroid();
        if (centroid != null) {
            LocationCentroidEntity locationCentroidEntity = new LocationCentroidEntity();
            double latitude = centroid.getLatitude();
            double longitude = centroid.getLongitude();
            locationCentroidEntity.setLatitude(latitude);
            locationCentroidEntity.setLongitude(longitude);
            return locationCentroidEntity;
        }
        return null;
    }

    public List<Integer> getLocationIds(FlatAd flatAd) {
        List<Location> locations = flatAd.getLocations();
        if (!CollectionUtils.isEmpty(locations)) {
            return Lists.newArrayList(Iterables.transform(locations, new Function<Location, Integer>() {
                @Override
                public Integer apply(@Nullable Location input) {
                    if (input != null) {
                        return input.getId().intValue();
                    }
                    return 0;
                }
            }));
        }
        return Collections.emptyList();
    }

    public List<Image> getImages(FlatAd flatAd) {
        List<String> additionalImageUrls = flatAd.getAdditionalImageUrls();
        if (!CollectionUtils.isEmpty(additionalImageUrls)) {
            return Lists.newArrayList(Iterables.transform(additionalImageUrls, new Function<String, Image>() {
                @Override
                public Image apply(@Nullable String url) {
                    return new ImageEntity(0L, url);
                }
            }));
        }
        return Collections.emptyList();
    }

    public boolean isSpotlighted(FlatAd advert) {
        List<Feature> features = advert.getFeatures();
        if (features != null && !features.isEmpty()) {
            for (Feature feature : features) {
                ProductName productName = ProductName.valueOf(feature.getProduct());
                if (ProductName.HOMEPAGE_SPOTLIGHT == productName) {
                    return true;
                }
            }
        }
        return false;
    }

    private String getDateLabel(Map<String, Attribute> adAttributeMap, Category category) {
        for (AttributeMetadata attributeMetadata : category.getAttributeMetadata()) {
            if (attributeMetadata.getType() == DATETIME) {
                Attribute attribute = adAttributeMap.get(attributeMetadata.getName());
                if (attribute != null && attribute.getValue() != null) {
                    Optional<DisplayAttribute> displayAttributeOption
                            = attributeService.getDisplayAttribute(attribute, category.getId());
                    if (displayAttributeOption.isPresent()) {
                        return attributeMetadata.getLabel() + ": "
                                + displayAttributeOption.get().getDisplayValue();
                    }
                }
            }
        }
        return null;
    }
}
