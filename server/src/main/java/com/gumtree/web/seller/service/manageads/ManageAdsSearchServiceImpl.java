package com.gumtree.web.seller.service.manageads;

import com.gumtree.api.AdSearchResponse;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.fulladsearch.model.FullAdFlatAd;
import com.gumtree.fulladsearch.model.FullAdManageAdsRequest;
import com.gumtree.fulladsearch.model.FullAdManageAdsResponse;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * The implementation of the {@link ManageAdsSearchService}
 */
@Service
public class ManageAdsSearchServiceImpl implements ManageAdsSearchService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ManageAdsSearchServiceImpl.class);

    private final BushfireApi bushfireApi;
    private final FullAdsSearchApi fullAdsSearchApi;

    private final CustomMetricRegistry metrics;

    @Autowired
    ManageAdsSearchServiceImpl(BushfireApi bushfireApi, FullAdsSearchApi fullAdsSearchApi, CustomMetricRegistry metrics) {
        this.bushfireApi = bushfireApi;
        this.fullAdsSearchApi = fullAdsSearchApi;
        this.metrics = metrics;
    }

    @Override
    public final ManageAdsSearchServiceResponse search(List<String> statuses, String searchTerms, long accountId, int pageNumber, int pageSize) {
        return metrics.madPageTimer("search").record(() -> {
            ManageAdsSearchServiceResponse response = new ManageAdsSearchServiceResponse();
            response.setSearchTerms(searchTerms);
            if ("".equals(searchTerms) || searchTerms == null) {  //to regular search
                AdSearchResponse result = metrics.madPageTimer("bushfireApi.advertApi().search").record(() ->
                        bushfireApi.advertApi().search(accountId, statuses, pageNumber, pageSize, false));
                response.setAdverts(result.getPostings());
                response.setTotalResults(new Long(result.getTotalCount()).intValue());

            } else {
                FullAdManageAdsResponse manageAdsResponse = getManageAdsResponse(statuses, searchTerms, accountId, pageNumber, pageSize);
                List<Long> searchAdIds = getAdvertIds(manageAdsResponse);
                if (!searchAdIds.isEmpty()) {
                    response.setAdverts(metrics.madPageTimer("bushfireApi.advertApi().getAdverts").record(() ->
                            bushfireApi.advertApi().getAdverts(searchAdIds)));
                } else {
                    response.setAdverts(new ArrayList<>());
                }
                response.setTotalResults(manageAdsResponse.getTotal().intValue());
                response.setEmptySearch((response.getAdverts().isEmpty()));
            }
            return response;
        });
    }

    private FullAdManageAdsResponse getManageAdsResponse(List<String> statuses, String searchTerms, long id, int page, int size) {
        return metrics.madPageTimer("getManageAdsResponse").record(() -> {
            FullAdManageAdsRequest manageAdsRequest = new FullAdManageAdsRequest()
                    .accountId(id)
                    .search(searchTerms)
                    .statuses(statuses)
                    .offset(getFirstIndexOfPage(page, size))
                    .limit(size);

            return fullAdsSearchApi.manageAds(manageAdsRequest)
                    .onErrorReturn(error -> {
                        LOGGER.warn("Error getting manage Ads", error);
                        return new FullAdManageAdsResponse().items(Collections.emptyList()).total(0L);
                    })
                    .toBlocking().value();
        });
    }

    private List<Long> getAdvertIds(FullAdManageAdsResponse manageAdsResponse) {
        return manageAdsResponse.getItems()
                .stream()
                .map(FullAdFlatAd::getId)
                .collect(Collectors.toList());
    }

    protected int getFirstIndexOfPage(int page, int pageSize) {
        return (page - 1) * pageSize;
    }
}
