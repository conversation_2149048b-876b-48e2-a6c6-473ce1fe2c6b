package com.gumtree.web.seller.security.capi.controller;

import com.google.common.collect.Maps;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.VerificationDetails;
import com.gumtree.userapi.model.GumtreeAccessToken;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.capi.CapiAuthenticationCookie;
import com.gumtree.web.security.GtSecurityUtils;
import com.gumtree.web.security.shiro.RedirectUtils;
import com.gumtree.web.security.shiro.UserAuthenticationInfo;
import com.gumtree.web.seller.page.login.controller.LoginController;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.web.mgt.RemainingMaxAgeRememberMeManager;
import org.apache.shiro.web.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.Optional;

@Controller
public class CapiAuthenticationController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CapiAuthenticationController.class);
    private static final String DEFAULT_REDIRECT_URL = GtProps.getStr(SellerProperty.GUMTREE_HOST) + "/postad/category";

    private final CookieResolver cookieResolver;
    private final UserServiceFacade userServiceFacade;
    private final RemainingMaxAgeRememberMeManager rememberMeManager;

    @Autowired
    public CapiAuthenticationController(CookieResolver cookieResolver,
                                        UserServiceFacade userServiceFacade,
                                        RemainingMaxAgeRememberMeManager rememberMeManager) {
        this.cookieResolver = cookieResolver;
        this.userServiceFacade = userServiceFacade;
        this.rememberMeManager = rememberMeManager;
    }


    @RequestMapping(method = RequestMethod.GET, value = "/api/capi/authenticate")
    public void authenticateCapi(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            Optional<String> cookie = cookieResolver.resolve(request, CapiAuthenticationCookie.class).getValueOpt();
            if (cookie.isPresent()) {
                ApiResponse<VerificationDetails> verificationDetails = userServiceFacade.verifyCapiAccessToken(cookie.get());
                if (verificationDetails.isDefined()) {
                    logoutIfLoggedIn();

                    UserAuthenticationInfo authInfo = new UserAuthenticationInfo();
                    authInfo.setUsername(verificationDetails.get().getUsername());
                    authInfo.setAccessToken(GumtreeAccessToken.createFromString(verificationDetails.get().getToken().getValue()));

                    OffsetDateTime authCreateTime = verificationDetails.get().getCreatedDateTime();

                    rememberMeManager.onSuccessfulLogin(SecurityUtils.getSubject(), authInfo, authCreateTime);

                    Optional<String> callbackURL = RedirectUtils.getCallbackURL(request);
                    String redirectUrl = callbackURL.orElse(DEFAULT_REDIRECT_URL);
                    WebUtils.issueRedirect(request, response, redirectUrl, Maps.newHashMap(), false, false);
                    return;
                }
            }
        } catch (Exception e) {
            LOGGER.warn("Capi authentication error", e);
        }

        WebUtils.issueRedirect(request, response, LoginController.PAGE_PATH, Maps.newHashMap(), false, false);
    }

    private static void logoutIfLoggedIn() {
        if (GtSecurityUtils.isUserRemembered()) {
            SecurityUtils.getSubject().logout();
        }
    }
}
