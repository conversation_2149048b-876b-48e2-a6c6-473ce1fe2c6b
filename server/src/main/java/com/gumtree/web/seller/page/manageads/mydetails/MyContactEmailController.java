package com.gumtree.web.seller.page.manageads.mydetails;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.google.common.collect.Maps;
import com.gumtree.api.ApiContactEmail;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.config.SellerProperty;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.postad.model.path.ManageAdsPath;
import com.gumtree.web.service.ContactEmailService;
import org.apache.commons.collections.IteratorUtils;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

import static com.codahale.metrics.MetricRegistry.name;

@Controller
@RequestMapping(value = "/manage-account/contact-email")
public class MyContactEmailController extends BaseSellerController {

    public static final String SECURE_TOKEN = "secureToken";
    public static final String EMAIL = "email";
    private final UserSession userSession;
    private final ContactEmailService contactEmailService;
    private final UserServiceFacade userServiceFacade;
    private PropSupplier<Integer> contactEmailLimit = GtProps.getDInt(SellerProperty.CONTACT_EMAIL_LIMIT);
    private MetricRegistry metricRegistry;
    private Counter emailLimitCounter;

    @Autowired
    public MyContactEmailController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                    ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                    UrlScheme urlScheme, UserSession userSession,
                                    ContactEmailService contactEmailService, UserSessionService userSessionService,
                                    UserServiceFacade userServiceFacade, MetricRegistry metricRegistry) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.userSession = userSession;
        this.contactEmailService = contactEmailService;
        this.userServiceFacade = userServiceFacade;
        this.metricRegistry = metricRegistry;
        this.emailLimitCounter = metricRegistry.counter(name(MyContactEmailController.class, "emailLimitCounter"));
    }

    @RequestMapping(value = "/", method = RequestMethod.GET, headers = "X-Requested-With=XMLHttpRequest")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public Iterable<ApiContactEmail> get() {
        return contactEmailService.getEditable(userSession.getUser());
    }

    @RequestMapping(value = "/", method = RequestMethod.PUT, headers = "X-Requested-With=XMLHttpRequest")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public Response add(@RequestBody MultiValueMap<String, String> body) {
        if (isContactEmailWithinLimit()) {
            return new Response(contactEmailService.createNew(userSession.getUser(), body.getFirst("email")));
        } else {
            emailLimitCounter.inc();
            return new Response(false);
        }
    }

    @RequestMapping(value = "/", method = RequestMethod.DELETE, headers = "X-Requested-With=XMLHttpRequest")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public Response delete(@RequestBody MultiValueMap<String, String> body) {
        return new Response(contactEmailService.delete(userSession.getUser(), body.getFirst("email")));
    }

    @RequestMapping(value = "/primary", method = RequestMethod.PUT, headers = "X-Requested-With=XMLHttpRequest")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public Response setPreferred(@RequestBody MultiValueMap<String, String> body) {
        return new Response(contactEmailService.setPreferred(userSession.getUser(), body.getFirst(EMAIL)));
    }

    @RequestMapping(value = "/resendverify", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    public ModelAndView resendVerification(@RequestBody MultiValueMap<String, String> body) {
        contactEmailService.sendVerification(userSession.getUser(), body.getFirst(EMAIL));
        return redirect(new ManageAdsPath().getPath());
    }

    private boolean isContactEmailWithinLimit() {
        Iterable<ApiContactEmail> apiContactEmails = contactEmailService.getEditable(userSession.getUser());
        return IteratorUtils.toList(apiContactEmails.iterator()).size() < contactEmailLimit.get();
    }

    @JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
    public static final class Response {
        private String successNotice;
        private Map<String, String> errors;

        public Response(Boolean result) {
            if (result) {
                successNotice = "Success";
            } else {
                errors = Maps.newHashMap();
                errors.put("allResolvedFieldErrorMessages", "Operation failed");
            }
        }

        public String getSuccessNotice() {
            return successNotice;
        }

        public void setSuccessNotice(String successNotice) {
            this.successNotice = successNotice;
        }

        public Map<String, String> getErrors() {
            return errors;
        }

        public void setErrors(Map<String, String> errors) {
            this.errors = errors;
        }
    }
}
