package com.gumtree.web.seller.security.apiauthentication;

import com.gumtree.api.User;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;

import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class JsonWebTokenService {

    private String issuer;
    private String privateKey;
    private int tokenNotValidBeforeSeconds;
    private int tokenNotValidAfterSeconds;

    public JsonWebTokenService(JsonWebTokenProperties properties) {
        this.issuer = properties.getIssuer();
        this.privateKey = properties.getPrivateKey();
        this.tokenNotValidBeforeSeconds = properties.getTokenNotValidBeforeSeconds();
        this.tokenNotValidAfterSeconds = properties.getTokenNotValidAfterSeconds();
    }

    /**
     * Create token with given user information if present
     *
     * @param user user info
     * @return json web token
     * @throws JOSEException if any error occurs during creation of the token
     */
    public String generateTokenForUser(User user) throws JOSEException, NoSuchAlgorithmException, InvalidKeySpecException{
        Map<String, Object> customClaims = new HashMap<>();
        if (user != null) {
            customClaims.put("userId", user.getId());
            customClaims.put("email", user.getEmail());
            customClaims.put("firstName", user.getFirstName());
            customClaims.put("status", user.getStatus());

            String apiKey = user.getApiKey() != null ? user.getApiKey().getAccessKey() : "";
            customClaims.put("apiKey", apiKey);

            String userType = user.getType() != null ? user.getType().toString() : "";
            customClaims.put("userType", userType);
        }
        return generateToken(customClaims);
    }

    /**
     * Create a Token which is valid in time range defined by notBeforeDelayInSeconds and expireInSeconds
     *
     * @param customClaims values to be included in the token
     * @throws JOSEException if any error occurs during creation of the token
     */
    private String generateToken(Map<String, Object> customClaims) throws JOSEException, NoSuchAlgorithmException, InvalidKeySpecException {
        JWSHeader jwsHeader = new JWSHeader.Builder(JWSAlgorithm.RS256).type(JOSEObjectType.JWT).build();
        SignedJWT signedJWT = new SignedJWT(jwsHeader, buildClaimSet(customClaims));
        signedJWT.sign(new RSASSASigner(decode(privateKey)));
        return signedJWT.serialize();
    }

    private JWTClaimsSet buildClaimSet(Map<String, Object> customClaims) {
        ZonedDateTime utc = ZonedDateTime.now(ZoneOffset.UTC);
        JWTClaimsSet.Builder claimsSet = new JWTClaimsSet.Builder();
        claimsSet.issuer(issuer);
        claimsSet.issueTime(Date.from(utc.toInstant()));

        claimsSet.notBeforeTime(Date.from(utc.plusSeconds(tokenNotValidBeforeSeconds).toInstant()));
        claimsSet.expirationTime(Date.from(utc.plusSeconds(tokenNotValidAfterSeconds).toInstant()));
        for (String key : customClaims.keySet()) {
            claimsSet.claim(key, customClaims.get(key));
        }
        return claimsSet.build();
    }

    private PrivateKey decode(String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        String privateK = privateKey.replaceAll("\\n", "")
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "");
        KeyFactory kf = KeyFactory.getInstance("RSA");
        PKCS8EncodedKeySpec keySpecPKCS8 = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateK));
        return kf.generatePrivate(keySpecPKCS8);
    }
}
