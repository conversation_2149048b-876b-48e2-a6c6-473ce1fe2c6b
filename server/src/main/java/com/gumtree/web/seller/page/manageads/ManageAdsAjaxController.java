package com.gumtree.web.seller.page.manageads;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.security.UserSession;
import com.gumtree.zeno.core.domain.PageType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.Map;


/**
 * Controller for Ajax requests made from Responsive Manage Ads
 */
@Controller
@GumtreePage(PageType.MyAds)
public final class ManageAdsAjaxController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManageAdsAjaxController.class);

    public static final String BUMPUP_VIEW = "pages/feature-bump-up/feature-bump-up";
    public static final String URGENT_VIEW = "pages/feature-urgent/feature-urgent";
    public static final String TOPAD_VIEW = "pages/feature-top-ad/feature-top-ad";
    public static final String SPOTLIGHT_VIEW = "pages/feature-spotlight/feature-spotlight";

    private BushfireApi bushfireApi;

    private UserSession userSession;

    /**
     * Constructor.
     *
     * @param bushfireApi        bushfireApi
     * @param userSession        userSession
     */
    @Autowired
    public ManageAdsAjaxController(BushfireApi bushfireApi,
                                   UserSession userSession) {
        this.bushfireApi = bushfireApi;
        this.userSession = userSession;
    }

    @RequestMapping(value = "/ajax/ad/{id}/bump-up/", method = RequestMethod.GET)
    public ModelAndView showBumpUp(@PathVariable("id") Long advertId) {
        Map<String, Object> model = new HashMap<String, Object>();
        return new ModelAndView(BUMPUP_VIEW, model);
    }

    @RequestMapping(value = "/ajax/ad/{id}/urgent/", method = RequestMethod.GET)
    public ModelAndView showUrgent(@PathVariable("id") Long advertId) {
        Map<String, Object> model = new HashMap<String, Object>();
        return new ModelAndView(URGENT_VIEW, model);
    }

    @RequestMapping(value = "/ajax/ad/{id}/top-ad/", method = RequestMethod.GET)
    public ModelAndView showTopAd(@PathVariable("id") Long advertId) {
        Map<String, Object> model = new HashMap<String, Object>();
        return new ModelAndView(TOPAD_VIEW, model);
    }

    @RequestMapping(value = "/ajax/ad/{id}/spotlight/", method = RequestMethod.GET)
    public ModelAndView showSpotlight(@PathVariable("id") Long advertId) {
        Map<String, Object> model = new HashMap<String, Object>();
        return new ModelAndView(SPOTLIGHT_VIEW, model);
    }
}
