package com.gumtree.web.seller.service.category;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.category.predictor.client.CategoryPredictApi;
import com.gumtree.category.predictor.model.CategoryDTO;
import com.gumtree.category.predictor.model.PredictRequest;
import com.gumtree.category.predictor.model.PredictResult;
import com.gumtree.liveadsearch.client.LiveAdsSearchApi;
import com.gumtree.liveadsearch.model.CategorySuggestionsResponse;
import com.gumtree.liveadsearch.model.SearchableField;
import com.gumtree.util.NLPUtils;
import com.gumtree.web.seller.page.ajax.category.SuggestedCategory;
import com.gumtree.web.seller.service.category.model.CategorySuggesterScoreModel;
import io.micrometer.core.instrument.MeterRegistry;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Collections;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.util.stream.Collectors.toMap;

/**
 * CategorySuggesterServiceImpl.
 */
@Service
public class CategorySuggesterServiceImpl implements CategorySuggesterService {

  private static final Logger LOGGER = LoggerFactory.getLogger(CategorySuggesterServiceImpl.class);
  private LiveAdsSearchApi liveAdsSearchApi;

  private MeterRegistry meterRegistry;

  private CategoryModel categoryModel;

  private CategoryPredictApi categoryPredictApi;

  private static final String MISSING_CATEGORY = "MISSING_CATEGORY";

  private static final String SEARCH_SCENARIO = "syi";

  private static final String RECALL_TYPE = "recall_type";


  /**
   * CategorySuggesterServiceImpl.
   *
   * @param liveAdsSearchApi   liveAdsSearchApi
   * @param meterRegistry      meterRegistry
   * @param categoryModel      categoryModel
   * @param categoryPredictApi categoryPredictApi
   */
  @Autowired
  public CategorySuggesterServiceImpl(
      LiveAdsSearchApi liveAdsSearchApi,
      MeterRegistry meterRegistry,
      CategoryModel categoryModel,
      CategoryPredictApi categoryPredictApi) {
    this.liveAdsSearchApi = liveAdsSearchApi;
    this.meterRegistry = meterRegistry;
    this.categoryModel = categoryModel;
    this.categoryPredictApi = categoryPredictApi;
  }

  @Override
  public List<Long> getSuggestedCategories(String input) {
    CategorySuggestionsResponse searchResult = getCategorySuggestions(input);
    if (searchResult != null && searchResult.getAggregations() != null) {
      Optional<Map<Long, Long>> cats = getCategories(searchResult);
      if (cats.isPresent() && !CollectionUtils.isEmpty(cats.get())) {
        return cats.get()
            .entrySet()
            .stream()
            .sorted(Collections.reverseOrder(Map.Entry.comparingByValue()))
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
      } else {
        missingCategoryMetric(input);
      }
    } else {
      missingCategoryMetric(input);
    }

    return Collections.emptyList();
  }

  public Optional<Map<Long, Long>> getSuggestedCategoriesMap(String input) {
    return Optional.ofNullable(getCategorySuggestions(input))
        .flatMap(this::getCategories);
  }

  private CategorySuggestionsResponse getCategorySuggestions(String searchTerm) {
    return liveAdsSearchApi.getCategorySuggestions(searchTerm)
        .onErrorReturn(error -> {
          LOGGER.warn("Error getting suggested categories", error);
          return new CategorySuggestionsResponse().total(0L);
        })
        .toBlocking().value();
  }

  private Optional<Map<Long, Long>> getCategories(CategorySuggestionsResponse searchResult) {
    return Optional.ofNullable(searchResult.getAggregations().get(SearchableField.CATEGORY_ID.getValue()))
        .map(counts -> counts.entrySet()
            .stream()
            .collect(toMap(item -> Long.valueOf(item.getKey()), Map.Entry::getValue))
        );
  }

  private void missingCategoryMetric(String input) {
    LOGGER.info("missed category for search input {}", input);
    meterRegistry.counter(MISSING_CATEGORY).increment();
  }

  @Override
  public List<SuggestedCategory> getSuggestedCategoriesAllName(String query, int top) {
    String normalizedQuery = Optional.ofNullable(query).map(String::trim).map(String::toLowerCase).orElse("");

    List<Category> leafChildrenCategory = categoryModel.getLeafChildren(1L).stream()
        .filter(child -> !child.getReadOnly() && !child.isHidden())
        .filter(category -> {
          String normalizedName = Optional.ofNullable(category.getName()).map(String::trim).map(String::toLowerCase).orElse("");
          return normalizedName.equals(normalizedQuery);
        })
        .limit(top)
        .collect(Collectors.toList());
    Map<String, String> categoryCrumb = new HashMap<>();
    categoryCrumb.put(RECALL_TYPE, "category_name");
    return leafChildrenCategory.stream().map(category -> new SuggestedCategory(category.getId(), category.getName(), categoryModel.getBreadcrumb(category.getId(), ";"), categoryCrumb)).collect(Collectors.toList());
  }


  @Override
  public List<SuggestedCategory> getSuggestedTextMatchCategories(String query, boolean esMatch, int top) {
    String normalizedQuery = Optional.ofNullable(query).map(String::trim).map(String::toLowerCase).orElse("");
    //es agg
    Optional<Map<Long, Long>> advertCountMap = Optional.empty();
    if (esMatch) {
      advertCountMap = getSuggestedCategoriesMap(normalizedQuery);
    }
    //leaf category
    List<Category> leafChildrenCategory = categoryModel.getLeafChildren(1L).stream()
        .filter(child -> !child.getReadOnly() && !child.isHidden() && isAdPostingPermitted(child))
        .collect(Collectors.toList());

    //parent category and advert count
    List<CategorySuggesterScoreModel> suggesterTextModels = new ArrayList<>();
    for (Category category : leafChildrenCategory) {
      String tree = categoryModel.getBreadcrumb(category.getId(), ";");
      long advertCount = advertCountMap.isPresent() ? advertCountMap.get().getOrDefault(category.getId(), 0L) : 0L;
      suggesterTextModels.add(new CategorySuggesterScoreModel(category.getId(), category.getName(), tree, advertCount));

    }
    //numerical score  for each category
    List<CategorySuggesterScoreModel> sortedList = suggesterTextModels.stream().map(model -> {
          float parent_name_match_score = calculateTextMatchScore(normalizedQuery, model.getTree());
          float leaf_name_match_score = calculateTextMatchScore(normalizedQuery, model.getDisplayName());
          double ad_score = parent_name_match_score * 10 + leaf_name_match_score * 100 + model.getAdvertCount() * 0.0001;
          model.setScore(ad_score);
          return model;
        }).sorted(Comparator.comparing(CategorySuggesterScoreModel::getScore).reversed())
        .filter(model -> model.getScore() > 0)
        .collect(Collectors.toList());

    Map<String, String> categoryCrumb = new HashMap<>();
    categoryCrumb.put(RECALL_TYPE, "nlp_match");
    return sortedList.stream().map(model -> new SuggestedCategory(model.getId(), model.getDisplayName(), model.getTree(), categoryCrumb))
        .limit(top)
        .collect(Collectors.toList());
  }

  @Override
  public List<SuggestedCategory> getCategoryPredictApi(String query, int top) {
    PredictResult predictResult = categoryPredictApi.categoryPredict(new PredictRequest().scenario(SEARCH_SCENARIO).search(query))
        .onErrorReturn(error -> {
          LOGGER.warn("Error getting suggested categories", error);
          return new PredictResult().code(300);
        })
        .toBlocking().value();
    if (predictResult.getCode().equals(200)) {
      Map<String, String> categoryCrumb = new HashMap<>();
      categoryCrumb.put(RECALL_TYPE, "model");

      List<CategoryDTO> topCategories = predictResult.getCategories()
          .stream()
          .sorted(Comparator.comparing(CategoryDTO::getScore).reversed())
          .collect(Collectors.toList());

      List<CategoryDTO> filteredCategories = filterCategoriesDTO(topCategories);
      return filteredCategories.stream()
          .limit(top)
          .map(item -> new SuggestedCategory(
              item.getId(), item.getDisplayName(), item.getTree(), categoryCrumb))
          .collect(Collectors.toList());
    }
    return Collections.emptyList();
  }

  /**
   * calculate text match score between query and ad title
   *
   * @param query   keywords of user's query
   * @param adTitle title if ad
   * @return score within [0,1]
   */
  public Float calculateTextMatchScore(String query, String adTitle) {
    List<String> queryWords = NLPUtils.tokenizeText(query, true, false);
    List<String> adWords = NLPUtils.tokenizeText(adTitle, true, false);

    Set<String> adWordSet = new HashSet<>(adWords);
    long oneGramIntersection = queryWords.stream().filter(adWordSet::contains).count();
    float oneGramScore = queryWords.isEmpty() ? 0 : (float) oneGramIntersection / queryWords.size();

    List<String> queryBiGrams = NLPUtils.generateNGram(queryWords, 2);
    List<String> adBiGrams = NLPUtils.generateNGram(adWords, 2);
    Set<String> adBiGramSet = new HashSet<>(adBiGrams);
    long biGramIntersection = queryBiGrams.stream().filter(adBiGramSet::contains).count();
    float biGramScore = 0.0f;
    if (!NLPUtils.isEmpty(queryBiGrams)) {
      biGramScore = (float) biGramIntersection / queryBiGrams.size();
    } else if (0 < oneGramScore) {
      biGramScore = 1.0f;
    }

    BigDecimal factor = new BigDecimal("0.5");

    float finalResult = BigDecimal.valueOf(oneGramScore)
        .multiply(factor)
        .add(BigDecimal.valueOf(biGramScore).multiply(factor))
        .setScale(4, RoundingMode.HALF_UP)
        .floatValue();
    return finalResult;
  }

  static boolean isAdPostingPermitted(Category category) {
    return (category.getEnabled() == null || category.getEnabled())
        && (category.getReadOnly() == null || !category.getReadOnly())
        && !category.isHidden()
        && category.isAdPostingPermitted();
  }

  private List<CategoryDTO> filterCategoriesDTO(List<CategoryDTO> originalList) {
    List<CategoryDTO> result = new ArrayList<>();
    boolean foundFirstJob = false;
    for (CategoryDTO cat : originalList) {
      if (cat.getTree().startsWith("Jobs;")) {
        if (!foundFirstJob) {
          // Create a new object and change the id to -1
          CategoryDTO modifiedCat = new CategoryDTO();
          modifiedCat.setId(-1L);
          modifiedCat.setDisplayName(cat.getDisplayName());
          modifiedCat.setTree(cat.getTree());
          modifiedCat.setScore(cat.getScore());
          result.add(modifiedCat);
          foundFirstJob = true;
        }
        // Skip all other Jobs elements
      }
      // Direct addition of non-Jobs elements
      else {
        result.add(cat);
      }
    }
    return result;
  }
}
