package com.gumtree.web.seller.page.registration.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.springframework.web.servlet.ModelAndView;

public class RegistrationModel extends CommonModel {

    private RegistrationForm registrationForm;
    private String facebookAppId;
    private String googleAppId;

    public RegistrationModel() {
        // required for Spring form binding
    }

    private RegistrationModel(CoreModel core, Builder builder) {
        super(core);
        this.registrationForm = builder.registrationForm;
        this.facebookAppId = builder.facebookAppId;
        this.googleAppId = builder.googleAppId;
    }

    public RegistrationForm getForm() {
        return registrationForm;
    }

    public void setForm(RegistrationForm registrationForm) {
        this.registrationForm = registrationForm;
    }

    public String getFacebookAppId() {
        return facebookAppId;
    }

    public String getGoogleAppId() {
        return googleAppId;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private RegistrationForm registrationForm;
        private String facebookAppId;
        private String googleAppId;

        public Builder withRegistrationForm(RegistrationForm registrationForm) {
            this.registrationForm = registrationForm;
            return this;
        }

        public Builder withFacebookAppId(String facebookAppId) {
            this.facebookAppId = facebookAppId;
            return this;
        }

        public Builder withGoogleAppId(String googleAppId) {
            this.googleAppId = googleAppId;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page =  Page.Registration;
            coreModelBuilder.withTitle("Create an account | My Gumtree - Gumtree");

            return new ModelAndView(
                    page.getTemplateName(),
                    CommonModel.MODEL_KEY,
                    new RegistrationModel(coreModelBuilder.build(page), this));
        }
    }
}
