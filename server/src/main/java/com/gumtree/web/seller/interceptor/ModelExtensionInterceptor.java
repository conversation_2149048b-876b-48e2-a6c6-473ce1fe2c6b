package com.gumtree.web.seller.interceptor;

import com.google.common.collect.Maps;
import com.gumtree.api.User;
import com.gumtree.web.security.UserSession;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * interceptor that adds some cross concern information to the view model.<br/>
 * Right now, it will add the name of the view that is shown to the model, which is required by all new
 * responsive templates.
 */
public class ModelExtensionInterceptor extends HandlerInterceptorAdapter {

    private final UserSession authenticatedUserSession;

    public ModelExtensionInterceptor(UserSession authenticatedUserSession) {
        this.authenticatedUserSession = authenticatedUserSession;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {
        if (modelAndView != null) {
            // model and view is null if the controller returns an @ResponseBody itself
            if (authenticatedUserSession.isAuthenticated() && !authenticatedUserSession.getUserType().isNew()) {
                modelAndView.addObject("loggedInUser", getUserCoreData(authenticatedUserSession));
            }
            modelAndView.addObject("viewName", modelAndView.getViewName());
        }
    }

    /**
     * Exposes only the necessary details of the user to the FE model
     */
    private Map<String, String> getUserCoreData(UserSession userSession) {
        Map<String, String> map = Maps.newHashMap();

        User user = userSession.getUser();
        map.put("status", userSession.getUserType().toString());
        map.put("firstName", user.getFirstName());
        map.put("lastName", user.getLastName());
        map.put("isEbayMotorsUser", user.isEbayMotorsUser().toString());

        return map;
    }
}
