package com.gumtree.web.seller.security.apiauthentication;

import java.util.Map;
import java.util.Optional;

/**
 * Some Requests will utilise JWT Tokens which are time limited, provide data and feedback concerning request.
 *
 * @param <T>
 */
public class TimeSensitiveResponse<T> {

    private boolean valid;
    private boolean expired;
    private T data;
    private Map<String,Object> claims;

    public TimeSensitiveResponse() {
    }

    public boolean isValid() {
        return valid;
    }

    public boolean isExpired() {
        return expired;
    }

    public T getData() {
        return data;
    }

    public Optional<Map<String, Object>> getClaims() {
        return Optional.ofNullable(claims);
    }

    public static class Builder<T> {

        private final TimeSensitiveResponse<T> timeSensitiveResponse;

        public Builder() {
            this.timeSensitiveResponse = new TimeSensitiveResponse<>();
        }

        public Builder<T> withData(T data) {
            this.timeSensitiveResponse.data = data;
            return this;
        }

        public Builder<T> withExpired(boolean expired) {
            this.timeSensitiveResponse.expired = expired;
            return this;
        }

        public Builder<T> withValid(boolean valid) {
            this.timeSensitiveResponse.valid = valid;
            return this;
        }

        public Builder<T> withClaims(Map<String,Object> claims) {
            this.timeSensitiveResponse.claims = claims;
            return this;
        }

        public TimeSensitiveResponse<T> build() {
            return this.timeSensitiveResponse;
        }

    }

}
