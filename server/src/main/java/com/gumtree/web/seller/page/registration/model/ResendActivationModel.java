package com.gumtree.web.seller.page.registration.model;

import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public class ResendActivationModel{

    private Map<String, List<String>> errors = Maps.newHashMap();
    private String resendPath;
    private boolean withError;

    public ResendActivationModel() {
        // required for Spring form binding
    }

    private ResendActivationModel(Builder builder) {
        this.errors = builder.errors;
        this.resendPath = builder.resendPath;
        this.withError = builder.withError;
    }

    public Map<String, List<String>> getErrors() {
        return errors;
    }

    public String getResendPath() {
        return resendPath;
    }

    public boolean isWithError() {
        return withError;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private Map<String, List<String>> errors = Maps.newHashMap();
        private String resendPath;
        private boolean withError;

        public Builder resendPath(String resendPath) {
            this.resendPath = resendPath;
            return this;
        }

        public Builder withError(boolean withError) {
            this.withError = withError;
            return this;
        }

        public Builder errors(Map<String, List<String>> errors){
            this.errors = errors;
            return this;
        }

        public ResendActivationModel build() {
            return new ResendActivationModel(this);
        }
    }
}
