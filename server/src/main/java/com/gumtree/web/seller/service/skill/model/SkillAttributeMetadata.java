package com.gumtree.web.seller.service.skill.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class SkillAttributeMetadata {
    @JsonProperty("categories")
    private List<SkillAttributeCategory> categories;

    @Getter
    @Setter
    public static class SkillAttributeCategory {
        @JsonProperty("categoryId")
        private Integer categoryId;

        @JsonProperty("name")
        private String name;

        @JsonProperty("skills")
        private List<SkillAttribute> skills;
    }

    @Setter
    @Getter
    public static class SkillAttribute {
        @JsonProperty("skillId")
        private String skillId;

        @JsonProperty("name")
        private String name;

        @JsonProperty("categoryId")
        private String categoryId;

        @JsonProperty("suggested")
        private boolean suggested;

    }
} 
