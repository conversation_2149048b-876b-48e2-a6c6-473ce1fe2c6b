package com.gumtree.web.seller.service.pricing;

import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;

/**
 * Service for retrieving {@link PricingMetadata} in a specified {@link PricingContext}.
 */
public interface PricingService {

    /**
     * Retrieve {@link PricingMetadata} in a specified {@link PricingContext}.
     *
     * @param context pricing context
     * @return {@link PricingMetadata} in a specified {@link PricingContext}.
     */
    PricingMetadata getPriceInformation(PricingContext context);
}
