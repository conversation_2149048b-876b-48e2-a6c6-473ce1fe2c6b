package com.gumtree.web.seller.service.adstats;

import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;
import org.joda.time.DateTime;

public class AdvertStatisticData {

    //todo use typed fields

    private String advertId;
    private String advertTitle;
    private String advertVIPUrl;
    private ManageAdStatus advertStatus;
    private String contactEmail;
    private DateTime creationDate;
    private DateTime lastPostedDate;
    private DateTime lastModifiedDate;
    private Long numberOfTimesReposted;
    private Integer srpViews;
    private Integer vipViews;
    private Integer numberOfReplies;

    void setAdvertId(String advertId) {
        this.advertId = advertId;
    }

    void setAdvertTitle(String advertTitle) {
        this.advertTitle = advertTitle;
    }

    void setAdvertVIPUrl(String advertVIPUrl) {
        this.advertVIPUrl = advertVIPUrl;
    }

    void setAdvertStatus(ManageAdStatus advertStatus) {
        this.advertStatus = advertStatus;
    }

    void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getAdvertId() {
        return advertId;
    }

    public String getAdvertTitle() {
        return advertTitle;
    }

    public String getAdvertVIPUrl() {
        return advertVIPUrl;
    }

    public ManageAdStatus getAdvertStatus() {
        return advertStatus;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    void setCreationDate(DateTime creationDate) {
        this.creationDate = creationDate;
    }

    public DateTime getCreationDate() {
        return creationDate;
    }

    public DateTime getLastPostedDate() {
        return lastPostedDate;
    }

    void setLastPostedDate(DateTime lastPostedDate) {
        this.lastPostedDate = lastPostedDate;
    }

    public DateTime getLastModifiedDate() {
        return lastModifiedDate;
    }

    void setLastModifiedDate(DateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    void setNumberOfTimesReposted(Long numberOfTimesReposted) {
        this.numberOfTimesReposted = numberOfTimesReposted;
    }

    public Long getNumberOfTimesReposted() {
        return numberOfTimesReposted;
    }

    public Integer getSRPViews() {
        return srpViews;
    }

    void setSRPViews(Integer srpViews) {
        this.srpViews = srpViews;
    }

    public Integer getVIPViews() {
        return vipViews;
    }

    void setVIPViews(Integer vipViews) {
        this.vipViews = vipViews;
    }

    public Integer getNumberOfReplies() {
        return numberOfReplies;
    }

    void setNumberOfReplies(Integer numberOfReplies) {
        this.numberOfReplies = numberOfReplies;
    }

}
