package com.gumtree.web.seller.page.manageads.mydetails.api;

import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.domain.user.beans.UpdateUserBean;
import com.gumtree.api.client.executor.ApiCall;

/**
 * API call for updating a user.
 */
public final class UpdateUserApiCall implements ApiCall<User> {

    private UpdateUserBean updateUserBean;
    private Long id;

    /**
     * Constructor
     * @param id the user id
     * @param updateUserBean the bean containing the updates
     */
    public UpdateUserApiCall(Long id, UpdateUserBean updateUserBean) {
        this.updateUserBean = updateUserBean;
        this.id = id;
    }

    /**
     *
     * @param api the {@link BushfireApi} that this object uses to make the api call.
     * @return  -
     */
    @Override
    public User execute(BushfireApi api) {
        return api.create(UserApi.class).updateUser(id, updateUserBean);
    }
}
