package com.gumtree.web.seller.page.registration;

import com.google.common.collect.Lists;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.util.StringUtils;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.UserResponse;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.registration.model.ConfirmationModel;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.zeno.userregistration.UserActivationFailureZenoEvent;
import com.gumtree.web.zeno.userregistration.UserRegistrationSuccessZenoEvent;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationFail;
import com.gumtree.zeno.core.service.ZenoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

import static com.gumtree.web.seller.page.registration.ConfirmationPageController.PAGE_PATH;

@Controller
@RequestMapping(PAGE_PATH)
@GumtreePage(PageType.UserRegistrationSuccess)
@GoogleAnalytics
public final class ConfirmationPageController extends BaseSellerController {

    private final Logger logger = LoggerFactory.getLogger(ConfirmationPageController.class);

    public static final String PAGE_PATH = "/register-confirmation";

    private final ZenoService zenoService;
    private final ParameterEncryption parameterEncryption;
    private final UserServiceFacade userServiceFacade;

    @Autowired
    public ConfirmationPageController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                      ApiCallExecutor apiCallExecutor,
                                      ErrorMessageResolver messageResolver,
                                      UrlScheme urlScheme,
                                      ZenoService zenoService,
                                      UserSessionService userSessionService,
                                      ParameterEncryption parameterEncryption,
                                      UserServiceFacade userServiceFacade) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.zenoService = zenoService;
        this.parameterEncryption = parameterEncryption;
        this.userServiceFacade = userServiceFacade;
    }

    @RequestMapping(method = RequestMethod.GET)
    public ModelAndView renderPage(HttpServletResponse response,
                                   GumtreePageContext gumtreePageContext,
                                   HttpServletRequest request) throws IOException {

        CoreModel.Builder coreModel = getCoreModelBuilder(request);

        String encryptedParameterMap = request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME);

        if(StringUtils.hasText(encryptedParameterMap)) {
            Map<String, String> data = parameterEncryption.decryptUrlEncodedParameterMap(encryptedParameterMap);

            boolean registrationFlow = data.containsKey("registrationFlow");

            if(data.containsKey("userId") && data.containsKey("emailAddress")){
                String userId = data.get("userId");
                String emailAddress = data.get("emailAddress");
                if(Long.parseLong(userId) == -1){
                   ApiResponse<UserResponse> userResponse = userServiceFacade.getUserByEmail(emailAddress);
                   Long id = userResponse.isDefined() ? userResponse.get().getUserId() : -1L;
                   userId = String.valueOf(id);
                }
                populateGumtreeContext(gumtreePageContext, data);

                if (registrationFlow) {
                    sendZenoRegisteredEvent(gumtreePageContext);
                }

                //Note the trailing forward slash which is required for spring to recognize the .com on the GET
                String resendPath = ResendActivationEmailPageController.PAGE_PATH + "/" + userId + "/";

                ConfirmationModel.Builder confirmationModel = ConfirmationModel.builder()
                        .withEmailAddress(emailAddress)
                        .withResendPath(resendPath);
                return confirmationModel.build(coreModel);
            } else {
                return handleFailure(request, response, registrationFlow);
            }
        } else {
            logger.info("Registration confirmation failed");
            return redirect(RegistrationPageController.PAGE_PATH);
        }
    }

    private void sendZenoRegisteredEvent(GumtreePageContext gumtreePageContext) {
        User user = gumtreePageContext.getUser();
        zenoService.logEvent(new UserRegistrationSuccessZenoEvent(user.getId(), user.getEmail()));
    }

    private ModelAndView handleFailure(HttpServletRequest request,
                                       HttpServletResponse response,
                                       boolean registrationFlow) throws IOException {
        if (registrationFlow) {
            getCoreModelBuilder(request).withGaEvents(Lists.newArrayList(UserRegistrationFail.class.getSimpleName()));
            zenoService.logEvent(new UserActivationFailureZenoEvent());
        }
        logger.error("Registration confirmation failed.");
        response.sendError(500);
        return null;
    }

    /**
     * Populate the gumtreePageContext with anything that is required from 3rd party services
     *
     * @param gumtreePageContext the page context
     * @param data              the model
     */
    private void populateGumtreeContext(GumtreePageContext gumtreePageContext, Map<String,String> data) {
        String emailAddress = data.get("emailAddress");
        Long id = Long.valueOf(data.get("userId"));
        User user = new User();
        user.setEmail(emailAddress);
        user.setId(id);
        gumtreePageContext.setUser(user);
    }


}
