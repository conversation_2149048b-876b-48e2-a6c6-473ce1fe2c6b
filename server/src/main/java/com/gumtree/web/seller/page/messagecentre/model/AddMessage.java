package com.gumtree.web.seller.page.messagecentre.model;


import org.codehaus.jackson.annotate.JsonProperty;

public class AddMessage {
    @JsonProperty("sender_email")
    private String senderEmail;
    private String message;
    @JsonProperty("ad_id")
    private Long adId;
    @JsonProperty("receiver_id")
    private Long receiverId;
    @JsonProperty("sender_id")
    private Long senderId;

    public String getSenderEmail() {
        return senderEmail;
    }

    public void setSenderEmail(String senderEmail) {
        this.senderEmail = senderEmail;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getAdId() {
        return adId;
    }

    public void setAdId(Long adId) {
        this.adId = adId;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public static Builder builder() {
        return Builder.getBuilder();
    }

    public static final class Builder {
        private String senderEmail;
        private String message;
        private Long adId;
        private Long receiverId;
        private Long senderId;

        private Builder() {
        }

        public static Builder getBuilder() {
            return new Builder();
        }

        public Builder withSenderEmail(String senderEmail) {
            this.senderEmail = senderEmail;
            return this;
        }

        public Builder withMessage(String message) {
            this.message = message;
            return this;
        }

        public Builder withAdId(Long adId) {
            this.adId = adId;
            return this;
        }

        public Builder withReceiverId(Long receiverId) {
            this.receiverId = receiverId;
            return this;
        }

        public Builder withSenderId(Long senderId) {
            this.senderId = senderId;
            return this;
        }

        public AddMessage build() {
            AddMessage addMessage = new AddMessage();
            addMessage.setAdId(adId);
            addMessage.setMessage(message);
            addMessage.setSenderEmail(senderEmail);
            addMessage.setReceiverId(receiverId);
            addMessage.setSenderId(senderId);
            return addMessage;
        }
    }
}
