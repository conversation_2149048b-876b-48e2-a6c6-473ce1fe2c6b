package com.gumtree.web.seller.resolver;

import com.gumtree.api.Account;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.web.security.UserSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

public class AccountArgumentResolver implements HandlerMethodArgumentResolver {
    @Autowired
    private AccountApi accountApi;

    @Autowired
    private UserSession userSession;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterType().equals(Account.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter,
                                  ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest,
                                  WebDataBinderFactory binderFactory) throws Exception {
        return accountApi.getAccount(userSession.getSelectedAccountId());
    }
}
