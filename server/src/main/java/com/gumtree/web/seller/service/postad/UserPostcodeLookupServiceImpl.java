package com.gumtree.web.seller.service.postad;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.liveadsearch.model.SearchableField;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.fulladsearch.model.FullAdEqualInstruction;
import com.gumtree.fulladsearch.model.FullAdFieldSortOption;
import com.gumtree.fulladsearch.model.FullAdFlatAd;
import com.gumtree.fulladsearch.model.FullAdSearchInstruction;
import com.gumtree.fulladsearch.model.FullAdSearchRequest;
import com.gumtree.fulladsearch.model.FullAdSearchResult;
import com.gumtree.fulladsearch.model.FullAdSearchableField;
import com.gumtree.fulladsearch.model.FullAdSortOption;
import com.gumtree.fulladsearch.model.FullAdSortOrder;

import java.util.List;

public class UserPostcodeLookupServiceImpl implements UserPostcodeLookupService {
    private final FullAdsSearchApi fullAdsSearchApi;

    public UserPostcodeLookupServiceImpl(FullAdsSearchApi fullAdsSearchApi) {
        this.fullAdsSearchApi = fullAdsSearchApi;
    }

    @Override
    public Optional<String> getLastUsedPostcode(Long accountId) {
        FullAdSearchRequest searchRequest = buildSearchRequest(accountId);
        FullAdSearchResult searchResult = fullAdsSearchApi.search(searchRequest).toBlocking().value();
        if (searchResult != null && searchResult.getItems() != null && !searchResult.getItems().isEmpty()) {
            FullAdFlatAd flatAd = searchResult.getItems().get(0);
            return Optional.fromNullable(flatAd.getPostcode());
        }
        return Optional.absent();
    }

    private static FullAdSearchRequest buildSearchRequest(Long accountId) {
        List<FullAdSearchInstruction> searchInstructions = Lists.newArrayList(new FullAdSearchInstruction()
                .eq(new FullAdEqualInstruction().field(SearchableField.ACCOUNT_ID.getValue()).value(String.valueOf(accountId))));

        List<FullAdSortOption> sortOptions = Lists.newArrayList(
                new FullAdSortOption()
                        .field(new FullAdFieldSortOption().field(FullAdSearchableField.CREATED_DATE.getValue())
                                .order(FullAdSortOrder.DESC)));

        FullAdSearchRequest searchRequest = new FullAdSearchRequest().instructions(searchInstructions);
        searchRequest.offset(0);
        searchRequest.limit(1);
        searchRequest.setSort(sortOptions);

        return searchRequest;
    }


}
