package com.gumtree.web.seller.converter;

import com.gumtree.api.Ad;
import com.gumtree.web.seller.model.AdPreview;

/**
 * Convert from an API {@link Ad} to an {@link AdPreview}.
 */
public interface AdToAdPreviewConverter {
    String TRAVEL_DATE_ATTR = "travel_date";
    String WANTED_DATE_ATTR = "wanted_date";
    String AVAILABLE_DATE_ATTR = "available_date";
    String EVENT_DATE_ATTR = "event_date";
    String TICKET_DATE_ATTR = "ticket_date";

    /**
     * Convert from an API {@link Ad} to an {@link AdPreview}.
     *
     * @param advert the advert to convert
     * @return the ad preview model
     */
    AdPreview convert(Ad advert);
}
