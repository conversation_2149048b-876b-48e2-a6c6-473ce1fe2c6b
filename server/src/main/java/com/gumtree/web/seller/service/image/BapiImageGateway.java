package com.gumtree.web.seller.service.image;

import com.gumtree.bapi.ImageApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class BapiImageGateway {

    private final ImageApi imageApi;

    @Autowired
    public BapiImageGateway(@Qualifier(value = "bapiContractImageApi") ImageApi imageApi) {
        this.imageApi = imageApi;
    }

    public com.gumtree.api.Image get(Long id) {
        com.gumtree.bapi.model.Image image = imageApi.getImage(id).toBlocking().value();
        com.gumtree.api.Image legacyImage = new com.gumtree.api.Image();
        legacyImage.setId(image.getId());
        legacyImage.setUrl(image.getUrl());
        return legacyImage;
    }

}
