package com.gumtree.web.seller.security;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.Assert;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.Key;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class ParameterEncryption {

    public static final String AES = "AES";
    public static final String ISO_8859_1 = "ISO-8859-1";

    private final Cipher encryptCipher;
    private final Cipher decryptCipher;

    private final ObjectMapper objectMapper;

    public ParameterEncryption(String encryptionKey) throws Exception {
        this.encryptCipher = encryptionCipher(encryptionKey);
        this.decryptCipher = decryptionCipher(encryptionKey);
        this.objectMapper = new ObjectMapper();
    }

    public String encrypt(String token) throws Exception {
        Assert.notNull(token);
        byte[] encryptedBytes = encryptCipher.doFinal(token.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    public String decrypt(String encrypted) throws Exception {
        byte[] decodedBytes = Base64.getDecoder().decode(encrypted);
        byte[] decryptedBytes = decryptCipher.doFinal(decodedBytes);
        return new String(decryptedBytes);
    }

    /**
     * Encrypt map of data, add timestamp for variance.
     *
     * @param data
     * @return encrypted data as a String
     * @throws Exception if there was error converting to json or encrypting data
     */
    public String encryptMapAndUrlEncode(Map<String,String> data) throws ParameterProcessingException {

        data.put("timestamp", Instant.now().toString());

        String value;
        try {
            value = objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            throw new ParameterProcessingException(e);
        }

        return encryptAndUrlEncode(value);
    }

    /**
     * Counterpart of encryptMapAndUrlEncode method.
     *
     * @param encrypted encrypted data in json format. Data is URL encoded.
     * @return Map<String,String>
     * @throws Exception
     */
    public Map<String,String> decryptUrlEncodedParameterMap(String encrypted) throws ParameterProcessingException {
        try {
            String decrypted = decryptAndUrlDecode(encrypted);
            TypeReference<HashMap<String, String>> typeRef = new TypeReference<HashMap<String, String>>() {};
            // TODO: check time parameter added in the encrypt method
            return objectMapper.readValue(decrypted, typeRef);
        } catch (Exception e) {
            throw new ParameterProcessingException(e);
        }
    }

    public String encryptAndUrlEncode(String token) throws RuntimeException {
        try {
            String base64 = encrypt(token);
            return URLEncoder.encode(base64, ISO_8859_1);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String decryptAndUrlDecode(String encrypted) throws RuntimeException {
        try {
            String base64 = URLDecoder.decode(encrypted, ISO_8859_1);
            return decrypt(base64);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Cipher encryptionCipher(String encryptionKey) throws Exception {
        Cipher encryptionCipher = Cipher.getInstance(AES);
        Key cipherKey = new SecretKeySpec(keyAsBytes(encryptionKey), AES);
        encryptionCipher.init(Cipher.ENCRYPT_MODE, cipherKey);
        return encryptionCipher;
    }

    private Cipher decryptionCipher(String encryptionKey) throws Exception {
        Key cipherKey = new SecretKeySpec(keyAsBytes(encryptionKey), AES);
        Cipher decryptionCipher = Cipher.getInstance(AES);
        decryptionCipher.init(Cipher.DECRYPT_MODE, cipherKey);
        return decryptionCipher;
    }

    private byte[] keyAsBytes(String encryptionKey) {
        return Base64.getDecoder().decode(encryptionKey);
    }
}
