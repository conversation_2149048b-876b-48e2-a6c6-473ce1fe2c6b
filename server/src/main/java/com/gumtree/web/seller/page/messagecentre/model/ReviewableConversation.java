package com.gumtree.web.seller.page.messagecentre.model;

import com.gumtree.web.common.domain.messagecentre.Advert;
import com.gumtree.web.common.domain.messagecentre.Message;
import com.gumtree.web.common.domain.messagecentre.Messages;
import com.gumtree.web.common.domain.messagecentre.Role;
import com.gumtree.web.seller.page.reviews.model.UserRating;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class ReviewableConversation {
    private Messages messages;
    private boolean reviewAllowed;
    private Optional<UserRating> converseeRating;
    private String converseePublicAccountId;

    public Integer getNumUnreadConversations() {
        return messages.getNumUnreadConversations();
    }

    public String getConverseeName() {
        return messages.getConverseeName();
    }

    public String getConversationId() {
        return messages.getConversationId();
    }

    public List<Message> getMessages() {
        return messages.getMessages();
    }

    public Role getUserRole() {
        return messages.getUserRole();
    }

    public Advert getAdvert() {
        return messages.getAdvert().orElse(null);
    }

    public boolean isReviewAllowed() {
        return reviewAllowed;
    }

    public UserRating getConverseeRating() {
        return converseeRating.orElse(new UserRating());
    }

    public String getConverseePublicAccountId() {
        return converseePublicAccountId;
    }

    void setConverseeRating(Optional<UserRating> converseeRating) {
        this.converseeRating =  converseeRating != null ? converseeRating : Optional.empty();
    }

    @Override
    public String toString() {
        return "ReviewableConversation{" +
                "messages=" + messages +
                ", reviewAllowed=" + reviewAllowed +
                ", converseeRating=" + converseeRating +
                ", converseePublicAccountId='" + converseePublicAccountId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReviewableConversation that = (ReviewableConversation) o;
        return reviewAllowed == that.reviewAllowed &&
                Objects.equals(messages, that.messages) &&
                Objects.equals(converseeRating, that.converseeRating) &&
                Objects.equals(converseePublicAccountId, that.converseePublicAccountId);
    }

    @Override
    public int hashCode() {

        return Objects.hash(messages, reviewAllowed, converseeRating, converseePublicAccountId);
    }


    public static Builder builder() {
        return Builder.aReviewableConversation();
    }

    public static final class Builder {
        private Messages messages;
        private boolean reviewAllowed;
        private Optional<UserRating> converseeRating;
        private String converseePublicAccountId;

        private Builder() {
        }

        public static Builder aReviewableConversation() {
            return new Builder();
        }

        public Builder withMessages(Messages messages) {
            this.messages = messages;
            return this;
        }

        public Builder withReviewAllowed(boolean reviewAllowed) {
            this.reviewAllowed = reviewAllowed;
            return this;
        }

        public Builder withConverseeRating(Optional<UserRating> converseeRating) {
            this.converseeRating = converseeRating;
            return this;
        }

        public Builder withConverseePublicAccountId(String converseePublicAccountId) {
            this.converseePublicAccountId = converseePublicAccountId;
            return this;
        }

        public ReviewableConversation build() {
            ReviewableConversation reviewableConversation = new ReviewableConversation();
            reviewableConversation.setConverseeRating(converseeRating);
            reviewableConversation.converseePublicAccountId = this.converseePublicAccountId;
            reviewableConversation.messages = this.messages;
            reviewableConversation.reviewAllowed = this.reviewAllowed;
            return reviewableConversation;
        }
    }
}
