package com.gumtree.web.seller.service.threatmetrix;

import com.gumtree.common.properties.GtProps;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.CookieUtils;
import com.gumtree.web.cookie.GumtreeCookieProperty;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookieCutter;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrixTracking;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;


@Service
public class ThreatMetrixService {

    private static final Logger log = LoggerFactory.getLogger(ThreatMetrixService.class);

    @Autowired
    private CookieResolver cookieResolver;

    @Autowired
    private ThreatMetrixCookieCutter threatMetrixCookieCutter;

    @Getter
    @Value("${gumtree.threatmetrix.orgId:njrya493}")
    private String organisationId;

    @Getter
    @Value("${gumtree.threatmetrix.webBaseUrl}")
    private String webBaseUrl;

    @Getter
    @Value("${gumtree.threatmetrix.enabled:false}")
    private boolean enabled;


    public ThreatMetrixInfo processThreatMetrixForApiResponse(HttpServletRequest request, HttpServletResponse response) {
        if (!enabled) {
            return ThreatMetrixInfo.disabled();
        }

        ThreatMetrixCookie tmCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);
        

        ThreatMetrixTracking tracking = ThreatMetrixTracking.builder()
                .orgId(organisationId)
                .sessionId(tmCookie.getDefaultValue())
                .webBaseUrl(webBaseUrl)
                .build();


        String environmentAwareCookieName = threatMetrixCookieCutter.getName(GtProps.getEnv());
        Cookie cookie = CookieUtils.createHttpCookie(environmentAwareCookieName, tmCookie);
        response.addCookie(cookie);

        return new ThreatMetrixInfo(tmCookie, tracking, true);
    }

    public ThreatMetrixCookie getExistingThreatMetrixCookie(HttpServletRequest request) {
        try {
            return cookieResolver.resolve(request, ThreatMetrixCookie.class);
        } catch (Exception e) {
            return null;
        }
    }


    public void invalidateThreatMetrixCookie(HttpServletRequest request, HttpServletResponse response) {
        try {
            if (!enabled) {
                return;
            }

            String environmentAwareCookieName = threatMetrixCookieCutter.getName(GtProps.getEnv());
            Optional<Cookie> existingCookie =
                    CookieUtils.findCookie(request.getCookies(), environmentAwareCookieName);

            if (existingCookie.isPresent()) {
                Cookie originalCookie = existingCookie.get();
                originalCookie.setMaxAge(0);
                originalCookie.setValue("");
                originalCookie.setDomain(GtProps.getStr(GumtreeCookieProperty.COOKIES_DOMAIN));
                originalCookie.setSecure(GtProps.getBool(GumtreeCookieProperty.COOKIES_SECURE));
                originalCookie.setPath(ThreatMetrixCookieCutter.PATH);
                response.addCookie(originalCookie);
            }
        } catch (Exception e) {
            log.error("Error invalidating ThreatMetrix Cookie", e);
        }
    }
}
