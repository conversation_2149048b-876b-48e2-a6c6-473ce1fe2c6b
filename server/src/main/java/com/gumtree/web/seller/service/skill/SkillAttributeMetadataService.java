package com.gumtree.web.seller.service.skill;

import com.gumtree.web.seller.service.skill.model.SkillAttributeMetadata;
import java.util.List;

public interface SkillAttributeMetadataService {
    List<SkillAttributeMetadata.SkillAttributeCategory> getAllSkillCategories();
    
    List<SkillAttributeMetadata.SkillAttribute> getSkillsByCategoryId(Integer categoryId);
    
    void setSkillCategories(List<SkillAttributeMetadata.SkillAttributeCategory> skillCategories);
    
    List<SkillAttributeMetadata.SkillAttributeCategory> getAllCategories();
    
    List<SkillAttributeMetadata.SkillAttribute> getSkillsByCategoryName(String categoryName);
    
    SkillAttributeMetadata.SkillAttribute getSkillById(String skillId);

    List<Integer> getSuggestedSkillIdsByCategoryId(Integer categoryId);
} 
