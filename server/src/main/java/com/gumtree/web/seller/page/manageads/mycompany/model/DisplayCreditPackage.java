package com.gumtree.web.seller.page.manageads.mycompany.model;

import com.gumtree.api.CreditPackage;
import com.gumtree.util.model.Link;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.UrlScheme;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

/**
 * Display model for a credit package
 */
public final class DisplayCreditPackage {

    private String type;

    private String startDate;

    private String endDate;

    private String initialCredits;

    private String bonusCredits;

    private String usedCredits;

    private String remainingCredits;

    private String totalCredits;

    private boolean negativeRemainingCredits;

    private Link usageLink;

    public String getType() {
        return type;
    }

    public String getStartDate() {
        return startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public String getInitialCredits() {
        return initialCredits;
    }

    public String getBonusCredits() {
        return bonusCredits;
    }

    public String getUsedCredits() {
        return usedCredits;
    }

    public String getRemainingCredits() {
        return remainingCredits;
    }

    public Link getUsageLink() {
        return usageLink;
    }

    public String getTotalCredits() {
        return totalCredits;
    }

    public boolean isNegativeRemainingCredits() {
        return negativeRemainingCredits;
    }

    /**
     * Builder for display credit packages
     */
    public static class DisplayCreditPackageBuilder {

        private static final DateTimeFormatter DATE_FORMAT = DateTimeFormat.forPattern("dd/MM/yyyy");

        private String type;

        private Long packageTypeId;

        private String startDate;

        private String endDate;

        private int initialCredits;

        private int bonusCredits;

        private int usedCredits;

        private int remainingCredits;

        private boolean negativeRemainingCredits = false;

        private int totalCredits;

        private Link usageLink;

        private UrlScheme urlScheme;

        private boolean unlimited;

        /**
         * Constructor.
         *
         * @param urlScheme url scheme for generating urls
         */
        public DisplayCreditPackageBuilder(UrlScheme urlScheme) {
            this.urlScheme = urlScheme;
        }

        /**
         * Populate using API credit package model.
         *
         * @param creditPackage the source model containing data to modify for display
         * @return builder
         */
        public final DisplayCreditPackageBuilder creditPackage(CreditPackage creditPackage) {
            type = creditPackage.getName();
            packageTypeId = creditPackage.getPackageTypeId();
            initialCredits = creditPackage.getInitialCredits().intValue();
            bonusCredits = creditPackage.getAdjustedCredits().intValue();
            usedCredits = creditPackage.getUsedCredits().intValue();
            totalCredits = initialCredits + bonusCredits;
            remainingCredits = totalCredits - usedCredits;
            startDate = DATE_FORMAT.print(creditPackage.getStartDate());
            endDate = DATE_FORMAT.print(creditPackage.getEndDate().minusDays(1));
            usageLink = new SimpleLink("View ads", urlScheme.urlForPackageUsageHistory(creditPackage.getId()));
            negativeRemainingCredits = remainingCredits < 0;
            unlimited = creditPackage.isUnlimited() != null ? creditPackage.isUnlimited() : false;
            return this;
        }

        /**
         * @return a display credit package
         */
        public final DisplayCreditPackage build() {
            DisplayCreditPackage displayCreditPackage = new DisplayCreditPackage();
            displayCreditPackage.type = type;
            displayCreditPackage.startDate = startDate;
            displayCreditPackage.endDate = endDate;
            displayCreditPackage.initialCredits = String.valueOf(initialCredits);
            displayCreditPackage.bonusCredits = String.valueOf(bonusCredits);
            displayCreditPackage.usedCredits = String.valueOf(usedCredits);
            displayCreditPackage.negativeRemainingCredits = negativeRemainingCredits;

            if (unlimited) {
                displayCreditPackage.totalCredits = "Unlimited";
                displayCreditPackage.remainingCredits = "Unlimited";
                displayCreditPackage.negativeRemainingCredits = false;
            } else {
                displayCreditPackage.totalCredits = String.valueOf(totalCredits);
                displayCreditPackage.remainingCredits = String.valueOf(remainingCredits);
            }

            displayCreditPackage.usageLink = usageLink;

            return displayCreditPackage;
        }
    }
}
