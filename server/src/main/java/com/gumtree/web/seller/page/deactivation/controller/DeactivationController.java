package com.gumtree.web.seller.page.deactivation.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.deactivation.model.DeactivateModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@Controller
public final class DeactivationController extends BaseSellerController {

    private final UserServiceFacade userServiceFacade;
    private final UserSession userSession;

    @Autowired
    public DeactivationController(UserServiceFacade userServiceFacade,
                                  UserSession userSession,
                                  CookieResolver cookieResolver,
                                  CategoryModel categoryModel,
                                  ApiCallExecutor apiCallExecutor,
                                  ErrorMessageResolver messageResolver,
                                  UrlScheme urlScheme,
                                  UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.userServiceFacade = userServiceFacade;
        this.userSession = userSession;
    }

    @RequestMapping(value = "/deactivate/{token}", method = RequestMethod.GET)
    public ModelAndView viewAccountDeactivationPage(HttpServletRequest request, HttpServletResponse response) {
        CoreModel.Builder coreModelBuilder = getCoreModelBuilder(request);
        return DeactivateModel.builder().build(coreModelBuilder);
    }

    @RequestMapping(value = "/deactivate/{token}", method = RequestMethod.POST)
    public ModelAndView finalizeAccountDeactivation(@PathVariable("token") String token, HttpServletRequest request) {

        ApiResponse<Boolean> response = userServiceFacade.finalizeAccountDeactivation(token);
        if (!response.isDefined()) {
            return redirect(getUrlScheme().urlFor(Actions.PAGE_EXPIRED));
        } else {
            if (userSession.isLoggedIn()){
                userSession.logout();
            }
            return redirect(getUrlScheme().urlFor(Actions.USER_HOME));
        }
    }
}
