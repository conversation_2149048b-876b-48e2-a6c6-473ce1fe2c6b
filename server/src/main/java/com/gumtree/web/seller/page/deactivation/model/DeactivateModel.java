package com.gumtree.web.seller.page.deactivation.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.springframework.web.servlet.ModelAndView;

public final class DeactivateModel extends CommonModel {

    private DeactivateModel(CoreModel core, Builder builder) {
        super(core);
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.Deactivate;
            coreModelBuilder.withTitle("Deactivate Account");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new DeactivateModel(coreModelBuilder.build(page), this));
        }

    }
}
