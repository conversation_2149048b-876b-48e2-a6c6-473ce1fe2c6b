package com.gumtree.web.seller.service.skill;

import com.gumtree.bapi.SkillControllerApi;
import com.gumtree.bapi.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

@Component
public class BapiSkillGateway {

    private static final String ERROR_ACCOUNT_ID_NULL = "Account ID cannot be null";

    private final SkillControllerApi skillControllerApi;
    private final SkillAttributeMetadataService skillAttributeMetadataService;


    @Autowired
    public BapiSkillGateway(@Qualifier(value = "bapiContractSkillControllerApi") SkillControllerApi skillControllerApi,
                            SkillAttributeMetadataService skillAttributeMetadataService) {
        this.skillControllerApi = skillControllerApi;
        this.skillAttributeMetadataService = skillAttributeMetadataService;

    }

    public SkillResponse getSelectedSkills(Long accountId, Integer categoryId) {
        Assert.notNull(accountId, ERROR_ACCOUNT_ID_NULL);
        Assert.notNull(categoryId, "Category ID cannot be null");

        SkillResponse response = skillControllerApi.getSelectedSkills(accountId, categoryId).toBlocking().value();
        if (CollectionUtils.isEmpty(response.getSkills())) {
            // suggested skills
            response.setSkills(skillAttributeMetadataService.getSuggestedSkillIdsByCategoryId(categoryId));
        }
        return response;
    }

    public Boolean createSkill(Long accountId, SkillRequest skillRequest) {
        Assert.notNull(accountId, ERROR_ACCOUNT_ID_NULL);
        validateSkillRequest(skillRequest);
        
        SkillCreateResponse response = skillControllerApi.createSkill(accountId, skillRequest).toBlocking().value();
        return response.getSuccess();

    }

    public SkillUpdateResponse updateSkill(Long accountId, SkillRequest skillRequest) {
        Assert.notNull(accountId, ERROR_ACCOUNT_ID_NULL);
        validateSkillRequest(skillRequest);

        return skillControllerApi.updateSkill(accountId, skillRequest).toBlocking().value();
    }
    
    private void validateSkillRequest(SkillRequest skillRequest) {
        Assert.notNull(skillRequest, "Skill request cannot be null");
        Assert.notNull(skillRequest.getCategoryId(), "Category ID in skill request cannot be null");
        Assert.isTrue(!CollectionUtils.isEmpty(skillRequest.getSelectedSkills()), 
                "Selected skills in skill request cannot be empty");
    }
} 
