package com.gumtree.web.seller.reporting;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsConfigurer;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsCustomVar;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.impl.LoginAttempt;
import com.gumtree.web.reporting.google.ga.events.impl.PostAdBegin;
import com.gumtree.web.reporting.google.ga.events.impl.UserRegistrationBegin;
import com.gumtree.web.seller.page.ResponsiveGroupRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class ResponsiveGoogleAnalyticsConfigurer implements GoogleAnalyticsConfigurer<GoogleAnalytics> {

    private ResponsiveGroupRequest responsiveGroupRequest;

    public static final String GROUP_NAME = "responsive-group";

    public static final int CUSTOM_VAR = 45;

    @Autowired
    public ResponsiveGoogleAnalyticsConfigurer(ResponsiveGroupRequest responsiveGroupRequest) {
        this.responsiveGroupRequest = responsiveGroupRequest;
    }

    @Override
    public void configure(GoogleAnalyticsReportBuilder reportBuilder, ThirdPartyRequestContext<GoogleAnalytics> ctx) {
        reportBuilder
                .pageType(ctx.getPageType())
                .addTrackEvent(new PostAdBegin(ctx))
                .addTrackEvent(new LoginAttempt(ctx))
                .addTrackEvent(new UserRegistrationBegin(ctx));

        if (StringUtils.hasText(responsiveGroupRequest.getGroup())) {
            reportBuilder.addCustomVar(new GoogleAnalyticsCustomVar(CUSTOM_VAR, GROUP_NAME,
                    responsiveGroupRequest.getGroup(), 3));
        }
    }
}
