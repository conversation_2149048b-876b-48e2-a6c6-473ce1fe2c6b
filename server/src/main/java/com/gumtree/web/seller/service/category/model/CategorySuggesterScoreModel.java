package com.gumtree.web.seller.service.category.model;

import com.gumtree.web.seller.page.ajax.category.SuggestedCategory;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class CategorySuggesterScoreModel extends SuggestedCategory {
    private double score;
    private long advertCount;
    private List<String> displayNameTokens;
    private List<String> treeTokens;

    public CategorySuggesterScoreModel(long id, String displayName, String tree) {
        super(id, displayName, tree);
    }

    public CategorySuggesterScoreModel(long id, String displayName, String tree, Map<String, String> categoryCrumb) {
        super(id, displayName, tree, categoryCrumb);
    }

    public CategorySuggesterScoreModel(long id, String displayName, String tree, long advertCount) {
        super(id, displayName, tree);
        this.advertCount = advertCount;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public long getAdvertCount() {
        return advertCount;
    }

    public void setAdvertCount(long advertCount) {
        this.advertCount = advertCount;
    }
}
