package com.gumtree.web.seller.service.pricing;

import com.google.common.base.Optional;
import com.google.common.collect.Maps;
import com.gumtree.api.SellerType;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.joda.time.DateTime;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public final class PricingContextImpl implements PricingContext {

    private final AdvertEditor advertEditor;
    private final CategoryModel categoryModel;
    private final Optional<SellerType> sellerType;

    public PricingContextImpl(AdvertEditor advertEditor, CategoryModel categoryModel) {
        this.advertEditor = advertEditor;
        this.categoryModel = categoryModel;
        this.sellerType = Optional.absent();
    }

    public PricingContextImpl(AdvertEditor advertEditor, CategoryModel categoryModel, SellerType sellerType) {
        this.advertEditor = advertEditor;
        this.categoryModel = categoryModel;
        this.sellerType = Optional.fromNullable(sellerType);
    }

    @Override
    public Long getCategoryId() {
        return advertEditor.getCategoryId();
    }

    @Override
    public Long getLocationId() {
        return advertEditor.getLocationId();
    }

    @Override
    public Long getAccountId() {
        return advertEditor.getAccountId();
    }

    @Override
    public Map<String, String> getPriceSensitiveAttributes() {
        final Map<String, String> attributes = advertEditor.getPostAdFormBean().getAttributes();
        final Optional<List<AttributeMetadata>> categoryAttributes = categoryModel.getCategoryAttributes(getCategoryId());
        if (categoryAttributes.isPresent()) {
            Map<String, String> result = Maps.newHashMapWithExpectedSize(1);
            for (AttributeMetadata attr : categoryAttributes.get()) {
                if (attributes.containsKey(attr.getName()) && attr.isPriceSensitive()) {
                    result.put(attr.getName(), attributes.get(attr.getName()));
                }
            }
            return result;
        } else {
            return Collections.emptyMap();
        }
    }

    @Override
    public boolean isProductActive(ProductType type) {
        return advertEditor.getAdvertDetail().getExistingFeatures().containsKey(type);
    }

    @Override
    public DateTime getProductExpiryDate(ProductType type) {
        return advertEditor.getAdvertDetail().getExistingFeatures().get(type);
    }

    @Override
    public AdvertEditor getAdvertEditor(){
        return advertEditor;
    }

    @Override
    public Optional<SellerType> getSellerType() {
        return sellerType;
    }
}
