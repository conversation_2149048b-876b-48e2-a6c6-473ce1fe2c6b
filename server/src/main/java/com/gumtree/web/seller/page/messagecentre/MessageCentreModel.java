package com.gumtree.web.seller.page.messagecentre;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.gumtree.web.common.domain.messagecentre.Conversations;
import com.gumtree.web.common.domain.messagecentre.Message;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.messagecentre.model.ReviewTriggerState;
import com.gumtree.web.seller.page.messagecentre.model.ReviewableConversation;
import com.gumtree.web.seller.page.messagecentre.util.XssSafeMessageSerializer;
import com.gumtree.web.seller.page.reviews.model.UserRating;
import org.springframework.web.servlet.ModelAndView;

import java.util.Optional;

public class MessageCentreModel extends CommonModel {

    private final String contactEmail;
    private final String conversationsAsJsonString;
    private final String messagesAsJsonString;
    private final boolean messageCentreErrors;
    private final boolean showAppBanner;
    private final Optional<UserRating> converseeRating;
    private final Long lastConversationAdId;
    private final ReviewTriggerState reviewTriggerState;

    private MessageCentreModel(CoreModel core, Builder builder) {
        super(core);
        this.conversationsAsJsonString = builder.conversationsAsJsonString;
        this.messagesAsJsonString = builder.messagesAsJsonString;
        this.contactEmail = builder.contactEmail;
        this.messageCentreErrors = builder.messageCentreErrors;
        this.showAppBanner = builder.showAppBanner;
        this.converseeRating = builder.converseeRating;
        this.lastConversationAdId = builder.lastConversationAdId;
        this.reviewTriggerState = builder.reviewTriggerState;
    }

    public MessageCentreModel() {
        super(null);
        this.contactEmail = null;
        this.conversationsAsJsonString = null;
        this.messagesAsJsonString = null;
        this.messageCentreErrors = false;
        this.showAppBanner = false;
        this.converseeRating = Optional.empty();
        this.lastConversationAdId = null;
        this.reviewTriggerState = null;
    }

    public String getConversationsAsJsonString() {
        return conversationsAsJsonString;
    }

    public String getMessagesAsJsonString() {
        return messagesAsJsonString;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public boolean isMessageCentreErrors() {
        return messageCentreErrors;
    }

    public boolean isShowAppBanner() {
        return showAppBanner;
    }

    public Optional<UserRating> getConverseeRating() {
        return converseeRating;
    }

    public Optional<Long> getLastConversationAdId() {
        return Optional.ofNullable(lastConversationAdId);
    }

    public ReviewTriggerState getReviewTriggerState() {
        return reviewTriggerState;
    }

    public static final class Builder {
        public static final ObjectMapper OBJECT_MAPPER = createObjectMapper();

        private String conversationsAsJsonString;
        private String messagesAsJsonString;
        private String contactEmail;
        private boolean messageCentreErrors;
        private boolean showAppBanner;
        private Optional<UserRating> converseeRating = Optional.empty();
        private Long lastConversationAdId;
        private ReviewTriggerState reviewTriggerState;


        public Builder withConversations(Conversations conversations) throws JsonProcessingException {
            this.conversationsAsJsonString = OBJECT_MAPPER.writeValueAsString(conversations);
            return this;
        }

        public Builder withMessages(ReviewableConversation messages) throws JsonProcessingException {
            if (messages != null) {
                this.converseeRating = Optional.ofNullable(messages.getConverseeRating());
                this.messagesAsJsonString = OBJECT_MAPPER.writeValueAsString(messages);
            }
            return this;
        }

        public Builder withContactEmail(String contactEmail){
            this.contactEmail = contactEmail;
            return this;
        }

        public Builder withMessageCentreErrors(boolean hasErrors) {
            this.messageCentreErrors = hasErrors;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder, Page page) {
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY, build(coreModelBuilder.build(page)));
        }

        MessageCentreModel build(CoreModel coreModel) {
            return new MessageCentreModel(coreModel, this);
        }

        private static ObjectMapper createObjectMapper() {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mapper.registerModule(new SimpleModule().addSerializer(Message.class, new XssSafeMessageSerializer()));
            return mapper;
        }
    }
}
