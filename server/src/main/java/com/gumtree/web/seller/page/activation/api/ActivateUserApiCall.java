package com.gumtree.web.seller.page.activation.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.domain.user.beans.UserStatusBean;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.api.client.executor.ApiCall;

/**
 * Implementation of {@link ApiCall} to activate a user.
 * @deprecated should be replace with one from bapi-client
 */
public final class ActivateUserApiCall implements ApiCall<Void> {

    private String username;

    private String activationKey;

    /**
     * Constructor.
     *
     * @param username      the username to activate
     * @param activationKey the activation key that was supplied by the user
     */
    public ActivateUserApiCall(String username, String activationKey) {
        this.username = username;
        this.activationKey = activationKey;
    }

    @Override
    public Void execute(BushfireApi api) {
        UserStatusBean userStatusBean = new UserStatusBean();
        userStatusBean.setActivationKey(activationKey);
        userStatusBean.setStatus(UserStatus.ACTIVE);
        api.create(UserApi.class).newStatus(username, userStatusBean);
        return null;
    }
}
