package com.gumtree.web.seller.page.manageads;

import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.command.GetAccountCommand;
import com.gumtree.api.client.executor.command.GetUserApiCall;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.config.SellerProperty;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.Link;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.util.MapViewModelAdapter;
import com.gumtree.web.common.util.SpringViewModelAdapter;
import com.gumtree.web.common.util.ViewModelAdapter;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.model.ManageAdsAccountSelectionFormBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;

import java.util.Map;

/**
 * Implementation of ManageAdsHelper
 */
@Component
public class ManageAdsHelperImpl implements ManageAdsHelper {

    @Autowired
    private UrlScheme urlScheme;
    @Autowired
    private ApiCallExecutor apiCallExecutor;

    /**
     * Populate manage ads urls into a map
     *
     * @param model the map to populate
     */
    @Override
    public final void addManageAdsUrls(Map<String, Object> model, Link currentPage) {
        new ManageAdsLinkModel().populate(new MapViewModelAdapter(model), currentPage);
    }

    /**
     * Populate manage ads urls into a model
     *
     * @param model the model to populate
     */
    @Override
    public final void addManageAdsUrls(Model model, Link currentPage) {
        new ManageAdsLinkModel().populate(new SpringViewModelAdapter(model), currentPage);
    }

    /**
     * Get an account selection form populated with the user's accounts
     *
     * @param userSession Current User session
     * @return ManageAdsAccountSelectionFormBean
     */
    @Override
    public final ManageAdsAccountSelectionFormBean getAccountSelectionForm(UserSession userSession) {
        ManageAdsAccountSelectionFormBean manageAdsAccountSelectionFormBean = new ManageAdsAccountSelectionFormBean();
        manageAdsAccountSelectionFormBean.addAccounts(
                userSession.getSelectableAccounts(),
                userSession.getUser(),
                userSession.getSelectedAccountId());
        return manageAdsAccountSelectionFormBean;
    }

    /**
     * Get the currently selected Account from the user session
     *
     * @param userSession The current user session
     * @return the currently selected Account
     */
    @Override
    public final Account getSelectedAccount(UserSession userSession) {
        return apiCallExecutor.call(
                new GetAccountCommand(userSession.getSelectedAccountId(), userSession))
                .getResponseObject();
    }

    /**
     * Get the user from the username stored in the session
     *
     * @param userSession the User Session
     * @return the User Object associated with the session
     */
    @Override
    public final User getSessionUser(UserSession userSession) {
        return apiCallExecutor.call(new GetUserApiCall(userSession.getUsername())).getResponseObject();
    }

    /**
     * Model of all manage ads urls.
     */
    private class ManageAdsLinkModel {

        private PropSupplier<String> salesforceGumtreeUrl = GtProps.getDStr(SellerProperty.SALESFORCE_GUMTREE_URL);

        /**
         * Populate a {@link org.springframework.ui.Model} with manage ads links
         *
         * @param model the model to populate
         */
        private void populate(ViewModelAdapter model, Link currentPage) {
            model.addObject("manageAdsUrl", new SimpleLink("Manage my ads",
                    urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)));
            model.addObject("myDetailsUrl", new SimpleLink("My details",
                    urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ACCOUNT)));
            model.addObject("packageUsageUrl", new SimpleLink("Package usage history",
                    urlScheme.urlFor(Actions.BUSHFIRE_PACKAGE_USAGE)));
            model.addObject("currentPageUrl", currentPage);
            //add SalesForce links
            model.addObject("sfCompanyUrl", salesforceGumtreeUrl.get() + "manage/company");
            model.addObject("sfFinancialsUrl", salesforceGumtreeUrl.get() + "manage/financials");
            model.addObject("sfMetricsUrl", salesforceGumtreeUrl.get() + "manage/metrics");
            model.addObject("sfStoreUrl", salesforceGumtreeUrl.get() + "manage/store");
            model.addObject("sfSupportUrl", salesforceGumtreeUrl.get() + "manage/support");
        }
    }
}
