package com.gumtree.web.seller.service.image.error;

import com.gumtree.mediaprocessor.model.ApiErrors;
import com.gumtree.shared.client.FeignHystrixServerException;

public class MediaProcessorServerException extends FeignHystrixServerException {

    private final String apiMethod;
    private final Integer status;
    private final ApiErrors apiErrors;

    public MediaProcessorServerException(String apiMethod, Integer status, ApiErrors apiErrors) {
        super(String.format("Error method: %s, status: %s errors: %s", apiMethod, status, apiErrors));
        this.apiMethod = apiMethod;
        this.status = status;
        this.apiErrors = apiErrors;
    }

    public String getApiMethod() {
        return apiMethod;
    }

    public Integer getStatus() {
        return status;
    }

    public ApiErrors getApiErrors() {
        return apiErrors;
    }

    @Override
    public String toString() {
        return "MediaProcessorException{" +
                "method=" + getApiMethod() +
                ", httpStatus=" + getStatus() +
                ", apiErrors=" + getApiErrors() +
                '}';
    }
}
