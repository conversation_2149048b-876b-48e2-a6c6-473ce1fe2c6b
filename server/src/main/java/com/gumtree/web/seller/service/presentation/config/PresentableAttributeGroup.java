package com.gumtree.web.seller.service.presentation.config;

import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.common.util.StringUtils;
import com.gumtree.web.seller.page.postad.model.PostAdAttribute;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeGroup;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

/**
 * Models a presentable attribute group.
 */
public final class PresentableAttributeGroup {

    private String id;

    private String label;

    private String panelId;

    private Integer index;

    private boolean highPriority;

    private List<PresentableAttribute> attributes = new ArrayList<PresentableAttribute>();

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<PresentableAttribute> getAttributes() {
        return attributes;
    }

    public boolean isHighPriority() {
        return highPriority;
    }

    public void setHighPriority(boolean highPriority) {
        this.highPriority = highPriority;
    }

    public void setAttributes(List<PresentableAttribute> attributes) {
        this.attributes = attributes;
    }

    /**
     * Convert this group into a {@link PostAdAttributeGroup}.
     *
     * @param attributesMetadata a list of attributes from API metadata.
     * @param formAttributes the attributes values entered by the user to the SYI form
     * @return a {@link PostAdAttributeGroup}.
     */
    public PostAdAttributeGroup toPostAdAttributeGroup(@Nonnull Collection<AttributeMetadata> attributesMetadata,
                                                       @Nullable Map<String, String> formAttributes) {
        if (formAttributes == null) {
            formAttributes = new HashMap<>();
        }

        List<PostAdAttribute> postAdAttributes = new ArrayList<PostAdAttribute>();

        for (AttributeMetadata metadata : attributesMetadata) {
            PresentableAttribute presentableAttribute = lookupAttribute(attributes, metadata.getName());

            if (presentableAttribute != null) {
                postAdAttributes.add(presentableAttribute.toPostAdAttribute(metadata, formAttributes));
            }
        }

        Collections.sort(postAdAttributes, new Comparator<PostAdAttribute>() {
            @Override
            public int compare(PostAdAttribute o1, PostAdAttribute o2) {
                Integer o1Index = indexOfAttribute(attributes, o1.getId());
                Integer o2Index = indexOfAttribute(attributes, o2.getId());
                return o1Index.compareTo(o2Index);
            }
        });

        PostAdAttributeGroup postAdAttributeGroup = PostAdAttributeGroup.builder()
                .setId(id)
                .setLabel(label)
                .setPanelId(getPanelIdOrDefault())
                .setAttributes(postAdAttributes, formAttributes)
                .setHighPriority(highPriority)
                .build();

        return postAdAttributes.size() > 0 ? postAdAttributeGroup : null;
    }

    /**
     * Merge the given source group into this one.
     *
     * @param attributeGroup the source group
     * @return the merged result
     */
    public PresentableAttributeGroup merge(final PresentableAttributeGroup attributeGroup) {

        if (!id.equals(attributeGroup.getId())) {
            throw new IllegalArgumentException("Can't merge an attribute group with a different id: "
                    + id
                    + " => " + attributeGroup.getId());
        }

        PresentableAttributeGroup mergedGroup = new PresentableAttributeGroup();
        mergedGroup.setId(id);

        if (!StringUtils.hasText(label)) {
            mergedGroup.setLabel(attributeGroup.getLabel());
        } else {
            mergedGroup.setLabel(label);
        }

        if (index == null) {
            mergedGroup.setIndex(attributeGroup.getIndex());
        } else {
            mergedGroup.setIndex(index);
        }

        if (index == null) {
            mergedGroup.setPanelId(attributeGroup.getPanelId());
        } else {
            mergedGroup.setPanelId(panelId);
        }

        mergedGroup.setHighPriority(highPriority);

        List<PresentableAttribute> mergedAttributes = new ArrayList<>();
        for (PresentableAttribute attribute : attributeGroup.getAttributes()) {
            PresentableAttribute existingAttribute = lookupAttribute(attributes, attribute.getId());
            if (existingAttribute != null) {
                mergedAttributes.add(existingAttribute.merge(attribute));
            } else {
                mergedAttributes.add(attribute.clone());
            }
        }

        // Now add, to the end of the list, any attributes that were not in the merge source
        for (PresentableAttribute attribute : attributes) {

            PresentableAttribute mergedAttribute = lookupAttribute(mergedAttributes, attribute.getId());

            if (mergedAttribute == null) {
                mergedAttributes.add(attribute.clone());
            }
        }

        mergedGroup.setAttributes(mergedAttributes);
        return mergedGroup;
    }

    @Override
    public PresentableAttributeGroup clone() {

        PresentableAttributeGroup group = new PresentableAttributeGroup();
        group.setId(id);
        group.setIndex(index);
        group.setLabel(label);
        group.setPanelId(panelId);
        group.setHighPriority(highPriority);

        for (PresentableAttribute attribute : attributes) {
            group.getAttributes().add(attribute.clone());
        }

        return group;
    }

    private PresentableAttribute lookupAttribute(
            List<PresentableAttribute> values, final String id) {

        try {
            return Iterables.find(values, new Predicate<PresentableAttribute>() {
                @Override
                public boolean apply(@Nullable PresentableAttribute input) {
                    return id.equals(input.getId());
                }
            });
        } catch (NoSuchElementException ex) {
            return null;
        }
    }

    private int indexOfAttribute(List<PresentableAttribute> values, final String id) {
        try {
            return Iterables.indexOf(values, new Predicate<PresentableAttribute>() {
                @Override
                public boolean apply(@Nullable PresentableAttribute input) {
                    return id.equals(input.getId());
                }
            });
        } catch (NoSuchElementException ex) {
            return -1;
        }
    }

    public String getPanelId() {
        return panelId;
    }

    public String getPanelIdOrDefault() {
        return panelId != null ? panelId : PostAdFormPanel.ATTRIBUTE_PANEL.getId();
    }

    public void setPanelId(String panelId) {
        this.panelId = panelId;
    }
}
