package com.gumtree.web.seller.service.pricing;

import com.google.common.base.Optional;
import com.gumtree.api.SellerType;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.joda.time.DateTime;

import java.util.Map;

/**
 * Represents a context from which pricing metadata can be derived.
 */
public interface PricingContext {

    /**
     * @return the category id within this context
     */
    Long getCategoryId();

    /**
     * @return the location id within this context
     */
    Long getLocationId();

    Long getAccountId();

    Map<String, String> getPriceSensitiveAttributes();

    /**
     * Determine whether the given product type is active within this context.
     *
     * @param type the product type
     * @return whether the given product type is active within this context.
     */
    boolean isProductActive(ProductType type);

    /**
     * Get the expiry date for the given product type in this context.
     *
     * @param type the product type
     * @return the expiry date for the given product type in this context.
     */
    DateTime getProductExpiryDate(ProductType type);

    AdvertEditor getAdvertEditor();

    Optional<SellerType> getSellerType();
}
