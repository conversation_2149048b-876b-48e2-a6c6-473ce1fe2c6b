package com.gumtree.web.seller.security.apiauthentication;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.MwebProperty;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;

public class CSRFTokenService {
    private static final Logger LOGGER = LoggerFactory.getLogger(JsonWebTokenService.class);

    private String issuer;
    private JWSSigner signer;
    private JWSHeader jwsHeader;
    private JWSVerifier verifier;
    private ParameterEncryption parameterEncryption;

    public static final String EMAIL = "email";
    public static final String IP_ADDRESS = "ip_address";

    public CSRFTokenService(JsonWebTokenProperties properties, ParameterEncryption parameterEncryption) throws JOSEException {
        this.issuer = properties.getIssuer();
        this.signer = new MACSigner(properties.getJwtSecret());
        this.jwsHeader = new JWSHeader(JWSAlgorithm.HS256);
        this.verifier = new MACVerifier(properties.getJwtSecret());
        this.parameterEncryption = parameterEncryption;
    }

    /**
     * Create a Token which is valid in time range defined by notBeforeDelayInSeconds and expireInSeconds
     *
     * @param customClaims
     * @throws JOSEException
     */
    public String generateToken(Map<String,Object> customClaims) throws JOSEException {
        SignedJWT signedJWT = new SignedJWT(jwsHeader, buildClaimSet(customClaims));
        signedJWT.sign(signer);
        return signedJWT.serialize();
    }

    public TimeSensitiveResponse<Object> verifyTokenSignatureAndTimeConstraints(String token) throws ParseException, JOSEException {
        SignedJWT signedJWT = SignedJWT.parse(token);
        boolean verifiedSignature = signedJWT.verify(verifier);

        Date now =  Date.from(ZonedDateTime.now(ZoneOffset.UTC).toInstant());

        TimeSensitiveResponse.Builder<Object> tsrb = new TimeSensitiveResponse.Builder<>();
        tsrb.withClaims(signedJWT.getJWTClaimsSet().getClaims());

        if (!verifiedSignature) {
            return tsrb.withValid(false).build();
        } else if(now.after(signedJWT.getJWTClaimsSet().getExpirationTime())) {
            return tsrb.withExpired(true).withValid(false).build();
        } else if(now.before(signedJWT.getJWTClaimsSet().getNotBeforeTime())) {
            return tsrb.withValid(false).build();
        } else {
            return tsrb.withValid(true).build();
        }
    }

    private JWTClaimsSet buildClaimSet(Map<String, Object> customClaims) {
        ZonedDateTime utc = ZonedDateTime.now(ZoneOffset.UTC);
        JWTClaimsSet.Builder claimsSet = new JWTClaimsSet.Builder();
        claimsSet.issuer(issuer);
        claimsSet.issueTime(Date.from(utc.toInstant()));

        int tokenNotValidBeforeSeconds = GtProps.getInt(MwebProperty.TOKEN_VALID_NOT_BEFORE_SECONDS);
        int tokenNotValidAfterSeconds = GtProps.getInt(MwebProperty.TOKEN_VALID_NOT_AFTER_SECONDS);

        claimsSet.notBeforeTime(Date.from(utc.plusSeconds(tokenNotValidBeforeSeconds).toInstant()));
        claimsSet.expirationTime(Date.from(utc.plusSeconds(tokenNotValidAfterSeconds).toInstant()));
        for(String key: customClaims.keySet()) {
            claimsSet.claim(key, customClaims.get(key));
        }
        return claimsSet.build();
    }

    public TimeSensitiveResponse<Object> verifyCsrfTokenForEmail(String csrfToken, String email) throws Exception {
        Predicate<Map<String,Object>> check = claims -> {
            if(claims.containsKey(EMAIL)) {
                return claims.get(EMAIL).equals(email);
            } else {
                LOGGER.error("JWT should have Email claim, failing CSRF token check");
                return false;
            }
        };
        return verifyCsrfToken(csrfToken, check);
    }

    public TimeSensitiveResponse<Object> verifyCsrfTokenForIpAddress(String csrfToken, RemoteIP remoteIP) throws Exception {
        Predicate<Map<String,Object>> check = claims -> {
            if(claims.containsKey(IP_ADDRESS)) {
                return claims.get(IP_ADDRESS).equals(remoteIP.getIpAddress());
            } else {
                LOGGER.error("JWT should have IP claim, failing CSRF token check");
                return false;
            }
        };
        return verifyCsrfToken(csrfToken, check);
    }

    public String csrfTokenForEmail(String email){
        return parameterEncryption.encryptAndUrlEncode(tokenForEmail(email));
    }

    private String tokenForEmail(String email) {
        Map<String,Object> customClaims = new HashMap<>();
        if(email != null) {
            customClaims.put(EMAIL, email);
        }
        SignedJWT signedJWT = new SignedJWT(jwsHeader, buildClaimSet(customClaims));
        try {
            signedJWT.sign(signer);
        } catch (JOSEException e) {
            LOGGER.warn("Failed to verify csrf token. Msg: {}", e.getClass().getSimpleName() + " - " + e.getMessage());
        }
        return signedJWT.serialize();
    }

    private TimeSensitiveResponse<Object> verifyCsrfToken(String csrfToken, Predicate<Map<String,Object>> check)
            throws Exception {

        TimeSensitiveResponse.Builder<Object> tsrb = new TimeSensitiveResponse.Builder<>();

        if (StringUtils.isBlank(csrfToken)) {
            return tsrb.withValid(false).build();
        }

        String token = parameterEncryption.decrypt(csrfToken);

        try {
            TimeSensitiveResponse<Object> tsr = verifyTokenSignatureAndTimeConstraints(token);
            Optional<Map<String, Object>> claims = tsr.getClaims();
            if (tsr.isValid() && claims.isPresent()) {
                tsrb.withValid(check.test(claims.get()));
            } else {
                tsrb.withValid(false);
            }
        } catch (Exception e) {
            LOGGER.warn("Failed to verify csrf token. Msg: {}", e.getClass().getSimpleName() + " - " + e.getMessage());
            tsrb.withValid(false);
        }
        return tsrb.build();
    }

    public String csrfTokenForIpAddress(RemoteIP remoteIP) throws RuntimeException {
        try {
            return parameterEncryption.encryptAndUrlEncode(tokenForIpAddress(remoteIP));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String tokenForIpAddress(RemoteIP remoteIP) throws JOSEException {
        Map<String,Object> customClaims = new HashMap<>();
        if(remoteIP != null) {
            customClaims.put(IP_ADDRESS, remoteIP.getIpAddress());
        }
        SignedJWT signedJWT = new SignedJWT(jwsHeader, buildClaimSet(customClaims));
        signedJWT.sign(signer);
        return signedJWT.serialize();
    }
}
