package com.gumtree.web.seller.security;

import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.ManageAdsController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * This interceptor supports super users logging into any chosen account.
 */
public final class SuperUserAccountOverrideInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private UserSession userSession;

    @Override
    public boolean preHandle(
            HttpServletRequest request,
            HttpServletResponse response,
            Object handler) throws Exception {

        HandlerMethod method = (HandlerMethod) handler;
        if (method.getBeanType().equals(ManageAdsController.class) && userSession.isSuperUser()) {
            String accountIdOverrideString = request.getParameter("accountId");
            try {
                Long accountIdOverride = Long.valueOf(accountIdOverrideString);
                userSession.setSuperUserOverrideAccountId(accountIdOverride);
            } catch (NumberFormatException ex) {
                return true;
            }
        }
        return true;
    }
}
