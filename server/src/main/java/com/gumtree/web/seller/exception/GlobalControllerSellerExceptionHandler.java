package com.gumtree.web.seller.exception;

import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.security.exception.FormValidationException;
import com.gumtree.web.security.exception.InvalidUserException;
import com.gumtree.web.security.exception.UserNotRecognisedException;
import com.gumtree.web.seller.page.PageNotFoundException;
import com.gumtree.web.seller.page.ajax.userConsent.ThirdPartyConsentException;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.error.ErrorModel;
import org.apache.commons.fileupload.FileUploadBase;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

@ControllerAdvice
public class GlobalControllerSellerExceptionHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalControllerSellerExceptionHandler.class);

    @Autowired
    private CoreModel.BuilderFactory builderFactory;

    @Autowired
    private UrlScheme urlScheme;

    @ExceptionHandler
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ModelAndView handlePageNotFound(PageNotFoundException e, HttpServletRequest request) throws Exception {
        LOGGER.info(e.getClass().getName() + ": " + e.getMessage());
        CoreModel.Builder coreBuilder = builderFactory.create(request);

        return ErrorModel.builder()
                .coreBuilder(coreBuilder)
                .page(Page.Error404)
                .buildModelAndView();
    }

    @ExceptionHandler
    public ModelAndView handleUserNotRecognised(UserNotRecognisedException e) throws Exception {
        // we lower this to debug as user nto recognised happens quite often in the context of the app,
        // is not an error in itself and we don't want to spam logs
        LOGGER.debug("User not recognised", e);
        cleanSession();
        return new ModelAndView(createRedirect(urlScheme.urlFor(Actions.BUSHFIRE_LOGIN)));
    }

    @ExceptionHandler
    public ModelAndView handleInvalidUser(InvalidUserException e) throws Exception {
        // happens when salesforce creates user without account, destroying session and removing cookies
        // makes user login with username/password and BAPI puts user/account into correct state
        LOGGER.info("Invalid user", e);
        cleanSession();
        return new ModelAndView(createRedirect(urlScheme.urlFor(Actions.BUSHFIRE_LOGIN)));
    }

    @ExceptionHandler
    public ModelAndView handleFileUploadException(FileUploadBase.IOFileUploadException e) throws Exception {
        LOGGER.info("Image upload failed", e);
        return new ModelAndView(createRedirect(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)));
    }

    @ExceptionHandler
    public ModelAndView handleMultipartException(MultipartException e) throws Exception {
        LOGGER.info("Image upload failed", e);
        return new ModelAndView(createRedirect(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)));
    }

    @ExceptionHandler
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ModelAndView handleUnsupportedOperation(UnsupportedOperationException e, HttpServletRequest request) throws Exception {
        LOGGER.error(e.getClass().getName() + ": " + e.getMessage(), e);
        CoreModel.Builder coreBuilder = builderFactory.create(request);

        return ErrorModel.builder()
                .coreBuilder(coreBuilder)
                .page(Page.Error500)
                .buildModelAndView();
    }

    @ExceptionHandler
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ModelAndView handleDefault(Exception e, HttpServletRequest request) throws Exception {
        LOGGER.error(e.getClass().getName() + ": " + e.getMessage(), e);
        CoreModel.Builder coreBuilder = builderFactory.create(request);

        return ErrorModel.builder()
                .coreBuilder(coreBuilder)
                .page(Page.Error500)
                .buildModelAndView();
    }


    @ExceptionHandler({ThirdPartyConsentException.class})
    public ResponseEntity handleThirdPartyConsentException(ThirdPartyConsentException ex) {
        LOGGER.info("Invalid user or user is not signed in", ex.getMessage());
        return new ResponseEntity(HttpStatus.UNAUTHORIZED);
    }


    @ExceptionHandler({FormValidationException.class})
    public ResponseEntity handleFormValidationException(FormValidationException ex) {
        LOGGER.debug("Form validation error ", ex.getMessage());
        return new ResponseEntity(ex.getFormErrors(), HttpStatus.BAD_REQUEST);
    }

    private void cleanSession() {
        SecurityUtils.getSubject().logout();
    }

    /**
     * Create a redirect view name.
     *
     * @param path the path
     * @return a redirect view name.
     */
    private String createRedirect(String path) {
        StringBuilder builder = new StringBuilder("redirect:");
        builder.append(path);
        return builder.toString();
    }
}
