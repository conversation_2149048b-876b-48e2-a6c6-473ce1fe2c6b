package com.gumtree.web.seller.service.phoneverify;

import com.gumtree.security.phone.number.authenticator.api.PhoneNumberAuthenticatorApi;
import com.gumtree.security.phone.number.authenticator.model.AuthenticationStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import rx.Single;

@Service
public class PhoneVerifyServiceImpl implements PhoneVerifyService{


    @Autowired
    private PhoneNumberAuthenticatorApi phoneNumberAuthenticatorApi;


    @Override
    public AuthenticationStatus getAuthenticationStatus(Long userId) {
        try {
            Single<AuthenticationStatus> statusSingle = phoneNumberAuthenticatorApi.getUserPhoneAuthenticationStatus(userId);

            return statusSingle.toObservable().toBlocking().first();
        }catch (Exception e){
            throw new RuntimeException("get user phone authentication status error");
        }
    }
}
