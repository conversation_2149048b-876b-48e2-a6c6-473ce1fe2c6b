package com.gumtree.web.seller.service.image.error;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.io.CharStreams;
import com.gumtree.mediaprocessor.model.ApiError;
import com.gumtree.mediaprocessor.model.ApiErrors;
import feign.Response;
import feign.codec.ErrorDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.io.Reader;
import java.util.Optional;

public class MediaProcessorErrorDecoder implements ErrorDecoder {

    private static final Logger LOGGER = LoggerFactory.getLogger(MediaProcessorErrorDecoder.class);

    private final ObjectMapper objectMapper;
    private final ApiErrors defaultApiErrors;

    public MediaProcessorErrorDecoder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.defaultApiErrors = getDefaultApiErrors();
    }

    @Override
    public Exception decode(String s, Response response) {
        Optional<ApiErrors> mediaProcessorError =
                getResponseBody(response, ApiErrors.class);
        int status = response.status();

        if(status >= 400 && status < 500) {
            return mediaProcessorError.map(apiErrors -> new MediaProcessorClientException(s, status, apiErrors))
                    .orElseGet(() -> new MediaProcessorClientException(s, status, defaultApiErrors));
        } else {
            return mediaProcessorError.map(apiErrors -> new MediaProcessorServerException(s, status, apiErrors))
                    .orElseGet(() -> new MediaProcessorServerException(s, status, defaultApiErrors));
        }

    }

    protected <T> Optional<T> getResponseBody(Response response, Class<T> klass) {
        if(response.body() == null) {
            return Optional.empty();
        }
        try (Reader reader = response.body().asReader()){
            String bodyJson = CharStreams.toString(reader);
            return Optional.ofNullable(objectMapper.readValue(bodyJson, klass));
        } catch (IOException e) {
            LOGGER.error("Unable to parse response body to JSON {}, {}", e.getMessage(), response);
            return Optional.empty();
        }
    }

    private ApiErrors getDefaultApiErrors() {
        int statusCode = HttpStatus.BAD_GATEWAY.value();
        return new ApiErrors().status(statusCode)
                .addErrorsItem(new ApiError().detail("Unknown error")).status(statusCode);
    }


}

