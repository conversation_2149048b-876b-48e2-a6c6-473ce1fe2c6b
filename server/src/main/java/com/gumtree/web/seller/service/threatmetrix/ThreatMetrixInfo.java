package com.gumtree.web.seller.service.threatmetrix;

import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrixTracking;

/**
 * ThreatMetrix信息的数据传输对象
 * 封装Cookie和Tracking信息，用于API响应场景
 */
public class ThreatMetrixInfo {
    
    private final ThreatMetrixCookie cookie;
    private final ThreatMetrixTracking tracking;
    private final boolean enabled;

    /**
     * 构造函数
     * 
     * @param cookie ThreatMetrix Cookie
     * @param tracking ThreatMetrix Tracking数据
     * @param enabled 是否启用
     */
    public ThreatMetrixInfo(ThreatMetrixCookie cookie, ThreatMetrixTracking tracking, boolean enabled) {
        this.cookie = cookie;
        this.tracking = tracking;
        this.enabled = enabled;
    }

    /**
     * 创建一个禁用状态的ThreatMetrixInfo
     * 
     * @return ThreatMetrixInfo 禁用状态的实例
     */
    public static ThreatMetrixInfo disabled() {
        return new ThreatMetrixInfo(null, null, false);
    }

    /**
     * 获取ThreatMetrix Cookie
     * 
     * @return ThreatMetrixCookie
     */
    public ThreatMetrixCookie getCookie() {
        return cookie;
    }

    /**
     * 获取ThreatMetrix Tracking数据
     * 
     * @return ThreatMetrixTracking
     */
    public ThreatMetrixTracking getTracking() {
        return tracking;
    }

    /**
     * 检查ThreatMetrix是否启用
     * 
     * @return boolean 是否启用
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 获取Session ID（从Cookie中）
     * 
     * @return String Session ID，如果Cookie为null则返回null
     */
    public String getSessionId() {
        return cookie != null ? cookie.getDefaultValue() : null;
    }

    /**
     * 检查是否有有效的ThreatMetrix数据
     * 
     * @return boolean 是否有有效数据
     */
    public boolean hasValidData() {
        return enabled && cookie != null && tracking != null;
    }

    @Override
    public String toString() {
        return "ThreatMetrixInfo{" +
                "enabled=" + enabled +
                ", sessionId=" + getSessionId() +
                ", hasTracking=" + (tracking != null) +
                '}';
    }
}
