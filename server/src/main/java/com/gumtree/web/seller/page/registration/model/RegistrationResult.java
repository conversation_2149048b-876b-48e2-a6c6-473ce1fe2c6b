package com.gumtree.web.seller.page.registration.model;

import com.google.common.collect.Maps;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;

import java.util.List;
import java.util.Map;

public class RegistrationResult extends CommonModel {

    private static final String CORE_MODEL_PAGE_TITLE = "Create an account | My Gumtree - Gumtree";

    private Map<String, List<String>> formErrors = Maps.newHashMap();
    private String resendPath;
    private boolean withError;

    public RegistrationResult() {
        // required for Spring form binding
    }

    private RegistrationResult(CoreModel core, Builder builder) {
        super(core);
        this.formErrors = builder.formErrors;
        this.resendPath = builder.resendPath;
        this.withError = builder.withError;
    }

    public Map<String, List<String>> getFormErrors() {
        return formErrors;
    }

    public String getResendPath() {
        return resendPath;
    }

    public boolean isWithError() {
        return withError;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private Map<String, List<String>> formErrors = Maps.newHashMap();
        private String resendPath;
        private boolean withError;

        public Builder resendPath(String resendPath) {
            this.resendPath = resendPath;
            return this;
        }

        public Builder withError(boolean withError) {
            this.withError = withError;
            return this;
        }

        public Builder errors(Map<String, List<String>> formErrors){
            this.formErrors = formErrors;
            return this;
        }

        public RegistrationResult build(CoreModel.Builder coreModelBuilder, Page page) {
            coreModelBuilder.withTitle(CORE_MODEL_PAGE_TITLE);
            return new RegistrationResult(coreModelBuilder.build(page), this);
        }
    }
}
