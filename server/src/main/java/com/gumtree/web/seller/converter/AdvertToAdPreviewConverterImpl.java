package com.gumtree.web.seller.converter;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.Category;
import com.gumtree.common.format.PriceFormatter;
import com.gumtree.common.util.StringUtils;
import com.gumtree.common.util.time.Clock;
import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeService;
import com.gumtree.domain.newattribute.DisplayAttribute;
import com.gumtree.location.LocationDisplayUtils;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.seller.model.AdPreview;
import com.gumtree.web.seller.model.AdPreviewImpl;
import com.gumtree.web.common.format.PostingTimeFormatter;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.PRICE_FREQUENCY;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.SELLER_TYPE;

/**
 * Converts recent ads to {@link AdPreview}.
 *
 * <AUTHOR>
 */
@Component
public class AdvertToAdPreviewConverterImpl implements AdvertToAdPreviewConverter {

    @Autowired
    private UrlScheme urlScheme;

    @Autowired
    private AttributeService attributeService;

    @Autowired
    private LocationService locationService;

    @Autowired
    private Clock clock;

    @Autowired
    private PriceFormatter priceFormatter;

    private DateTimeFormatter dateTimeFormatter = ISODateTimeFormat.dateTime();


    @Override
    public final AdPreview convert(
            Advert advert,
            Location location,
            Category category) {

        Image mainImage = advert.getMainImage();
        String previewUrl = urlScheme.previewUrlFor(mainImage);
        String thumbUrl = urlScheme.thumbnailUrlFor(mainImage);
        Date now = clock.getDate();
        String displayLocationText = getDisplayLocationText(advert, location);

        String sellerType = "";

        if (category.findAttribute(SELLER_TYPE.getName()).isPresent()) {
            Attribute sellerTypeAttribute = advert.getAttribute(SELLER_TYPE.getName());
            if (sellerTypeAttribute != null) {
                Optional<DisplayAttribute> sellerTypeDisplayAttribute =
                        attributeService.getDisplayAttribute(sellerTypeAttribute, category.getId());
                if (sellerTypeDisplayAttribute.isPresent()) {
                    sellerType = sellerTypeDisplayAttribute.get().getDisplayValue();
                }
            }
        }

        return new AdPreviewImpl.Builder()
                .id(advert.getId())
                .mainImage(advert.getMainImage())
                .previewUrl(previewUrl)
                .thumbUrl(thumbUrl)
                .title(advert.getTitle())
                .description(advert.getDescription())
                .isUrgent(advert.isUrgent())
                .isFeatured(advert.isFeatured())
                .advertUrl(urlScheme.urlFor(advert))
                .reportAdUrl(urlScheme.urlToReportAd(advert.getId()))
                .displayDate(getDateLabel(advert, category))
                .seoTimeStamp(getSeoTimeStamp(advert))
                .postedTime(PostingTimeFormatter.formatPostingTime(advert.getLiveDate(), now))
                .postedTimeAsDate(advert.getLiveDate())
                .displayLocationText(displayLocationText)
                .displayPrice(
                        priceFormatter.format(
                                advert.getPrice(),
                                advert.getAttribute(PRICE_FREQUENCY.getName()),
                                category.getId()))
                .sellerType(sellerType)
                .build();
    }

    private String getSeoTimeStamp(Advert advert) {
        Date liveDate = advert.getLiveDate();
        if (liveDate == null) {
            return null;
        }
        return dateTimeFormatter.print(liveDate.getTime());
    }

    private String getDisplayLocationText(Advert advert, Location location) {
        String locationText = advert.getLocationText();
        if (StringUtils.hasText(locationText)) {
            return LocationDisplayUtils.getCleanedLocationText(locationText);
        }

        Location landingLocation = locationService.getLanding(location);
        if (landingLocation != null) {
            return landingLocation.getDisplayName();
        }

        return "";
    }

    private String getDateLabel(Advert advert, Category category) {
        for (AttributeMetadata attributeMetadata : category.getAttributeMetadata()) {
            if (attributeMetadata.getType() == AttributeType.DATETIME) {
                Attribute attribute = advert.getAttribute(attributeMetadata.getName());
                if (attribute != null && attribute.getValue() != null) {
                    Optional<DisplayAttribute> displayAttributeOption
                            = attributeService.getDisplayAttribute(attribute, category.getId());
                    if (displayAttributeOption.isPresent()) {
                        return attributeMetadata.getLabel() + ": "
                                + displayAttributeOption.get().getDisplayValue();
                    }
                }
            }
        }
        return null;
    }
}
