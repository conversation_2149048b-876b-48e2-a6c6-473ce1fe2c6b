package com.gumtree.web.seller.service.skill;

import com.gumtree.web.seller.service.skill.model.SkillAttributeMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class SkillAttributeMetadataServiceImpl implements SkillAttributeMetadataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SkillAttributeMetadataServiceImpl.class);

    private List<SkillAttributeMetadata.SkillAttributeCategory> skillCategories;
    private Map<Integer, List<SkillAttributeMetadata.SkillAttribute>> categoryIdSkillsCache;
    private final Map<String, SkillAttributeMetadata.SkillAttributeCategory> categoryCache = new ConcurrentHashMap<>();
    private final Map<String, List<SkillAttributeMetadata.SkillAttribute>> categorySkillsCache = new ConcurrentHashMap<>();
    private final Map<String, SkillAttributeMetadata.SkillAttribute> skillCache = new ConcurrentHashMap<>();

    public SkillAttributeMetadataServiceImpl() {
        this.skillCategories = new ArrayList<>();
        this.categoryIdSkillsCache = new HashMap<>();
    }

    public SkillAttributeMetadataServiceImpl(List<SkillAttributeMetadata.SkillAttributeCategory> skillCategories) {
        this.skillCategories = skillCategories;
        this.categoryIdSkillsCache = new HashMap<>();
        initializeCache();
    }

    public void initializeCache() {
        // cache : categoryId -> skills
        if (skillCategories != null) {
            categoryIdSkillsCache = skillCategories.stream()
                .collect(Collectors.toMap(
                    SkillAttributeMetadata.SkillAttributeCategory::getCategoryId,
                    SkillAttributeMetadata.SkillAttributeCategory::getSkills,
                    (existing, replacement) -> replacement
                ));
        }

        // cache : categoryName -> skills
        if (skillCategories != null) {
            for (SkillAttributeMetadata.SkillAttributeCategory category : skillCategories) {
                categoryCache.put(category.getName(), category);
                categorySkillsCache.put(category.getName(), category.getSkills());

                for (SkillAttributeMetadata.SkillAttribute skill : category.getSkills()) {
                    skillCache.put(skill.getSkillId(), skill);
                }
            }
        }
        if (skillCategories != null) {
            LOGGER.info("Successfully initialized skill attribute metadata cache with {} categories", skillCategories.size());
        }
    }

    @Override
    public List<SkillAttributeMetadata.SkillAttributeCategory> getAllSkillCategories() {
        return skillCategories;
    }

    @Override
    public List<SkillAttributeMetadata.SkillAttribute> getSkillsByCategoryId(Integer categoryId) {
        return categoryIdSkillsCache.getOrDefault(categoryId, new ArrayList<>());
    }

    @Override
    public void setSkillCategories(List<SkillAttributeMetadata.SkillAttributeCategory> skillCategories) {
        this.skillCategories = skillCategories;
        initializeCache();
    }

    @Override
    @Cacheable(value = "skillCategories", unless = "#result == null")
    public List<SkillAttributeMetadata.SkillAttributeCategory> getAllCategories() {
        return skillCategories;
    }

    @Override
    @Cacheable(value = "categorySkills", key = "#categoryName", unless = "#result == null")
    public List<SkillAttributeMetadata.SkillAttribute> getSkillsByCategoryName(String categoryName) {
        return categorySkillsCache.get(categoryName);
    }

    @Override
    @Cacheable(value = "skills", key = "#skillId", unless = "#result == null")
    public SkillAttributeMetadata.SkillAttribute getSkillById(String skillId) {
        return skillCache.get(skillId);
    }

    @Override
    @Cacheable(value = "suggestedSkills", key = "#categoryId", unless = "#result == null")
    public List<Integer> getSuggestedSkillIdsByCategoryId(Integer categoryId) {
        return getSkillsByCategoryId(categoryId).stream()
            .filter(SkillAttributeMetadata.SkillAttribute::isSuggested)
            .map(SkillAttributeMetadata.SkillAttribute::getSkillId)
                .map(Integer::parseInt)
            .collect(Collectors.toList());
    }
} 
