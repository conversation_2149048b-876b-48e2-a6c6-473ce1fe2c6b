package com.gumtree.web.seller.page.activation.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.command.VerifyEmailApiCall;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.CoreModelBaseController;
import com.gumtree.web.seller.page.activation.model.EmailVerificationModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.verification.EmailVerification;
import com.gumtree.zeno.core.service.ZenoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Controller
@GumtreePage(PageType.EmailVerification)
@RequestMapping(EmailVerificationController.PAGE_PATH)
@GoogleAnalytics
public final class EmailVerificationController extends CoreModelBaseController {
    private static final Logger LOG = LoggerFactory.getLogger(EmailVerificationController.class);

    public static final String PAGE_PATH = "/verify-email";
    private static final String RESULT_PAGE_SUBPATH = "/result";
    private static final String USER_ID_PARAM = "id";
    private static final String ACTIVATION_KEY_PARAM = "key";

    private final ZenoService zenoService;

    private final ApiCallExecutor apiCallExecutor;

    @Autowired
    public EmailVerificationController(UserSessionService userSessionService,
                                       ZenoService zenoService,
                                       ApiCallExecutor apiCallExecutor,
                                       CategoryModel categoryModel,
                                       CookieResolver cookieResolver) {
        super(userSessionService, categoryModel, cookieResolver);
        this.zenoService = zenoService;
        this.apiCallExecutor = apiCallExecutor;
    }

    @RequestMapping(method = RequestMethod.GET)
    public final ModelAndView handleRequest(@RequestParam Map<String, String> allRequestParams,
                                            RedirectAttributes redirectAttributes) {
        redirectAttributes.addFlashAttribute(USER_ID_PARAM, allRequestParams.get(USER_ID_PARAM));
        redirectAttributes.addFlashAttribute(ACTIVATION_KEY_PARAM, allRequestParams.get(ACTIVATION_KEY_PARAM));
        return createRedirectWithTracking(PAGE_PATH + RESULT_PAGE_SUBPATH, allRequestParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = RESULT_PAGE_SUBPATH)
    public final ModelAndView activateEmail(Model model, HttpServletRequest request) {

        String username = (String) model.asMap().get(USER_ID_PARAM);
        String activationKey = (String) model.asMap().get(ACTIVATION_KEY_PARAM);

        boolean verified = validateArguments(username, activationKey) && verifyEmail(username, activationKey);

        CoreModel.Builder coreModel = getCoreModelBuilder(request).withIndex();
        EmailVerificationModel.Builder modelBuilder = EmailVerificationModel.builder(coreModel).withVerified(verified);

        zenoService.logEvent(Page.EmailVerification.getPageType(), modelBuilder.getModel(), EmailVerification.class);

        return modelBuilder.build();
    }

    private boolean validateArguments(String username, String activationKey) {
        return StringUtils.isNoneEmpty(username) && StringUtils.isNotEmpty(activationKey);
    }

    private boolean verifyEmail(String username, String activationKey) {
        try {
            ApiCallResponse<Void> apiResponse = apiCallExecutor.call(new VerifyEmailApiCall(username, activationKey));
            if (!apiResponse.isErrorResponse()) {
                return true;
            } else {
                LOG.info("Email verification has failed. ActKey [ "
                        + activationKey + " ]. Errors: " + apiResponse);
                return false;
            }
        } catch (Exception ex) {
            LOG.error("Email verification has failed. ActKey [ " + activationKey + " ]", ex);
            return false;
        }
    }
}
