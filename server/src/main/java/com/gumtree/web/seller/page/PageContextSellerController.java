package com.gumtree.web.seller.page;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class PageContextSellerController<T> extends BaseSellerController {

    private final GumtreePageContext<T> pageContext;
    protected final CategoryService categoryService;
    protected final LocationService locationService;

    @Autowired
    public PageContextSellerController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                       ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                       UrlScheme urlScheme, GumtreePageContext<T> pageContext,
                                       CategoryService categoryService, LocationService locationService,
                                       UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.pageContext = pageContext;
        this.categoryService = categoryService;
        this.locationService = locationService;
    }

    protected GumtreePageContext<T> getPageContext() {
        return pageContext;
    }

    protected final CategoryService getCategoryService() {
        return categoryService;
    }

    protected final void populatePageContextWithCategoryLocation(Long categoryId, Long locationId) {
        if (categoryId != null) {
            Optional<Category> category = categoryService.getById(categoryId);
            if (category.isPresent()) {
                pageContext.setCategory(category.get());
                pageContext.setCategoryLevelHierarchy(categoryService.getLevelHierarchy(category.get()));
            }
        }
        if (locationId != null) {
            pageContext.setLocation(locationService.getById(locationId.intValue()));
        }
    }
}
