package com.gumtree.web.seller.service.adstats;

import com.gumtree.api.Ad;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounterService;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class BatchAdStatsProvider {

    private AdvertStatisticDataFactoryOfFactory advertStatisticDataFactoryOfFactory;
    private AdCounterService adCounterService;

    @Autowired
    public BatchAdStatsProvider(AdvertStatisticDataFactoryOfFactory advertStatisticDataFactoryOfFactory,
                                AdCounterService adCounterService) {
        this.advertStatisticDataFactoryOfFactory = advertStatisticDataFactoryOfFactory;
        this.adCounterService = adCounterService;
    }

    public List<AdvertStatisticData> getStatsFor(List<Ad> ads) {
        Map<Long, AdCounters> adCountersMap = adCounterService.getAdvertCounters(advertIdsFor(ads));
        AdvertStatisticDataFactory factory = advertStatisticDataFactoryOfFactory.createFactoryWithStats(adCountersMap);
        return ads.stream()
                .map(factory::forAdvert)
                .collect(Collectors.toList());
    }

    private List<Long> advertIdsFor(List<Ad> ads) {
        return ads.stream()
                .map(Ad::getId)
                .collect(Collectors.toList());
    }
}