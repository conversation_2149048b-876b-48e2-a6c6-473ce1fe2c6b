package com.gumtree.web.seller.service.presentation.config;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeGroup;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

/**
 * Models configuration for a category's attribute groups.
 */
public final class PresentableAttributeGroups {

    private String categoryName;

    private List<PresentableAttributeGroup> groups = new ArrayList<PresentableAttributeGroup>();

    public PresentableAttributeGroups() {
    }

    public PresentableAttributeGroups(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public List<PresentableAttributeGroup> getGroups() {
        return groups;
    }

    public void setGroups(List<PresentableAttributeGroup> groups) {
        this.groups = groups;
    }

    /**
     * Convert this model to a list of {@link PostAdAttributeGroup}s.
     *
     * @param attributes the source attribute metadata.
     * @param formAttributes the attributes values entered by the user to the SYI form
     * @return list of {@link PostAdAttributeGroup}s.
     */
    public List<PostAdAttributeGroup> toPostAdAttributeGroups(Collection<AttributeMetadata> attributes,
                                                              Map<String, String> formAttributes) {

        List<PostAdAttributeGroup> postAdAttributeGroups = new ArrayList<PostAdAttributeGroup>();

        for (PresentableAttributeGroup group : groups) {
            PostAdAttributeGroup postAdAttributeGroup = group.toPostAdAttributeGroup(attributes, formAttributes);

            if (postAdAttributeGroup != null) {
                postAdAttributeGroups.add(postAdAttributeGroup);
            }
        }

        return postAdAttributeGroups;
    }

    @Override
    public PresentableAttributeGroups clone() {
        PresentableAttributeGroups cloned = new PresentableAttributeGroups();
        cloned.setCategoryName(categoryName);

        for (PresentableAttributeGroup group : groups) {
            cloned.getGroups().add(group.clone());
        }

        return cloned;
    }

    /**
     * Merge the given source groups into this list of groups
     *
     * @param groups the source groups
     * @return the merged result
     */
    public PresentableAttributeGroups merge(final PresentableAttributeGroups groups) {

        PresentableAttributeGroups mergedGroups = new PresentableAttributeGroups();
        mergedGroups.setCategoryName(categoryName);

        List<PresentableAttributeGroup> mergedGroupList = new ArrayList<PresentableAttributeGroup>();

        Iterables.addAll(mergedGroupList, Iterables.transform(this.groups,

                new Function<PresentableAttributeGroup, PresentableAttributeGroup>() {

                    @Override
                    public PresentableAttributeGroup apply(@Nullable PresentableAttributeGroup input) {

                        PresentableAttributeGroup sourceGroup = lookupAttributeGroup(
                                groups.getGroups(),
                                input.getId());

                        if (sourceGroup == null) {
                            return input.clone();
                        }

                        return input.merge(sourceGroup);
                    }
                }));

        for (PresentableAttributeGroup attributeGroup : groups.getGroups()) {

            PresentableAttributeGroup mergedGroup = lookupAttributeGroup(mergedGroupList, attributeGroup.getId());

            if (mergedGroup == null) {
                mergedGroupList.add(attributeGroup.clone());
            }
        }

        Collections.sort(mergedGroupList, new Comparator<PresentableAttributeGroup>() {
            @Override
            public int compare(PresentableAttributeGroup o1, PresentableAttributeGroup o2) {
                Integer o1Index = o1.getIndex() != null ? o1.getIndex() : 0;
                Integer o2Index = o2.getIndex() != null ? o2.getIndex() : 0;
                return o1Index.compareTo(o2Index);
            }
        });

        mergedGroups.setGroups(mergedGroupList);

        return mergedGroups;
    }

    private PresentableAttributeGroup lookupAttributeGroup(
            List<PresentableAttributeGroup> values, final String id) {

        try {
            return Iterables.find(values, new Predicate<PresentableAttributeGroup>() {
                @Override
                public boolean apply(@Nullable PresentableAttributeGroup input) {
                    return id.equals(input.getId());
                }
            });
        } catch (NoSuchElementException ex) {
            return null;
        }
    }
}
