package com.gumtree.web.seller.service.adstats;

import com.gumtree.api.Ad;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounters;

import java.util.List;
import java.util.Map;

/**
 * Ad stats service.
 */
public interface AdvertStatsService {

    Map<String, AdCounters> getStatisticForAdvert(Long advertId, Integer numOfDays);

    /**
     * Get stats for account
     *
     * @param accountId the account id
     * @return stats for account
     */
    List<AdvertStatisticData> getStatisticForAccount(Long accountId);

    /**
     * Get stats for advert.
     *
     * @param advertId the advert id
     * @return stats for advert
     */
    AdvertStatisticData getStatisticForAdvert(Long advertId);

    /**
     * Get list of stats for list of adverts
     *
     * @param adverts list of advert's
     * @return list of stats for list of adverts
     */
    List<AdvertStatisticData> getStatisticForAdvertList(List<Ad> adverts);
}
