package com.gumtree.web.seller.page.manageads;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.domain.advert.DeleteReason;
import com.gumtree.api.domain.advert.NewStatusBean;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.manageads.api.GumtreeSuccessApiCall;
import com.gumtree.web.seller.page.manageads.api.NewAdvertStatusApiCall;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.metric.Metric;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ExtendedModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@Controller
@GumtreePage(PageType.DeleteAd)
public final class DeleteAdController extends BaseSellerController {

    public static final String PAGE_PATH = "/manage/ads/delete-ad";

    private final UserSession authenticatedUserSession;

    private final CustomMetricRegistry customMetricRegistry;

    @Autowired
    public DeleteAdController(CookieResolver cookieResolver, CategoryModel categoryModel,
                              ApiCallExecutor apiCallExecutor,
                              ErrorMessageResolver messageResolver,
                              UrlScheme urlScheme,
                              UserSession authenticatedUserSession,
                              UserSessionService userSessionService,
                              CustomMetricRegistry customMetricRegistry) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.authenticatedUserSession = authenticatedUserSession;
        this.customMetricRegistry = customMetricRegistry;
    }

    /**
     * Delete a single advert
     *
     * @param advertId - advert id to delete
     * @return redirect to manage ads page
     */
    @RequestMapping(value = PAGE_PATH + "/{id}", method = RequestMethod.DELETE)
    @ResponseStatus(value = HttpStatus.OK)
    public ModelAndView deleteAdvert(@PathVariable("id") String advertId,
                                     @RequestParam(value = "deleteReason", required = false) DeleteReason deleteReason) {

        execute(new NewAdvertStatusApiCall(advertId, new NewStatusBean("DELETED_USER"), authenticatedUserSession));
        if (deleteReason != null) {
            execute(new GumtreeSuccessApiCall(advertId, deleteReason, authenticatedUserSession));
            customMetricRegistry.metricCounter(Metric.DELETE.name(), deleteReason.name());
        }
        return createParametrizedRedirect(ManageAdsController.PAGE_PATH, toMap("advertId", advertId));
    }

    /**
     * Delete a single advert through "Your ad's expiring soon" email
     * Allow GET as RequestMethods as this endpoint needs to be accessible from an email link.
     * See <a href="https://gumtree-uk.atlassian.net/browse/GTP-2210">GTP-2210</a>
     *
     * @param advertId - advert id to delete
     * @return redirect to manage ads page showing inactive adverts
     */
    @RequestMapping(value = PAGE_PATH + "/{id}", method = RequestMethod.GET)
    public ModelAndView deleteAdvertThroughEmail(@PathVariable("id") String advertId) {

        execute(new NewAdvertStatusApiCall(advertId, new NewStatusBean("DELETED_USER"), authenticatedUserSession));

        Map<String, Object> map = new HashMap<>();
        map.put("responsive", "true");
        map.put("mad-filter-search", "Update");
        map.put("status", "INACTIVE_ADS");

        return createParametrizedRedirectPopulatingModel(ManageAdsController.PAGE_PATH, new ExtendedModelMap(), map, true);
    }

    @RequestMapping(PAGE_PATH + "/success/{id}/{deleteReason}")
    @ResponseStatus(value = HttpStatus.OK)
    public void deleteAdvertGumtreeSuccess(@PathVariable("id") String advertId,
                                           @PathVariable("deleteReason") DeleteReason deleteReason) {

        execute(new GumtreeSuccessApiCall(advertId, deleteReason, authenticatedUserSession));
    }

    private Map<String, Object> toMap(String key, Object value) {
        Map<String, Object> map = new HashMap<>();
        map.put(key, value);
        return map;
    }
}
