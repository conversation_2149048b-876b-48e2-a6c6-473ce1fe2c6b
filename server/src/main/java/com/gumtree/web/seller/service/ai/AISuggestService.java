package com.gumtree.web.seller.service.ai;

import org.json.JSONObject;

import java.util.List;
import java.util.Map;

public interface AISuggestService {

    public List<Long> getSuggestedCategories(String input, String type);

    public Map<String, String> getSuggestedAttributes(String input, String type, String categoryName);

    public List<String> getSuggestedTitles(String input);

    public Map<String, String> getSuggestedTitleAndDesc(String categoryName, JSONObject attributes, String title, String desc, String images);
}
