package com.gumtree.web.seller.page.manageads.mycompany.model;

import com.gumtree.api.Account;
import com.gumtree.util.model.Link;
import com.gumtree.web.seller.page.common.SelectableValue;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.page.manageads.model.ManageAdsAccountSelectionFormBean;
import com.gumtree.web.seller.page.manageads.mycompany.PackageUsageController;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public final class PackageUsageModel extends CommonModel {

    private Long packageTypeId;

    private String from;

    private String to;

    private String status;

    private List<SelectableValue> packageTypes;

    private List<DisplayCreditPackageUsage> packageUsages;

    private int page;

    private int pageSize;

    private boolean lightbox;

    private ManageAdsAccountSelectionFormBean accountSelectionForm;

    private Account account;

    private PackageUsageModel(CoreModel core, Builder builder) {
        super(core);
        this.packageTypeId = builder.packageTypeId;
        this.from = builder.from;
        this.to = builder.to;
        this.status = builder.status;
        this.packageTypes = builder.packageTypes;
        this.packageUsages = builder.packageUsages;
        this.page = builder.page;
        this.pageSize = builder.pageSize;
        this.lightbox = builder.lightbox;
        this.account = builder.account;
        this.accountSelectionForm = builder.accountSelectionForm;
    }

    public List<PackageStatusFilter> getStatusFilters() {
        return Arrays.asList(PackageStatusFilter.values());
    }

    public String getFormAction() {
        return PackageUsageController.FULL_PAGE_PATH;
    }

    public Account getAccount() {
        return account;
    }

    public Long getPackageTypeId() {
        return packageTypeId;
    }

    public String getFrom() {
        return from;
    }

    public String getTo() {
        return to;
    }

    public String getStatus() {
        return status;
    }

    public List<SelectableValue> getPackageTypes() {
        return packageTypes;
    }

    public List<DisplayCreditPackageUsage> getPackageUsages() {
        return packageUsages;
    }

    public int getPage() {
        return page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public boolean isLightbox() {
        return lightbox;
    }

    public ManageAdsAccountSelectionFormBean getAccountSelectionForm() {
        return accountSelectionForm;
    }

    public static final class Builder {

        private final ManageAdsHelper manageAdsHelper;

        private Link currentPage;

        private Long packageTypeId;

        private String from;

        private String to;

        private String status;

        private List<SelectableValue> packageTypes;

        private List<DisplayCreditPackageUsage> packageUsages;

        private int page;

        private int pageSize;

        private boolean lightbox;

        private ManageAdsAccountSelectionFormBean accountSelectionForm;

        private Account account;

        public Builder(ManageAdsHelper manageAdsHelper) {
            this.manageAdsHelper = manageAdsHelper;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = lightbox ? Page.PackageUsageLight : Page.PackageUsage;
            ModelAndView modelAndView = new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new PackageUsageModel(coreModelBuilder.build(page), this));

            Map<String, Object> rawModel = modelAndView.getModel();
            coreModelBuilder.withTitle("Package usage");
            manageAdsHelper.addManageAdsUrls(rawModel, currentPage);
            return modelAndView;
        }

        public Builder withCurrentPage(Link currentPage) {
            this.currentPage = currentPage;
            return this;
        }

        public Builder withPackageTypeId(Long packageTypeId) {
            this.packageTypeId = packageTypeId;
            return this;
        }

        public Builder withFrom(String from) {
            this.from = from;
            return this;
        }

        public Builder withTo(String to) {
            this.to = to;
            return this;
        }

        public Builder withStatus(String status) {
            this.status = status;
            return this;
        }

        public Builder withPackageTypes(List<SelectableValue> packageTypes) {
            this.packageTypes = packageTypes;
            return this;
        }

        public Builder withPackageUsages(List<DisplayCreditPackageUsage> packageUsages) {
            this.packageUsages = packageUsages;
            return this;
        }

        public Builder withPage(int page) {
            this.page = page;
            return this;
        }

        public Builder withPageSize(int pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder withLightbox(boolean lightbox) {
            this.lightbox = lightbox;
            return this;
        }

        public Builder withAccountSelectionForm(ManageAdsAccountSelectionFormBean accountSelectionForm) {
            this.accountSelectionForm = accountSelectionForm;
            return this;
        }

        public Builder withAccount(Account account) {
            this.account = account;
            return this;
        }
    }
}
