package com.gumtree.web.seller.service.ai;

import com.gumtree.util.HttpUtils;
import com.gumtree.util.url.UrlScheme;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
@Slf4j
public class AISuggestServiceImpl implements AISuggestService{

    private final UrlScheme urlScheme;

    private final HttpUtils httpUtils;

    @Autowired
    public AISuggestServiceImpl(HttpUtils httpUtils, UrlScheme urlScheme) {
        this.httpUtils = httpUtils;
        this.urlScheme = urlScheme;
    }

    @Override
    public List<Long> getSuggestedCategories(String input, String type) {
        List<Long> result = new ArrayList<>();
        if(StringUtils.isEmpty( input)){
            return result;
        }

        JSONObject requestBody = new JSONObject();
        requestBody.put("reqId", UUID.randomUUID().toString());
        switch ( type) {
            case "img":
                requestBody.put("imageUrls", Arrays.asList(input.split(",")));
                break;
            default:
                requestBody.put("title", input);
                break;
        }

        try {
            String urlForCateSuggest = urlScheme.urlforPredictCate();
            JSONObject response = httpUtils.sendJsonPost(urlForCateSuggest, requestBody);
            log.info("getSuggestedCategories->url:{},request:{},Response: {}", urlForCateSuggest, requestBody, response);

            if(response.getInt("code")==200 && response.get("data")!=null){
                JSONArray dataArray = response.getJSONArray("data");
                for (int i = 0; i < dataArray.length(); i++) {
                    result.add(dataArray.getLong(i));
                }
            }

        } catch (Exception e) {
            log.error("getSuggestedCategories error: input=" + input, e);
        }

        return result;
    }

    @Override
    public Map<String, String> getSuggestedAttributes(String input, String type, String categoryName) {
        Map<String, String> result = Collections.EMPTY_MAP;

        if(StringUtils.isEmpty(categoryName) && StringUtils.isEmpty(type) && StringUtils.isEmpty(input)){
            return result;
        }

        JSONObject requestBody = new JSONObject();
        requestBody.put("reqId", UUID.randomUUID().toString());
        requestBody.put("categoryName", categoryName);
        requestBody.put("title", type.equals("title") ? input : "");
        requestBody.put("imageUrls", type.equals("img") ? Arrays.asList(input.split(",")) : Collections.EMPTY_LIST);

        try {
            String urlforAttributeSuggest = urlScheme.urlforAttributeSuggest();
            JSONObject response = httpUtils.sendJsonPost(urlforAttributeSuggest, requestBody);
            log.info("getSuggestedAttributes->URL:{},request{},Response: {}", urlforAttributeSuggest, requestBody, response);

            if(response != null && response.getInt("code")==200 && response.get("data")!=null){
                result = toStringMap(response.getJSONObject("data"));
            }

        } catch (Exception e) {
            log.error("getSuggestedAttributes->Error, categoryName=" + categoryName + ",input=" + input , e);
        }

        return result;
    }

    @Override
    public List<String> getSuggestedTitles(String input) {
        List<String> result = new ArrayList<>();

        if(StringUtils.isEmpty(input)){
            return result;
        }

        try {
            String urlForTitleSuggest = urlScheme.urlforTitleSuggest();
            String encodedInput = URLEncoder.encode(input, StandardCharsets.UTF_8.toString());
            JSONObject response = httpUtils.sendJsonGet(urlForTitleSuggest, UUID.randomUUID().toString(), encodedInput);
            log.info("getSuggestedTitles->url:{},request:{},Response:{} ", urlForTitleSuggest, input, response);

            if(response.getInt("code")==200 && response.get("data")!=null ){
                JSONArray dataArray = response.getJSONArray("data");
                for (int i = 0; i < dataArray.length(); i++) {
                    result.add(dataArray.getString(i));
                }
            }

        } catch (Exception e) {
            log.error("getSuggestedTitles->Error, input= " + input + ",e=", e);
        }

        return result;
    }

    @Override
    public Map<String, String> getSuggestedTitleAndDesc(String categoryName, JSONObject attributes, String title, String desc, String images) {
        Map<String, String> result = Collections.EMPTY_MAP;

        JSONObject requestBody = new JSONObject();
        requestBody.put("reqId", UUID.randomUUID().toString());
        requestBody.put("categoryName", categoryName);
        requestBody.put("title", title);
        requestBody.put("originDesc", desc);
        if(StringUtils.isNotEmpty(images) && images.contains(",")){
            requestBody.put("imageUrls", Arrays.asList(images.split(",")));
        }
        requestBody.put("attributes", attributes);


        try {
            String url = urlScheme.urlforDescSuggest();
            JSONObject response = httpUtils.sendJsonPost(url, requestBody);
            log.info("getSuggestedTitleAndDesc->url:{}, request:{}, Response:{} ", url, requestBody, response);

            if(response.getInt("code")==200 && response.get("data") != null){
                result = toStringMap(response.getJSONObject("data"));
            }

        } catch (Exception e) {
            log.error("getSuggestedTitleAndDesc->Error, categoryName=" + categoryName, e);
        }

        return result;
    }

    public static Map<String, String> toStringMap(JSONObject jsonObject) {
        Map<String, String> map = new HashMap<>();

        if (jsonObject == null) {
            return map;
        }

        Iterator<String> keys = jsonObject.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            Object value = jsonObject.get(key);

            if (value == null) {
                map.put(key, (String) value);
            }else{
                map.put(key, String.valueOf( value));
            }
        }

        return map;
    }

}
