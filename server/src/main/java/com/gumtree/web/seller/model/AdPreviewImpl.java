package com.gumtree.web.seller.model;


import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.gumtree.api.AdStatus;
import com.gumtree.liveadsearch.model.Category;
import com.gumtree.common.util.StringUtils;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.web.common.format.PostingTimeFormatter;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_ENGINE_SIZE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_FUEL_TYPE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_MILEAGE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_REGISTRATION_YEAR;
import static com.gumtree.common.util.StringUtils.trimOnWhitespace;

/**
 * A specialized version of an advert for being displayed in an overview.
 *
 * <AUTHOR>
 */
public class AdPreviewImpl implements AdPreview {
    private Long id;
    private String title;
    private String description;
    private String descriptionExtensionOrNull = null;
    private String advertUrl;
    private String editAdvertUrl;
    private String restoreAdvertUrl;
    private String displayPrice;
    private String displayLocationText;
    private String displayDate;
    private String seoTimeStamp;
    private String postedTime;
    private Date postedTimeAsDate;
    private Image mainImage;
    private String previewUrl;
    private String thumbnailUrl;
    private boolean isUrgent;
    private boolean isFeatured;
    private String contactEmail;
    private String createdTime;
    private Date createdTimeAsDate;
    private String lastModifiedTime;
    private Date lastModifiedTimeAsDate;
    private AdStatus status;
    private String sellerType;
    private boolean publishedRecently;
    private String customDateText;
    private Boolean categoryReadOnly = Boolean.FALSE;
    private Long categoryId;
    private Long l1CategoryId;
    private Long l2CategoryId;
    private Long l3CategoryId;
    private Optional<Double> distance;
    private int numberOfImages;
    private int numberOfVideos;
    private String markAsLabel;
    private String reportAdUrl;
    private boolean markedAsSold;


    AdPreviewImpl() {
        // nothing to do
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public String getTitle() {
        return title;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getAdvertUrl() {
        return advertUrl;
    }

    @Override
    public String getEditAdvertUrl() {
        return editAdvertUrl;
    }

    @Override
    public String getRestoreAdvertUrl() {
        return restoreAdvertUrl;
    }

    @Override
    public String getDisplayPrice() {
        return displayPrice;
    }

    @Override
    public String getDisplayLocationText() {
        return displayLocationText;
    }

    @Override
    public String getDisplayDate() {
        return displayDate;
    }

    @Override
    public String getSeoTimeStamp() {
        return seoTimeStamp;
    }

    @Override
    public Image getMainImage() {
        return mainImage;
    }

    @Override
    public boolean isUrgent() {
        return isUrgent;
    }

    @Override
    public String getPostedTime() {
        if (postedTime == null && postedTimeAsDate != null) {
            postedTime = PostingTimeFormatter.formatPostingTime(postedTimeAsDate, new Date());
        }
        return postedTime;
    }

    @Override
    public Date getPostedTimeAsDate() {
        return postedTimeAsDate;
    }

    @Override
    public boolean isFeatured() {
        return isFeatured;
    }

    @Override
    public int getNumberOfImages() {
        return numberOfImages;
    }

    @Override
    public int getNumberOfVideos() {
        return numberOfVideos;
    }

    public void setMarkAsLabel(String markAsLabel) {
        this.markAsLabel = markAsLabel;
    }

    @Override
    public String getMarkAsLabel() {
        return markAsLabel;
    }

    @Override
    public String getReportAdUrl() {
        return reportAdUrl;
    }

    /**
     * @param id the id to set
     */
    private void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getPreviewUrl() {
        return previewUrl;
    }

    @Override
    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    @Override
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    @Override
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    @Override
    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    @Override
    public boolean getPublishedRecently() {
        return publishedRecently;
    }

    @Override
    public void setPublishedRecently(boolean publishedRecently) {
        this.publishedRecently = publishedRecently;
    }

    public void setCategoryReadOnly(Boolean categoryReadOnly) {
        this.categoryReadOnly = categoryReadOnly;
    }

    @Override
    public Boolean getCategoryReadOnly() {
        return categoryReadOnly;
    }

    @Override
    public Long getCategoryId() {
        return categoryId;
    }

    private void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    @Override
    public Long getL1CategoryId() {
        return l1CategoryId;
    }

    private void setL1CategoryId(Long l1CategoryId) {
        this.l1CategoryId = l1CategoryId;
    }

    public void setDistance(Optional<Double> distance) {
        this.distance = distance;
    }

    public void setReportAdUrl(String reportAdUrl) {
        this.reportAdUrl = reportAdUrl;
    }

    /**
     * @param title the title to set
     */
    private void setTitle(String title) {
        this.title = title;
    }

    /**
     * @param description the description to set
     */
    private void setDescription(String description) {
      this.description = description;
    }

    private void setDescriptionExtension(String descriptionExtension) {
        this.descriptionExtensionOrNull = descriptionExtension;
    }

    /**
     * @param advertUrl the advertUrl to set
     */
    private void setAdvertUrl(String advertUrl) {
        this.advertUrl = advertUrl;
    }

    /**
     * @param editAdvertUrl the editAdvertUrl to set
     */
    private void setEditAdvertUrl(String editAdvertUrl) {
        this.editAdvertUrl = editAdvertUrl;
    }

    /**
     * @param restoreAdvertUrl the restoreAdvertUrl to set
     */
    private void setRestoreAdvertUrl(String restoreAdvertUrl) {
        this.restoreAdvertUrl = restoreAdvertUrl;
    }

    /**
     * @param displayPrice the displayPrice to set
     */
    private void setDisplayPrice(String displayPrice) {
        this.displayPrice = displayPrice;
    }

    /**
     * @param displayLocationText the displayLocationText to set
     */
    private void setDisplayLocationText(String displayLocationText) {
        this.displayLocationText = displayLocationText;
    }

    /**
     * @param displayDate the displayDate to set
     */
    private void setDisplayDate(String displayDate) {
        this.displayDate = displayDate;
    }

    /**
     * @param seoTimeStamp the seoTimeStamp to set
     */
    private void setSeoTimeStamp(String seoTimeStamp) {
        this.seoTimeStamp = seoTimeStamp;
    }

    /**
     * @param postedTime the postedTime to set
     */
    private void setPostedTime(String postedTime) {
        this.postedTime = postedTime;
    }

    private void setPostedTimeAsDate(Date postedTimeAsDate) {
        this.postedTimeAsDate = postedTimeAsDate;
    }

    /**
     * @param mainImage the mainImage to set
     */
    private void setMainImage(Image mainImage) {
        this.mainImage = mainImage;
    }

    /**
     * @param isUrgent the isUrgent to set
     */
    private void setUrgent(boolean isUrgent) {
        this.isUrgent = isUrgent;
    }



    @Override
    public void setFeatured(boolean isFeatured) {
        this.isFeatured = isFeatured;
    }

    @Override
    public String getAltTag() {

        if (StringUtils.hasText(this.displayLocationText)) {
            return this.title + " " + this.displayLocationText;
        }
        return this.title;
    }

    @Override
    public String getCreatedTime() {
        if (createdTime == null && createdTimeAsDate != null) {
            createdTime = PostingTimeFormatter.formatPostingTime(createdTimeAsDate, new Date());
        }
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public Date getCreatedTimeAsDate() {
        return createdTimeAsDate;
    }

    public void setCreatedTimeAsDate(Date createdTimeAsDate) {
        this.createdTimeAsDate = createdTimeAsDate;
    }

    @Override
    public String getLastModifiedTime() {
        if (lastModifiedTime == null && lastModifiedTimeAsDate != null) {
            lastModifiedTime = PostingTimeFormatter.formatPostingTime(lastModifiedTimeAsDate, new Date());
        }
        return lastModifiedTime;
    }

    public void setLastModifiedTime(String lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    @Override
    public Date getLastModifiedTimeAsDate() {
        return lastModifiedTimeAsDate;
    }

    public void setLastModifiedTimeAsDate(Date lastModifiedTimeAsDate) {
        this.lastModifiedTimeAsDate = lastModifiedTimeAsDate;
    }

    @Override
    public AdStatus getStatus() {
        return status;
    }

    public void setStatus(AdStatus status) {
        this.status = status;
    }

    @Override
    public String getSellerType() {
        return sellerType;
    }

    @Override
    public String getDistance() {
        if (distance.isPresent()) {
            return calculateRange(Math.round(Math.ceil(distance.get())),
                    Math.round(distance.get() * 100.0) / 100.0);
        }
        return "";
    }

    @Override
    public String getDescriptionExtensionOrNull() {
        return descriptionExtensionOrNull;
    }

    public boolean isMarkedAsSold() {
        return markedAsSold;
    }

    public void setMarkedAsSold(boolean markedAsSold) {
        this.markedAsSold = markedAsSold;
    }

    private String calculateRange(long ceilDistance, double dist) {
        String dif;
        if (dist >= 0 && dist <= 0.25){
             dif = (Math.abs(0 - dist) > Math.abs(0.25 - dist)) ? "1/4 mile" : "0 miles";
        } else if (dist > 0.25 && dist <= 0.50) {
             dif = (Math.abs(0.26 - dist) > Math.abs(0.50 - dist)) ? "1/2 mile" : "1/4 mile";
        } else {
             dif = String.valueOf(ceilDistance) + (ceilDistance == 1 ? " mile" : " miles");
        }
        return dif;
    }

    public void setSellerType(String sellerType) {
        this.sellerType = sellerType;
    }

    @Override
    public String getCustomDateText() {
        return customDateText;
    }

    private void setCustomDateText(String text) {
        customDateText = text;
    }

    private void setNumberOfImages(int numberOfImages) {
        this.numberOfImages = numberOfImages;
    }

    private void setNumberOfVideos(int numberOfVideos) {
        this.numberOfVideos = numberOfVideos;
    }

    @Override
    public Long getL2CategoryId() {
        return l2CategoryId;
    }

    public void setL2CategoryId(Long l2CategoryId) {
        this.l2CategoryId = l2CategoryId;
    }

    @Override
    public Long getL3CategoryId() {
        return l3CategoryId;
    }

    public void setL3CategoryId(Long l3CategoryId) {
        this.l3CategoryId = l3CategoryId;
    }

    /* (non-Javadoc)
    * @see java.lang.Object#hashCode()
    */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof AdPreviewImpl)) {
            return false;
        }
        AdPreviewImpl other = (AdPreviewImpl) obj;
        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }
        return true;
    }

    /**
     * Builder for {@link AdPreviewImpl}
     *
     * <AUTHOR>
     */
    public static class Builder {
        private static final int MAX_DESCRIPTION_LENGTH_NO_EXTENDING = 166;
        private static final int MAX_DESCRIPTION_LENGTH_WITH_EXTENDING = 136;
        private static final int MAX_FULL_DESCRIPTION_DISPLAY = MAX_DESCRIPTION_LENGTH_WITH_EXTENDING + 360;
        private static final double UPPERCASE_CHARACTERS_AMOUNT_THRESHOLD = 0.4;
        private static final int MAX_DESCRIPTION_LENGTH_FOR_UPPERCASE_CHARACTERS = 124;
        private Long id;
        private String title;
        private String description;
        private List<Long> extendedDescriptionCategories = Lists.newArrayList();
        private String advertUrl;
        private String editAdvertUrl;
        private String restoreAdvertUrl;
        private String displayPrice;
        private String displayLocationText;
        private String displayDate;
        private String seoTimeStamp;
        private String postedTime;
        private Date postedTimeAsDate;
        private Image mainImage;
        private String previewUrl;
        private String thumbnailUrl;
        private boolean isUrgent;
        private boolean isFeatured;
        private String contactEmail;
        private String createdTime;
        private Date createdTimeAsDate;
        private String lastModifiedTime;
        private Date lastModifiedTimeAsDate;
        private AdStatus status;
        private String sellerType;
        private boolean publishedRecently;
        private Optional<String> customDate = Optional.absent();
        private Boolean isCategoryReadOnly = Boolean.FALSE;
        private Long categoryId;
        private Long l1CategoryId;
        private Long l2CategoryId;
        private Long l3CategoryId;
        private Optional<Double> distance = Optional.absent();
        private Collection<Attribute> attributes;
        private Collection<Category> categories;
        private int numOfImages;
        private int numOfVideos;
        private String markAsLabel;
        private String reportAdUrl;
        private boolean markedAsSold;

        private static final Ordering<Attribute> ATTRIBUTE_ORDERING = Ordering.explicit(
                ImmutableList.of(
                        VEHICLE_REGISTRATION_YEAR.getName(),
                        VEHICLE_MILEAGE.getName(),
                        VEHICLE_ENGINE_SIZE.getName(),
                        VEHICLE_FUEL_TYPE.getName()))
                .onResultOf(new Function<Attribute, String>() {
            @Override
            public String apply(@Nullable Attribute input) {
                return input.getType();
            }
        });


        /**
         * @param value the id
         * @return same instance
         */
        public final Builder id(Long value) {
            this.id = value;
            return this;
        }

        /**
         * @param value the title
         * @return same instance
         */
        public final Builder title(String value) {
            this.title = value;
            return this;
        }

        public final Builder extendedDescriptionL1Categories(List<Long> categories) {
            this.extendedDescriptionCategories = (categories != null) ? categories : Lists.<Long>newArrayList();
            return this;
        }

        /**
         * @param value the description
         * @return same instance
         */
        public final Builder description(String value) {
            this.description = value;
            return this;
        }

        /**
         * @param value the advertUrl
         * @return same instance
         */
        public final Builder advertUrl(String value) {
            this.advertUrl = value;
            return this;
        }

        /**
         * @param value the report ad base url
         * @return same instance
         */
        public final Builder reportAdUrl(String value) {
            this.reportAdUrl = value;
            return this;
        }

        /**
         * @param value the editAdvertUrl
         * @return same instance
         */
        public final Builder editAdvertUrl(String value) {
            this.editAdvertUrl = value;
            return this;
        }

        /**
         * @param value the restoreAdvertUrl
         * @return same instance
         */
        public final Builder restoreAdvertUrl(String value) {
            this.restoreAdvertUrl = value;
            return this;
        }

        /**
         * @param value the displayPrice
         * @return same instance
         */
        public final Builder displayPrice(String value) {
            this.displayPrice = value;
            return this;
        }

        /**
         * @param value the displayLocationText
         * @return same instance
         */
        public final Builder displayLocationText(String value) {
            this.displayLocationText = value;
            return this;
        }

        /**
         * @param value the displayDate
         * @return same instance
         */
        public final Builder displayDate(String value) {
            this.displayDate = value;
            return this;
        }

        /**
         * @param value the seoTimeStamp
         * @return same instance
         */
        public final Builder seoTimeStamp(String value) {
            this.seoTimeStamp = value;
            return this;
        }

        /**
         * @param value the postedTime
         * @return same instance
         */
        public final Builder postedTime(String value) {
            this.postedTime = value;
            return this;
        }

        /**
         * @param value the postedTime
         * @return same instance
         */
        public final Builder postedTimeAsDate(Date value) {
            this.postedTimeAsDate = value;
            return this;
        }

        /**
         * @param value the advert creation time
         * @return same instance
         */
        public final Builder createdTime(String value) {
            this.createdTime = value;
            return this;
        }

        /**
         * @param value the advert creation time
         * @return same instance
         */
        public final Builder createdTimeAsDate(Date value) {
            this.createdTimeAsDate = value;
            return this;
        }

        /**
         * @param value the advert's last modification time
         * @return same instance
         */
        public final Builder lastModifiedTime(String value) {
            this.lastModifiedTime = value;
            return this;
        }

        /**
         * @param value the advert's last modification time
         * @return same instance
         */
        public final Builder lastModifiedTimeAsDate(Date value) {
            this.lastModifiedTimeAsDate = value;
            return this;
        }

        /**
         * @param value the mainImage
         * @return same instance
         */
        public final Builder mainImage(Image value) {
            this.mainImage = value;
            return this;
        }

        /**
         * @param value the preview url
         * @return same instance
         */
        public final Builder previewUrl(String value) {
            this.previewUrl = value;
            return this;
        }

        /**
         * @param value the thumbnail url
         * @return same instance
         */
        public final Builder thumbUrl(String value) {
            this.thumbnailUrl = value;
            return this;
        }

        /**
         * @param value the isUrgent flag
         * @return same instance
         */
        public final Builder isUrgent(boolean value) {
            this.isUrgent = value;
            return this;
        }

        /**
         * @param value the featured flag
         * @return same instance
         */
        public final Builder isFeatured(boolean value) {
            this.isFeatured = value;
            return this;
        }

        /**
         * Set the value of the contact email
         *
         * @param value contact email
         * @return contact email
         */
        public final Builder contactEmail(String value) {
            this.contactEmail = value;
            return this;
        }

        /**
         * Set the status
         *
         * @param status status
         * @return same instance
         */
        public final Builder status(AdStatus status) {
            this.status = status;
            return this;
        }

        public final Builder customDateText(Optional<String> date) {
            this.customDate = date != null ? date : Optional.<String>absent();
            return this;
        }

        /**
         * Set the seller type
         *
         * @param sellerType seller type
         * @return same instance
         */
        public final Builder sellerType(String sellerType) {
            this.sellerType = sellerType;
            return this;
        }

        public final Builder publishedRecently(boolean v) {
            this.publishedRecently = v;
            return this;
        }

        public final Builder isCategoryReadOnly(Boolean value) {
            this.isCategoryReadOnly = value;
            return this;
        }

        public final Builder categoryId(Long value) {
            this.categoryId = value;
            return this;
        }

        public final Builder l1CategoryId(Long value) {
            this.l1CategoryId = value;
            return this;
        }

        public final Builder l2CategoryId(Long value) {
            this.l2CategoryId = value;
            return this;
        }

        public final Builder l3CategoryId(Long value) {
            this.l3CategoryId = value;
            return this;
        }

        public final Builder distance(Double distance) {
            this.distance = distance != null ? Optional.of(distance) : Optional.<Double>absent();
            return this;
        }

        public final Builder attributes(Collection<Attribute> attributes) {
            this.attributes = attributes;
            return this;
        }

        public final Builder categories(Collection<Category> categories) {
            this.categories = categories;
            return this;
        }

        public final Builder numberOfImages(int numOfImages) {
            this.numOfImages = numOfImages;
            return this;
        }

        public final Builder numberOfVideos(int numOfVideos) {
            this.numOfVideos = numOfVideos;
            return this;
        }

        /**
         * @param value the 'Mark As' label
         * @return same instance
         */
        public final Builder markAsLabel(String value) {
            this.markAsLabel = value;
            return this;
        }

        /**
         * @param value the sold flag
         * @return same instance
         */
        public final Builder markedAsSold(boolean value) {
            this.markedAsSold = value;
            return this;
        }

        /**
         * @return a new instance
         */
        public final AdPreview build() {

            if (isVehicleCategory()) {
                return buildVehicleAdPreview();
            }
            return buildAdPreview();
        }

        private VehicleAdPreviewImpl buildVehicleAdPreview() {
            VehicleAdPreviewImpl vehicleAdPreview = new VehicleAdPreviewImpl();
            populateAdPreview(vehicleAdPreview);
            populateVehicleAdPreview(vehicleAdPreview);
            return vehicleAdPreview;
        }

        private AdPreviewImpl buildAdPreview() {
            AdPreviewImpl adPreview = new AdPreviewImpl();
            populateAdPreview(adPreview);
            return adPreview;
        }

        private void populateAdPreview(AdPreviewImpl adPreview) {
            adPreview.setId(id);
            adPreview.setAdvertUrl(advertUrl);
            adPreview.setEditAdvertUrl(editAdvertUrl);
            adPreview.setRestoreAdvertUrl(restoreAdvertUrl);
            adPreview.setDisplayDate(displayDate);
            adPreview.setDisplayLocationText(displayLocationText);
            adPreview.setDisplayPrice(displayPrice);
            adPreview.setMainImage(mainImage);
            adPreview.setPreviewUrl(previewUrl);
            adPreview.setThumbnailUrl(thumbnailUrl);
            adPreview.setPostedTime(postedTime);
            adPreview.setPostedTimeAsDate(postedTimeAsDate);
            adPreview.setSeoTimeStamp(seoTimeStamp);
            adPreview.setTitle(title);
            adPreview.setUrgent(isUrgent);
            adPreview.setFeatured(isFeatured);
            adPreview.setContactEmail(contactEmail);
            adPreview.setCreatedTime(createdTime);
            adPreview.setCreatedTimeAsDate(createdTimeAsDate);
            adPreview.setLastModifiedTime(lastModifiedTime);
            adPreview.setLastModifiedTimeAsDate(lastModifiedTimeAsDate);
            adPreview.setStatus(status);
            adPreview.setSellerType(sellerType);
            adPreview.setPublishedRecently(publishedRecently);
            adPreview.setCustomDateText(evaluateCustomSortFieldValue());
            adPreview.setCategoryReadOnly(isCategoryReadOnly);
            adPreview.setCategoryId(categoryId);
            adPreview.setL1CategoryId(l1CategoryId);
            adPreview.setL2CategoryId(l2CategoryId);
            adPreview.setL3CategoryId(l3CategoryId);
            adPreview.setDistance(distance);
            adPreview.setNumberOfImages(numOfImages);
            adPreview.setNumberOfVideos(numOfVideos);
            adPreview.setMarkAsLabel(markAsLabel);
            adPreview.setReportAdUrl(reportAdUrl);
            adPreview.setMarkedAsSold(markedAsSold);
            if (shouldUseShowMoreSnippet()) {
                setDescriptionWithDescriptionExtension(adPreview);
            } else {
                setDescriptionWithoutDescriptionExtension(adPreview);
            }
        }

        private boolean shouldUseShowMoreSnippet() {
            if (categories == null) {
                return false;
            }
            List<Long> categoryIds = getCategoryIds(categories);
            for (Long snippetCatId : extendedDescriptionCategories) {
                if (categoryIds.contains(snippetCatId)) {
                    return true;
                }
            }
            return false;
        }

        private List<Long> getCategoryIds(Collection<Category> categories) {
            List<Long> categoryIds = new ArrayList(categories.size());
            for (Category cat : categories) {
                categoryIds.add(cat.getId());
            }
            return categoryIds;
        }

        private void setDescriptionWithDescriptionExtension(AdPreviewImpl adPreview) {
            String normalisedDescription = StringUtils.hasText(description) ? description.replaceAll("\\s+", " ") : "";
            if (normalisedDescription.length() > MAX_DESCRIPTION_LENGTH_WITH_EXTENDING) {
                setDescriptionAndDescriptionExtension(adPreview, normalisedDescription);
            } else {
                adPreview.setDescription(normalisedDescription);
            }
        }

        private void setDescriptionAndDescriptionExtension(AdPreviewImpl adPreview, String normalisedDescription) {
            int newDescLength = getMaxDescriptionLengthPenalizingUppercases(
                    MAX_DESCRIPTION_LENGTH_WITH_EXTENDING, normalisedDescription);
            String trimmedDescription = trimOnWhitespace(normalisedDescription, newDescLength);
            String descriptionExtension = getDescriptionExtension(normalisedDescription, trimmedDescription);
            adPreview.setDescription(trimmedDescription);
            adPreview.setDescriptionExtension(descriptionExtension);
        }

        private String getDescriptionExtension(String normalisedDescription, String trimmedDescription) {
            String trimmedDescriptionExtension =
                    trimOnWhitespace(normalisedDescription.substring(trimmedDescription.length()),
                    MAX_FULL_DESCRIPTION_DISPLAY - trimmedDescription.length());
            return normalisedDescription.length() > MAX_FULL_DESCRIPTION_DISPLAY
                    ? trimmedDescriptionExtension + "..."
                    : trimmedDescriptionExtension;
        }

        private void setDescriptionWithoutDescriptionExtension(AdPreviewImpl adPreview) {
            String descToUse = description;
            if (StringUtils.hasText(descToUse)) {
                descToUse = trimDescriptionAddingTreeDotsAtTheEnd(descToUse.replaceAll("[\\s]+", " "));
            }
            adPreview.setDescription(descToUse);
        }

        private String trimDescriptionAddingTreeDotsAtTheEnd(String descr) {
            if (descr.length() > MAX_DESCRIPTION_LENGTH_NO_EXTENDING) {
                int newDescLength = getMaxDescriptionLengthPenalizingUppercases(
                        MAX_DESCRIPTION_LENGTH_NO_EXTENDING - 3, descr);
                descr = StringUtils.trimOnWhitespace(descr, newDescLength) + "...";
            }
            return descr;
        }

        private int getMaxDescriptionLengthPenalizingUppercases(int initialThreshold, String txt) {
            double uppercaseCharacters = countUpperCasecharacters(initialThreshold, txt);
            return uppercaseCharacters / initialThreshold < UPPERCASE_CHARACTERS_AMOUNT_THRESHOLD
                    ? initialThreshold
                    : MAX_DESCRIPTION_LENGTH_FOR_UPPERCASE_CHARACTERS;
        }

        private int countUpperCasecharacters(int initialThreshold, String txt) {
            int upperCaseChars = 0;
            for (int i = 0; i < initialThreshold; i++) {
                if (Character.isUpperCase(txt.charAt(i))) {
                    upperCaseChars++;
                }
            }
            return upperCaseChars;
        }

        private void populateVehicleAdPreview(VehicleAdPreviewImpl vehicleAdPreview) {
            Collection<Attribute> vehicleAttributes = new ArrayList<>();
            if (attributes != null) {
                for (Attribute attribute : attributes) {
                    if (VEHICLE_ENGINE_SIZE.nameEqualTo(attribute.getType())
                            || VEHICLE_MILEAGE.nameEqualTo(attribute.getType())
                            || VEHICLE_FUEL_TYPE.nameEqualTo(attribute.getType())
                            || VEHICLE_REGISTRATION_YEAR.nameEqualTo(attribute.getType())) {
                        vehicleAttributes.add(attribute);
                    }
                }

                ImmutableList<Attribute> attributesSorted = ATTRIBUTE_ORDERING.immutableSortedCopy(vehicleAttributes);
                vehicleAdPreview.setVehicleAttributes(attributesSorted);
            }
        }

        private String evaluateCustomSortFieldValue() {
            return customDate.isPresent() ? customDate.get() : postedTime;
        }

        private boolean isVehicleCategory() {
            return hasCategory(categories, Categories.CARS.getId());
        }

        private boolean hasCategory(Collection<Category> categories, Long categoryId) {
            if (categories != null) {
                for (Category category : categories) {
                    if (categoryId.equals(category.getId())) {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}
