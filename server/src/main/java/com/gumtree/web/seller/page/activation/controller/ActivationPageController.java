package com.gumtree.web.seller.page.activation.controller;


import com.gumtree.api.Ad;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.util.StringUtils;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.UserActivationRequest;
import com.gumtree.util.http.HttpRequestUtils;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.login.controller.LoginController;
import com.gumtree.web.seller.page.login.controller.LoginForm;
import com.gumtree.web.seller.page.login.model.LoginModel;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.seller.service.user.logout.UserLogoutService;
import com.gumtree.web.zeno.userregistration.UserActivationSuccessZenoEvent;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.service.ZenoService;
import org.apache.shiro.SecurityUtils;
import org.jboss.resteasy.client.ClientResponseFailure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gumtree.web.seller.page.activation.controller.ActivationPageController.PAGE_PATH;

@Controller
@GumtreePage(PageType.UserActivationSuccess)
@RequestMapping(PAGE_PATH)
@GoogleAnalytics
public final class ActivationPageController extends BaseSellerController {
    private static final Logger LOG = LoggerFactory.getLogger(ActivationPageController.class);

    private static final String RESULT_PAGE_SUBPATH = "/result";

    public static final String PAGE_PATH = "/activate-user";

    static final String USER_ID_PARAM = "id";

    static final String ACTIVATION_KEY_PARAM = "key";

    static final String NEW_REGISTRATION_PARAM = "new-registration";

    private final BushfireApi bushfireApi;

    private final LoginUtils loginUtils;

    private final ZenoService zenoService;

    private final SellerProperty.RecaptchaEnabled recaptchaEnabled;

    private final UserLogoutService userLogoutService;

    private final UserServiceFacade userServiceFacade;

    private final ParameterEncryption parameterEncryption;

    @Autowired
    public ActivationPageController(CookieResolver cookieResolver,
                                    CategoryModel categoryModel,
                                    ApiCallExecutor apiCallExecutor,
                                    ErrorMessageResolver messageResolver,
                                    UrlScheme urlScheme,
                                    BushfireApi bushfireApi,
                                    LoginUtils loginUtils,
                                    ZenoService zenoService,
                                    UserSessionService userSessionService,
                                    SellerProperty.RecaptchaEnabled recaptchaEnabled,
                                    UserLogoutService userLogoutService,
                                    UserServiceFacade userServiceFacade,
                                    ParameterEncryption parameterEncryption) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.bushfireApi = bushfireApi;
        this.loginUtils = loginUtils;
        this.zenoService = zenoService;
        this.recaptchaEnabled = recaptchaEnabled;
        this.userLogoutService = userLogoutService;
        this.userServiceFacade = userServiceFacade;
        this.parameterEncryption = parameterEncryption;
    }

    /**
     * @return the login form action
     */
    @ModelAttribute("loginFormAction")
    public String loginFormAction() {
        return LoginController.PAGE_PATH;
    }

    @RequestMapping(method = RequestMethod.GET)
    public final ModelAndView handleRequest(@RequestParam Map<String, String> params) {

        Map<String,String> hiddenParams = new HashMap<>();
        if (StringUtils.hasText(params.get(USER_ID_PARAM))) {
            hiddenParams.put(USER_ID_PARAM, params.get(USER_ID_PARAM));
        }
        if (StringUtils.hasText(params.get(ACTIVATION_KEY_PARAM))) {
            hiddenParams.put(ACTIVATION_KEY_PARAM, params.get(ACTIVATION_KEY_PARAM));
        }
        if (StringUtils.hasText(params.get(NEW_REGISTRATION_PARAM))) {
            hiddenParams.put(NEW_REGISTRATION_PARAM, params.get(NEW_REGISTRATION_PARAM));
        }
        if (hiddenParams.size() < 2 || !hiddenParams.containsKey(USER_ID_PARAM) || !hiddenParams.containsKey(ACTIVATION_KEY_PARAM)) {
            return redirect(ActivationFailureController.PAGE_PATH);
        }

        params.put(ENCRYPTED_PARAMETER_MAP_NAME, parameterEncryption.encryptMapAndUrlEncode(hiddenParams));
        return createRedirectWithTracking(PAGE_PATH + RESULT_PAGE_SUBPATH, params);
    }

    @RequestMapping(method = RequestMethod.GET, value = RESULT_PAGE_SUBPATH)
    public final ModelAndView activateUser(HttpServletRequest request, HttpServletResponse response) {

        String encryptedParameterMap = request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME);

        if(StringUtils.hasText(encryptedParameterMap)) {
            Map<String, String> decryptedParameterMap = parameterEncryption.decryptUrlEncodedParameterMap(encryptedParameterMap);
            String username = decryptedParameterMap.get(USER_ID_PARAM);
            String activationKey = decryptedParameterMap.get(ACTIVATION_KEY_PARAM);
            String newRegistrationKey = decryptedParameterMap.get(NEW_REGISTRATION_PARAM);

            if (userIsAlreadyActive(username)) {
                return aUserIsLoggedIn() ? redirect("/") : redirect(LoginController.PAGE_PATH);
            }

            if (activateUser(username, activationKey)) {
                if (aUserIsLoggedIn()) {
                    userLogoutService.logoutUser(request, response);
                }
                return showPostActivationPage(request, username, newRegistrationKey);
            }

            return redirectWithParameters(ActivationFailureController.PAGE_PATH, ENCRYPTED_PARAMETER_MAP_NAME, encryptedParameterMap);
        } else {
            LOG.info("Encrypted parameter map param missing {}", request.getRequestURL());
            return redirect(ActivationFailureController.PAGE_PATH);
        }
    }

    private boolean aUserIsLoggedIn() {
        return SecurityUtils.getSubject().isAuthenticated();
    }

    private boolean userIsAlreadyActive(String username) {
        try {
            return UserStatus.ACTIVE == bushfireApi.userApi().getUser(username).getStatus();
        } catch (ClientResponseFailure exception) {
            LOG.error("Unable to retrieve status for user {}", exception.getMessage());
        }
        return false;
    }

    private boolean activateUser(String username, String activationKey) {
        try {
            UserActivationRequest activationRequest = new UserActivationRequest();
            activationRequest.setUsername(username);
            activationRequest.setToken(activationKey);
            ApiResponse<Boolean> activationResp = userServiceFacade.activate(activationRequest);

            // If no errors then activation is successful
            if (activationResp.isDefined()) {
                return true;
            } else {
                LOG.info("User activation has failed. ActKey [ {} ]. Errors: {}", activationKey, activationResp.getError());
            }
        } catch (Exception ex) {
            LOG.error("User activation has failed. ActKey [ {} ]", activationKey, ex);
        }
        return false;
    }


    private ModelAndView showPostActivationPage(HttpServletRequest request, String username, String newRegistrationKey) {
        User user = bushfireApi.userApi().getUser(username);
        loginUtils.storeNewUserEmailAddressInSession(username);

        Long accountId = !user.getAccountIds().isEmpty() ? user.getAccountIds().get(0) : null;
        // accountId should not ever ever be null
        List<Ad> adsForUser = bushfireApi.accountApi().getAdverts(accountId);

        LoginForm loginForm = new LoginForm();
        loginForm.setUsername(username);
        loginForm.setNewUser(false);

        LoginModel.Builder loginModel = LoginModel.builder()
                .withLoginForm(loginForm)
                .withRecaptchaEnabled(recaptchaEnabled.get())
                .withSuccessTitle(resolveMessage("activate.success"));

        if (adsForUser.isEmpty()) {
            loginModel.withLoginTitle(resolveMessage("activate.login"));
        } else {
            Ad ad = adsForUser.get(0);
            loginModel.withAdTitle(ad.getTitle())
                    .withAdUrl(getUrlScheme().urlFor(ad))
                    .withAdId(ad.getId())
                    .withLoginTitle(resolveMessage("activate.login.ad"));
        }

        zenoService.logEvent(new UserActivationSuccessZenoEvent(user));
        return redirectToLogin(request, newRegistrationKey);
    }

    private ModelAndView redirectToLogin(HttpServletRequest request, String newRegistrationKey) {
        StringBuilder path = new StringBuilder(getHost());
        path.append(LoginController.PAGE_PATH);
        path.append("?cb=").append(HttpRequestUtils.encode(getHost(), Charset.defaultCharset()));
        if (StringUtils.hasText(newRegistrationKey)) {
            path.append(HttpRequestUtils.encode("/?", Charset.defaultCharset()))
                    .append(HttpRequestUtils.encode(NEW_REGISTRATION_PARAM, Charset.defaultCharset()))
                    .append(HttpRequestUtils.encode("=", Charset.defaultCharset()))
                    .append(HttpRequestUtils.encode(newRegistrationKey, Charset.defaultCharset()));
        }
        return redirect(path.toString());
    }
}
