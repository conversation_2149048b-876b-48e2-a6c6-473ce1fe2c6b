package com.gumtree.web.seller.service.presentation.config;

/**
 * Models a presentable attribute value.
 */
public final class PresentableAttributeValue {

    private String id;

    private String label;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    @Override
    public PresentableAttributeValue clone() {
        PresentableAttributeValue clonedValue = new PresentableAttributeValue();
        clonedValue.setId(id);
        clonedValue.setLabel(label);
        return clonedValue;
    }
}
