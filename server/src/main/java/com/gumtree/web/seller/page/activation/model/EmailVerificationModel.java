package com.gumtree.web.seller.page.activation.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.springframework.web.servlet.ModelAndView;

public class EmailVerificationModel extends CommonModel {
    private boolean verified;

    public EmailVerificationModel(CoreModel core) {
        super(core);
    }

    public boolean isVerified() {
        return verified;
    }

    public static Builder builder(CoreModel.Builder core) {
        return new Builder(core);
    }

    public static final class Builder {
        private final EmailVerificationModel model;

        public Builder(CoreModel.Builder core) {
            core.withTitle(CommonModel.createTitle("Email Verification"));
            this.model = new EmailVerificationModel(core.build(Page.EmailVerification));
        }

        public Builder withVerified(boolean verified) {
            this.model.verified = verified;
            return this;
        }

        public EmailVerificationModel getModel() {
            return model;
        }

        public ModelAndView build() {
            return new ModelAndView(Page.EmailVerification.getTemplateName(), CommonModel.MODEL_KEY, model);
        }
    }
}
