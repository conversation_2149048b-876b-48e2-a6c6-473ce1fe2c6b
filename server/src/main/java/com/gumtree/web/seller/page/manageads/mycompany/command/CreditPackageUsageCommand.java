package com.gumtree.web.seller.page.manageads.mycompany.command;

import com.gumtree.api.PackageUsageSearchResponse;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.PackageApi;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

/**
 * Command object for fetching credit usages for a specified package
 */
public final class CreditPackageUsageCommand extends AuthenticatedApiCall<PackageUsageSearchResponse> {

    private long accountId;

    private long packageId;

    private int page;

    private int batchSize;

    /**
     * Constructor.
     *
     * @param accountId      the accountId
     * @param packageId      the package id
     * @param page           the pageNumber
     * @param batchSize      the page batch size
     * @param apiKeyProvider the api key provider
     */
    public CreditPackageUsageCommand(
            long accountId,
            long packageId,
            int page,
            int batchSize,
            ApiKeyProvider apiKeyProvider) {

        super(apiKeyProvider);
        this.accountId = accountId;
        this.packageId = packageId;
        this.page = page;
        this.batchSize = batchSize;
    }

    @Override
    public PackageUsageSearchResponse execute(BushfireApi api) {
        return api.create(PackageApi.class, getApiKey()).getPackageUsages(accountId, packageId, page, batchSize);
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(o == null || getClass() != o.getClass()){
            return false;
        }

        CreditPackageUsageCommand that = (CreditPackageUsageCommand) o;

        if(accountId != that.accountId){
            return false;
        }
        if(batchSize != that.batchSize){
            return false;
        }
        if(packageId != that.packageId){
            return false;
        }
        if(page != that.page){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (accountId ^ (accountId >>> 32));
        result = 31 * result + (int) (packageId ^ (packageId >>> 32));
        result = 31 * result + page;
        result = 31 * result + batchSize;
        return result;
    }
}
