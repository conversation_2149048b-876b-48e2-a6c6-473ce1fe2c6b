package com.gumtree.web.seller.page.registration.model;

import com.google.common.collect.Maps;
import com.gumtree.api.domain.user.beans.RegisterUserBean;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;

import java.util.List;
import java.util.Map;

/**
 * As we are using a bean in BAPI we can't really use the Form abstract class
 * as in other forms. But we need to follow the same pattern due to FE standards on
 * templates
 * <p/>
 * The only solution, given the lack of traits and we not using Java 8 yet, is to
 * just duplicate some code in here (the FormErrors part). A minor sin, but no other
 * simple option I cna think of
 */
public class RegistrationForm extends RegisterUserBean {

    private Map<String, List<String>> formErrors = Maps.newHashMap();

    private Boolean legacy = true;
    private Boolean optInThirdPartyMarketing;

    public void addErrors(ReportableErrorsMessageResolvingErrorSource errors) {
        formErrors.putAll(errors.getAllResolvedFieldErrorMessages());
        formErrors.put("global", errors.getResolvedGlobalErrorMessages());
    }

    public Boolean getOptInThirdPartyMarketing() {
        return optInThirdPartyMarketing;
    }

    public void setOptInThirdPartyMarketing(Boolean optInThirdPartyMarketing) {
        this.optInThirdPartyMarketing = optInThirdPartyMarketing;
    }

    public Boolean getLegacy() {
        return legacy;
    }

    public void setLegacy(Boolean legacy) {
        this.legacy = legacy;
    }
    public Map<String, List<String>> getFormErrors() {
        return formErrors;
    }
}
