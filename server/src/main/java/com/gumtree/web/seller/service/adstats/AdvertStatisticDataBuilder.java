package com.gumtree.web.seller.service.adstats;

import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;
import org.joda.time.DateTime;

public class AdvertStatisticDataBuilder {

    private AdvertStatisticData advertStatisticData = new AdvertStatisticData();

    public static AdvertStatisticDataBuilder advertStatisticData() {
        return new AdvertStatisticDataBuilder();
    }

    public AdvertStatisticDataBuilder with() {
        return this;
    }

    public AdvertStatisticDataBuilder advertId(long advertId) {
        advertStatisticData.setAdvertId(Long.toString(advertId));
        return this;
    }

    public AdvertStatisticDataBuilder advertTitle(String advertTitle) {
        advertStatisticData.setAdvertTitle(advertTitle);
        return this;
    }

    public AdvertStatisticDataBuilder vipUrl(String vipUrl) {
        advertStatisticData.setAdvertVIPUrl(vipUrl);
        return this;
    }

    public AdvertStatisticDataBuilder advertStatus(ManageAdStatus advertStatus) {
        advertStatisticData.setAdvertStatus(advertStatus);
        return this;
    }

    public AdvertStatisticDataBuilder contactEmail(String contactEmail) {
        advertStatisticData.setContactEmail(contactEmail);
        return this;
    }

    public AdvertStatisticDataBuilder creationDate(DateTime creationDate) {
        advertStatisticData.setCreationDate(creationDate);
        return this;
    }

    public AdvertStatisticDataBuilder lastModifiedDate(DateTime lastModifiedDate) {
        advertStatisticData.setLastModifiedDate(lastModifiedDate);
        return this;
    }

    public AdvertStatisticDataBuilder lastPostedDate(DateTime lastPostedDate) {
        advertStatisticData.setLastPostedDate(lastPostedDate);
        return this;
    }

    public AdvertStatisticDataBuilder numberOfTimesReposted(long numberOfTimesReposted) {
        advertStatisticData.setNumberOfTimesReposted(numberOfTimesReposted);
        return this;
    }

    public AdvertStatisticDataBuilder vipViews(int vipViews) {
        advertStatisticData.setVIPViews(vipViews);
        return this;
    }

    public AdvertStatisticDataBuilder srpViews(int srpViews) {
        advertStatisticData.setSRPViews(srpViews);
        return this;
    }

    public AdvertStatisticDataBuilder numberOfReplies(int numberOfReplies) {
        advertStatisticData.setNumberOfReplies(numberOfReplies);
        return this;
    }

    public AdvertStatisticData build() {
        return advertStatisticData;
    }

    public AdvertStatisticDataBuilder noData() {
        return this;
    }
}
