package com.gumtree.web.seller.page;

import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCall;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.util.error.ErrorReporter;
import com.gumtree.common.util.error.ReportableErrors;
import com.gumtree.common.util.error.ReportableErrorsArguments;
import com.gumtree.config.SellerProperty;
import com.gumtree.recaptcha.RecaptchaValidator;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.model.ApiError;
import com.gumtree.user.service.model.UserApiErrors;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.common.error.json.JsonValidationErrorReporter;
import com.gumtree.web.common.error.json.JsonValidationResponse;
import com.gumtree.web.common.page.util.RedirectViewWithRequiredModelKeys;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.common.PageExpiredController;
import com.gumtree.web.seller.page.common.model.Form;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.postad.model.exception.UnknownAdvertEditorException;
import com.gumtree.web.seller.service.CheckoutContainer.CheckoutNotFoundException;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * A base controller with some utility methods.
 */
public abstract class BaseSellerController extends CoreModelBaseController {
    public static final String CHECKOUT_KEY = "checkoutKey";

    @Autowired
    protected ZenoService zenoService;

    protected ApiCallExecutor apiCallExecutor;
    protected ErrorMessageResolver messageResolver;
    protected UrlScheme urlScheme;

    class ModelException extends RuntimeException {
        ModelException(String s) {
            super(s);
        }
    }

    @Autowired
    public BaseSellerController(CookieResolver cookieResolver,
                                CategoryModel categoryModel,
                                ApiCallExecutor apiCallExecutor,
                                ErrorMessageResolver messageResolver,
                                UrlScheme urlScheme,
                                UserSessionService userSessionService) {
        super(userSessionService, categoryModel, cookieResolver);
        this.apiCallExecutor = apiCallExecutor;
        this.messageResolver = messageResolver;
        this.urlScheme = urlScheme;

    }

    protected ModelAndView view(Page page) {
        if (page == null) {
            throw new ModelException("Null page parameter");
        }
        ModelAndView mav = new ModelAndView(page.getTemplateName());
        mav.addObject("pageName", page.getTemplateName());
        mav.addObject("pageType", page.getPageType());
        return mav;
    }

    protected ModelAndView redirect(String url) {
        if (Strings.isNullOrEmpty(url)) {
            throw new ModelException("Null path parameter on redirect");
        }
        // note we are not http/1.0 compatible due to a bug in spring that looses https when redirecting and being 1.0 compatible
        // given all browsers we support use http/1.1 and only bots may not be 1.1 compatible, and that seller
        // is not indexed by Google... no harm should come from this
        RedirectView redirect = new RedirectView(url, false, false, false);
        return new ModelAndView(redirect);
    }

    protected ModelAndView redirectWithModel(String url, Map<String, ?> model){
        RedirectView redirect = new RedirectViewWithRequiredModelKeys(url, model.keySet(), true);
        return new ModelAndView(redirect, model);
    }

    protected ModelAndView redirectWithParameters(String url, String parameterName, String value) {
        Set<String> parametersAllowed = new HashSet<>();
        parametersAllowed.add(parameterName);
        RedirectView redirect = new RedirectViewWithRequiredModelKeys(url, parametersAllowed, true);
        return new ModelAndView(redirect, parameterName, value);
    }

    public final ApiCallExecutor getApiCallExecutor() {
        return apiCallExecutor;
    }

    protected final <T> ApiCallResponse<T> execute(ApiCall<T> apiCall) {
        return getApiCallExecutor().call(apiCall);
    }

    protected final void populateErrors(Form form, ApiResponse response){

    }

    protected final void populateErrors(Form form, ReportableErrors errors) {
        ReportableErrorsMessageResolvingErrorSource reportableErrors =
                new ReportableErrorsMessageResolvingErrorSource(errors, messageResolver);
        form.addErrors(reportableErrors);
    }

    protected final void populateErrors(Form form, ReportableErrors errors, String globalErrorMessage) {
        ReportableErrorsMessageResolvingErrorSource reportableErrors =
                new ReportableErrorsMessageResolvingErrorSource(errors, messageResolver, globalErrorMessage);
        form.addErrors(reportableErrors);
    }

    protected final void populateErrors(Form form, ReportableErrors errors, ReportableErrorsArguments args) {
        ReportableErrorsMessageResolvingErrorSource reportableErrors =
                new ReportableErrorsMessageResolvingErrorSource(errors, messageResolver, args);
        form.addErrors(reportableErrors);
    }

    protected final JsonValidationResponse createJsonValidationResponse(ReportableErrors errors) {
        JsonValidationErrorReporter errorReporter = new JsonValidationErrorReporter(messageResolver);
        errors.report(errorReporter);
        return errorReporter.getValidationResponse();
    }

    protected final boolean isUserLoggedIn() {
        return getLoggedInUser().isPresent();
    }

    protected final UrlScheme getUrlScheme() {
        return urlScheme;
    }

    protected final ErrorMessageResolver getMessageResolver() {
        return messageResolver;
    }


    /**
     * @return redirect path
     */
    @ExceptionHandler(CheckoutNotFoundException.class)
    public final String handleExpiredCheckout() {
        return redirectToExpired("checkout");
    }

    /**
     * @return redirect path
     */
    @ExceptionHandler(UnknownAdvertEditorException.class)
    public final String handleUnknownAdvertEditorException() {
        return redirectToExpired("postad");
    }

    protected String resolveMessage(String code, Object... args) {
        String message = getMessageResolver().getMessage(code, code);
        return String.format(message, args);
    }

    // non-responsive below

    private String redirectToExpired(String srcPage) {
        return createRedirect(PageExpiredController.PAGE_PATH, queryParam("src", srcPage));
    }

    protected final JsonObject populateErrors(Model model, ReportableErrors errors, ReportableErrorsArguments args) {
        ReportableErrorsMessageResolvingErrorSource reportableErrors =
                new ReportableErrorsMessageResolvingErrorSource(errors, messageResolver, args);
        model.addAttribute("errors", reportableErrors);
        return reportableErrorToJsonObject(reportableErrors);
    }

    protected final JsonObject populateErrors(Model model, ReportableErrors errors) {
        ReportableErrorsMessageResolvingErrorSource reportableErrors = populateErrors(errors);
        model.addAttribute("errors", reportableErrors);
        return reportableErrorToJsonObject(reportableErrors);
    }

    protected final ReportableErrorsMessageResolvingErrorSource populateErrors(ReportableErrors errors) {
        return new ReportableErrorsMessageResolvingErrorSource(errors, messageResolver);
    }

    protected final JsonObject reportableErrorToJsonObject(ReportableErrorsMessageResolvingErrorSource reportableErrors) {
        JsonObject json = new JsonObject();
        JsonObject messages = new JsonObject();

        messages.add("allResolvedFieldErrorMessages", new Gson().toJsonTree(reportableErrors.getAllResolvedFieldErrorMessages()));
        json.add("errors", messages);

        return json;
    }

    protected ModelAndView getRedirectForCheckout(String url, Checkout checkout) {
        return getRedirectModelAndViewForKeyAndValue(url, CHECKOUT_KEY, checkout.getKey());
    }

    private ModelAndView getRedirectModelAndViewForKeyAndValue(String url, String key, String value) {
        RedirectViewWithRequiredModelKeys redirectView = new RedirectViewWithRequiredModelKeys(url, key);
        ModelAndView mv = new ModelAndView(redirectView);
        mv.addObject(key, value);
        return mv;
    }


    public static class ReportableUserApiErrors implements ReportableErrors {
        private static final Object[] NO_ARGS = new Object[]{};

        private final UserApiErrors apiError;

        public ReportableUserApiErrors(UserApiErrors apiError) {
            Assert.notNull(apiError);
            this.apiError = apiError;
        }

        @Override
        public void report(ErrorReporter errorReporter, ReportableErrorsArguments args) {
            report(errorReporter);
        }

        @Override
        public void report(ErrorReporter errorReporter) {
            if (apiError.getErrors() != null) {
                for (ApiError error : apiError.getErrors()) {

                    if (error.getField() == null) {
                        errorReporter.globalError(
                                error.getError(),
                                error.getError(),
                                NO_ARGS);
                    } else {
                        errorReporter.fieldError(
                                error.getField(),
                                error.getError(),
                                error.getError(),
                                NO_ARGS);
                    }
                }
            }
        }
    }

    protected String getHost() {
        String newHost = System.getenv(SellerProperty.GUMTREE_HOST_NEW_BASE_URI.getPropertyName());
        return newHost != null ? newHost : GtProps.getStr(SellerProperty.GUMTREE_HOST_NEW);
    }
}
