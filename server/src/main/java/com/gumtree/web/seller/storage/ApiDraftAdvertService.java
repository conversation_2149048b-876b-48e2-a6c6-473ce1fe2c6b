package com.gumtree.web.seller.storage;

import com.google.common.base.Optional;
import com.gumtree.api.User;
import com.gumtree.draftsapi.client.command.CreateOrUpdateDraftCommand;
import com.gumtree.draftsapi.client.command.DeleteDraftCommand;
import com.gumtree.draftsapi.client.command.GetDraftCommand;
import com.gumtree.draftsapi.model.ApiResponse;
import com.gumtree.draftsapi.model.IdResponse;
import com.gumtree.draftsapi.spec.DraftsApiClient;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import org.springframework.util.Assert;


/**
 * Persists, retrieves and clears {@link com.gumtree.web.seller.page.postad.model.PostAdDetail}
 * based on currently logged in user
 */
public class ApiDraftAdvertService implements DraftAdvertService {
    private final UserSessionService userSessionService;
    private final DraftsApiClient draftsApiClient;

    public ApiDraftAdvertService(DraftsApiClient draftsApiClient,
                                 UserSessionService userSessionService) {
        this.draftsApiClient = draftsApiClient;
        this.userSessionService = userSessionService;
    }

    @Override
    public Optional<PostAdDetail> retrieve() {
        Optional<Long> userId = getUserId();
        if (userId.isPresent()) {
            ApiResponse<PostAdDetail> resp =
                    draftsApiClient.execute(new GetDraftCommand<>(userId.get(), PostAdDetail.class));
            if (resp.isDefined()) {
                return Optional.of(resp.get());
            }
        }

        return Optional.absent();
    }

    @Override
    public boolean persist(PostAdDetail postAdDetail) {
        Assert.notNull(postAdDetail);

        Optional<Long> userId = getUserId();
        boolean isWorthSaving = postAdDetail.getCategoryId() != null; // at least user picked category!
        if (userId.isPresent() && isWorthSaving) {
            ApiResponse<IdResponse> resp =
                    draftsApiClient.execute(new CreateOrUpdateDraftCommand(userId.get(), postAdDetail));
            if (resp.isDefined()) {
                return true;
            }

        }

        return false;
    }

    @Override
    public void clear() {
        Optional<Long> userId = getUserId();
        if (userId.isPresent()) {
            draftsApiClient.execute(new DeleteDraftCommand(userId.get()));
        }
    }

    private Optional<Long> getUserId() {
        final Optional<User> user = userSessionService.getUser();
        if (user.isPresent()) {
            return Optional.of(user.get().getId());
        }
        return Optional.absent();
    }
}
