package com.gumtree.web.seller.service.adstats.adcounter;

public class AdCounters {

    private Long advertId;
    private int viewsCounter;
    private int searchImpressionCounter;
    private int repliesCount;

    public AdCounters() {
    }

    public AdCounters(Long advertId, int viewsCounter, int searchImpressionCounter, int repliesCount) {
        this.advertId = advertId;
        this.viewsCounter = viewsCounter;
        this.searchImpressionCounter = searchImpressionCounter;
        this.repliesCount = repliesCount;
    }

    public Long getAdvertId() {
        return advertId;
    }

    public int getViewsCounter() {
        return viewsCounter;
    }

    public int getSearchImpressionCounter() {
        return searchImpressionCounter;
    }

    public int getRepliesCount() {
        return repliesCount;
    }

    public static AdCounters.Builder builder(){
        return new Builder();
    }

    public static class Builder {
        private Long advertId;
        private int viewsCounter;
        private int searchImpressionCounter;
        private int repliesCount;

        public Builder() {
        }

        public Builder setAdvertId(Long advertId) {
            this.advertId = advertId;
            return this;
        }

        public Builder setViewsCounter(int viewsCounter) {
            this.viewsCounter = viewsCounter;
            return this;
        }

        public Builder setSearchImpressionCounter(int searchImpressionCounter) {
            this.searchImpressionCounter = searchImpressionCounter;
            return this;
        }

        public Builder setRepliesCount(int repliesCount) {
            this.repliesCount = repliesCount;
            return this;
        }

        public AdCounters build() {
            return new AdCounters(advertId, viewsCounter, searchImpressionCounter, repliesCount);
        }
    }

    @Override
    public String toString() {
        return "AdCounters{" +
                "advertId=" + advertId +
                ", viewsCounter=" + viewsCounter +
                ", searchImpressionCounter=" + searchImpressionCounter +
                ", repliesCount=" + repliesCount +
                '}';
    }
}
