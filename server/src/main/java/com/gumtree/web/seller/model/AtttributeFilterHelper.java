package com.gumtree.web.seller.model;

import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.FeaturesExpCategoryConstants;

 public class AtttributeFilterHelper {

    private CategoryService categoryService;

     public AtttributeFilterHelper(CategoryService categoryService) {
        this.categoryService = categoryService;
    }

     public boolean isGymEquipmentCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.GYM_EQUIPMENT_ID.equals(category.getId()));
     }

     public boolean isGuitarsCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.GUITARS_ID.equals(category.getId()));
     }

     public boolean isPramsStrollersCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.PRAMS_STROLLERS_ID.equals(category.getId()));
     }

     public boolean isMobilityDisabilityMedicalCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.MOBILITY_DISABILITY_MEDICAL_ID.equals(category.getId()));
     }

     public boolean isGamesBoardGamesCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.GAMES_BOARD_GAMES_ID.equals(category.getId()));
     }

     public boolean isFilmsTvShowsCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.FILMS_TV_SHOWS_ID.equals(category.getId()));
     }

     public boolean isBabyChildSafetyCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.BABY_CHILD_SAFETY_ID.equals(category.getId()));
     }

     public boolean isCarpentryServicesCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.CARPENTRY_SERVICES_ID.equals(category.getId()));
     }

     public boolean isComputersPcsLaptopsCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.COMPUTERS_PCS_LAPTOPS_ID.equals(category.getId()));
     }

     public boolean isBicyclesCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.BICYCLES_ID.equals(category.getId()));
     }

     public boolean isGameConsolesCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.GAME_CONSOLES_ID.equals(category.getId()));
     }

     public boolean isOfficeFurnitureCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.OFFICE_FURNITURE_ID.equals(category.getId()));
     }

     public boolean isDiningLivingRoomFurnitureCategory(AdvertEditor editor) {
         return categoryService.getCategoryModel().getHierarchy(editor.getCategoryId()).stream()
                 .anyMatch(category -> FeaturesExpCategoryConstants.DINING_LIVING_ROOM_FURNITURE_ID.equals(category.getId()));
     }
}
