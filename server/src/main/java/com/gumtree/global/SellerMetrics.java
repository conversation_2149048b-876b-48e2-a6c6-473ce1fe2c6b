package com.gumtree.global;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;

import static com.codahale.metrics.MetricRegistry.name;

public abstract class SellerMetrics {
    private static final String BASE_NAME = SellerMetrics.class.getSimpleName();

    private static MetricRegistry metricRegistry;

    private SellerMetrics() {
    }

    public static void setMetricRegistry(MetricRegistry metricRegistry) {
        SellerMetrics.metricRegistry = metricRegistry;
    }

    public static Timer timer(String name) {
        return metricRegistry.timer(name(name));
    }
}
