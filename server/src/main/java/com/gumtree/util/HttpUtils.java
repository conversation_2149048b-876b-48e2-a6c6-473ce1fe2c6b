package com.gumtree.util;

import org.json.JSONObject;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Component
public class HttpUtils {

    private static final int DEFAULT_CONNECT_TIMEOUT = 5000;
    private static final int DEFAULT_READ_TIMEOUT = 5000;

    /**
     * Sends an HTTP POST request with a JSON body to the specified API URL.
     *
     * @param apiUrl      The target API endpoint URL.
     * @param requestBody The JSON object to send in the request body.
     * @return A JSONObject representing the server's response.
     */
    public JSONObject sendJsonPost(String apiUrl, JSONObject requestBody) {
        return sendJsonPost(apiUrl, requestBody, null);
    }

    /**
     * Sends an HTTP POST request with a JSON body to the specified API URL. Headers can be added.
     *
     * @param apiUrl       The target API endpoint URL.
     * @param requestBody  The JSON object to send in the request body.
     * @param headers       The headers to add to the request.
     * @return A JSONObject representing the server's response.
     */
    public JSONObject sendJsonPost(String apiUrl, JSONObject requestBody,
                                   Map<String, String> headers) {
        return sendJsonRequest(apiUrl, "POST", requestBody, headers);
    }

    /**
     * send json get request
     *
     * @param apiUrl The target API endpoint URL.
     * @return A JSONObject representing the server's response.
     */
    public JSONObject sendJsonGet(String apiUrl, String... args) {
        apiUrl = String.format(apiUrl,  (Object[]) args);
        return sendJsonGet(apiUrl, null, null);
    }

    /**
     * send json get request, headers can be added
     *
     * @param apiUrl The target API endpoint URL.
     * @param headers   The headers to add to the request.
     * @return A JSONObject representing the server's response.
     */
    public JSONObject sendJsonGet(String apiUrl, JSONObject requestBody, Map<String, String> headers) {
        return sendJsonRequest(apiUrl, "GET", requestBody, headers);
    }

    /**
     * send normal json request
     *
     * @param apiUrl The target API endpoint URL.
     * @param method http method
     * @param requestBody The JSON object to send in the request body.
     * @param headers   The headers to add to the request.
     * @return A JSONObject representing the server's response.
     */
    public JSONObject sendJsonRequest(String apiUrl, String method,
                                      JSONObject requestBody,
                                      Map<String, String> headers) {
        HttpURLConnection conn = null;
        try {
            URL url = new URL(apiUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod(method);
            conn.setRequestProperty("Accept", "application/json");
            conn.setConnectTimeout(DEFAULT_CONNECT_TIMEOUT);
            conn.setReadTimeout(DEFAULT_READ_TIMEOUT);

            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    conn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }

            boolean hasOutput = "POST".equalsIgnoreCase(method) ||
                    "PUT".equalsIgnoreCase(method);
            if (hasOutput) {
                conn.setDoOutput(true);
                conn.setRequestProperty("Content-Type", "application/json");

                try (OutputStream os = conn.getOutputStream()) {
                    String jsonInputString = requestBody.toString();
                    os.write(jsonInputString.getBytes(StandardCharsets.UTF_8));
                }
            }

            int responseCode = conn.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK &&
                    responseCode != HttpURLConnection.HTTP_CREATED) {
                throw new IOException("HTTP request failed with code: " + responseCode);
            }

            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine);
                }
                return new JSONObject(response.toString());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

}
