package com.gumtree.util;

import org.apache.commons.lang3.ObjectUtils;

import java.util.Collections;
import java.util.List;

import static com.google.common.base.MoreObjects.firstNonNull;

public abstract class GtUtils {
    private GtUtils() {}


    public static <T> T nullSafe(T value, T defaultValue) {
        return value != null ? value : defaultValue;
    }

    public static <T> List<T> emptyIfNull(List<T> value) {
        return ObjectUtils.firstNonNull(value, Collections.<T>emptyList());
    }
}
