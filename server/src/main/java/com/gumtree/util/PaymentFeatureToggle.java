package com.gumtree.util;

import java.util.Arrays;
import java.util.List;

public class PaymentFeatureToggle {
    
    // Fixed grayscale percentage
    private static final int GRAYSCALE_PERCENTAGE = 50;
    
    // Hardcoded whitelist user IDs
    private static final List<Long> WHITELIST_USER_IDS = Arrays.asList(
        2888396L, // Test user jimm zoidberg
        2934905L, // Test user liu
        2916518L, // Test user shao bixi
        2902055L, // Test user shao zoidberg
        2918551L, // Test user shao stage
        2939479L, // Test user mark stage
        99592107L, // Test user shao Prod
        99471583L, // Test user mark Prod
        9999999999L  // Test user for Unit test
    );
    
    /**
     * Check if user is in grayscale range
     * @param userId User ID to check
     * @return true if user is in grayscale range
     */
    public boolean isInGrayScale(long userId) {
        return isInWhitelist(userId) || (-1 < userId % 100 && userId % 100 < GRAYSCALE_PERCENTAGE);
    }
    
    /**
     * Check if user is in whitelist
     * @param userId User ID to check
     * @return true if user is in whitelist
     */
    public boolean isInWhitelist(long userId) {
        return WHITELIST_USER_IDS.contains(userId);
    }
}