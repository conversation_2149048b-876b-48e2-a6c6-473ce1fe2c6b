package com.gumtree.web.seller.mvc.spec

import com.gumtree.common.properties.GtProps
import com.gumtree.web.seller.mvc.SellerMvcSpec
import org.hamcrest.Matchers._
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders._
import org.springframework.test.web.servlet.result.MockMvcResultMatchers._

class RegistrationControllerMvcSpec extends SellerMvcSpec {

  scenario("show create account page") {
    // when
    val request: MockHttpServletRequestBuilder = get("/create-account")
    val result: ResultActions = mockMvc.perform(request)

    // then
    result.andExpect(status.isOk)

    if(GtProps.getBool("gumtree.threatmetrix.enabled")){
      result.andExpect(cookie.exists("DEV_gt_tm"))
    }

    // and
    val modelAndView = result.andReturn().getModelAndView
    val model = modelAndView.getModel
    modelAndView.getViewName should be("pages/registration/registration")
  }
}
