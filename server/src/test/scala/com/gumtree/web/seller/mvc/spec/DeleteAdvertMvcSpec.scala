package com.gumtree.web.seller.mvc.spec

import com.gumtree.web.seller.mvc.SellerMvcSpec
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders._

class DeleteAdvertMvcSpec extends SellerMvcSpec {

  scenario("should redirect to login when not logged in and tries to delete ad through email") {
    // when
    val request: MockHttpServletRequestBuilder = get("/manage/ads/delete-ad/3123129")
    val result: ResultActions = mockMvc.perform(request)

    // then
    result.andReturn().getModelAndView.getViewName should be("redirect:http://localhost/login")
  }
}
