package com.gumtree.web.seller.mvc.spec

import com.gumtree.domain.category.Categories
import com.gumtree.web.seller.builder.VehicleAttributeHelper
import com.gumtree.web.seller.mvc.SellerMvcSpec
import org.hamcrest.Matchers._
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders._
import org.springframework.test.web.servlet.result.MockMvcResultMatchers._

class VrmSearchControllerMvcSpec extends SellerMvcSpec {

  scenario("should lookup valid cars Vrm") {
    // when
    val result: ResultActions = mockMvc.perform(get("/ajax/vrn")
      .param("input", VehicleAttributeHelper.VALID_CAR_VRM)
      .param("categoryId", Categories.CARS.getId.toString))

    // then
    result.andExpect(status.isOk)
    result.andExpect(content().contentType("application/json;charset=UTF-8"))
    result.andExpect(jsonPath("$.state").value(equalTo("FOUND")))
    result.andExpect(jsonPath("$.attributes.vrn.value").value(equalTo(VehicleAttributeHelper.VALID_CAR_VRM)))
    result.andExpect(jsonPath("$.attributes.vrn.attributeDisplayName").value(equalTo("Vehicle Registration Number")))
    result.andExpect(jsonPath("$.attributes.vehicle_make.attributeName").value(equalTo("vehicle_make")))
    result.andExpect(jsonPath("$.attributes.vehicle_make.attributeDisplayName").value(equalTo("Make")))
    result.andExpect(jsonPath("$.attributes.vehicle_make.value").value(equalTo("chevrolet")))
    result.andExpect(jsonPath("$.attributes.vehicle_make.valueDisplayName").value(equalTo("Chevrolet")))
    // model is free text for now (should change to drop down later -> correct value should be standardized one = matiz)
    result.andExpect(jsonPath("$.attributes.vehicle_model.value").value(equalTo("MATIZ")))
    result.andExpect(jsonPath("$.attributes.vehicle_model.valueDisplayName").value(equalTo("MATIZ")))
    result.andExpect(jsonPath("$.attributes.vehicle_body_type.value").value(equalTo("hatchback")))
    result.andExpect(jsonPath("$.attributes.vehicle_body_type.valueDisplayName").value(equalTo("Hatchback")))
    result.andExpect(jsonPath("$.attributes.vehicle_colour.value").value(equalTo("SILVER")))
    result.andExpect(jsonPath("$.attributes.vehicle_colour.valueDisplayName").value(equalTo("Silver")))
    result.andExpect(jsonPath("$.attributes.vehicle_registration_year.value").value(equalTo("2008")))
    result.andExpect(jsonPath("$.attributes.vehicle_registration_year.valueDisplayName").value(equalTo("2008")))
    result.andExpect(jsonPath("$.attributes.vehicle_fuel_type.value").value(equalTo("petrol")))
    result.andExpect(jsonPath("$.attributes.vehicle_fuel_type.valueDisplayName").value(equalTo("Petrol")))
    result.andExpect(jsonPath("$.attributes.vehicle_transmission.value").value(equalTo("manual")))
    result.andExpect(jsonPath("$.attributes.vehicle_transmission.valueDisplayName").value(equalTo("Manual")))
    result.andExpect(jsonPath("$.attributes.vehicle_engine_size.value").value(equalTo("995")))
    result.andExpect(jsonPath("$.attributes.vehicle_engine_size.valueDisplayName").value(equalTo("995")))
    result.andExpect(jsonPath("$.attributes.vehicle_not_stolen").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_not_scrapped").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_not_exported").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_road_worthy").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_not_writeoff").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_uk_model").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_original_number_plate").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_original_colour").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_vhc_checked").doesNotExist())
  }

  scenario("should lookup valid motorbike Vrm") {
    // when
    val result: ResultActions = mockMvc.perform(get("/ajax/vrn")
      .param("input", VehicleAttributeHelper.VALID_MOTORBIKE_VRM)
      .param("categoryId", Categories.MOTORBIKES.getId.toString))

    // then
    result.andExpect(status.isOk)
    result.andExpect(content().contentType("application/json;charset=UTF-8"))
    result.andExpect(jsonPath("$.state").value(equalTo("FOUND")))
    result.andExpect(jsonPath("$.attributes.vrn.value").value(equalTo(VehicleAttributeHelper.VALID_MOTORBIKE_VRM)))
    result.andExpect(jsonPath("$.attributes.vrn.attributeDisplayName").value(equalTo("Vehicle Registration Number")))
    result.andExpect(jsonPath("$.attributes.motorbike_make.attributeName").value(equalTo("motorbike_make")))
    result.andExpect(jsonPath("$.attributes.motorbike_make.attributeDisplayName").value(equalTo("Make")))
    result.andExpect(jsonPath("$.attributes.motorbike_make.value").value(equalTo("aprilia-motorbikes")))
    result.andExpect(jsonPath("$.attributes.motorbike_make.valueDisplayName").value(equalTo("Aprilia")))
    result.andExpect(jsonPath("$.attributes.vehicle_colour.value").value(equalTo("BLACK")))
    result.andExpect(jsonPath("$.attributes.vehicle_colour.valueDisplayName").value(equalTo("Black")))
    result.andExpect(jsonPath("$.attributes.vehicle_registration_year.value").value(equalTo("2001")))
    result.andExpect(jsonPath("$.attributes.vehicle_engine_size.value").value(equalTo("49")))
    result.andExpect(jsonPath("$.attributes.vehicle_fuel_type").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_model").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_body_type").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_transmission").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_make").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_not_stolen").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_not_scrapped").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_not_exported").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_road_worthy").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_not_writeoff").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_uk_model").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_original_number_plate").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_original_colour").doesNotExist())
    result.andExpect(jsonPath("$.attributes.vehicle_vhc_checked").doesNotExist())
  }

  scenario("should return NOT_FOUND if no vehicle data is found") {
    // when
    val result: ResultActions = mockMvc.perform(get("/ajax/vrn")
      .param("input", VehicleAttributeHelper.INVALID_CAR_VRM)
      .param("categoryId", Categories.CARS.getId.toString))

    // then
    result.andExpect(status.isOk)
    result.andExpect(content().contentType("application/json;charset=UTF-8"))
    result.andExpect(jsonPath("$.state").value(equalTo("NOT_FOUND")))
  }

}
