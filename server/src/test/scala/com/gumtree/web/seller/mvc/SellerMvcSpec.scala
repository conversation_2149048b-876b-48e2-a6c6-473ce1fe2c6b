package com.gumtree.web.seller.mvc

import com.google.common.collect.Lists
import com.gumtree.api.User
import com.gumtree.api.client.BushfireApi
import com.gumtree.api.client.stub.{Fixtures<PERSON><PERSON><PERSON>, Stub<PERSON>ccount<PERSON>pi, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}
import com.gumtree.common.properties.GtProps
import com.gumtree.common.util.json.JsonSerializeUtils._
import com.gumtree.common.util.url.UrlUtils
import com.gumtree.config.profiles.CommonConfigProfiles
import com.gumtree.config.{DefaultPropertyInitializer, SellerApplicationContextInitializer}
import com.gumtree.mobile.test.{Fixtures, SecurityTestHelper}
import com.gumtree.seller.infrastructure.driven.user.UserAdPreferenceServiceApi
import com.gumtree.userapi.stub.UserApiStub
import com.gumtree.web.cookie.CookieCutter
import com.gumtree.web.security.config.CommonSecurityConfig
import com.gumtree.web.security.shiro.GumtreeRealm
import com.gumtree.web.seller.config.{Seller<PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SellerMvcTestConfig}
import com.gumtree.web.seller.mvc.utils.GumtreeMockMvcBuilder
import com.gumtree.webmvc.testing.scala.springcontext.SpringWebContext
import com.gumtree.zeno.core.InMemoryEventHandler
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext
import org.apache.shiro.SecurityUtils
import org.apache.shiro.codec.Base64
import org.apache.shiro.mgt.{AbstractRememberMeManager, DefaultSecurityManager}
import org.apache.shiro.subject.SimplePrincipalCollection
import org.apache.shiro.util.{ByteSource, ThreadContext}
import org.codehaus.jackson.map.ObjectMapper
import org.mockito.Mockito.reset
import org.scalatest.concurrent.Eventually
import org.scalatest.time.SpanSugar
import org.scalatest.{BeforeAndAfterEach, FeatureSpec, Matchers}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.http.converter.json.MappingJacksonHttpMessageConverter
import org.springframework.test.context.web.WebAppConfiguration
import org.springframework.test.context.{ActiveProfiles, ContextConfiguration}
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders._
import org.springframework.test.web.servlet.{MockMvc, ResultActions}
import org.springframework.web.context.WebApplicationContext

import java.util.concurrent.atomic.AtomicInteger
import javax.annotation.Resource
import javax.servlet.Filter

@WebAppConfiguration
@ContextConfiguration(
  initializers = Array(classOf[SellerApplicationContextInitializer], classOf[DefaultPropertyInitializer]),
  loader = classOf[SellerMvcContextLoader],
  classes = Array(classOf[SellerMvcTestConfig]))
@ActiveProfiles(profiles = Array(
  "stub-api",
  "stub-models",
  CommonConfigProfiles.ELASTIC_SEARCH_API,
  CommonConfigProfiles.SESSION_PERSISTENCE_STUB))
abstract class SellerMvcSpec extends FeatureSpec
  with Matchers
  with SpringWebContext
  with BeforeAndAfterEach
  with Eventually
  with SpanSugar {

  val SECURITY_TOKEN = "valid-token"

  @Autowired
  protected var zenoEventHandler: InMemoryEventHandler = _

  @Autowired
  protected var wac: WebApplicationContext = _

  @Autowired
  protected var bushfireApi: BushfireApi = _

  @Resource(name = "objectMapper")
  protected var objectMapper: ObjectMapper = _

  @Autowired
  var rememberMeManager: AbstractRememberMeManager = _

  @Autowired
  var userAdPreferenceServiceApi: UserAdPreferenceServiceApi = _

  var hystrixContext: HystrixRequestContext = _

  lazy val mvc: MockMvc = {
    val builder = new GumtreeMockMvcBuilder(wac)
    builder.filter(wac.getBean("shiroFilter").asInstanceOf[Filter])
    builder.build
  }

  override def beforeEach(): Unit = {
    hystrixContext = HystrixRequestContext.initializeContext()

    // shiro
    ThreadContext.remove(); // create subject
    SecurityUtils.setSecurityManager(new DefaultSecurityManager())

    // clear stubs
    StubAccountApi.clearStubData()
    StubUserApi.clearStubData()
    StubAccountApi.clearStubData()
    StubAuthenticationApi.clearStubData()
    zenoEventHandler.clear()
    reset(userAdPreferenceServiceApi)
  }

  override protected def afterEach(): Unit = {
    super.afterEach()
    if (hystrixContext != null) {
      hystrixContext.shutdown()
    }
  }

  def loginAs(user: User) {
    SecurityTestHelper.userIsLoggedIn(user)
  }

  def loginAsDefaultUser: User = {
    val user: User = Fixtures.user.withAccountIds(Lists.newArrayList(FixturesBapi.DEF_ACC_ID)).build
    loginAs(user)
    user
  }

  def POST(url: String, body: String): MockHttpServletRequestBuilder = {
    post(url)
      .contentType(MediaType.APPLICATION_FORM_URLENCODED)
      .content(body)
  }

  def restPOST(url: String, body: String): MockHttpServletRequestBuilder = {
    post(url)
      .contentType(MediaType.APPLICATION_JSON)
      .content(body)
  }

  def POST(url: String): MockHttpServletRequestBuilder = {
    post(url)
      .contentType(MediaType.APPLICATION_FORM_URLENCODED)
  }

  def ajaxPUT(url: String, body: String): MockHttpServletRequestBuilder = {
    put(url).header("X-Requested-With", "XMLHttpRequest")
      .contentType(MediaType.APPLICATION_FORM_URLENCODED)
      .content(body)
      .accept(MediaType.APPLICATION_JSON)
  }

  def ajaxPOST(url: String, body: String): MockHttpServletRequestBuilder = {
    post(url).header("X-Requested-With", "XMLHttpRequest")
      .contentType(MediaType.APPLICATION_FORM_URLENCODED)
      .content(body)
      .accept(MediaType.APPLICATION_JSON)
  }

  def POST(url: String, form: Map[String, String]): MockHttpServletRequestBuilder = {
    val body: String = form.map { case (k, v) => s"$k=${UrlUtils.enc(v)}" }.mkString("&")
    post(url)
      .contentType(MediaType.APPLICATION_FORM_URLENCODED)
      .content(body)
  }

  def ajaxPOST(url: String, form: Map[String, String]): MockHttpServletRequestBuilder = {
    val body: String = form.map { case (k, v) => s"$k=${UrlUtils.enc(v)}" }.mkString("&")
    post(url)
      .header("X-Requested-With", "XMLHttpRequest")
      .contentType(MediaType.APPLICATION_FORM_URLENCODED)
      .content(body)
  }

  def ajaxDELETE(url: String, jsonBody: String): MockHttpServletRequestBuilder = {
    delete(url).header("X-Requested-With", "XMLHttpRequest")
      .contentType(MediaType.APPLICATION_FORM_URLENCODED)
      .content(jsonBody)
  }

  def ajaxGET(url: String): MockHttpServletRequestBuilder = {
    get(url).header("X-Requested-With", "XMLHttpRequest")
  }

  def generateUsername: String = "user_" + SellerMvcSpec.ID_GEN.getAndIncrement + "@gtblackhole.com"

  def createRememberMeCookie(username: String): javax.servlet.http.Cookie = {
    val principals: SimplePrincipalCollection = new SimplePrincipalCollection(username, GumtreeRealm.GUMTREE_REALM_NAME)
    val encrypt: ByteSource = rememberMeManager.getCipherService.encrypt(rememberMeManager.getSerializer.serialize(principals), rememberMeManager.getCipherKey)
    new javax.servlet.http.Cookie(CookieCutter.getName(CommonSecurityConfig.BUSHFIRE_SOFT_LOGIN_COOKIE_NAME, GtProps.getEnv), Base64.encodeToString(encrypt.getBytes))
  }

  def createUser(): User = {
    val username = generateUsername
    val user = Fixtures.user.withEmail(username).withAccountIds(Lists.newArrayList(FixturesBapi.DEF_ACC_ID)).build
    StubUserApi.addUser(user)
    UserApiStub.DATA.CREDENTIALS.put(username, "password")
    user
  }

  protected def printContent(result: ResultActions): Unit = {
    println(result.andReturn().getResponse.getContentAsString)
  }

  /**
   * Converts object into json string that can be saved eg into redis
   *
   * @param fromValue
   * @return
   */
  protected def convertObjectToJson(fromValue: Any): String = writeToString(objectMapper, fromValue)

  /**
   * Converts json string coming eg from redis into object
   *
   * @param fromValue
   * @param toValueType
   * @tparam T
   * @return
   */
  protected def convertJsonToObject[T](fromValue: String, toValueType: Class[T]): T = readFromString(objectMapper, fromValue, toValueType)

  /**
   * Converts http request parameter object into json
   *
   * @see converters in [[com.gumtree.config.SellerConfig]]
   * @param fromValue
   * @return json
   */
  protected def convertRequestParameterToJson(fromValue: Any) = writeToString(new MappingJacksonHttpMessageConverter().getObjectMapper, fromValue)
}

object SellerMvcSpec {
  val ID_GEN = new AtomicInteger()
}
