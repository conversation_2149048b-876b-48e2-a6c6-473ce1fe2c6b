package com.gumtree.web.seller.mvc.spec

import com.gumtree.api.User
import com.gumtree.seller.infrastructure.driven.user.model.UserAdPreference
import com.gumtree.seller.infrastructure.driven.user.model.UserAdPreference.ConsentEnum
import com.gumtree.web.seller.mvc.SellerMvcSpec
import org.mockito.Matchers.{any, eq => equalsTo}
import org.mockito.Mockito.{when, verify}
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.result.MockMvcResultMatchers._
import org.springframework.test.web.servlet.{ResultActions}
import rx.Single


class UserAdPreferenceControllerMvcSpec extends SellerMvcSpec {

  private val AJAX_PATH = "/ajax/ad-preference/user"

  scenario("return user ad preference on Ajax call") {
    // given
    val user: User = loginAsDefaultUser
    val userAdPreference = new UserAdPreference()
    userAdPreference.setConsent(ConsentEnum.YES)
    when(userAdPreferenceServiceApi.getAdPreference(user.getId)).thenReturn(Single.just(userAdPreference))

    // when
    val request: MockHttpServletRequestBuilder = ajaxGET(AJAX_PATH)
    val result: ResultActions = mockMvc.perform(request)

    // then
    result.andExpect(status.isOk)

    // and
    val response: String = result.andReturn().getResponse.getContentAsString
    response shouldBe "{\"consent\":\"YES\"}"
    verify(userAdPreferenceServiceApi).getAdPreference(user.getId)
  }

  scenario("return null if user ad preference is missing on Ajax call") {
    // given
    val user: User = loginAsDefaultUser
    val userAdPreference = new UserAdPreference()
    userAdPreference.setConsent(ConsentEnum.YES)
    when(userAdPreferenceServiceApi.getAdPreference(user.getId))
      .thenReturn(Single.just(null.asInstanceOf[UserAdPreference]))

    // when
    val request: MockHttpServletRequestBuilder = ajaxGET(AJAX_PATH)
    val result: ResultActions = mockMvc.perform(request)

    // then
    result.andExpect(status.isOk)

    // and
    val response: String = result.andReturn().getResponse.getContentAsString
    response shouldBe "{\"consent\":null}"
    verify(userAdPreferenceServiceApi).getAdPreference(user.getId)
  }

  scenario("return null if there is a backend error.") {
    // given
    val user: User = loginAsDefaultUser
    when(userAdPreferenceServiceApi.getAdPreference(user.getId))
      .thenReturn(Single.error(new RuntimeException("Some error!")).asInstanceOf[Single[UserAdPreference]])

    // when
    val request: MockHttpServletRequestBuilder = ajaxGET(AJAX_PATH)
    val result: ResultActions = mockMvc.perform(request)

    // then
    result.andExpect(status.isOk)

    // and
    val response: String = result.andReturn().getResponse.getContentAsString
    response shouldBe "{\"consent\":null}"
    verify(userAdPreferenceServiceApi).getAdPreference(user.getId)
  }

  scenario("save given ad preference") {
    // given
    val user: User = loginAsDefaultUser

    // when
    val request: MockHttpServletRequestBuilder = restPOST(AJAX_PATH, "{\"consent\":\"YES\"}")
    val result: ResultActions = mockMvc.perform(request)

    // then
    result.andExpect(status.isOk)

    // and
    verify(userAdPreferenceServiceApi).saveAdPreference(any[UserAdPreference], equalsTo(user.getId));
  }

}
