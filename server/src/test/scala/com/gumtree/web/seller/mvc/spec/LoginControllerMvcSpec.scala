package com.gumtree.web.seller.mvc.spec

import com.gumtree.api.client.stub.FixturesBapi
import com.gumtree.web.seller.mvc.SellerMvcSpec
import com.gumtree.web.seller.page.login.controller.LoginForm
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders._
import org.springframework.test.web.servlet.result.MockMvcResultMatchers._

class LoginControllerMvcSpec extends SellerMvcSpec {

  scenario("show login page") {
    // when
    val result: ResultActions = mockMvc.perform(get("/login"))

    // then
    result.andExpect(status.isOk)

    // and
    val viewName: String = result.andReturn().getModelAndView.getViewName
    viewName should be("pages/login/login")
  }

  scenario("user can login with valid username and password") {
    // when
    val request = POST("/login")
      .param("username", FixturesBapi.DEF_USER_EMAIL)
      .param("password", FixturesBapi.DEF_USER_PASSWORD)
      .param("newUser", "false")
    val result = mvc.perform(request)

    // then
    result.andExpect(status.isSeeOther)
    result.andReturn().getResponse.getRedirectedUrl should endWith ("/manage/ads")
  }

  scenario("user can not login with incorrect password") {
    // when
    val request = POST("/login")
      .param("username", FixturesBapi.DEF_USER_EMAIL)
      .param("password", "invalid")
      .param("newUser", "false")

    val result = mvc.perform(request)

    // then
    result.andExpect(status.isOk)
    val modelAndView = result.andReturn().getModelAndView

    modelAndView.getViewName should be("pages/login/login")

    val model = modelAndView.getModel
    val errors = model.get("loginForm").asInstanceOf[LoginForm].getFormErrors
    assert(errors.size() == 3)
    assert(errors.containsKey("username"))
    assert(errors.containsKey("password"))
  }
}
