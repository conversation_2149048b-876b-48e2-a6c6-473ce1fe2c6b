package util;

import org.hamcrest.Description;
import org.hamcrest.Factory;
import org.hamcrest.Matcher;
import org.hamcrest.TypeSafeMatcher;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;

public class RedirectMatcher extends TypeSafeMatcher<View> {

    String expectedDestination;

    @Override
    public boolean matchesSafely(View view) {
        if (!(view instanceof RedirectView)) return false;
        return expectedDestination.equals(((RedirectView) view).getUrl());
    }

    public void describeTo(Description description) {
        description.appendText("view is not a redirect to " + expectedDestination);
    }

    @Factory
    public static <T> Matcher<View> isRedirectTo(String expectedDestination) {
        RedirectMatcher matcher = new RedirectMatcher();
        matcher.expectedDestination = expectedDestination;
        return matcher;
    }
}
