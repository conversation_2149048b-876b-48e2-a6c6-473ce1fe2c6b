package util;

import rx.Single;

import java.util.function.Consumer;

public class RxAssertions {

    private RxAssertions() {
    }

    public static <T> void verifyError(Single<T> single, Consumer<Throwable> verifier) {
        Throwable result = single
                .<Throwable>map(RxAssertions.ValueException::new)
                .onErrorReturn(error -> error).toBlocking().value();

        verifier.accept(result);
    }

    public static class ValueException extends Exception {
        private final Object value;

        public ValueException(Object value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return "ValueException{" +
                    "value=" + value +
                    '}';
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof ValueException)) return false;

            ValueException that = (ValueException) o;

            return value != null ? value.equals(that.value) : that.value == null;
        }

        @Override
        public int hashCode() {
            return value != null ? value.hashCode() : 0;
        }
    }
}
