package com.gumtree.motors.webapi.converter;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.gumtree.api.Image;
import com.gumtree.api.User;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.motors.webapi.model.MotorsAd;
import com.gumtree.motorssyi.client.model.CreateMotorAdRequest;
import com.gumtree.motorssyi.client.model.ErrorItem;
import com.gumtree.motorssyi.client.model.InternalError;
import com.gumtree.motorssyi.client.model.MotorAdFail;
import com.gumtree.motorssyi.client.model.MotorAdSuccess;
import com.gumtree.motorssyi.client.model.MotorUploadImageFail;
import com.gumtree.motorssyi.client.model.MotorUploadImageSuccess;
import com.gumtree.motorssyi.client.model.PostCodeLookUpApiResponse;
import com.gumtree.motorssyi.client.model.VrmAttribute;
import com.gumtree.motorssyi.client.model.VrmDisplayAttribute;
import com.gumtree.motorssyi.client.model.VrmLookupApiResponse;
import com.gumtree.web.api.WebApiError;
import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState;
import org.junit.Test;
import org.springframework.http.HttpStatus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.function.Consumer;

import static com.gumtree.mobile.test.Fixtures.AUDI;
import static com.gumtree.mobile.test.Fixtures.BMW;
import static com.gumtree.mobile.test.Fixtures.createVehicleMake;
import static com.gumtree.mobile.test.Fixtures.createVehicleRegistrationYear;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildInternalError;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildMotorAdFail;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildMotorUploadImageFail;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildMotorUploadImageSuccess;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildPostCodeLookUpApiResponse;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildVrmAttributeDropDownValue;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildVrmDisplayAttribute;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildVrmLookupApiResponse;
import static com.gumtree.motors.webapi.model.MotorsAd.buildMotorsAd;
import static org.fest.assertions.api.Assertions.assertThat;

public class MotorsSYIConvertersTest {

    @Test
    public void shouldBeAbleToConvertValidPostCodes() {
        //        Given
        String postcode = "TW91Dh";
        PostcodeLookupResponse lookupResponse = new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_RECOGNISED, postcode);

        //        When
        PostCodeLookUpApiResponse response = MotorsSYIConverters.convertToPostCodeLookUpApiResponse(lookupResponse);

        //        Then
        assertThat(response).isEqualTo(buildPostCodeLookUpApiResponse(postCodeLookUpApiResponse -> {
            postCodeLookUpApiResponse.setIsValid(true);
            postCodeLookUpApiResponse.setPostcode(postcode);
        }));
    }

    @Test
    public void shouldBeAbleToConvertInvalidPostCodes() {
        //        Given
        String postcode = "TW91Dhsadadd";
        PostcodeLookupResponse lookupResponse = new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_INVALID, postcode);

        //        When
        PostCodeLookUpApiResponse response = MotorsSYIConverters.convertToPostCodeLookUpApiResponse(lookupResponse);

        //        Then
        assertThat(response).isEqualTo(buildPostCodeLookUpApiResponse(postCodeLookUpApiResponse -> {
            postCodeLookUpApiResponse.setIsValid(false);
            postCodeLookUpApiResponse.setPostcode(postcode);
        }));
    }

    @Test
    public void shouldBeAbleToConvertSimpleVehicleAttributes() {
        //        Given
        ArrayList<AttributeMetadata> attributeMetadata = Lists.newArrayList(createVehicleRegistrationYear().build());
        ImmutableMap<String, String> vehiclesValues = ImmutableMap.of("vehicle_registration_year", "2015");

        //        When
        VrmLookupApiResponse response = MotorsSYIConverters.convertToVrmLookupApiResponse(attributeMetadata, vehiclesValues);

        //        Then
        assertThat(response).isEqualTo(buildVrmLookupApiResponse(vrmLookupApiResponse -> {
            VrmDisplayAttribute vrmDisplayAttribute1 = buildVrmDisplayAttribute(vrmDisplayAttribute -> {
                vrmDisplayAttribute.setName("vehicle_registration_year");
                vrmDisplayAttribute.setLabel("Age");
                vrmDisplayAttribute.isRequired(false);
                vrmDisplayAttribute.setType(AttributeType.YEAR.name());
                vrmDisplayAttribute.setValue("2015");
            });
            vrmLookupApiResponse.setAttributes(Arrays.asList(vrmDisplayAttribute1));
        }));
    }

    @Test
    public void shouldGetVrmAttributesWithDropDownValues() {
        //        Given
        ArrayList<AttributeMetadata> attributeMetadata = Lists.newArrayList(createVehicleMake(AUDI, BMW).build());
        ImmutableMap<String, String> vehiclesValues = ImmutableMap.of("vehicle_make", "audi");

        //        When
        VrmLookupApiResponse response = MotorsSYIConverters.convertToVrmLookupApiResponse(attributeMetadata, vehiclesValues);

        //        Then
        assertThat(response).isEqualTo(buildVrmLookupApiResponse(vrmLookupApiResponse -> {
            VrmDisplayAttribute aVrmDisplayAttribute = buildVrmDisplayAttribute(vrmDisplayAttribute -> {
                vrmDisplayAttribute.setName("vehicle_make");
                vrmDisplayAttribute.setLabel("Make");
                vrmDisplayAttribute.isRequired(false);
                vrmDisplayAttribute.setType(AttributeType.ENUM.name());
                vrmDisplayAttribute.setValue("audi");
                vrmDisplayAttribute.setOptions(Arrays.asList(
                        buildVrmAttributeDropDownValue(vrmAttributeDropDownValue -> {
                            vrmAttributeDropDownValue.setName("audi");
                            vrmAttributeDropDownValue.setLabel("Audi");
                            vrmAttributeDropDownValue.setIsSelected(true);
                        }),
                        buildVrmAttributeDropDownValue(vrmAttributeDropDownValue -> {
                                    vrmAttributeDropDownValue.setName("bmw");
                                    vrmAttributeDropDownValue.setLabel("BMW");
                                    vrmAttributeDropDownValue.setIsSelected(false);
                                }
                        )));
            });
            vrmLookupApiResponse.setAttributes(Arrays.asList(aVrmDisplayAttribute));
        }));
    }

    @Test
    public void shouldGetVrmAttributesWithDropDownValuesEvenIfValueMissingFromDropDown() {
        //        Given
        ArrayList<AttributeMetadata> attributeMetadata = Lists.newArrayList(createVehicleMake(AUDI, BMW).build());
        ImmutableMap<String, String> vehiclesValues = ImmutableMap.of("vehicle_make", "valueDoesNotExistInList");

        //        When
        VrmLookupApiResponse response = MotorsSYIConverters.convertToVrmLookupApiResponse(attributeMetadata, vehiclesValues);

        //        Then
        assertThat(response).isEqualTo(buildVrmLookupApiResponse(vrmLookupApiResponse -> {
            VrmDisplayAttribute aVrmDisplayAttribute = buildVrmDisplayAttribute(vrmDisplayAttribute -> {
                vrmDisplayAttribute.setName("vehicle_make");
                vrmDisplayAttribute.setLabel("Make");
                vrmDisplayAttribute.isRequired(false);
                vrmDisplayAttribute.setType(AttributeType.ENUM.name());
                vrmDisplayAttribute.setValue("valueDoesNotExistInList");
                vrmDisplayAttribute.setOptions(Arrays.asList(
                        buildVrmAttributeDropDownValue(vrmAttributeDropDownValue -> {
                            vrmAttributeDropDownValue.setName("audi");
                            vrmAttributeDropDownValue.setLabel("Audi");
                            vrmAttributeDropDownValue.setIsSelected(false);
                        }),
                        buildVrmAttributeDropDownValue(vrmAttributeDropDownValue -> {
                                    vrmAttributeDropDownValue.setName("bmw");
                                    vrmAttributeDropDownValue.setLabel("BMW");
                                    vrmAttributeDropDownValue.setIsSelected(false);
                                }
                        )));
            });
            vrmLookupApiResponse.setAttributes(Arrays.asList(aVrmDisplayAttribute));
        }));
    }


    @Test
    public void shouldGetVrmAttributesWithDropDownValuesEvenIfKeyMissingFromDropDown() {
        //        Given
        ArrayList<AttributeMetadata> attributeMetadata = Lists.newArrayList(createVehicleMake(AUDI, BMW).build());
        ImmutableMap<String, String> vehiclesValues = ImmutableMap.of("valueDoesNotExistInList", "audi");

        //        When
        VrmLookupApiResponse response = MotorsSYIConverters.convertToVrmLookupApiResponse(attributeMetadata, vehiclesValues);

        //        Then
        assertThat(response).isEqualTo(buildVrmLookupApiResponse(vrmLookupApiResponse -> {
            VrmDisplayAttribute aVrmDisplayAttribute = buildVrmDisplayAttribute(vrmDisplayAttribute -> {
                vrmDisplayAttribute.setName("vehicle_make");
                vrmDisplayAttribute.setLabel("Make");
                vrmDisplayAttribute.isRequired(false);
                vrmDisplayAttribute.setType(AttributeType.ENUM.name());
                vrmDisplayAttribute.setValue(null);
                vrmDisplayAttribute.setOptions(Arrays.asList(
                        buildVrmAttributeDropDownValue(vrmAttributeDropDownValue -> {
                            vrmAttributeDropDownValue.setName("audi");
                            vrmAttributeDropDownValue.setLabel("Audi");
                            vrmAttributeDropDownValue.setIsSelected(false);
                        }),
                        buildVrmAttributeDropDownValue(vrmAttributeDropDownValue -> {
                                    vrmAttributeDropDownValue.setName("bmw");
                                    vrmAttributeDropDownValue.setLabel("BMW");
                                    vrmAttributeDropDownValue.setIsSelected(false);
                                }
                        )));
            });
            vrmLookupApiResponse.setAttributes(Arrays.asList(aVrmDisplayAttribute));
        }));
    }

    @Test
    public void shouldGetVrmAttributesWithNullValuesIfVehiclesValuesIncomplete() {
        //        Given
        ArrayList<AttributeMetadata> attributeMetadata = Lists.newArrayList(createVehicleRegistrationYear().build());
        Map<String, String> vehiclesValues = Collections.emptyMap();

        //        When
        VrmLookupApiResponse response = MotorsSYIConverters.convertToVrmLookupApiResponse(attributeMetadata, vehiclesValues);

        //        Then
        assertThat(response).isEqualTo(buildVrmLookupApiResponse(vrmLookupApiResponse -> {
            VrmDisplayAttribute aVrmDisplayAttribute = buildVrmDisplayAttribute(vrmDisplayAttribute -> {
                vrmDisplayAttribute.setName("vehicle_registration_year");
                vrmDisplayAttribute.setLabel("Age");
                vrmDisplayAttribute.isRequired(false);
                vrmDisplayAttribute.setType(AttributeType.YEAR.name());
                vrmDisplayAttribute.setValue(null);
            });
            vrmLookupApiResponse.setAttributes(Arrays.asList(aVrmDisplayAttribute));
        }));
    }

    @Test
    public void shouldConvertToMotorsAdWithAttributes() {
        //        Given
        User user = givenDefaultUser();
        CreateMotorAdRequest createMotorAdRequest = new CreateMotorAdRequest();
        createMotorAdRequest.setTitle("test ad");
        createMotorAdRequest.setDescription("test description");
        createMotorAdRequest.setPostcode("tw91dh");
        createMotorAdRequest.setPrice(5478500L);
        createMotorAdRequest.setCategoryId(9311L);
        createMotorAdRequest.setShowMapOnAd(true);
        createMotorAdRequest.setVrmAttributes(Arrays.asList(
                buildVrmAttribute(vrmAttribute -> {
                    vrmAttribute.setName("vehicle_make");
                    vrmAttribute.setValue("morgan");
                }), buildVrmAttribute(vrmAttribute -> {
                    vrmAttribute.setName("vehicle_model");
                    vrmAttribute.setValue("ROADSTER V6");
                })
        ));
        long selectedAccountId = 1L;


        //        When
        MotorsAd response = MotorsSYIConverters.convertToMotorsAd(createMotorAdRequest);

        //        Then
        assertThat(response).isEqualTo(buildMotorsAd(motorsAd -> {
            motorsAd.setTitle(createMotorAdRequest.getTitle());
            motorsAd.setDescription(createMotorAdRequest.getDescription());
            motorsAd.setCategoryId(createMotorAdRequest.getCategoryId());
            motorsAd.setPostcode(createMotorAdRequest.getPostcode());
            motorsAd.setPrice(createMotorAdRequest.getPrice());
            motorsAd.setShowMapOnAd(createMotorAdRequest.getShowMapOnAd());
            motorsAd.setAttributes(ImmutableMap.of("vehicle_make", "morgan", "vehicle_model", "ROADSTER V6"));
        }));
    }

    @Test
    public void shouldConvertToMotorsAdWithAttributesEvenIfDuplicates() {
        //        Given
        User user = givenDefaultUser();
        CreateMotorAdRequest createMotorAdRequest = new CreateMotorAdRequest();
        createMotorAdRequest.setTitle("test ad");
        createMotorAdRequest.setDescription("test description");
        createMotorAdRequest.setPostcode("tw91dh");
        createMotorAdRequest.setPrice(5478500L);
        createMotorAdRequest.setCategoryId(9311L);
        createMotorAdRequest.setShowMapOnAd(true);
        createMotorAdRequest.setVrmAttributes(Arrays.asList(
                buildVrmAttribute(vrmAttribute -> {
                    vrmAttribute.setName("vehicle_make");
                    vrmAttribute.setValue("morgan");
                }), buildVrmAttribute(vrmAttribute -> {
                    vrmAttribute.setName("vehicle_make");
                    vrmAttribute.setValue("morgan2");
                })
        ));
        long selectedAccountId = 1L;


        //        When
        MotorsAd response = MotorsSYIConverters.convertToMotorsAd(createMotorAdRequest);

        //        Then
        assertThat(response).isEqualTo(buildMotorsAd(motorsAd -> {
            motorsAd.setTitle(createMotorAdRequest.getTitle());
            motorsAd.setDescription(createMotorAdRequest.getDescription());
            motorsAd.setCategoryId(createMotorAdRequest.getCategoryId());
            motorsAd.setPostcode(createMotorAdRequest.getPostcode());
            motorsAd.setPrice(createMotorAdRequest.getPrice());
            motorsAd.setShowMapOnAd(createMotorAdRequest.getShowMapOnAd());
            motorsAd.setAttributes(ImmutableMap.of("vehicle_make", "morgan2"));
        }));
    }

    @Test
    public void shouldConvertMotorAdSuccess() {
        //        Given
        String redirectId = "5487541";

        //        When
        MotorAdSuccess motorAdSuccess = MotorsSYIConverters.convertToMotorAdSuccess(redirectId);

        //        Then
        assertThat(motorAdSuccess.getRedirect()).isEqualTo(redirectId);
    }

    @Test
    public void shouldConvertToMotorAdFail() {
        //        Given
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.BAD_REQUEST, "validation error", "validation error " +
                "message");
        WebApiErrorException exception = new WebApiErrorException(webApiErrorResponse);

        //        When
        MotorAdFail response = MotorsSYIConverters.converterToMotorAdFail().apply(webApiErrorResponse);

        //        Then
        assertThat(response).isEqualTo(buildMotorAdFail(motorAdFail -> {
            WebApiErrorResponse error = exception.getError();
            motorAdFail.setCode(error.getCode());
            motorAdFail.setMessage(error.getMessage());
            motorAdFail.setErrors(Collections.emptyList());
        }));
    }

    @Test
    public void shouldConvertToMotorAdFailWithErrorItems() {
        //        Given
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.BAD_REQUEST, "validation error", "validation error " +
                "message", Arrays.asList(new WebApiError("invalid-postcode", "invalid postcode", "postcode")));
        WebApiErrorException exception = new WebApiErrorException(webApiErrorResponse);

        //        When
        MotorAdFail response = MotorsSYIConverters.converterToMotorAdFail().apply(webApiErrorResponse);

        //        Then
        assertThat(response).isEqualTo(buildMotorAdFail(motorAdFail -> {
            WebApiErrorResponse error = exception.getError();
            motorAdFail.setCode(error.getCode());
            motorAdFail.setMessage(error.getMessage());
            ErrorItem errorItem = new ErrorItem();
            WebApiError apiError = error.getErrors().get(0);
            errorItem.setError(apiError.getMessage());
            errorItem.setPath(Arrays.asList(apiError.getField()));
            motorAdFail.setErrors(Arrays.asList(errorItem));
        }));
    }

    @Test
    public void shouldBeAbleToConvertAnUploadImageSuccess() {
        //        Given
        String originalName = "amazing_car_pic.jpg";
        Image image = createImage();

        //        When
        MotorUploadImageSuccess response = MotorsSYIConverters.convertToMotorUploadImageSuccess(originalName,
                image);

        //        Then
        assertThat(response).isEqualTo(buildMotorUploadImageSuccess(motorUploadImageSuccess -> {
            motorUploadImageSuccess.setFileName(originalName);
            motorUploadImageSuccess.setId(image.getId());
            motorUploadImageSuccess.setSize(image.getSize());
            motorUploadImageSuccess.setUrl(image.getUrl());
            motorUploadImageSuccess.setThumbnailUrl(image.getThumbnailUrl());
        }));
    }

    @Test
    public void shouldBeAbleToConvertAnMotorUploadImageFail() {
        //        Given
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.BAD_REQUEST, "validation error", "validation error " +
                "message");

        //        When
        MotorUploadImageFail response = MotorsSYIConverters.converterToMotorUploadImageFail().apply(webApiErrorResponse);

        //        Then
        assertThat(response).isEqualTo(buildMotorUploadImageFail(motorUploadImageFail -> {
                    motorUploadImageFail.setCode(webApiErrorResponse.getCode());
                    motorUploadImageFail.setMessage(webApiErrorResponse.getMessage());
                })
        );
    }

    @Test
    public void shouldConvertToInternalError() {
        //        Given
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "something went wrong",
                "something went wrong");

        //        When
        InternalError response = MotorsSYIConverters.convertToInternalError(webApiErrorResponse);

        //        Then
        assertThat(response).isEqualTo(buildInternalError(internalError -> {
            internalError.setErrorCode(webApiErrorResponse.getCode());
            internalError.setErrorDetail(webApiErrorResponse.getMessage());
            internalError.setUserMessage("Unexpected error");
        }));
    }

    private Image createImage() {
        Long id = 1L;
        String size = "2";
        String thumbnailUrl = "http://i.sandbox.ebayimg.com/t.jpg";
        String url = "http://i.sandbox.ebayimg.com/u.jpg";

        Image image = new Image();
        image.setId(id);
        image.setSize(size);
        image.setThumbnailUrl(thumbnailUrl);
        image.setUrl(url);
        return image;
    }

    private VrmAttribute buildVrmAttribute(Consumer<VrmAttribute> consumer) {
        VrmAttribute vrmAttribute = new VrmAttribute();
        consumer.accept(vrmAttribute);
        return vrmAttribute;
    }

    private User givenDefaultUser() {
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setFirstName("Test");
        return user;
    }
}