
package com.gumtree.util;

import org.json.JSONObject;
import org.junit.Test;

import static org.junit.Assert.*;

public class HttpUtilsTest {

    HttpUtils httpUtils = new HttpUtils();

    @Test
    public void testSendJsonPost_realCall() {
        String apiUrl = "https://jsonplaceholder.typicode.com/posts";

        JSONObject requestBody = new JSONObject();
        requestBody.put("title", "foo");
        requestBody.put("body", "bar");
        requestBody.put("userId", 1);

        JSONObject response = httpUtils.sendJsonPost(apiUrl, requestBody);

        assertNotNull(response);
        assertTrue(response.has("id"));
    }

    @Test
    public void testSendJsonGet_realCall() {
        String apiUrl = "https://jsonplaceholder.typicode.com/posts/1?input=%s&type=%s";

        JSONObject response = httpUtils.sendJsonGet(apiUrl,"123","345");

        assertNotNull(response);
        assertTrue(response.has("id"));
        assertTrue(response.has("title"));
        assertTrue(response.has("body"));
    }

    @Test(expected = RuntimeException.class)
    public void testSendJsonGet_invalidUrl_throwsException() {
        String apiUrl = "https://invalid-url-that-will-fail.example.com/nonexistent";

        httpUtils.sendJsonGet(apiUrl); // 应该抛出 IOException
    }

    @Test
    public void testSendJsonRequest_withHeaders() throws Exception {
        // 使用本地ServerSocket模拟一个简单的HTTP服务
        java.net.ServerSocket serverSocket = new java.net.ServerSocket(0);
        int port = serverSocket.getLocalPort();
        Thread serverThread = new Thread(() -> {
            try {
                java.net.Socket socket = serverSocket.accept();
                java.io.BufferedReader in = new java.io.BufferedReader(new java.io.InputStreamReader(socket.getInputStream()));
                String line;
                boolean headerFound = false;
                while ((line = in.readLine()) != null && !line.isEmpty()) {
                    if (line.startsWith("X-Test-Header: test-value")) {
                        headerFound = true;
                    }
                }
                java.io.OutputStream out = socket.getOutputStream();
                String response = "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\n\r\n{\"ok\":true}";
                out.write(response.getBytes());
                out.flush();
                socket.close();
                serverSocket.close();
                assertTrue(headerFound);
            } catch (Exception e) {
                // ignore
            }
        });
        serverThread.start();
        String apiUrl = "http://localhost:" + port + "/test";
        JSONObject requestBody = new JSONObject();
        requestBody.put("foo", "bar");
        java.util.Map<String, String> headers = new java.util.HashMap<>();
        headers.put("X-Test-Header", "test-value");
        JSONObject response = httpUtils.sendJsonRequest(apiUrl, "GET", requestBody, headers);
        assertNotNull(response);
        assertTrue(response.has("ok"));
    }

    @Test(expected = RuntimeException.class)
    public void testSendJsonRequest_non200Response_throwsException() throws Exception {
        // 使用本地ServerSocket模拟一个返回404的HTTP服务
        java.net.ServerSocket serverSocket = new java.net.ServerSocket(0);
        int port = serverSocket.getLocalPort();
        Thread serverThread = new Thread(() -> {
            try {
                java.net.Socket socket = serverSocket.accept();
                java.io.BufferedReader in = new java.io.BufferedReader(new java.io.InputStreamReader(socket.getInputStream()));
                while ((in.readLine()) != null && !in.readLine().isEmpty()) {}
                java.io.OutputStream out = socket.getOutputStream();
                String response = "HTTP/1.1 404 Not Found\r\nContent-Type: application/json\r\n\r\n{\"error\":true}";
                out.write(response.getBytes());
                out.flush();
                socket.close();
                serverSocket.close();
            } catch (Exception e) {
                // ignore
            }
        });
        serverThread.start();
        String apiUrl = "http://localhost:" + port + "/test";
        JSONObject requestBody = new JSONObject();
        requestBody.put("foo", "bar");
        httpUtils.sendJsonRequest(apiUrl, "GET", requestBody, null); // 应抛出RuntimeException
    }
}