package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationBegin;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserRegistrationBeginEventConverterTest {

    @Mock
    private RequestDetailsService requestDetailsService;

    @Mock
    private ZenoConverterService zenoConverterService;

    @InjectMocks
    private UserRegistrationBeginEventConverter eventConverter;


    @Test
    public void convertUserRegistrationBeginZenoEvent() {

        // given
        String emailAddress = "<EMAIL>";
        UserData userData = UserData.aUser().withUserEmail(emailAddress).build();
        DeviceData deviceData = new DeviceData("", "", "", "");
        PageData pageData = PageData.aPage().withPageType(PageType.UserRegistrationForm).build();

        when(requestDetailsService.getUserData()).thenReturn(userData);
        when(requestDetailsService.getDeviceData()).thenReturn(deviceData);
        when(requestDetailsService.getPageData(PageType.UserRegistrationForm)).thenReturn(pageData);

        UserRegistrationBeginZenoEvent userRegistrationBeginZenoEvent = new UserRegistrationBeginZenoEvent(emailAddress);

        // when
        UserRegistrationBegin userRegistrationBegin = (UserRegistrationBegin) eventConverter.convert(userRegistrationBeginZenoEvent);

        // then
        assertThat(userRegistrationBegin.getP().getT()).isEqualTo(PageType.UserRegistrationForm);
    }


}
