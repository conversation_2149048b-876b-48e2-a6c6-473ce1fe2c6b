package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationFail;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserRegistrationFailEventConverterTest {

    @Mock
    private RequestDetailsService requestDetailsService;

    @Mock
    private ZenoConverterService zenoConverterService;

    @InjectMocks
    private UserRegistrationFailEventConverter eventConverter;


    @Test
    public void convertUserRegistrationFailZenoEvent() {

        // given
        String emailAddress = "<EMAIL>";
        UserData userData = UserData.aUser().withUserEmail(emailAddress).build();
        DeviceData deviceData = new DeviceData("", "", "", "");
        PageData pageData = PageData.aPage().withPageType(PageType.UserRegistrationForm).build();

        when(requestDetailsService.getUserData()).thenReturn(userData);
        when(requestDetailsService.getDeviceData()).thenReturn(deviceData);
        when(requestDetailsService.getPageData(PageType.UserRegistrationForm)).thenReturn(pageData);

        UserRegistrationFailZenoEvent userRegistrationFailZenoEvent = new UserRegistrationFailZenoEvent(emailAddress);

        // when
        UserRegistrationFail userRegistrationFail = (UserRegistrationFail) eventConverter.convert(userRegistrationFailZenoEvent);

        // then
        assertThat(userRegistrationFail.getP().getT()).isEqualTo(PageType.UserRegistrationForm);
    }

}
