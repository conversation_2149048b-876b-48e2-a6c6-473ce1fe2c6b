package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationSuccess;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserRegistrationSuccessEventConverterTest {
    @Mock
    private RequestDetailsService requestDetailsService;

    @Mock
    private ZenoConverterService zenoConverterService;

    @InjectMocks
    private UserRegistrationSuccessEventConverter eventConverter;


    @Test
    public void convertUserRegistrationSuccessZenoEvent() {

        // given
        Long userId = 1L;
        String emailAddress = "<EMAIL>";
        UserData userData = UserData.aUser().withUserId(userId).withUserEmail(emailAddress).build();
        DeviceData deviceData = new DeviceData("", "", "", "");
        PageData pageData = PageData.aPage().withPageType(PageType.UserRegistrationSuccess).build();

        when(requestDetailsService.getUserData()).thenReturn(userData);
        when(requestDetailsService.getDeviceData()).thenReturn(deviceData);
        when(requestDetailsService.getPageData(PageType.UserRegistrationSuccess)).thenReturn(pageData);

        UserRegistrationSuccessZenoEvent userRegistrationSuccessZenoEvent = new UserRegistrationSuccessZenoEvent(userId, emailAddress);

        // when
        UserRegistrationSuccess userRegistrationSuccess = (UserRegistrationSuccess) eventConverter.convert(userRegistrationSuccessZenoEvent);

        // then
        assertThat(userRegistrationSuccess.getP().getT()).isEqualTo(PageType.UserRegistrationSuccess);
    }

}
