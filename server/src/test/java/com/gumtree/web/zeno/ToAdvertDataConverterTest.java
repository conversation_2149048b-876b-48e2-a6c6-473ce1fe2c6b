package com.gumtree.web.zeno;

import com.gumtree.common.util.search.AdvertClickSource;
import com.gumtree.fulladsearch.model.FullAdAccount;
import com.gumtree.fulladsearch.model.FullAdFlatAd;
import com.gumtree.mobile.web.storage.SessionDataService;
import com.gumtree.zeno.core.domain.AdvertData;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static com.gumtree.fulladsearch.model.FullAdAccount.ReplyTypeEnum.TRIFECTA;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.when;

public class ToAdvertDataConverterTest {

    private FullAdFlatAd flatAd;
    @Mock
    private SessionDataService sessionDataService;
    @InjectMocks
    private ToAdvertDataConverter converter;

    @Before
    public void setup() {

        DateTime NOW = new DateTime();
        String testVat = "test_vat_987654321";

        flatAd = new FullAdFlatAd();
        flatAd.setId(123L);
        flatAd.setDescription("desc");
        flatAd.setAccount(new FullAdAccount().id(1l)
                .pro(true)
                .postingSince(NOW.getMillis())
                .replyType(TRIFECTA)
                .vatNumber(testVat));
        flatAd.setLastModifiedDate(1234L);
        flatAd.setCreatedDate(12345L);

        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void advertClickSourceFoundWhenAvailable() {
        when(sessionDataService.getAdvertClickThroughType(123L)).thenReturn(AdvertClickSource.NATURAL);
        AdvertData advertData = converter.flatAdToAdvertData(flatAd);
        assertThat(advertData.getAcs(), equalTo(AdvertClickSource.NATURAL.getParameterValue()));
    }

    @Test
    public void advertClickSourceOtherWhenNotAvailable() {
        when(sessionDataService.getAdvertClickThroughType(123L)).thenReturn(AdvertClickSource.OTHER);
        AdvertData advertData = converter.flatAdToAdvertData(flatAd);
        assertThat(advertData.getAcs(), equalTo(AdvertClickSource.OTHER.getParameterValue()));
    }

    @Test
    public void advertClickSourceFoundWhenManyTypesAvailable() {
        when(sessionDataService.getAdvertClickThroughType(123L)).thenReturn(AdvertClickSource.NEARBY);
        AdvertData advertData = converter.flatAdToAdvertData(flatAd);
        assertThat(advertData.getAcs(), equalTo(AdvertClickSource.NEARBY.getParameterValue()));
    }

}
