package com.gumtree.web.zeno.messages;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.messagecentre.MessageCentreModel;
import com.gumtree.web.seller.page.reviews.model.UserRating;
import com.gumtree.zeno.core.converter.impl.GenericPageEventConverter;
import com.gumtree.zeno.core.domain.AdvertData;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.HierarchicalData;
import com.gumtree.zeno.core.domain.MessagesData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.GenericPageEvent;
import com.gumtree.zeno.core.event.user.sellerside.messages.LoadMessagesEvent;
import com.gumtree.zeno.core.integration.ZenoHelperService;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.ZenoService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.springframework.web.servlet.ModelAndView;

import java.util.Collections;
import java.util.Optional;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.junit.Assert.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

public class LoadMessagesEventConverterTest {
    @Mock
    ZenoService zenoService;
    @Mock
    ZenoConverterService zenoConverterService;
    @Mock
    ZenoHelperService zenoHelperService;

    final static String contactEmail = "<EMAIL>";
    final static Long TEST_AD_ID = 12345L;

    LoadMessagesEventConverter converter;

    @Before
    public void setup(){
        initMocks(this);
        this.converter = new LoadMessagesEventConverter(zenoConverterService, zenoService, zenoHelperService);
    }

    @Test
    public void testConvertToEventNull(){
        ModelAndView modelAndView = new ModelAndView();
        when(zenoHelperService.getPageEventInputs(modelAndView)).thenReturn(null);
        LoadMessagesEvent loadMessagesEvent = converter.convertToEvent(new MessagesData(contactEmail), modelAndView);
        assertThat(loadMessagesEvent, nullValue());
    }

    @Test
    public void testConvertToEvent(){
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject(CommonModel.MODEL_KEY, new MessageCentreModel());
        PageType pageType = PageType.Unknown;
        HierarchicalData hierarchicalData = mock(HierarchicalData.class);
        AdvertData advertData = mock(AdvertData.class);
        GenericPageEvent genericPageEvent = mock(GenericPageEvent.class);
        DeviceData deviceData = mock(DeviceData.class);
        UserData userData = mock(UserData.class);
        GenericPageEventConverter.InputType pageEventInputs = new GenericPageEventConverter.InputType(pageType,
                hierarchicalData, hierarchicalData, advertData);
        setExpectations(modelAndView, hierarchicalData, advertData,
                genericPageEvent, deviceData, userData, pageEventInputs);

        LoadMessagesEvent loadMessagesEvent = converter.convertToEvent(new MessagesData(contactEmail), modelAndView);

        assertThat(loadMessagesEvent.getU(), equalTo(userData));
        assertThat(loadMessagesEvent.getD(), equalTo(deviceData));
        assertThat(loadMessagesEvent.getC(), equalTo(hierarchicalData));
        assertThat(loadMessagesEvent.getL(), equalTo(hierarchicalData));
        assertThat(loadMessagesEvent.getA(), equalTo(advertData));
        assertThat(loadMessagesEvent.getA().getU(), nullValue());
    }

    @Test
    public void testConvertToEventWithLastConversationAdIdAndRating(){
        ModelAndView modelAndView = new ModelAndView();
        UserRating userRating = new UserRating(10, 4.7f, Collections.emptyMap());
        MessageCentreModel mockMessageCentreModel = spy(new MessageCentreModel());
        when(mockMessageCentreModel.getLastConversationAdId()).thenReturn(Optional.of(TEST_AD_ID));
        when(mockMessageCentreModel.getConverseeRating()).thenReturn(Optional.of(userRating));
        modelAndView.addObject(CommonModel.MODEL_KEY, mockMessageCentreModel);
        PageType pageType = PageType.Unknown;
        HierarchicalData hierarchicalData = mock(HierarchicalData.class);
        GenericPageEvent genericPageEvent = mock(GenericPageEvent.class);
        DeviceData deviceData = mock(DeviceData.class);
        UserData userData = mock(UserData.class);
        GenericPageEventConverter.InputType pageEventInputs = new GenericPageEventConverter.InputType(pageType,
                hierarchicalData, hierarchicalData);
        setExpectations(modelAndView, hierarchicalData, null,
                genericPageEvent, deviceData, userData, pageEventInputs);

        LoadMessagesEvent loadMessagesEvent = converter.convertToEvent(new MessagesData(contactEmail), modelAndView);

        assertThat(loadMessagesEvent.getA().getId(), equalTo(TEST_AD_ID));
        assertThat(loadMessagesEvent.getA().getU(), notNullValue());
        assertThat(loadMessagesEvent.getA().getU().getScr(), equalTo(4.7f));
        assertThat(loadMessagesEvent.getA().getU().getRc(), equalTo(10));
    }

    private void setExpectations(ModelAndView modelAndView, HierarchicalData hierarchicalData, AdvertData advertData,
                                 GenericPageEvent genericPageEvent, DeviceData deviceData,
                                 UserData userData, GenericPageEventConverter.InputType pageEventInputs) {
        when(zenoHelperService.getPageEventInputs(modelAndView)).thenReturn(pageEventInputs);
        when(zenoService.makeGenericEvent(any(GenericPageEventConverter.InputType.class))).thenReturn(genericPageEvent);
        when(genericPageEvent.getU()).thenReturn(userData);
        when(genericPageEvent.getD()).thenReturn(deviceData);
        when(genericPageEvent.getC()).thenReturn(hierarchicalData);
        when(genericPageEvent.getL()).thenReturn(hierarchicalData);
        when(genericPageEvent.getA()).thenReturn(advertData);
        when(genericPageEvent.getP()).thenReturn(mock(PageData.class));
    }
}
