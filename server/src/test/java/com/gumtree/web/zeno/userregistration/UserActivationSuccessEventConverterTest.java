package com.gumtree.web.zeno.userregistration;

import com.gumtree.api.User;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserActivationSuccess;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationSuccess;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserActivationSuccessEventConverterTest {
    @Mock
    private RequestDetailsService requestDetailsService;

    @Mock
    private ZenoConverterService zenoConverterService;

    @InjectMocks
    private UserActivationSuccessEventConverter eventConverter;


    @Test
    public void convertUserActivationSuccessZenoEvent() {

        // given
        Long userId = 1L;
        String emailAddress = "<EMAIL>";
        User user = User.builder().withEmail(emailAddress).withId(userId).build();
        UserData userData = UserData.aUser().withUserId(userId).withUserEmail(emailAddress).build();
        DeviceData deviceData = new DeviceData("", "", "", "");
        PageData pageData = PageData.aPage().withPageType(PageType.UserActivationSuccess).build();

        when(requestDetailsService.getUserData()).thenReturn(userData);
        when(requestDetailsService.getDeviceData()).thenReturn(deviceData);
        when(requestDetailsService.getPageData(PageType.UserActivationSuccess)).thenReturn(pageData);

        UserActivationSuccessZenoEvent userActivationSuccessZenoEvent = new UserActivationSuccessZenoEvent(user);

        // when
        UserActivationSuccess userActivationSuccess = (UserActivationSuccess) eventConverter.convert(userActivationSuccessZenoEvent);

        // then
        assertThat(userActivationSuccess.getP().getT()).isEqualTo(PageType.UserActivationSuccess);
    }

}
