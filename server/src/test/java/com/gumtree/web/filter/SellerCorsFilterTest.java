 package com.gumtree.web.filter;

 import org.junit.BeforeClass;
 import org.junit.Test;
 import org.junit.runner.RunWith;
 import org.mockito.Mock;
 import org.mockito.runners.MockitoJUnitRunner;

 import javax.servlet.FilterChain;
 import javax.servlet.ServletException;
 import javax.servlet.http.HttpServletRequest;
 import javax.servlet.http.HttpServletResponse;

 import java.io.IOException;
 import java.util.Arrays;
 import java.util.List;

 import static org.mockito.Matchers.contains;
 import static org.mockito.Matchers.eq;
 import static org.mockito.Mockito.when;
 import static org.mockito.Mockito.reset;
 import static org.mockito.Mockito.verify;
 import static org.mockito.Mockito.times;
 import static org.mockito.Mockito.never;
 import static org.mockito.Mockito.anyString;

 @RunWith(MockitoJUnitRunner.class)
 public class SellerCorsFilterTest {

     @Mock
     HttpServletRequest request;

     @Mock
     HttpServletResponse response;

     @Mock
     FilterChain filterChain;

     static SellerCorsFilter sellerCorsFilter;

     static List<String> allowedOrigins = Arrays.asList(
             "https://www.gumtree.com",
             "https://www.staging.gumtree.io",
             "https://www.mobile.gumtree.io",
             "http://dev.gumtree.com:8080"
    );
     static List<String> paths = Arrays.asList(
             "/postad/",
             "/ajax/category/children",
             "/ajax/vrn",
             "/api/category/suggest",
             "/my-account/jobs/cv",
             "/my-account/jobs/cv/upload",
             "/my-account/jobs/cv/delete",
             "/my-account/jobs/cv/download",
             "/manage-account/update",
             "/manage-account/change-password",
             "/manage-account/subscribe",
             "/manage-account/contact-email/",
             "/manage-account/deactivate"
     );

     @BeforeClass
     public static void setUp() {
         sellerCorsFilter = new SellerCorsFilter();
     }

     @Test
     public void shouldMatchAllPaths() throws ServletException, IOException {
         for (String prefix : paths) {
             assertShouldMatch(prefix);
         }
     }

     @Test
     public void shouldMatchAndApplyHeadersOnPostAdSlash() throws ServletException, IOException {
         for (String domain : allowedOrigins) {
             //given
             when(request.getRequestURI()).thenReturn("/postad/123456");
             when(request.getHeader("Origin")).thenReturn(domain);
             //when
             sellerCorsFilter.doFilterInternal(request, response, filterChain);
             //then
             verify(response, times(1)).addHeader("Access-Control-Allow-Origin", domain);
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("GET"));
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("POST"));
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("OPTIONS"));
             verify(response).addHeader(eq("Access-Control-Allow-Headers"), contains("Content-Type,Authorization"));

             reset(request);
             reset(response);
         }
     }

     @Test
     public void shouldImplementOptionsOnPostAdSlash() throws ServletException, IOException {
         for (String domain : allowedOrigins) {
             //given
             when(request.getRequestURI()).thenReturn("/postad/123456");
             when(request.getHeader("Origin")).thenReturn(domain);
             when(request.getHeader("Access-Control-Request-Method")).thenReturn("POST");
             when(request.getMethod()).thenReturn("OPTIONS");
             //when
             sellerCorsFilter.doFilterInternal(request, response, filterChain);
             //then
             verify(response, times(1)).addHeader("Access-Control-Allow-Origin", domain);
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("GET"));
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("POST"));
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("OPTIONS"));
             verify(response).addHeader(eq("Access-Control-Allow-Headers"), contains("Content-Type,Authorization"));
             verify(response).setStatus(200);

             reset(request);
             reset(response);
         }
     }

     @Test
     public void shouldImplementOptionsOnManageAdContactEmailSlash() throws ServletException, IOException {
         for (String domain : allowedOrigins) {
             //given
             when(request.getRequestURI()).thenReturn("/manage-account/contact-email/123456");
             when(request.getHeader("Origin")).thenReturn(domain);
             when(request.getHeader("Access-Control-Request-Method")).thenReturn("DELETE");
             when(request.getMethod()).thenReturn("OPTIONS");
             //when
             sellerCorsFilter.doFilterInternal(request, response, filterChain);
             //then
             verify(response, times(1)).addHeader("Access-Control-Allow-Origin", domain);
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("GET"));
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("POST"));
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("PUT"));
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("DELETE"));
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("OPTIONS"));
             verify(response).addHeader(eq("Access-Control-Allow-Headers"), contains("Content-Type,Authorization,X-Requested-With"));
             verify(response).setStatus(200);

             reset(request);
             reset(response);
         }
     }

     @Test
     public void shouldNotMatchArbitraryOrigins() throws ServletException, IOException {
         //given
         when(request.getRequestURI()).thenReturn("/ajax/category/children?id=2551&postAd=true");
         when(request.getHeader("Origin")).thenReturn("https://www.example.com");
         //when
         sellerCorsFilter.doFilterInternal(request, response, filterChain);
         //then
         verify(response, never()).addHeader(eq("Access-Control-Allow-Origin"), anyString());
     }

     private void assertShouldMatch(String path) throws ServletException, IOException {
         for (String domain : allowedOrigins) {
             //given
             when(request.getRequestURI()).thenReturn(path);
             when(request.getHeader("Origin")).thenReturn(domain);
             //when
             sellerCorsFilter.doFilterInternal(request, response, filterChain);
             //then
             verify(response, times(1)).addHeader("Access-Control-Allow-Origin", domain);
             verify(response).addHeader(eq("Access-Control-Allow-Methods"), contains("GET"));

             reset(request);
             reset(response);
         }
     }

 }