package com.gumtree.web.seller.page;


import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.config.DefaultPropertyInitializer;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.abtest.Experiments;
import com.gumtree.web.abtest.ExperimentsProvider;
import com.gumtree.web.browse.BrowseCategoriesLocationResolvingService;
import com.gumtree.web.browse.BrowseCategoriesService;
import com.gumtree.web.common.domain.location.UnresolvedLocation;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper;
import com.gumtree.web.cookie.cutters.userpreferences.UserPreferencesCookie;
import com.gumtree.web.feature.FeatureSwitchManager;
import com.gumtree.web.seller.mvctests.SellerMvcTest;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.seller.security.apiauthentication.CSRFTokenService;
import com.gumtree.web.seller.security.shiro.BaseShiroTest;
import com.gumtree.zeno.core.service.ZenoService;
import org.junit.Before;
import org.junit.BeforeClass;
import org.mockito.Mockito;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.Collections;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.withSettings;

/**
 * Class defines common set up for controllers extending {@link BaseSellerController}
 */
public class BaseSellerControllerTest extends BaseShiroTest {
    // common to all controller test
    protected BushfireApi bushfireApi;
    protected UserSessionService userSessionService;
    protected CategoryModel categoryModel;
    protected CategoryService categoryService;
    protected LocationService locationService;
    protected CookieResolver cookieResolver;
    protected ZenoService zenoService;
    protected ExperimentsProvider experimentsProvider;
    protected FeatureSwitchManager featureSwitchManager;
    protected CSRFTokenService csrfTokenService;
    protected ErrorMessageResolver messageResolver;
    protected ApiCallExecutor apiCallExecutor;
    protected UrlScheme urlScheme;
    protected Experiments experiments;
    protected HttpServletRequest request;
    protected HttpServletResponse response;
    protected RemoteIP remoteIP;

    // utility properties - most often used
    protected GumtreePageContext pageContext;
    protected MetricRegistry metricRegistry;
    protected AppBannerCookieHelper appBannerCookieHelper;

    protected ParameterEncryption parameterEncryption;

    private BrowseCategoriesService browseCategoriesService;
    private BrowseCategoriesLocationResolvingService locationsService;
    protected UserServiceFacade userServiceFacade;

    @BeforeClass
    public static void initContext() {
        new DefaultPropertyInitializer().initialize(null);
    }

    @Before
    public void setupBaseSellerProperties() throws Exception {
        bushfireApi = mock(BushfireApi.class, withSettings().defaultAnswer(RETURNS_DEEP_STUBS));
        userSessionService = mock(UserSessionService.class);
        categoryModel = mock(CategoryModel.class);
        categoryService = mock(CategoryService.class);
        locationService = mock(LocationService.class);
        cookieResolver = mock(CookieResolver.class);
        csrfTokenService = mock(CSRFTokenService.class);
        zenoService = mock(ZenoService.class);
        experimentsProvider = mock(ExperimentsProvider.class);
        featureSwitchManager = mock(FeatureSwitchManager.class);
        messageResolver = mock(ErrorMessageResolver.class);
        apiCallExecutor = mock(ApiCallExecutor.class);
        urlScheme = mock(UrlScheme.class);
        experiments = new Experiments(Collections.emptyMap());
        request = mock(HttpServletRequest.class);
        response = mock(HttpServletResponse.class);
        pageContext = mock(GumtreePageContext.class);
        appBannerCookieHelper = mock(AppBannerCookieHelper.class);
        remoteIP = mock(RemoteIP.class);
        browseCategoriesService = Mockito.mock(BrowseCategoriesService.class);
        locationsService = Mockito.mock(BrowseCategoriesLocationResolvingService.class);
        parameterEncryption =  Mockito.mock(ParameterEncryption.class);
        userServiceFacade = Mockito.mock(UserServiceFacade.class);

        when(locationsService.locallyResolveLocation(any(UserPreferencesCookie.class))).thenReturn(new UnresolvedLocation());

        when(categoryModel.getRootCategory()).thenReturn(new Category(1l, "uk", "UK"));
        when(categoryModel.getCategory(anyLong())).thenReturn(Optional.of(new Category(1l, "uk", "UK")));

        when(userSessionService.getUser()).thenReturn(Optional.<com.gumtree.api.User>absent());

        metricRegistry = mock(MetricRegistry.class);
        when(metricRegistry.counter(anyString())).thenReturn(mock(Counter.class));

        when(request.getRequestURL()).thenReturn(new StringBuffer());

        when(remoteIP.getIpAddress()).thenReturn("*********");

        SellerMvcTest.setupSecManager();
    }

    /**
     * It can not be called in @Before as controller needs to be created first
     *
     * @param controller candidate test controller
     */
    protected void autowireAbExperimentsService(CoreModelBaseController controller) {
        when(experimentsProvider.get()).thenReturn(experiments);

        when(featureSwitchManager.isOn(any())).thenReturn(false);
        when(featureSwitchManager.isOff(any())).thenReturn(true);
        when(featureSwitchManager.getAllEnabledFeatures()).thenReturn(Collections.emptyList());


        PropSupplier<Boolean> falsePropSupplier = () -> false;

        PropSupplier<Integer> falsePropSupplierInt = () -> 0;

        CoreModel.BuilderProps props =
                new CoreModel.BuilderProps(Env.DEV, "www.gumtree.com", "my.gumtree.com", false, falsePropSupplier, falsePropSupplier, falsePropSupplierInt);
        CoreModel.BuilderFactory builderFactory =
                new CoreModel.BuilderFactory(props, categoryModel, cookieResolver, userSessionService, experimentsProvider,
                        browseCategoriesService, locationsService, featureSwitchManager, csrfTokenService);
        controller.setCoreModelBuilderFactory(builderFactory);
    }

    protected <T> T extractModel(ModelAndView mav, Class<T> clazz) {
        return (T) mav.getModel().get(CommonModel.MODEL_KEY);
    }
}
