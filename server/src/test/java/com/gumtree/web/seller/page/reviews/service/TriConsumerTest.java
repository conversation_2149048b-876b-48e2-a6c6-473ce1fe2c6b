package com.gumtree.web.seller.page.reviews.service;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class TriConsumerTest {

    @Test
    public void testTriConsumerAcceptsThreeArguments() {
        // given
        final String[] capturedValues = new String[3];
        TriConsumer<String, Integer, Boolean> triConsumer = (first, second, third) -> {
            capturedValues[0] = first;
            capturedValues[1] = String.valueOf(second);
            capturedValues[2] = String.valueOf(third);
        };

        // when
        triConsumer.accept("test", 123, true);

        // then
        assertThat(capturedValues[0]).isEqualTo("test");
        assertThat(capturedValues[1]).isEqualTo("123");
        assertThat(capturedValues[2]).isEqualTo("true");
    }

    @Test
    public void testTriConsumerWithNullValues() {
        // given
        final Object[] capturedValues = new Object[3];
        TriConsumer<String, Integer, Boolean> triConsumer = (first, second, third) -> {
            capturedValues[0] = first;
            capturedValues[1] = second;
            capturedValues[2] = third;
        };

        // when
        triConsumer.accept(null, null, null);

        // then
        assertThat(capturedValues[0]).isNull();
        assertThat(capturedValues[1]).isNull();
        assertThat(capturedValues[2]).isNull();
    }

    @Test
    public void testTriConsumerAsLambda() {
        // given
        final boolean[] wasCalled = {false};
        
        // when
        TriConsumer<Boolean, Boolean, Boolean> triConsumer = (a, b, c) -> {
            wasCalled[0] = a && b && c;
        };
        triConsumer.accept(true, true, true);

        // then
        assertThat(wasCalled[0]).isTrue();
    }

    @Test
    public void testTriConsumerWithDifferentTypes() {
        // given
        final Object[] capturedValues = new Object[3];
        TriConsumer<Long, String, Double> triConsumer = (longVal, stringVal, doubleVal) -> {
            capturedValues[0] = longVal;
            capturedValues[1] = stringVal;
            capturedValues[2] = doubleVal;
        };

        // when
        triConsumer.accept(100L, "test", 3.14);

        // then
        assertThat(capturedValues[0]).isEqualTo(100L);
        assertThat(capturedValues[1]).isEqualTo("test");
        assertThat(capturedValues[2]).isEqualTo(3.14);
    }
}