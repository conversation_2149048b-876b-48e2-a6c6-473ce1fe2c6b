package com.gumtree.web.seller.page.payment.messages;

import com.google.common.base.Optional;
import com.gumtree.api.Account;
import com.gumtree.api.Ad;
import com.gumtree.api.CreditPackage;
import com.gumtree.api.PaymentInstrument;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.api.domain.payment.ApiPaymentDetail;
import com.gumtree.domain.category.Categories;
import com.gumtree.service.category.CategoryService;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static com.gumtree.web.seller.page.payment.messages.OrderItemMessageGenerator.GUMTREE_MEDIA_FALLBACK_LINK;
import static com.gumtree.web.seller.page.payment.messages.OrderItemMessageGenerator.GUMTREE_MEDIA_JOBS_LINK;
import static com.gumtree.web.seller.page.payment.messages.OrderItemMessageGenerator.GUMTREE_MEDIA_MOTORS_LINK;
import static com.gumtree.web.seller.page.payment.messages.OrderItemMessageGenerator.GUMTREE_MEDIA_RENTALS_LINK;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

public class OrderItemMessageGeneratorTest {

    private static final long ADVERT_ID = 4567L;
    private static final Category ADVERT_CATEGORY = new Category(7890L, "", "");

    private CategoryService categoryService = mock(CategoryService.class);
    private OrderItemMessageGenerator.MessageProvider messageProvider = mock(OrderItemMessageGenerator.MessageProvider.class);

    private Account account = new Account();
    private OrderItemMessageGenerator messageGenerator;

    @Before
    public void setUp() throws Exception {
        account.setPro(false);

        Ad ad = new Ad();
        ad.setCategoryId(ADVERT_CATEGORY.getId());
        AdvertApi advertApi = when(mock(AdvertApi.class).getAdvert(ADVERT_ID)).thenReturn(ad).getMock();
        BushfireApi bushfireApi = when(mock(BushfireApi.class).advertApi()).thenReturn(advertApi).getMock();

        when(categoryService.getById(ADVERT_CATEGORY.getId())).thenReturn(Optional.of(ADVERT_CATEGORY));
        when(categoryService.isChild(any(Category.class), any(Category.class))).thenReturn(true);
        when(categoryService.getByUniqueName(anyString())).thenReturn(Optional.absent());

        messageGenerator = new OrderItemMessageGenerator(bushfireApi, categoryService, messageProvider);
    }

    @Test
    public void shouldReturnNoMessageWhenNoPaymentDetails() {
        // given
        ApiOrder apiOrder = newApiOrder(new ApiOrderItem());

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).isEmpty();
        verifyNoMoreInteractions(messageProvider);
    }

    @Test
    public void shouldReturnNoMessageWhenPaymentDetailsWithNoCreditPackage() {
        // given
        ApiOrderItem apiOrderItem = apiOrderItemWithoutCreditPackage();
        ApiOrder apiOrder = newApiOrder(apiOrderItem);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).isEmpty();
        verifyNoMoreInteractions(messageProvider);
    }

    @Test
    public void shouldReturnMessageForNonProAccountWithRemainingCredits() {
        // given
        long initialCredits = 10L, adjustedCredits = 2L, usedCredits = 5L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        when(messageProvider.resolveMessage("postad.package.free.remaining", creditPackage.getUsedCredits(),
                initialCredits + adjustedCredits, GUMTREE_MEDIA_FALLBACK_LINK))
                .thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldReturnMessageForNonProAccountWithRemainingCreditsInJobs() {
        // given
        setParentCategory(Categories.JOBS.getSeoName());
        long initialCredits = 10L, adjustedCredits = 2L, usedCredits = 5L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        when(messageProvider.resolveMessage("postad.package.free.remaining", creditPackage.getUsedCredits(),
                initialCredits + adjustedCredits, GUMTREE_MEDIA_JOBS_LINK))
                .thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldReturnMessageForNonProAccountWithRemainingCreditsInHouses() {
        // given
        setParentCategory(Categories.PROPERTY_TO_RENT.getSeoName());
        long initialCredits = 10L, adjustedCredits = 2L, usedCredits = 5L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        when(messageProvider.resolveMessage("postad.package.free.remaining", creditPackage.getUsedCredits(),
                initialCredits + adjustedCredits, GUMTREE_MEDIA_RENTALS_LINK))
                .thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldReturnMessageForNonProAccountWithRemainingCreditsInMotors() {
        // given
        setParentCategory(Categories.MOTORS.getSeoName());
        long initialCredits = 10L, adjustedCredits = 2L, usedCredits = 5L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        when(messageProvider.resolveMessage("postad.package.free.remaining", creditPackage.getUsedCredits(),
                initialCredits + adjustedCredits, GUMTREE_MEDIA_MOTORS_LINK))
                .thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldReturnMessageForNonProAccountWithNoCreditsRemaining() {
        // given
        long initialCredits = 10L, adjustedCredits = 2L, usedCredits = 12L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        when(messageProvider.resolveMessage("postad.package.free.nocredits", GUMTREE_MEDIA_FALLBACK_LINK)).thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldReturnNoMessageForProAccountWithCapability() {
        // given
        account.setPro(true);
        long initialCredits = 10L, adjustedCredits = 2L, usedCredits = 12L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        creditPackage.setPaymentInstrument(PaymentInstrument.CAPABILITY);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).isEmpty();
        verifyNoMoreInteractions(messageProvider);
    }

    @Test
    public void shouldReturnMessageForProAccountWithUnlimitedPackage() {
        // given
        account.setPro(true);
        long initialCredits = 10L, adjustedCredits = 2L, usedCredits = 12L;
        CreditPackage creditPackage = newUnlimitedCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        when(messageProvider.resolveMessage("postad.package.credits.unlimited", creditPackage.getName())).thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldReturnMessageForProAccountWithRemainingCredits() {
        // given
        account.setPro(true);
        long initialCredits = 13L, adjustedCredits = 2L, usedCredits = 12L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        long remainingCredits = initialCredits + adjustedCredits - usedCredits;
        when(messageProvider.resolveMessage("postad.package.credits.remaining", remainingCredits, creditPackage.getName()))
                .thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldReturnMessageForProAccountWithZeroRemainingCredits() {
        // given
        account.setPro(true);
        long initialCredits = 13L, adjustedCredits = 0L, usedCredits = 13L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        long remainingCredits = initialCredits + adjustedCredits - usedCredits;
        when(messageProvider.resolveMessage("postad.package.credits.remaining", remainingCredits, creditPackage.getName()))
                .thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldReturnMessageForProAccountWithLiveAdsTokens() {
        // given
        account.setPro(true);
        long initialCredits = 13L, adjustedCredits = 2L, usedCredits = 12L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        creditPackage.setPaymentInstrument(PaymentInstrument.TOKEN);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        when(messageProvider.resolveMessage("postad.package.tokens.liveads", creditPackage.getName(), initialCredits))
                .thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldReturnMessageForProAccountWithOverspentCredits() {
        // given
        account.setPro(true);
        long initialCredits = 13L, adjustedCredits = 2L, usedCredits = 20L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage));

        String notice = "some notice";
        long remainingCredits = initialCredits + adjustedCredits - usedCredits;
        when(messageProvider.resolveMessage("postad.package.credits.overspent", -1 * remainingCredits, creditPackage.getName()))
                .thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    @Test
    public void shouldCreateMessagesForAllItemsInOrder() {
        // given
        long initialCredits1 = 10L, adjustedCredits1 = 2L, usedCredits1 = 5L;
        long initialCredits2 = 5L, adjustedCredits2 = 0L, usedCredits2 = 1L;
        CreditPackage creditPackage1 = newCreditPackage("package1", initialCredits1, adjustedCredits1, usedCredits1);
        CreditPackage creditPackage2 = newCreditPackage("package2", initialCredits2, adjustedCredits2, usedCredits2);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage1), apiOrderItemWithCreditPackage(creditPackage2));

        String notice1 = "some notice1";
        String notice2 = "some notice2";
        when(messageProvider.resolveMessage("postad.package.free.remaining", creditPackage1.getUsedCredits(),
                initialCredits1 + adjustedCredits1, GUMTREE_MEDIA_FALLBACK_LINK))
                .thenReturn(notice1);
        when(messageProvider.resolveMessage("postad.package.free.remaining", creditPackage2.getUsedCredits(),
                initialCredits2 + adjustedCredits2, GUMTREE_MEDIA_FALLBACK_LINK))
                .thenReturn(notice2);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice1, notice2);
    }

    @Test
    public void shouldAvoidAddingSamePackageTwice() {
        // given
        long initialCredits = 10L, adjustedCredits = 2L, usedCredits = 5L;
        CreditPackage creditPackage = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        CreditPackage creditPackageSecondInstance = newCreditPackage(initialCredits, adjustedCredits, usedCredits);
        ApiOrder apiOrder = newApiOrder(apiOrderItemWithCreditPackage(creditPackage), apiOrderItemWithCreditPackage(creditPackageSecondInstance));

        String notice = "some notice";
        when(messageProvider.resolveMessage("postad.package.free.remaining", creditPackage.getUsedCredits(),
                initialCredits + adjustedCredits, GUMTREE_MEDIA_FALLBACK_LINK))
                .thenReturn(notice);

        // when
        List<String> updateNotices = messageGenerator.noticesForOrderItems(apiOrder, account);

        // then
        assertThat(updateNotices).containsExactly(notice);
    }

    private ApiOrder newApiOrder(ApiOrderItem... apiOrderItem) {
        ApiOrder apiOrder = new ApiOrder();
        apiOrder.setItems(Arrays.asList(apiOrderItem));
        return apiOrder;
    }

    private ApiOrderItem apiOrderItemWithoutCreditPackage() {
        return newApiOrderItem(new ApiPaymentDetail());
    }

    private ApiOrderItem apiOrderItemWithCreditPackage(CreditPackage creditPackage) {
        ApiPaymentDetail paymentDetail = new ApiPaymentDetail();
        paymentDetail.setCreditPackage(creditPackage);
        return newApiOrderItem(paymentDetail);
    }

    private ApiOrderItem newApiOrderItem(ApiPaymentDetail paymentDetail) {
        ApiOrderItem apiOrderItem = new ApiOrderItem();
        apiOrderItem.setPaymentDetail(paymentDetail);
        apiOrderItem.setAdvertId(ADVERT_ID);
        return apiOrderItem;
    }

    private CreditPackage newUnlimitedCreditPackage(Long initialCredits, Long adjustedCredits, Long usedCredits) {
        return newCreditPackage("Unlimited test package", initialCredits, adjustedCredits, usedCredits);
    }

    private CreditPackage newCreditPackage(Long initialCredits, Long adjustedCredits, Long usedCredits) {
        return newCreditPackage("test package", initialCredits, adjustedCredits, usedCredits);
    }

    private CreditPackage newCreditPackage(String name, Long initialCredits, Long adjustedCredits, Long usedCredits) {
        final CreditPackage creditPackage = new CreditPackage();
        creditPackage.setName(name);
        creditPackage.setInitialCredits(initialCredits);
        creditPackage.setAdjustedCredits(adjustedCredits);
        creditPackage.setUsedCredits(usedCredits);
        return creditPackage;
    }

    private void setParentCategory(String parentName) {
        when(categoryService.getByUniqueName(parentName)).thenReturn(Optional.of(new Category()));
    }
}