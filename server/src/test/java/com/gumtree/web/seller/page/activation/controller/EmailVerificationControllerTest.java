package com.gumtree.web.seller.page.activation.controller;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.exception.BushfireApiException;
import com.gumtree.api.client.executor.impl.ExceptionHandlingApiCallExecutor;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.client.util.BushfireApiExceptionTranslator;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.activation.model.EmailVerificationModel;
import com.gumtree.web.seller.page.common.model.Page;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.ui.ExtendedModelMap;
import org.springframework.ui.Model;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributesModelMap;
import org.springframework.web.servlet.view.RedirectView;

import java.io.IOException;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.instanceOf;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EmailVerificationControllerTest extends BaseSellerControllerTest {


    private EmailVerificationController controller;

    private UserApi userApi;

    @Before
    public void init() {

        configureBapiMock();

        controller = new EmailVerificationController(userSessionService, zenoService, apiCallExecutor,
                categoryModel, cookieResolver);

        autowireAbExperimentsService(controller);
    }

    private void configureBapiMock() {
        BushfireApi bushfireApi = mock(BushfireApi.class);
        userApi = mock(UserApi.class);
        when(bushfireApi.userApi()).thenReturn(userApi);
        when(bushfireApi.create(UserApi.class)).thenReturn(userApi);
        doThrow(new BushfireApiException(300)).when(userApi).verifyContactEmail("<EMAIL>","invalidKey");
        doThrow(new RuntimeException()).when(userApi).verifyContactEmail("<EMAIL>","serviceError");

        apiCallExecutor = new ExceptionHandlingApiCallExecutor(bushfireApi, new BushfireApiExceptionTranslator());
    }

    @Test
    public void shouldSuccessfullyActivateEmail() throws IOException {
        //when making the call to activation page
        ModelAndView pageModelAndView = requestPageAndFollowRedirect("<EMAIL>", "validKey");

        //then email should be verified
        Mockito.verify(userApi).verifyContactEmail("<EMAIL>", "validKey");

        //and user should be shown the confirmation page
        validateModel(pageModelAndView, true);

    }

    @Test
    public void shouldShowErrorPageIfActivationKeyNotProvided() throws IOException {

        //when making the call to activation page
        ModelAndView pageModelAndView = requestPageAndFollowRedirect("<EMAIL>", "");

        //then there should be no attempt to verify email
        Mockito.verifyZeroInteractions(userApi);

        //and user should be shown the confirmation page
        validateModel(pageModelAndView, false);

    }

    @Test
    public void shouldShowErrorPageIfUsernameKeyNotProvided() throws IOException {

        //when making the call to activation page
        ModelAndView pageModelAndView = requestPageAndFollowRedirect("", "validActivationKey");

        //then there should be no attempt to verify email
        Mockito.verifyZeroInteractions(userApi);

        //and user should be shown the confirmation page
        validateModel(pageModelAndView, false);

    }

    @Test
    public void shouldShowErrorPageIfValidationFailed() throws IOException {

        //when making the call to activation page
        ModelAndView pageModelAndView = requestPageAndFollowRedirect("<EMAIL>", "invalidKey");

        //and user should be shown the confirmation page
        validateModel(pageModelAndView, false);

    }

    @Test
    public void shouldShowErrorPageIfErrorOccurred() throws IOException {

        //when making the call to activation page
        ModelAndView pageModelAndView = requestPageAndFollowRedirect("<EMAIL>", "serviceError");

        //and user should be shown the confirmation page
        validateModel(pageModelAndView, false);

    }

    private void validateModel(ModelAndView pageModelAndView, boolean expectedValidationResult) {
        assertThat(pageModelAndView.getViewName(), equalTo(Page.EmailVerification.getTemplateName()));
        assertThat(pageModelAndView.getModel().get("model"), instanceOf(EmailVerificationModel.class));
        assertThat(extractModel(pageModelAndView, EmailVerificationModel.class).isVerified(),
                equalTo(expectedValidationResult));
    }

    private ModelAndView requestPageAndFollowRedirect(String username, String activationKey) throws IOException {
        RedirectAttributesModelMap map = new RedirectAttributesModelMap();
        Map<String, String> params = new HashMap<>();
        params.put("id", username);
        params.put("key", activationKey);
        params.put("utm_source", "source");
        params.put("utm_medium", "medium");
        params.put("utm_campaign", "campaign");

        ModelAndView result = controller.handleRequest(params, map);

        //and following HTTP redirect
        RedirectView view = (RedirectView) result.getView();
        assertThat(view.getUrl(), equalTo("/verify-email/result"));
        assertThat(result.getModelMap().size(), equalTo(3));
        assertThat(result.getModelMap().get("utm_source"), equalTo("source"));
        assertThat(result.getModelMap().get("utm_medium"), equalTo("medium"));
        assertThat(result.getModelMap().get("utm_campaign"), equalTo("campaign"));

        Model model = new ExtendedModelMap();
        model.addAllAttributes(map.getFlashAttributes());

        return controller.activateEmail(model, request);
    }



}
