package com.gumtree.web.seller.service;

import com.gumtree.api.Ad;
import com.gumtree.api.Attribute;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import org.hamcrest.CoreMatchers;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class CheckoutContainerImplTest {

    private CheckoutContainerImpl container;

    private SellerSessionDataService sessionDataService;
    private Checkout checkout1;
    private ApiOrder apiOrder1;

    @Before
    public void init() {
        sessionDataService = mock(SellerSessionDataService.class);
        container = new CheckoutContainerImpl(sessionDataService);

        checkout1 = new CheckoutImpl();
        apiOrder1 = new ApiOrder();
        apiOrder1.setId(1L);
        checkout1.setOrder(apiOrder1);

        checkout1.setKey("id1");

        when(sessionDataService.getCheckout("id1")).thenReturn(checkout1);
        when(sessionDataService.getCheckoutByOrder(apiOrder1)).thenReturn(checkout1);
    }

    @Test
    public void getExistingCheckoutReturnsCheckout() {
        Checkout checkout = container.getCheckout("id1");

        assertThat(checkout, CoreMatchers.sameInstance(checkout1));
    }

    @Test(expected = CheckoutContainer.CheckoutNotFoundException.class)
    public void getNonExistingCheckoutThrowsException() {
        container.getCheckout("non-id1");
    }

    @Test
    public void markCheckoutAsComplete() {
        container.markCheckoutAsComplete("id1");

        verify(sessionDataService).setCheckoutComplete("id1");
    }

    @Test
    public void checkoutWithAdvert() {
        ApiOrder order = new ApiOrder();
        Ad ad = new Ad();
        ad.setId(23L);
        ad.setCategoryId(11L);
        ad.setLocationId(42L);
        Checkout checkout = container.createCheckout(order, ad, true);
        assertThat(checkout.getAdvert().getId(), equalTo(23L));
        assertThat(checkout.getAdvert().getLocationId(), equalTo(42L));
        assertThat(checkout.getAdvert().getCategoryId(), equalTo(11L));
    }

    @Test
    public void checkoutWithAdvertSellerType() {
        Attribute[] attributes = {
                new Attribute("seller_type", "trade")
        };
        ApiOrder order = new ApiOrder();
        Ad ad = new Ad();
        ad.setId(23L);
        ad.setCategoryId(11L);
        ad.setLocationId(42L);
        ad.setAttributes(attributes);
        Checkout checkout = container.createCheckout(order, ad, true);
        assertThat(checkout.getAdvert().getId(), equalTo(23L));
        assertThat(checkout.getAdvert().getLocationId(), equalTo(42L));
        assertThat(checkout.getAdvert().getCategoryId(), equalTo(11L));
        assertThat(checkout.getAdvert().getSellerType(), equalTo("trade"));
    }

    @Test
    public void createCheckoutForNewOrderMakesNewCheckout() {
        ApiOrder order = new ApiOrder();
        Checkout checkout = container.createCheckout(order);

        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
        assertThat(checkout.getOrder(), CoreMatchers.sameInstance(order));
    }

    @Test
    public void createCheckoutForExistingOrderReturnsExistingCheckout() {
        Checkout checkout = container.createCheckout(apiOrder1);
        assertThat(checkout, CoreMatchers.sameInstance(checkout1));
    }

    private Ad createAd() {
        Ad ad = new Ad();
        ad.setId(1L);
        ad.setCategoryId(11L);
        ad.setLocationId(42L);
        return ad;
    }

    @Test
    public void createCheckoutWithAdvertForExistingOrderUpdatesExistingCheckout() {

        Checkout checkout = container.createCheckout(apiOrder1, createAd(), true);

        assertThat(checkout.getAdvert().getId(), equalTo(1L));
        assertThat(checkout.isCreateOrEdit(), equalTo(true));
        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
        assertThat(checkout.getOrder(), CoreMatchers.sameInstance(apiOrder1));
    }

    @Test
    public void createCheckoutWithAdvertForNewOrderMakesNewCheckout() {
        ApiOrder order = new ApiOrder();
        Checkout checkout = container.createCheckout(order, createAd(), true);

        assertThat(checkout.getAdvert().getId(), equalTo(1L));
        assertThat(checkout.isCreateOrEdit(), equalTo(true));
        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
        assertThat(checkout.getOrder(), CoreMatchers.sameInstance(order));
    }

}
