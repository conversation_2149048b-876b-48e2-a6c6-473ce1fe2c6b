package com.gumtree.web.seller.service.convertr;

import com.gumtree.api.Account;
import com.gumtree.api.Ad;
import com.gumtree.api.Attribute;
import com.gumtree.api.Location;
import com.gumtree.api.User;
import com.gumtree.api.category.domain.Category;
import com.gumtree.mobile.test.Fixtures;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.category.impl.CategoryServiceImpl;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.domain.order.entity.OrderEntity;
import com.gumtree.web.common.domain.order.entity.OrderItemEntity;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.google.common.collect.Lists.newArrayList;
import static com.gumtree.seller.domain.product.entity.ProductName.FEATURE_3_DAY;
import static com.gumtree.seller.domain.product.entity.ProductName.INSERTION;
import static org.fest.assertions.api.Assertions.assertThat;


public class ConvertrServiceTest {
    private ConvertrService service;

    private Account privateAccount;
    private User user;
    private Order newListingOrder;
    private Attribute[] attrs;
    private Ad bmwAd;
    private Category bmwCategory;


    @Before
    public void setup() {
        privateAccount = buildAccount(false);
        user = buildUser("<EMAIL>", "John", "Smith");
        newListingOrder = buidOrder(INSERTION, FEATURE_3_DAY);
        attrs = new Attribute[]{new Attribute("price", "10"), new Attribute("vrn", "abc")};
        bmwAd = buildAd(attrs, "*********", "TW91EL", newArrayList("United Kingdom", "London", "Richmond"));
        bmwCategory = Fixtures.STUB_CATEGORY_MODEL.getByName("bmw").get();

        service = new ConvertrService(false, new CategoryServiceImpl(Fixtures.STUB_CATEGORY_MODEL));
    }

    @Test
    public void buildConvertrUrlReturnsPopupulatedUrl() {
        assertThat(service.buildConvertrUrl(privateAccount, user, newListingOrder, bmwAd, bmwCategory))
                .isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?firstname=John&lastname=Smith&email=test%40gumtree.com&telephone_number=*********&vehicle_reg=abc&location=TW91EL&qa=yes"));
    }

    /*
        when to show
     */

    @Test
    public void buildConvertrUrlReturnsEmpty() {
        assertThat(service.buildConvertrUrl(privateAccount, user, newListingOrder, bmwAd, Fixtures.STUB_CATEGORY_MODEL.getByName("for-sale").get()))
                .as("do not show convertr: not cars")
                .isEqualTo(Optional.empty());

        assertThat(service.buildConvertrUrl(buildAccount(true), user, newListingOrder, bmwAd, bmwCategory))
                .as("do not show convertr: pro seller")
                .isEqualTo(Optional.empty());

        assertThat(service.buildConvertrUrl(privateAccount, user, buidOrder(FEATURE_3_DAY), bmwAd, bmwCategory))
                .as("do not show convertr: is not new advert (no insertion product)")
                .isEqualTo(Optional.empty());

    }

    /*
        user data
     */

    @Test
    public void buildConvertrUrlReturnsUrlWithoutEmail() {
        assertThat(service.buildConvertrUrl(privateAccount, buildUser(null, "john", "smith"), newListingOrder, bmwAd, bmwCategory))
                .isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?firstname=john&lastname=smith&email&telephone_number=*********&vehicle_reg=abc&location=TW91EL&qa=yes"));
    }

    @Test
    public void buildConvertrUrlReturnsUrlWithoutName() {
        assertThat(service.buildConvertrUrl(privateAccount, buildUser("<EMAIL>", null, "smith"), newListingOrder, bmwAd, bmwCategory))
                .isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?firstname&lastname=smith&email=a%40b.com&telephone_number=*********&vehicle_reg=abc&location=TW91EL&qa=yes"));
    }

    @Test
    public void buildConvertrUrlReturnsUrlWithoutSurname() {
        assertThat(service.buildConvertrUrl(privateAccount, buildUser("<EMAIL>", "john", null), newListingOrder, bmwAd, bmwCategory))
                .isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?firstname=john&lastname&email=a%40b.com&telephone_number=*********&vehicle_reg=abc&location=TW91EL&qa=yes"));
    }

    @Test
    public void buildConvertrUrlReturnsUrlWithoutPhone() {
        Ad ad = buildAd(new Attribute[]{new Attribute("vrn", "abc")}, null, "TW91EL", newArrayList("United Kingdom", "London", "Richmond"));
        assertThat(service.buildConvertrUrl(privateAccount, user, newListingOrder, ad, bmwCategory))
                .isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?firstname=John&lastname=Smith&email=test%40gumtree.com&telephone_number&vehicle_reg=abc&location=TW91EL&qa=yes"));
    }


    /*
        vrn
     */

    @Test
    public void buildConvertrUrlReturnsUrlWithVrn() {
        // given

        Ad ad = buildAd(new Attribute[]{new Attribute("price", "10")}, "1458741", "TW91EL", newArrayList("United Kingdom", "London", "Richmond"));

        // when & then
        assertThat(service.buildConvertrUrl(privateAccount, user, newListingOrder, ad, bmwCategory))
                .as("no postcode")
                .isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?firstname=John&lastname=Smith&email=test%40gumtree.com&telephone_number=1458741&vehicle_reg&location=TW91EL&qa=yes"));
    }

    /*
        location
     */

    @Test
    public void buildConvertrUrlPopulatesLocationWhenNoPostcode() {
        // given
        Ad ad = buildAd(attrs, "1458741", null, newArrayList("United Kingdom", "London", "Richmond"));

        // when & then
        assertThat(service.buildConvertrUrl(privateAccount, user, newListingOrder, ad, bmwCategory))
                .as("no postcode")
                .isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?firstname=John&lastname=Smith&email=test%40gumtree.com&telephone_number=1458741&vehicle_reg=abc&location=Richmond&qa=yes"));
    }

    @Test
    public void buildConvertrUrlPopulatesLocationWhenNoPostcodeAndNoLocations() {
        // given
        Ad ad = buildAd(null, "1458741", null, null);

        // when & then
        assertThat(service.buildConvertrUrl(privateAccount, user, newListingOrder, ad, bmwCategory))
                .as("no postcode")
                .isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?firstname=John&lastname=Smith&email=test%40gumtree.com&telephone_number=1458741&vehicle_reg&location=United+Kingdom&qa=yes"));
    }

    /*
        is qa
     */


    @Test
    public void buildConvertrUrlReturnsNoQaOnProd() {
        ConvertrService service = new ConvertrService(true, new CategoryServiceImpl(Fixtures.STUB_CATEGORY_MODEL));

        assertThat(service.buildConvertrUrl(privateAccount, user, newListingOrder, bmwAd, bmwCategory))
                .isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?firstname=John&lastname=Smith&email=test%40gumtree.com&telephone_number=*********&vehicle_reg=abc&location=TW91EL"));
    }

    /*
        escaping
     */


    @Test
    public void buildConvertrUrlEscapesContent() {
        // given
        String script = "<script type=\"text/javascript\"> alert('hi'); </script> ";

        // and
        Attribute[] attrs = {new Attribute("price", "10"), new Attribute("vrn", script)};
        Ad ad = buildAd(attrs, script, "TW91EL", newArrayList("United Kingdom", "London", "Richmond"));
        User user = buildUser(script, script, script);

        // when
        Optional<String> url = service.buildConvertrUrl(privateAccount, user, newListingOrder, ad, bmwCategory);

        assertThat(url).isEqualTo(Optional.of("https://gumtree.cvtr.io/forms/gumtree-v2?" +
                "firstname=%3Cscript+type%3D%22text%2Fjavascript%22%3E+alert%28%27hi%27%29%3B+%3C%2Fscript%3E+" +
                "&lastname=%3Cscript+type%3D%22text%2Fjavascript%22%3E+alert%28%27hi%27%29%3B+%3C%2Fscript%3E+" +
                "&email=%3Cscript+type%3D%22text%2Fjavascript%22%3E+alert%28%27hi%27%29%3B+%3C%2Fscript%3E+" +
                "&telephone_number=%3Cscript+type%3D%22text%2Fjavascript%22%3E+alert%28%27hi%27%29%3B+%3C%2Fscript%3E+" +
                "&vehicle_reg=%3Cscript+type%3D%22text%2Fjavascript%22%3E+alert%28%27hi%27%29%3B+%3C%2Fscript%3E+" +
                "&location=TW91EL&qa=yes"));
    }


    /*
        utility methods
    */
    private static Account buildAccount(boolean isPro) {
        Account a = new Account();
        a.setPro(isPro);
        return a;
    }

    private static User buildUser(String email, String firstName, String lastName) {
        User u = new User();
        u.setEmail(email);
        u.setFirstName(firstName);
        u.setLastName(lastName);
        return u;
    }

    private static Order buidOrder(ProductName... itemProducts) {
        OrderEntity o = new OrderEntity();
        o.setItems(Arrays.stream(itemProducts)
                .map(p -> {
                    OrderItemEntity i = new OrderItemEntity();
                    i.setProductName(p);
                    return i;
                })
                .collect(Collectors.toList())
        );
        return o;
    }

    private static Ad buildAd(Attribute[] attrs, String phoneNumber, String postcode, List<String> locationsDisplayName) {
        Ad a = new Ad();

        a.setAttributes(attrs);
        a.setPhoneNumber(phoneNumber);
        a.setPostcode(postcode);

        if (locationsDisplayName != null) {
            Location[] locations = locationsDisplayName.stream()
                    .map(name -> {
                        Location l = new Location();
                        l.setName(name);
                        return l;
                    })
                    .toArray(Location[]::new);

            a.setLocations(locations);
        }

        return a;
    }
}
