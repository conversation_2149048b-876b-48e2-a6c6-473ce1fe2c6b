package com.gumtree.web.seller.config;

import com.gumtree.webmvc.testing.MultiFolderResourceLoader;
import org.springframework.mock.web.MockServletContext;
import org.springframework.test.context.web.AnnotationConfigWebContextLoader;
import org.springframework.test.context.web.WebMergedContextConfiguration;
import org.springframework.web.context.support.GenericWebApplicationContext;

public class SellerMvcContextLoader extends AnnotationConfigWebContextLoader {

    @Override
    protected void customizeContext(GenericWebApplicationContext context, WebMergedContextConfiguration webMergedConfig) {
        MultiFolderResourceLoader resourceLoader = new MultiFolderResourceLoader(new String[]{});
        context.setServletContext(new MockServletContext("", resourceLoader));
    }
}
