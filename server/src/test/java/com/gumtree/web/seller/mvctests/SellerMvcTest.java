package com.gumtree.web.seller.mvctests;

import com.gumtree.analytics.stub.GoogleAnalyticsServiceStub;
import com.gumtree.api.client.stub.StubAccountApi;
import com.gumtree.api.client.stub.StubUserApi;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.MwebProperty;
import com.gumtree.config.SellerApplicationContextInitializer;
import com.gumtree.config.profiles.CommonConfigProfiles;
import com.gumtree.web.seller.config.SellerMvcContextLoader;
import com.gumtree.web.seller.config.SellerMvcTestConfig;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.DefaultSecurityManager;
import org.apache.shiro.util.ThreadContext;
import org.junit.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;

import org.springframework.web.context.WebApplicationContext;

@WebAppConfiguration(value = "seller-public-static-assets/src/main/webapp/")
@ContextConfiguration(
        initializers = SellerApplicationContextInitializer.class,
        loader = SellerMvcContextLoader.class,
        classes = {SellerMvcTestConfig.class})
@ActiveProfiles(profiles = {
        "stub-api",
        "stub-models",
        CommonConfigProfiles.ELASTIC_SEARCH_API,
        CommonConfigProfiles.SESSION_PERSISTENCE_STUB})
public class SellerMvcTest {
    @Autowired
    protected WebApplicationContext context;

    protected MockMvc mockMvc;

    @Autowired
    protected GoogleAnalyticsServiceStub.Server googleAnalyticsServiceStub;

    @Before
    public void beforeEach() {
        // clear stubs
        StubAccountApi.clearStubData();
        StubUserApi.clearStubData();
        setupSecManager();
    }

    public static void setupSecManager() {
        // Shiro
        ThreadContext.remove(); // clears subject
        SecurityUtils.setSecurityManager(new DefaultSecurityManager());
    }
}
