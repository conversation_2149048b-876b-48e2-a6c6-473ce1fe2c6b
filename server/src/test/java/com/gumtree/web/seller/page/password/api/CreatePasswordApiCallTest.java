package com.gumtree.web.seller.page.password.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.domain.user.beans.CreatePasswordBean;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Mockito.*;

/**
 */
public class CreatePasswordApiCallTest {

    private BushfireApi bushfireApi;

    private UserApi userApi;

    private CreatePasswordApiCall apiCall;

    @Before
    public void init() {
        bushfireApi = mock(BushfireApi.class);
        userApi = mock(UserApi.class);
        when(bushfireApi.create(UserApi.class)).thenReturn(userApi);
    }

    @Test
    public void makesCorrectCallToApiToActivateUser() {
        CreatePasswordBean createPasswordBean = new CreatePasswordBean();
        apiCall = new CreatePasswordApiCall("<EMAIL>", createPasswordBean);
        apiCall.execute(bushfireApi);
        verify(userApi).createPassword("<EMAIL>", createPasswordBean);
    }
}
