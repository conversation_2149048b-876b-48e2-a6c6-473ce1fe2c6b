package com.gumtree.web.seller.service.jobs;

import com.google.common.base.Optional;
import com.gumtree.api.category.UnfilteredCategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.mobile.test.Fixtures;
import com.gumtree.web.seller.page.ajax.category.SuggestedCategory;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class JobSuggesterServiceImplTest {

    @Mock
    private UnfilteredCategoryModel categoryModel;
    @InjectMocks
    private JobsSuggesterServiceImpl jobsSuggesterService;
    @Test
    public void makeSureServiceSuggestsJobsIfKeywordHasJobs() {
        List<SuggestedCategory> searchResult = jobsSuggesterService.getSuggestedJobCategories("dummy jobs ", Collections.emptyList())
                .stream()
                .filter(x -> x.getDisplayName().toLowerCase().contains("job"))
                .collect(Collectors.toList());

        assertThat(searchResult.size(), equalTo(1));
    }

    @Test
    public void doNotFetchAnyIfNotResultsAndContainsNoJobPhrase() {
        boolean empty = jobsSuggesterService.getSuggestedJobCategories("dummy phrase ", Collections.emptyList()).isEmpty();
        assertThat(empty, is(true));
    }

    @Test
    public void fetchJobSuggestionIfReturnsAny() {

        // given
        Category jobsCategory = mock(Category.class);
        Category accountancyJobCategory = mock(Category.class);

        long accountancyJobId = 11476L;
        List<Long> suggestedCategories = Collections.singletonList(accountancyJobId);

        when(categoryModel.getCategory(accountancyJobId))
                .thenReturn(com.google.common.base.Optional.of(accountancyJobCategory));
        when(accountancyJobCategory.getL1ParentId()).thenReturn(Fixtures.JOBS_CAT_ID);
        when(categoryModel.getCategory(Fixtures.JOBS_CAT_ID))
                .thenReturn(com.google.common.base.Optional.of(jobsCategory));
        when(jobsCategory.getSeoName()).thenReturn(CategoryConstants.WellKnown.JOBS.getSeoName());

        // when
        List<SuggestedCategory> searchResult = jobsSuggesterService.getSuggestedJobCategories("dummy phrase ", suggestedCategories)
                .stream()
                .filter(x -> x.getTree().toLowerCase().contains("job"))
                .collect(Collectors.toList());

        // then
        assertThat(searchResult.size(), equalTo(1));
    }

    @Test
    public void categoryDoesntExist() {
        long unknownCategoryId = -1L;
        List<Long> suggestedCategories = Collections.singletonList(unknownCategoryId);
        when(categoryModel.getCategory(unknownCategoryId)).thenReturn(Optional.absent());
        boolean empty = jobsSuggesterService.getSuggestedJobCategories("dummy phrase ", suggestedCategories)
                .isEmpty();

        assertThat(empty, is(true));
    }

    @Test
    public void categoryDoesntHaveL1Category() {
        long categoryId = 100L;
        Category category = mock(Category.class);
        when(categoryModel.getCategory(categoryId)).thenReturn(com.google.common.base.Optional.of(category));
        long unknownParentCategoryId = -1L;
        when(category.getL1ParentId()).thenReturn(unknownParentCategoryId);
        when(categoryModel.getCategory(unknownParentCategoryId)).thenReturn(Optional.absent());

        List<Long> suggestedCategories = Collections.singletonList(categoryId);

        when(categoryModel.getCategory(unknownParentCategoryId)).thenReturn(Optional.absent());
        boolean empty = jobsSuggesterService.getSuggestedJobCategories("dummy phrase ", suggestedCategories)
                .isEmpty();

        assertThat(empty, is(true));
    }

    @Test
    public void nonJobRelatedCategoriesReturned() {

        // given
        Category motorsCategory = mock(Category.class);
        Category vansCategory = mock(Category.class);

        long vansId = 100L;
        List<Long> suggestedCategories = Collections.singletonList(vansId);

        when(categoryModel.getCategory(vansId))
                .thenReturn(com.google.common.base.Optional.of(vansCategory));
        when(vansCategory.getL1ParentId()).thenReturn(Fixtures.MOTORS_CAT_ID);
        when(categoryModel.getCategory(Fixtures.MOTORS_CAT_ID))
                .thenReturn(com.google.common.base.Optional.of(motorsCategory));
        when(motorsCategory.getSeoName()).thenReturn(CategoryConstants.WellKnown.MOTORS.getSeoName());

        // when
        boolean empty = jobsSuggesterService.getSuggestedJobCategories("dummy phrase ", suggestedCategories)
                .isEmpty();

        // then
        assertThat(empty, is(true));
    }
}
