package com.gumtree.web.seller.page.postad.api;

import com.gumtree.api.Ad;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.client.executor.ApiKeyProvider;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class PostAdApiCallTest {

    private BushfireApi bushfireApi;

    private AdvertApi advertApi;

    private PostAdvertBean postAdvertBean;

    private ApiKeyProvider apiKeyProvider;

    private PostAdApiCall apiCall;

    private Ad ad;

    @Before
    public void init() {
        bushfireApi = mock(BushfireApi.class);
        advertApi = mock(AdvertApi.class);
        postAdvertBean = new PostAdvertBean();
        ad = new Ad();
        apiKeyProvider = mock(ApiKeyProvider.class);
        BushfireApiKey apiKey = new BushfireApiKey();
        apiCall = new PostAdApiCall(postAdvertBean, apiKeyProvider);

        when(apiKeyProvider.getApiKey()).thenReturn(apiKey);
        when(bushfireApi.create(AdvertApi.class, apiKey)).thenReturn(advertApi);
        when(advertApi.postAd(postAdvertBean)).thenReturn(ad);
    }

    @Test
    public void callsPostAdOnApi() {
        assertThat(apiCall.execute(bushfireApi), equalTo(ad));
    }
}
