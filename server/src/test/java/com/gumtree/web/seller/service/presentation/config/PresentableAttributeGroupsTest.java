package com.gumtree.web.seller.service.presentation.config;

import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeGroup;
import org.junit.Test;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class PresentableAttributeGroupsTest {

    @Test
    public void shouldProperlyConvertToAttributeGroups() {
        //given
        PresentableAttributeGroups groups = new PresentableAttributeGroups();
        groups.setCategoryName("c1");
        PresentableAttributeGroup group1 = new PresentableAttributeGroup();
        group1.setId("g1");
        group1.setIndex(1);
        group1.setLabel("Group 1");
        group1.setPanelId("panel1");

        PresentableAttribute attribute1 = new PresentableAttribute();
        attribute1.setId("attr1");

        group1.setAttributes(Lists.newArrayList(attribute1));

        PresentableAttributeGroup group2 = new PresentableAttributeGroup();
        group2.setId("g2");
        group2.setIndex(2);
        group2.setPanelId("panel2");

        PresentableAttribute attribute2 = new PresentableAttribute();
        attribute2.setId("attr2");
        group2.setAttributes(Lists.newArrayList(attribute2));

        groups.setGroups(Lists.newArrayList(group1, group2));

        List<AttributeMetadata> attributesMetadata = Lists.newArrayList(
                AttributeMetadata.builder().withName("attr1").withType(AttributeType.INTEGER).build(),
                AttributeMetadata.builder().withName("attr2").withType(AttributeType.DATETIME).build()
        );

        //when
        List<PostAdAttributeGroup> attributeGroups = groups.toPostAdAttributeGroups(attributesMetadata, null);

        //then
        assertThat(attributeGroups.size(), equalTo(2));
    }

    @Test
    public void shouldProperlyClone() {

    }

    @Test
    public void shouldProperlyMerge() {

    }

}