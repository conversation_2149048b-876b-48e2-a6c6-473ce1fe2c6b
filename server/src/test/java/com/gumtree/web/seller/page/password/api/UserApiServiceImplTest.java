package com.gumtree.web.seller.page.password.api;

import com.gumtree.bapi.UserApi;
import com.gumtree.bapi.model.PasswordKey;
import com.gumtree.bapi.model.ResetPasswordBean;
import org.junit.Before;
import org.junit.Test;
import rx.Single;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class UserApiServiceImplTest {

    private UserApi userApi;
    private UserApiService userApiService;

    @Before
    public void init() {
        userApi = mock(UserApi.class);
        userApiService = new UserApiServiceImpl(userApi);
    }

    @Test
    public void getResetPasswordKey(){
        //Given
        String key = "valid-reset-password-key";
        ResetPasswordBean resetPasswordBean = new ResetPasswordBean().key(key);
        when(userApi.getPasswordKey(resetPasswordBean))
                .thenReturn(Single.just(new PasswordKey().key(key).username("valid-user")));

        //when
        PasswordKey passwordKey = userApiService.getPasswordKey(key);

        //then
        assertThat(passwordKey,notNullValue());
        assertThat(key,equalTo(passwordKey.getKey()));
    }

    @Test
    public void getNullForIncorrectKey(){
        //Given
        String key = "invalid-reset-password-key";
        ResetPasswordBean resetPasswordBean = new ResetPasswordBean().key(key);
        when(userApi.getPasswordKey(resetPasswordBean))
                .thenReturn(Single.error(new Exception("Not Found")));

        //when
        PasswordKey passwordKey = userApiService.getPasswordKey(key);

        //then
        assertThat(passwordKey,nullValue());
    }
}
