package com.gumtree.web.seller.service.category;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.category.predictor.client.CategoryPredictApi;
import com.gumtree.category.predictor.model.CategoryDTO;
import com.gumtree.category.predictor.model.PredictRequest;
import com.gumtree.category.predictor.model.PredictResult;
import com.gumtree.liveadsearch.client.LiveAdsSearchApi;
import com.gumtree.liveadsearch.model.CategorySuggestionsResponse;
import com.gumtree.web.seller.page.ajax.category.SuggestedCategory;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import rx.Single;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeoutException;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CategorySuggesterServiceImplTest {
    @InjectMocks
    private CategorySuggesterServiceImpl categorySuggesterService;
    @Mock
    private LiveAdsSearchApi liveAdsSearchApi;
    @Mock
    private MeterRegistry meterRegistry;

    @Mock
    private CategoryModel categoryModel;

    @Mock
    private CategoryPredictApi categoryPredictApi;

    @Mock
    private Counter mockedCounter;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void shouldReturnSearchResults() {
        String searchTerm = "ValidKeyword";
        when(liveAdsSearchApi.getCategorySuggestions(searchTerm)).thenReturn(Single.just(getCategorySuggestionsResponse()));
        List<Long> suggestedCategories = categorySuggesterService.getSuggestedCategories(searchTerm);
        assertThat(suggestedCategories.size(), equalTo(4));
        assertThat(suggestedCategories.get(0), equalTo(1L));
        assertThat(suggestedCategories.get(1), equalTo(2551L));
        assertThat(suggestedCategories.get(2), equalTo(2549L));
        assertThat(suggestedCategories.get(3), equalTo(10022L));
    }

    @Test
    public void shouldNotReturnSearchResultsForInvalidKeyword() {
        String searchTerm = "InvalidKeyword";
        when(meterRegistry.counter(anyString())).thenReturn(mockedCounter);
        when(liveAdsSearchApi.getCategorySuggestions(searchTerm)).thenReturn(Single.just(new CategorySuggestionsResponse().total(0L)));
        List<Long> suggestedCategories = categorySuggesterService.getSuggestedCategories(searchTerm);
        assertThat(suggestedCategories.size(), equalTo(0));
        verify(meterRegistry, times(1)).counter(anyString());
    }

    @Test
    public void shouldReturnNoResultsForAPIError() {
        String searchTerm = "ValidKeyword";
        when(meterRegistry.counter(anyString())).thenReturn(mockedCounter);
        when(liveAdsSearchApi.getCategorySuggestions(searchTerm)).thenReturn(Single.error(new TimeoutException("API Request Failed")));
        List<Long> suggestedCategories = categorySuggesterService.getSuggestedCategories(searchTerm);
        assertThat(suggestedCategories.size(), equalTo(0));
        verify(meterRegistry, times(1)).counter(anyString());
    }

    private CategorySuggestionsResponse getCategorySuggestionsResponse() {
        Map<String, Long> categories = new HashMap<>();
        categories.put("2549", 2L);
        categories.put("1", 5L);
        categories.put("10022", 1L);
        categories.put("2551", 3L);

        Map<String, Map<String, Long>> aggregations = new HashMap<>();
        aggregations.put("category-id", categories);

        CategorySuggestionsResponse searchResult = new CategorySuggestionsResponse();
        searchResult.setTotal(3L);
        searchResult.setAggregations(aggregations);
        return searchResult;
    }

    @Test
    public void testGetSuggestedTextMatchCategories() {
        // Arrange
        List<Category> leafChildren = new ArrayList<>();

        Category cat1 = mock(Category.class);
        when(cat1.getId()).thenReturn(1L);
        when(cat1.getName()).thenReturn("Cars");
        when(cat1.getDisplayName()).thenReturn("Cars");
        when(cat1.isAdPostingPermitted()).thenReturn(true);
        when(cat1.getEnabled()).thenReturn(true);
        when(cat1.getReadOnly()).thenReturn(false);
        when(cat1.isHidden()).thenReturn(false);

        Category cat2 = mock(Category.class);
        when(cat2.getId()).thenReturn(2L);
        when(cat2.getName()).thenReturn("Bikes");
        when(cat2.getDisplayName()).thenReturn("Bikes");
        when(cat2.isAdPostingPermitted()).thenReturn(true);
        when(cat2.getEnabled()).thenReturn(true);
        when(cat2.getReadOnly()).thenReturn(false);
        when(cat2.isHidden()).thenReturn(false);

        leafChildren.add(cat1);
        leafChildren.add(cat2);

        when(categoryModel.getLeafChildren(1L)).thenReturn(leafChildren);

        // Mock getSuggestedCategoriesMap 返回值 ← 使用 doReturn 避免触发真实方法
        Map<Long, Long> advertCountMap = new HashMap<>();
        advertCountMap.put(1L, 100L);
        advertCountMap.put(2L, 50L);
        CategorySuggesterServiceImpl spiedService = spy(categorySuggesterService);

        doReturn(Optional.of(advertCountMap))
                .when(spiedService)
                .getSuggestedCategoriesMap("car");

        // 使用 spy 来 stub calculateTextMatchScore 方法
        doReturn(0.8f)
                .when(spiedService)
                .calculateTextMatchScore(anyString(), anyString());

        // Act
        List<SuggestedCategory> result = spiedService.getSuggestedTextMatchCategories("car", false, 2);

        // Assert
        assertThat(result.size(), equalTo(2));
        assertThat(result.get(0).getId(), equalTo(1L));
        assertThat(result.get(1).getId(), equalTo(2L));
    }
    @Test
    public void testGetCategoryPredictApi() {
        // Arrange
        PredictResult predictResult = new PredictResult().code(200);

        CategoryDTO dto1 = new CategoryDTO().id(1L).displayName("Cars").tree("Root;Cars").score(BigDecimal.valueOf(0.95));
        CategoryDTO dto2 = new CategoryDTO().id(2L).displayName("Bikes").tree("Root;Bikes").score(BigDecimal.valueOf(0.85));

        predictResult.setCategories(Arrays.asList(dto1, dto2));

        when(categoryPredictApi.categoryPredict(any(PredictRequest.class))).thenReturn(Single.just(predictResult));

        // Act
        List<SuggestedCategory> result = categorySuggesterService.getCategoryPredictApi("car", 2);

        // Assert
        assertThat(result.size(), equalTo(2));
        assertThat(result.get(0).getId(), equalTo(1L));
        assertThat(result.get(1).getId(), equalTo(2L));
    }

    @Test
    public void testGetCategoryPredictApi_whenError() {
        when(categoryPredictApi.categoryPredict(any(PredictRequest.class))).thenReturn(Single.error(new RuntimeException("API error")));

        List<SuggestedCategory> result = categorySuggesterService.getCategoryPredictApi("car", 5);
        assertThat(result.size(), equalTo(0));
    }

    @Test
    public void testCalculateTextMatchScore() throws Exception {
        Method method = CategorySuggesterServiceImpl.class.getDeclaredMethod("calculateTextMatchScore", String.class, String.class);
        method.setAccessible(true);

        Float score = (Float) method.invoke(categorySuggesterService, "red car", "used red sports car for sale");

        // 预期分数在 [0,1] 区间内
        assertThat(score >= 0.0f && score <= 1.0f, equalTo(true));
    }

    @Test
    public void shouldReturnEmptyListWhenQueryIsNull() {
        // Arrange
        when(categoryModel.getLeafChildren(1L)).thenReturn(Collections.emptyList());

        // Act
        List<SuggestedCategory> result = categorySuggesterService.getSuggestedCategoriesAllName(null, 5);

        // Assert
        assertThat(result.size(), equalTo(0));
        verify(categoryModel, times(1)).getLeafChildren(1L);
    }

    @Test
    public void shouldReturnEmptyListWhenQueryIsEmpty() {
        // Arrange
        when(categoryModel.getLeafChildren(1L)).thenReturn(Collections.emptyList());

        // Act
        List<SuggestedCategory> result = categorySuggesterService.getSuggestedCategoriesAllName("", 5);

        // Assert
        assertThat(result.size(), equalTo(0));
        verify(categoryModel, times(1)).getLeafChildren(1L);
    }

    @Test
    public void shouldReturnMatchingCategories() {
        // Arrange
        String query = "electronics";

        List<Category> categories = new ArrayList<>();

        Category category1 = mock(Category.class);
        when(category1.getName()).thenReturn("electronics");
        when(category1.getId()).thenReturn(1L);
        when(category1.getReadOnly()).thenReturn(false);
        when(category1.isHidden()).thenReturn(false);
        categories.add(category1);

        Category category2 = mock(Category.class);
        when(category2.getName()).thenReturn("electronics");
        when(category2.getId()).thenReturn(2L);
        when(category2.getReadOnly()).thenReturn(false);
        when(category2.isHidden()).thenReturn(false);
        categories.add(category2);

        when(categoryModel.getLeafChildren(1L)).thenReturn(categories);
        when(categoryModel.getBreadcrumb(1L, ";")).thenReturn("Electronics");
        when(category1.getName()).thenReturn("electronics");
        when(categoryModel.getBreadcrumb(2L, ";")).thenReturn("Electronics;Accessories");

        // Act
        List<SuggestedCategory> result = categorySuggesterService.getSuggestedCategoriesAllName(query, 5);

        // Assert
        assertThat(result.size(), equalTo(2));
        verify(categoryModel, times(1)).getLeafChildren(1L);

        assertThat(result.get(0).getId(), equalTo(1L));
        assertThat(result.get(0).getDisplayName(), equalTo("electronics"));
        assertThat(result.get(0).getTree(), equalTo("Electronics"));

        assertThat(result.get(1).getId(), equalTo(2L));
        assertThat(result.get(1).getDisplayName(), equalTo("electronics"));
        assertThat(result.get(1).getTree(), equalTo("Electronics;Accessories"));
    }

}
