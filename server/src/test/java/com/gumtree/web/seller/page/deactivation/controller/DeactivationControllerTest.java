package com.gumtree.web.seller.page.deactivation.controller;

import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.UserApiErrors;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class DeactivationControllerTest extends BaseSellerControllerTest {

    @Mock
    private UserServiceFacade userServiceFacade;

    @Mock
    private UserSession userSession;

    @Mock
    private UrlScheme urlScheme;

    @InjectMocks
    private DeactivationController deactivationController;


    @Test
    public void shouldSuccessfullyDeactivateLoggedInUser() {
        // Given
        when(userSession.isLoggedIn()).thenReturn(true);
        doNothing().when(userSession).logout();
        when(userServiceFacade.finalizeAccountDeactivation(anyString())).thenReturn(ApiResponse.of(true));
        when(urlScheme.urlFor(eq(Actions.USER_HOME))).thenReturn("/");

        // When
        ModelAndView mv = deactivationController.finalizeAccountDeactivation("b73f7b7f-4a04-433a-bf2f-6692030a24c4", request);

        // Then
        verify(userSession, times(1)).logout();
        verify(userServiceFacade, times(1)).finalizeAccountDeactivation(eq("b73f7b7f-4a04-433a-bf2f-6692030a24c4"));
        assertThat(((RedirectView) mv.getView()).getUrl(), is("/"));
    }

    @Test
    public void shouldSuccessfullyDeactivateLoggedOutUser() {
        // Given
        when(userSession.isLoggedIn()).thenReturn(false);
        when(userServiceFacade.finalizeAccountDeactivation(anyString())).thenReturn(ApiResponse.of(true));
        when(urlScheme.urlFor(eq(Actions.USER_HOME))).thenReturn("/");

        // When
        ModelAndView mv = deactivationController.finalizeAccountDeactivation("b73f7b7f-4a04-433a-bf2f-6692030a24c4", request);

        // Then
        verify(userSession, times(0)).logout();
        verify(userServiceFacade, times(1)).finalizeAccountDeactivation(eq("b73f7b7f-4a04-433a-bf2f-6692030a24c4"));
        assertThat(((RedirectView) mv.getView()).getUrl(), is("/"));
    }

    @Test
    public void shouldRedirectToPageExpiredForInvalidToken() {
        // Given
        when(userServiceFacade.finalizeAccountDeactivation(anyString())).thenReturn(ApiResponse.error(new UserApiErrors()));
        when(urlScheme.urlFor(eq(Actions.PAGE_EXPIRED))).thenReturn("/page-expired");

        // When
        ModelAndView mv = deactivationController.finalizeAccountDeactivation("b73f7b7f-4a04-433a-bf2f-6692030a24c4", request);

        // Then
        verify(userSession, times(0)).logout();
        verify(userServiceFacade, times(1)).finalizeAccountDeactivation(eq("b73f7b7f-4a04-433a-bf2f-6692030a24c4"));
        assertThat(((RedirectView) mv.getView()).getUrl(), is("/page-expired"));
    }
}
