package com.gumtree.web.seller.config;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.google.common.collect.Lists;
import com.gumtree.config.api.SellerStubWireMockApi;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.VehicleDataApi;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardisedVehicleDataResponse;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.VehicleAttribute;
import org.apache.commons.httpclient.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.stream.Stream;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.gumtree.common.properties.GtPropManager.setProperty;
import static com.gumtree.config.SellerProperty.MOTORS_API_CONNECTION_TIMEOUT;
import static com.gumtree.config.SellerProperty.MOTORS_API_HOST;
import static com.gumtree.config.SellerProperty.MOTORS_API_PORT;
import static com.gumtree.config.SellerProperty.MOTORS_API_READ_TIMEOUT;
import static com.gumtree.config.api.SellerStubWireMockApi.WIREMOCK_API_PORT;

import static com.gumtree.web.seller.builder.VehicleAttributeHelper.CARS_VEHICLE_ATTRIBUTE_LIST;
import static com.gumtree.web.seller.builder.VehicleAttributeHelper.MOTOR_BIKES_VEHICLE_ATTRIBUTE_LIST;
import static com.gumtree.web.seller.builder.VehicleAttributeHelper.CAR_CATEGORY_ID;
import static com.gumtree.web.seller.builder.VehicleAttributeHelper.MOTOR_BIKES_CATEGORY_ID;

@Configuration
public class MotorsApiStubConfig {
    protected static final Logger LOG = LoggerFactory.getLogger(MotorsApiStubConfig.class);

    static{
        setProperty(MOTORS_API_HOST.getPropertyName(), "localhost");
        setProperty(MOTORS_API_PORT.getPropertyName(), WIREMOCK_API_PORT);
        setProperty(MOTORS_API_CONNECTION_TIMEOUT.getPropertyName(), 10000);
        setProperty(MOTORS_API_READ_TIMEOUT.getPropertyName(), 10000);
    }

    @Bean
    @Lazy(false)
    public WireMockServer motorsApiStubConfig(final VehicleDataApi vehicleDataApi) {
        LOG.info("************** USING STUBBED MOTORS API **************");
        initialiseWithDefaults(vehicleDataApi);
        return SellerStubWireMockApi.getServer();
    }

    private void initialiseWithDefaults(final VehicleDataApi vehicleDataApi) {
        Stream.of(new StandardisedVehicleDataResponse().categoryId(CAR_CATEGORY_ID)
                        .attributes(CARS_VEHICLE_ATTRIBUTE_LIST),
                new StandardisedVehicleDataResponse().categoryId(MOTOR_BIKES_CATEGORY_ID)
                        .attributes(MOTOR_BIKES_VEHICLE_ATTRIBUTE_LIST))
                .forEach(response -> mockVehicleDataApi(vehicleDataApi, response, HttpStatus.SC_OK));
    }

    private void mockVehicleDataApi(final VehicleDataApi vehicleDataApi, final StandardisedVehicleDataResponse response, final Integer status) {
        try {
            SellerStubWireMockApi.getServer().stubFor(get(urlEqualTo(String.format("/api/vehicleData/%s?category_id=%s",
                    response.getAttributes().stream().filter(attr -> attr.getName().equals("vrn")).findFirst().get().getValue(), response.getCategoryId())))
                    .withHeader("Client-Id", equalTo("seller"))
                    .willReturn(aResponse()
                            .withStatus(status)
                            .withHeader("Content-Type", "application/json")
                            .withBody(vehicleDataApi.getApiClient().getObjectMapper().writeValueAsString(response))));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
