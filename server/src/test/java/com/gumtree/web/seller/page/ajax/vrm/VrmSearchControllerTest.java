package com.gumtree.web.seller.page.ajax.vrm;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.VehicleAttribute;
import com.gumtree.web.seller.page.ajax.vrm.model.VrmLookupResponse;
import com.gumtree.web.seller.page.ajax.vrm.model.VrmLookupState;
import com.gumtree.web.seller.page.postad.model.PriceGuidance;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceContext;
import com.gumtree.web.seller.service.postad.priceguidance.PriceGuidanceService;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class VrmSearchControllerTest {

    public static final String VRM = "vrm";
    public static final long CATEGORY_ID = 1L;

    @InjectMocks
    private VrmSearchController vrmSearchController;

    @Mock
    private CategoryModel categoryModelMock;

    @Mock
    private PriceGuidanceService priceGuidanceService;

    @Mock
    private MetricRegistry metricRegistry;

    @Rule
    public ExpectedException exception = ExpectedException.none();

    @Before
    public void beforeEach() {
        when(priceGuidanceService.getForCarAd(VRM,CATEGORY_ID)).thenReturn(java.util.Optional.empty());
        when(metricRegistry.counter(anyString())).thenReturn(mock(Counter.class));
    }

    @Test
    public void shouldReturnEmptyVrmLookupResponse_whenStandardisedVehicleDataResponseHasNoVehicleAttributes() throws Exception {
        // given
        when(priceGuidanceService.getForCarAd(VRM, CATEGORY_ID))
                .thenReturn(java.util.Optional.of(
                        new PriceGuidanceContext(new PriceGuidance(0, Collections.emptyList()), Collections.emptyMap())));
        when(categoryModelMock.getCategoryAttributes(CATEGORY_ID)).thenReturn(Optional.of(Lists.newArrayList()));

        // When
        VrmLookupResponse response = vrmSearchController.lookupVrn(VRM, CATEGORY_ID);

        // Then
        assertThat(response.getState(), equalTo(VrmLookupState.FOUND));
        assertThat(response.getAttributes(), is(not(nullValue())));
        assertThat(response.getAttributes().getMake(), is(nullValue()));
        assertThat(response.getAttributes().getModel(), is(nullValue()));
        assertThat(response.getAttributes().getBodyType(), is(nullValue()));
        assertThat(response.getAttributes().getColour(), is(nullValue()));
        assertThat(response.getAttributes().getEngineSize(), is(nullValue()));
        assertThat(response.getAttributes().getFuelType(), is(nullValue()));
        assertThat(response.getAttributes().getTransmission(), is(nullValue()));
        assertThat(response.getAttributes().getRegistrationYear(), is(nullValue()));
    }

    @Test
    public void shouldReturnNotFoundVrmLookupResponse_whenVehicleDataNotFound() throws Exception {
        // Given
        when(priceGuidanceService.getForCarAd(VRM, CATEGORY_ID)).thenReturn(java.util.Optional.empty());
        when(categoryModelMock.getCategoryAttributes(CATEGORY_ID)).thenReturn(Optional.of(Lists.newArrayList()));

        // When
        VrmLookupResponse response = vrmSearchController.lookupVrn(VRM, CATEGORY_ID);

        // Then
        assertThat(response.getState(), equalTo(VrmLookupState.NOT_FOUND));
        assertThat(response.getAttributes(), is(nullValue()));
    }

    @Test
    public void shouldReturnPopulatedVrmLookupResponse_whenStandardisedVehicleDataResponseReturnsVehicleAttributes() throws Exception {
        // Given
        VehicleAttribute vrm = new VehicleAttribute().name("vrn").value(VRM);
        VehicleAttribute make = new VehicleAttribute().name("vehicle_make").value("make");
        VehicleAttribute model = new VehicleAttribute().name("vehicle_model").value("model");
        VehicleAttribute doors = new VehicleAttribute().name("vehicle_doors").value("4");
        VehicleAttribute estimatedMileage = new VehicleAttribute().name("vehicle_estimated_mileage").value("65234");

        AttributeMetadata vrmMeta = attributeMetadata("vrn");
        AttributeMetadata makeMeta = attributeMetadata("vehicle_make");
        AttributeMetadata modelMeta = attributeMetadata("vehicle_model");
        AttributeMetadata doorsMeta = attributeMetadata("vehicle_doors");
        AttributeMetadata estimatedMileageMeta = attributeMetadata("vehicle_estimated_mileage");

        when(priceGuidanceService.getForCarAd(VRM, CATEGORY_ID))
                .thenReturn(java.util.Optional.of(
                        new PriceGuidanceContext(new PriceGuidance(0, Collections.emptyList()),
                                asMap(vrm, make, model, doors, estimatedMileage))));

        when(categoryModelMock.getCategoryAttributes(CATEGORY_ID))
                .thenReturn(Optional.of(Lists.newArrayList(vrmMeta, makeMeta, modelMeta, doorsMeta, estimatedMileageMeta)));

        // When
        VrmLookupResponse response = vrmSearchController.lookupVrn(VRM, CATEGORY_ID);

        // Then
        assertThat(response.getState(), equalTo(VrmLookupState.FOUND));
        assertThat(response.getAttributes(), is(not(nullValue())));
        assertThat(response.getAttributes().getMake().getAttributeName(), equalTo("vehicle_make"));
        assertThat(response.getAttributes().getMake().getValue(), equalTo("make"));
        assertThat(response.getAttributes().getModel().getAttributeName(), equalTo("vehicle_model"));
        assertThat(response.getAttributes().getModel().getValue(), equalTo("model"));
        assertThat(response.getAttributes().getBodyType(), is(nullValue()));
        assertThat(response.getAttributes().getColour(), is(nullValue()));
        assertThat(response.getAttributes().getEngineSize(), is(nullValue()));
        assertThat(response.getAttributes().getFuelType(), is(nullValue()));
        assertThat(response.getAttributes().getTransmission(), is(nullValue()));
        assertThat(response.getAttributes().getRegistrationYear(), is(nullValue()));
        assertThat(response.getAttributes().getEstimatedMileage().getAttributeName(), equalTo("vehicle_estimated_mileage"));
        assertThat(response.getAttributes().getEstimatedMileage().getValue(), equalTo("65234"));
        assertThat(response.getAttributes().getDoors().getAttributeName(), equalTo("vehicle_doors"));
        assertThat(response.getAttributes().getDoors().getValue(), equalTo("4"));
    }

    @Test
    public void shouldReturnNotFoundVrmLookupResponse_whenExceptionThrownInStandardisedVehicleDataResponseProcession() throws Exception {
        // Given
        VehicleAttribute vrm = new VehicleAttribute().name("vrn").value(VRM);
        VehicleAttribute make = new VehicleAttribute().name("vehicle_make").value("make");
        VehicleAttribute model = new VehicleAttribute().name("vehicle_model").value("model");

        when(priceGuidanceService.getForCarAd(VRM, CATEGORY_ID))
                .thenReturn(java.util.Optional.of(
                        new PriceGuidanceContext(new PriceGuidance(0, Collections.emptyList()),
                                asMap(vrm, make, model))));

        when(categoryModelMock.getCategoryAttributes(CATEGORY_ID)).thenThrow(new RuntimeException("error"));

        // When
        VrmLookupResponse response = vrmSearchController.lookupVrn(VRM, CATEGORY_ID);

        // Then
        assertThat(response.getState(), equalTo(VrmLookupState.NOT_FOUND));
        assertThat(response.getAttributes(), is(nullValue()));
    }

    private AttributeMetadata attributeMetadata(String name) {
        AttributeMetadata vehicleMakeMeta = new AttributeMetadata();
        vehicleMakeMeta.setName(name);
        vehicleMakeMeta.setType(AttributeType.ENUM);
        return vehicleMakeMeta;
    }

    private static Map<String, String> asMap(VehicleAttribute... vehicleAttributes) {
        return Stream.of(vehicleAttributes).collect(Collectors.toMap(VehicleAttribute::getName, VehicleAttribute::getValue));
    }

}
