package com.gumtree.web.seller.page.postad.common;

import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class AppVersionUtilsTest {

    // Sample user agent strings for testing
    private static final String ANDROID_USER_AGENT = "Mozilla/5.0 (Linux; Android 10; Pixel 3a Build/QQ1A.190821.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.119 Mobile Safari/537.36 Android/GumtreeApp";
    private static final String IOS_USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 iOS/GumtreeApp";
    private static final String WEB_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3";
    private static final String M_USER_AGENT = "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36";

    @Test
    public void isPhoneVerificationVersion_UserAgentNull_ReturnsTrue() {
        // Test case: User agent is null, should return true
        assertTrue(AppVersionUtils.isPhoneVerificationVersion(null, "18.1.19"));
    }

    @Test
    public void isPhoneVerificationVersion_AndroidVersionGreaterOrEqual_ReturnsTrue() {
        // Test case: Android version is greater than or equal to the required version, should return true
        String userAgent = "Mozilla/5.0 (Linux; Android 10; Pixel 3a Build/QQ1A.190821.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.119 Mobile Safari/537.36 Android/GumtreeApp AppVersion/18.1.19";
        assertTrue(AppVersionUtils.isPhoneVerificationVersion(userAgent,"18.1.19"));
    }

    @Test
    public void isPhoneVerificationVersion_AndroidVersionLess_ReturnsFalse() {
        // Test case: Android version is less than the required version, should return false
        String userAgent = ANDROID_USER_AGENT;
        assertFalse(AppVersionUtils.isPhoneVerificationVersion(userAgent,""));
    }

    @Test
    public void isPhoneVerificationVersion_IOSVersionGreaterOrEqual_ReturnsTrue() {
        // Test case: iOS version is greater than or equal to the required version, should return true
        String userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 iOS/GumtreeApp AppVersion/18.1.19";
        assertTrue(AppVersionUtils.isPhoneVerificationVersion(userAgent,"18.1.19"));
    }

    @Test
    public void isPhoneVerificationVersion_IOSVersionLess_ReturnsFalse() {
        // Test case: iOS version is less than the required version, should return false
        assertFalse(AppVersionUtils.isPhoneVerificationVersion(IOS_USER_AGENT," "));
    }

    @Test
    public void isPhoneVerificationVersion_WebUserAgent_ReturnsTrue() {
        // Test case: User agent is for a web client, should return true
        assertTrue(AppVersionUtils.isPhoneVerificationVersion(WEB_USER_AGENT,"18.1.19"));
    }

    @Test
    public void isPhoneVerificationVersion_MUserAgent_ReturnsTrue() {
        // Test case: User agent is for a web client, should return true
        assertTrue(AppVersionUtils.isPhoneVerificationVersion(M_USER_AGENT,""));
        assertTrue(AppVersionUtils.isPhoneVerificationVersion(M_USER_AGENT,null));
    }
}
