package com.gumtree.web.seller.service.image.error;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.mediaprocessor.model.ApiErrors;
import feign.Request;
import feign.Response;
import org.junit.Before;
import org.junit.Test;
import org.springframework.http.HttpStatus;

import java.nio.charset.Charset;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

public class MediaProcessorErrorDecoderTest {

    private MediaProcessorErrorDecoder mediaProcessorErrorDecoder;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Before
    public void setup() {
        mediaProcessorErrorDecoder = new MediaProcessorErrorDecoder(objectMapper);
    }

    @Test
    public void shouldHandleServersideError() throws JsonProcessingException {

        ApiErrors apiErrors = new ApiErrors();

        String methodName = "postImage";
        MediaProcessorServerException e = (MediaProcessorServerException) mediaProcessorErrorDecoder.decode(methodName,
                buildResponse(HttpStatus.INTERNAL_SERVER_ERROR, objectMapper.writeValueAsString(apiErrors)));
        assertThat(e.getStatus(), is(500));
    }

    @Test
    public void shouldHandleClientsideError() throws JsonProcessingException {
        ApiErrors apiErrors = new ApiErrors();

        String methodName = "postImage";
        MediaProcessorClientException e = (MediaProcessorClientException) mediaProcessorErrorDecoder.decode(methodName,
                buildResponse(HttpStatus.BAD_REQUEST, objectMapper.writeValueAsString(apiErrors)));
        assertThat(e.getStatus(), is(400));
    }

    private Response buildResponse(HttpStatus status, String body) {
        Map<String, Collection<String>> headers = Collections.emptyMap();
        Request request = Request.create("POST","/image/copy", headers, body.getBytes(), Charset.defaultCharset());
        return Response
                .builder()
                .headers(Collections.emptyMap())
                .status(status.value())
                .request(request)
                .body(body, Charset.defaultCharset())
                .build();
    }
}