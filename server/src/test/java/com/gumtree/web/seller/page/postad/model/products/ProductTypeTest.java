package com.gumtree.web.seller.page.postad.model.products;

import com.gumtree.seller.domain.product.entity.ProductName;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ProductTypeTest {

    @Test
    public void shouldReturnCorrectProductType() {
        assertEquals(ProductType.INSERTION, ProductType.getType(ProductName.INSERTION));
        assertEquals(ProductType.BUMP_UP, ProductType.getType(ProductName.BUMP_UP));
        assertEquals(ProductType.FEATURED, ProductType.getType(ProductName.FEATURE_3_DAY));
        assertEquals(ProductType.FEATURED, ProductType.getType(ProductName.FEATURE_7_DAY));
        assertEquals(ProductType.FEATURED, ProductType.getType(ProductName.FEATURE_14_DAY));
        assertEquals(ProductType.URGENT, ProductType.getType(ProductName.URGENT));
        assertEquals(ProductType.SPOTLIGHT, ProductType.getType(ProductName.HOMEPAGE_SPOTLIGHT));
        assertEquals(ProductType.NOT_VISIBLE_PRODUCT, ProductType.getType(ProductName.SEARCH_STANDOUT));
        assertEquals(ProductType.NOT_VISIBLE_PRODUCT, ProductType.getType(ProductName.EXTENDED_VEHICLE_HISTORY_CHECK));
        assertEquals(ProductType.NOT_VISIBLE_PRODUCT, ProductType.getType(ProductName.CALL_TRACKING_ACCOUNT_LEVEL));
        assertEquals(ProductType.NOT_VISIBLE_PRODUCT, ProductType.getType(ProductName.CALL_TRACKING_ACCOUNT_LEVEL_HIDDEN));
        assertEquals(ProductType.NOT_VISIBLE_PRODUCT, ProductType.getType(ProductName.CALL_TRACKING_ADVERT_LEVEL));
        assertEquals(ProductType.NOT_VISIBLE_PRODUCT, ProductType.getType(ProductName.CALL_TRACKING_ADVERT_LEVEL_WITH_BLACKLISTING));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrowExceptionForUnknownProductName() {
        ProductType.getType(ProductName.APPVAULT_RESPONSE_MANAGER);
    }
}