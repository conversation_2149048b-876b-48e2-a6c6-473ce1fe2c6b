package com.gumtree.web.seller.service.ai;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.ajax.adcontent.AdContentSuggestController;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.fest.assertions.data.MapEntry;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.util.*;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.*;

public class AdContentSuggestControllerTest {
    @Mock private CookieResolver cookieResolver;
    @Mock private CategoryModel categoryModel;
    @Mock private ApiCallExecutor apiCallExecutor;
    @Mock private ErrorMessageResolver messageResolver;
    @Mock private UrlScheme urlScheme;
    @Mock private UserSessionService userSessionService;
    @Mock private CustomMetricRegistry metrics;
    @Mock private AISuggestService aiSuggestService;
    @Spy
    private CustomMetricRegistry customMetricRegistry = new CustomMetricRegistry(new SimpleMeterRegistry());

    private AdContentSuggestController controller;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        controller = new AdContentSuggestController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService, metrics, aiSuggestService);
    }

    @Test
    public void testTitleAISuggest_normal() {
        String input = "car";
        List<String> titles = Arrays.asList("Used car for sale", "Cheap car for sale");
        Timer timer = mock(Timer.class);
        when(metrics.AIResponseTimer(anyString())).thenReturn(timer);
        when(aiSuggestService.getSuggestedTitles(input)).thenReturn(titles);
        List<String> result = controller.titleAISuggest(input);
        customMetricRegistry.AIResponseTimer(anyString());
    }

    @Test
    public void testTitleAndDescAISuggest_normal() {
        String categoryName = "cars";
        String title = "old car";
        String desc = "good car";
        String images = "image_url";
        JSONObject attributes = new JSONObject();
        Map<String, String> mockResult = new HashMap<>();
        mockResult.put("title", "Suggested title");
        mockResult.put("desc", "Suggested description");
        Timer timer = mock(Timer.class);
        when(metrics.AIResponseTimer(anyString())).thenReturn(timer);
        when(aiSuggestService.getSuggestedTitleAndDesc(categoryName, attributes, title, desc, images)).thenReturn(mockResult);
        Map<String, String> result = controller.titleAndDescAISuggest(categoryName, title, desc, images, attributes);
        customMetricRegistry.AIResponseTimer(anyString());
    }

    @Test
    public void testTitleAndDescAISuggest_metricsNull() {
        controller = new AdContentSuggestController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService, null, aiSuggestService);
        String categoryName = "cars";
        String title = "old car";
        String desc = "good car";
        String images = "image_url";
        JSONObject attributes = new JSONObject();
        Map<String, String> mockResult = new HashMap<>();
        mockResult.put("title", "Suggested title");
        mockResult.put("desc", "Suggested description");
        when(aiSuggestService.getSuggestedTitleAndDesc(categoryName, attributes, title, desc, images)).thenReturn(mockResult);
        Map<String, String> result = controller.titleAndDescAISuggest(categoryName, title, desc, images, attributes);
        customMetricRegistry.AIResponseTimer(anyString());

    }

    @Test
    public void testTitleAndDescAISuggest_aiSuggestServiceNull() {
        controller = new AdContentSuggestController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService, metrics, null);
        String categoryName = "cars";
        String title = "old car";
        String desc = "good car";
        String images = "image_url";
        JSONObject attributes = new JSONObject();
        Map<String, String> result = controller.titleAndDescAISuggest(categoryName, title, desc, images, attributes);
//        assertThat(result).isEmpty();
        customMetricRegistry.AIResponseTimer(anyString());
    }

    @Test
    public void testTitleAndDescAISuggest_timerNull() {
        String categoryName = "cars";
        String title = "old car";
        String desc = "good car";
        String images = "image_url";
        JSONObject attributes = new JSONObject();
        Map<String, String> mockResult = new HashMap<>();
        mockResult.put("title", "Suggested title");
        mockResult.put("desc", "Suggested description");
        when(metrics.AIResponseTimer(anyString())).thenReturn(null);
        when(aiSuggestService.getSuggestedTitleAndDesc(categoryName, attributes, title, desc, images)).thenReturn(mockResult);
        Map<String, String> result = controller.titleAndDescAISuggest(categoryName, title, desc, images, attributes);
        customMetricRegistry.AIResponseTimer(anyString());
    }

    @Test
    public void testTitleAndDescAISuggest_exception() {
        String categoryName = "cars";
        String title = "old car";
        String desc = "good car";
        String images = "image_url";
        JSONObject attributes = new JSONObject();
        Timer timer = mock(Timer.class);
        when(metrics.AIResponseTimer(anyString())).thenReturn(timer);
        Map<String, String> result = controller.titleAndDescAISuggest(categoryName, title, desc, images, attributes);
//        assertThat(result).isEmpty();
        customMetricRegistry.AIResponseTimer(anyString());
    }
}
