package com.gumtree.web.seller.page.postad.converter;


import com.gumtree.api.Image;
import com.gumtree.seller.domain.image.entity.ImageSize;
import com.gumtree.util.url.CdnImageUrlProvider;
import com.gumtree.web.seller.page.postad.model.PostAdImage;
import com.gumtree.web.service.images.secure.SecureImageService;
import org.junit.Before;
import org.junit.Test;

import static junit.framework.Assert.assertNotNull;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PostAdImageConverterTest {

    private PostAdImageConverter converter;
    private CdnImageUrlProvider cdnImageUrlProvider;

    @Before
    public void setup() {
        cdnImageUrlProvider = mock(CdnImageUrlProvider.class);
        converter = new PostAdImageConverterImpl(cdnImageUrlProvider);
    }

    @Test
    public void testSecurelyConvertImageToPostAdImage() throws Exception {
        when(cdnImageUrlProvider.getSecureImageUrl("http://gumtree.com/00/dsf796re786f32878734y_77.jpg",
                ImageSize.MINITHUMB.getId()))
                .thenReturn("https://secureimg.ebayimg.com/00/dsf796re786f32878734y_77.jpg");
        Image image = makeImage();
        PostAdImage postAdImage = converter.convertImageToPostAdImage(image);

        assertNotNull(postAdImage);
        assertThat(postAdImage.getId(), equalTo(123L));
        assertThat(postAdImage.getSize(), equalTo("BIG"));
        assertThat(postAdImage.getUrl(), equalTo("//gumtree.com/00/dsf796re786f32878734y_1.jpg"));
        assertThat(postAdImage.getThumbnailUrl(), equalTo("https://secureimg.ebayimg.com/00/dsf796re786f32878734y_77.jpg"));
    }

    private Image makeImage() {
        Image image = new Image();
        image.setId(123L);
        image.setSize("BIG");
        image.setUrl("http://gumtree.com/00/dsf796re786f32878734y_1.jpg");

        return image;
    }
}
