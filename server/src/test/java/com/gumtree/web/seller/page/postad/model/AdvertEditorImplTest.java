package com.gumtree.web.seller.page.postad.model;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.Account;
import com.gumtree.api.Ad;
import com.gumtree.api.AdFeature;
import com.gumtree.api.AdStatus;
import com.gumtree.api.Attribute;
import com.gumtree.api.Image;
import com.gumtree.api.LegacyImage;
import com.gumtree.api.User;
import com.gumtree.api.UserType;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.*;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.command.ValidateRegistrationFormApiCall;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.client.spec.PostcodeApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.domain.attribute.ApiAttribute;
import com.gumtree.api.domain.order.ProductNamesSearchResponse;
import com.gumtree.bapi.AccountApi;
import com.gumtree.api.util.SearchBeanRequestParams;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.config.SellerProperty;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.media.ImageSize;
import com.gumtree.domain.newattribute.value.SellerType;
import com.gumtree.mobile.test.Fixtures;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.category.impl.CategoryServiceImpl;
import com.gumtree.util.url.CdnImageUrlProvider;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.ajax.vrm.MotorsApiClient;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.api.ValidateAdApiCall;
import com.gumtree.web.seller.page.postad.model.exception.BreedNotFoundException;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.page.postad.service.CommonAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.PetAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.PhoneAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.legal.PostAdLegalService;
import com.gumtree.web.seller.service.location.PostAdLocationService;
import com.gumtree.web.seller.service.postad.UserPostcodeLookupService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import com.gumtree.web.seller.service.pricing.PricingContext;
import com.gumtree.web.seller.service.pricing.PricingContextImpl;
import com.gumtree.web.seller.service.pricing.PricingService;
import com.gumtree.web.service.ContactEmailService;
import com.gumtree.zeno.core.service.ZenoService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.iterableWithSize;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.Matchers.sameInstance;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

public class AdvertEditorImplTest {

    private static final Long ESSEX = 10000384L;
    private static final Long ESSEX_CB10_1NU = 11000547L;
    private static final Long CARS_CATEGORY_ID = 9311L;
    private static final Long USED_FIAT_CATEGORY_ID = 10305L;

    private static final Long RICHMOND_TW10_6LW = 203L;
    private static final Long SOFA_CATEGORY_ID = 11254L;

    private static final Long FREEBIES_CATEGORY_ID = 120L;

    private UserSession authenticatedUserSession;

    private UserSecurityManager userSecurityManager;

    private UserPostcodeLookupService userPostcodeLookupService;

    private BushfireApi bushfireApi;

    private PostcodeApi postcodeApi;

    private AdvertApi advertApi;

    private AttributePresentationService attributePresentationService;

    private AdvertEditor advertEditorImpl;

    private PostAdLocationService postAdLocationService;

    private ApiCallExecutor apiCallExecutor;

    private Validator validator;

    private CategoryService categoryService;

    private Account selectedAccount;

    private OrderApi orderApi;
    private PostAdDetail advertDetail;
    private ProductPrice insertionPrice;
    private CustomMetricRegistry metrics;

    @Mock
    private PricingService pricingService;

    @Mock
    private CategoryModel categoryModel;

    @Mock
    Category carsCategory;

    @Mock
    private ContactEmailService contactEmailService;

    @Mock
    private MotorsApiClient motorsApiClient;

    @Mock
    private Counter mockCounter;

    @Mock
    private ZenoService zenoService;

    @Mock
    private CdnImageUrlProvider cdnImageUrlProvider;

    @Mock
    private MeterRegistry meterRegistry;

    @Mock
    private AccountApi accountApi;

    private PetAttributesValidationService petAttributesValidationService;

    private PhoneAttributesValidationService phoneAttributesValidationService;
    private CommonAttributesValidationService commonAttributesValidationService;



    static {
        // Enable Autobiz in Essex
        GtPropManager.setProperty(SellerProperty.AUTOBIZ_EXPERIMENT_LOCATION_ID.getPropertyName(), ESSEX);
    }

    @Before
    public void init() {
        initMocks(this);
        authenticatedUserSession = mock(UserSession.class);
        userSecurityManager = mock(UserSecurityManager.class);
        userPostcodeLookupService = mock(UserPostcodeLookupService.class);
        bushfireApi = mock(BushfireApi.class);
        postcodeApi = mock(PostcodeApi.class);
        attributePresentationService = mock(AttributePresentationService.class);
        validator = mock(Validator.class);
        apiCallExecutor = mock(ApiCallExecutor.class);
        postAdLocationService = mock(PostAdLocationService.class);
        categoryService = mock(CategoryService.class);
        advertDetail = new PostAdDetail();
        advertDetail.setPostAdFormBean(new PostAdFormBean());
        when(postAdLocationService.lookupPostcode("TW9 1EH")).thenReturn(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_RECOGNISED, "TW9 1EH", 5L));
        advertEditorImpl = getAdvertEditor(advertDetail);
        orderApi = mock(OrderApi.class);
        advertApi = mock(AdvertApi.class);
        accountApi = mock(AccountApi.class);
        Category forSaleCategory = Category.newCategory().withId(2549L).withSeoName(Categories.FOR_SALE.getSeoName()).build();
        Category jobCategory = Category.newCategory().withId(2553L).withSeoName(Categories.JOBS.getSeoName()).build();
        Category carCategory = Category.newCategory().withId(2551L).withSeoName(Categories.MOTORS.getSeoName()).build();
        Category communityCategory = Category.newCategory().withId(2550L).withSeoName(Categories.COMMUNITY.getSeoName()).build();
        when(categoryModel.getL1CategoryFor(2549L)).thenReturn(Optional.of(forSaleCategory));
        when(categoryModel.getL1CategoryFor(2553L)).thenReturn(Optional.of(jobCategory));
        when(categoryModel.getL1CategoryFor(2551L)).thenReturn(Optional.of(carCategory));
        when(categoryModel.getL1CategoryFor(1234L)).thenReturn(Optional.of(communityCategory));
        when(categoryModel.getCategory(10L)).thenReturn(Optional.of(mock(Category.class)));
        when(categoryModel.getL1CategoryFor(10L)).thenReturn(Optional.of(mock(Category.class)));
        when(categoryModel.getCategory(4615L)).thenReturn(Optional.of(mock(Category.class)));
        when(categoryModel.getCategory(CategoryConstants.CARS_ID)).thenReturn(Optional.of(carsCategory));
        when(categoryModel.getCategory(9389L)).thenReturn(Optional.of(mock(Category.class)));
        when(categoryModel.getCategory(9390L)).thenReturn(Optional.of(mock(Category.class)));
        when(categoryModel.getAttributeType(anyString())).thenReturn(Optional.absent());
        when(categoryModel.findAttributesByNameForGivenCategory(anyString(), anyLong())).thenReturn(Optional.absent());

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setPhone("0800 50 50 50");

        when(authenticatedUserSession.getUsername()).thenReturn("<EMAIL>");
        when(authenticatedUserSession.getUser()).thenReturn(user);
        when(authenticatedUserSession.getUserType()).thenReturn(UserLoginStatus.EXISTING);

        when(attributePresentationService.loadAttributeGroups(eq(1L), anyMap())).thenReturn(createTestAttributeSetOne());
        when(attributePresentationService.loadAttributeGroups(eq(2L), anyMap())).thenReturn(createTestAttributeSetTwo());
        when(attributePresentationService.loadAttributeGroups(eq(3L), anyMap())).thenReturn(createTestAttributeSetOne());

        insertionPrice = mock(ProductPrice.class);
        PricingMetadata pricingMetadata = mock(PricingMetadata.class);
        when(pricingMetadata.getInsertionPrice()).thenReturn(insertionPrice);
        when(pricingService.getPriceInformation(any(PricingContext.class))).thenReturn(pricingMetadata);

        selectedAccount = new Account();
        selectedAccount.setId(24L);
        when(authenticatedUserSession.getSelectedAccountId()).thenReturn(24L);
        when(userSecurityManager.getAccount(24L, authenticatedUserSession)).thenReturn(selectedAccount);
        when(userPostcodeLookupService.getLastUsedPostcode(24L)).thenReturn(Optional.of("TW9 1EH"));
        when(bushfireApi.orderApi()).thenReturn(orderApi);
        when(bushfireApi.postcodeApi()).thenReturn(postcodeApi);
        when(bushfireApi.advertApi()).thenReturn(advertApi);

        petAttributesValidationService = new PetAttributesValidationService(categoryModel, new CustomMetricRegistry(new SimpleMeterRegistry()));
        phoneAttributesValidationService = new PhoneAttributesValidationService(categoryModel, new CustomMetricRegistry(new SimpleMeterRegistry()));
        commonAttributesValidationService =  new CommonAttributesValidationService(categoryModel);

    }

    @Test
    public void isValidReturnsFalseIfValidatingAdvertDetailReturnsErrors() {
        Set violations = new HashSet();
        violations.add(mock(ConstraintViolation.class));

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setCategoryId(10L);

        when(validator.validate(postAdDetail)).thenReturn(violations);

        ApiCallResponse response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(true);
        when(apiCallExecutor.call(any(ValidateAdApiCall.class))).thenReturn(response);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        assertThat(advertEditorImpl.isValid(), equalTo(false));
    }

    private AdvertEditor getAdvertEditor(PostAdDetail postAdDetail) {
        metrics = new CustomMetricRegistry(new SimpleMeterRegistry());
        petAttributesValidationService = new PetAttributesValidationService(categoryModel,metrics);
        phoneAttributesValidationService = new PhoneAttributesValidationService(categoryModel,metrics);
        commonAttributesValidationService = new CommonAttributesValidationService(categoryModel);
        AdvertEditor advertEditor = new AdvertEditorImpl("1", postAdDetail, bushfireApi, authenticatedUserSession, userSecurityManager,
                userPostcodeLookupService, attributePresentationService, postAdLocationService, apiCallExecutor, validator,
                categoryService, mock(PostAdLegalService.class), pricingService, categoryModel, contactEmailService, motorsApiClient,
                zenoService, metrics, cdnImageUrlProvider, petAttributesValidationService, phoneAttributesValidationService, accountApi,
                commonAttributesValidationService);

        if (postAdDetail.getPostAdFormBean() == null) {
            postAdDetail.setPostAdFormBean(new PostAdFormBean());
        }

        Long categoryId = postAdDetail.getPostAdFormBean().getCategoryId();
        Category category = new Category();
        category.setId(categoryId);
        when(categoryService.getById(categoryId)).thenReturn(Optional.of(category));
        return advertEditor;
    }

    private AdvertEditor getAdvertEditorWithStubbedCategoryModel(PostAdDetail postAdDetail) {
        categoryModel = Fixtures.STUB_CATEGORY_MODEL;
        categoryService = new CategoryServiceImpl(categoryModel);

        return new AdvertEditorImpl("1", postAdDetail, bushfireApi, authenticatedUserSession, userSecurityManager,
                userPostcodeLookupService, attributePresentationService, postAdLocationService, apiCallExecutor, validator,
                categoryService, mock(PostAdLegalService.class), pricingService, categoryModel, contactEmailService, motorsApiClient,
                zenoService, new CustomMetricRegistry(new SimpleMeterRegistry()), cdnImageUrlProvider, petAttributesValidationService,
                phoneAttributesValidationService, accountApi,commonAttributesValidationService);
    }

    @Test
    public void isValidSetsADummyIpAddress() {
        Set violations = new HashSet();

        Location location = mock(Location.class);
        when(postAdLocationService.get(5L)).thenReturn(location);
        when(postAdLocationService.hasZoomIn(location)).thenReturn(false);
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setCategoryId(10L);
        postAdDetail.setManualLocation(5L);

        when(validator.validate(postAdDetail)).thenReturn(violations);

        ApiCallResponse response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(false);
        when(apiCallExecutor.call(any(ValidateAdApiCall.class))).thenReturn(response);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        advertEditorImpl.isValid();
        ArgumentCaptor<ValidateAdApiCall> apiCall = ArgumentCaptor.forClass(ValidateAdApiCall.class);
        verify(apiCallExecutor).call(apiCall.capture());
        assertThat(apiCall.getValue().getPostAdvertBean().getIp(), equalTo("*********"));
    }

    @Test
    public void isValidReturnsFalseIfCategoryHasNotBeenSet() {
        Set violations = new HashSet();

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setCategoryId(10L);

        when(validator.validate(postAdDetail)).thenReturn(violations);

        ApiCallResponse response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(true);
        when(apiCallExecutor.call(any(ValidateAdApiCall.class))).thenReturn(response);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        assertThat(advertEditorImpl.isValid(), equalTo(false));
    }

    @Test
    public void isValidReturnsFalseIfNoValidationLocationHasBeenSet() {
        Set violations = new HashSet();

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setCategoryId(10L);

        when(validator.validate(postAdDetail)).thenReturn(violations);

        ApiCallResponse response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(true);
        when(apiCallExecutor.call(any(ValidateAdApiCall.class))).thenReturn(response);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        assertThat(advertEditorImpl.isValid(), equalTo(false));
    }

    @Test
    public void isValidReturnsFalseWhenValidatingApiBeanReturnsErrorResponse() {
        Set violations = new HashSet();

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setCategoryId(10L);

        when(validator.validate(postAdDetail)).thenReturn(violations);

        ApiCallResponse response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(true);
        when(apiCallExecutor.call(any(ValidateAdApiCall.class))).thenReturn(response);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        assertThat(advertEditorImpl.isValid(), equalTo(false));
    }

    @Test
    public void isValidReturnsFalseWhenValidatingRegisterUserBeanReturnsErrorResponse() {
        Set violations = new HashSet();

        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setOptInMarketing(true);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setCategoryId(10L);

        when(authenticatedUserSession.getUserType()).thenReturn(UserLoginStatus.NEW_UNREGISTERED);
        when(validator.validate(postAdDetail)).thenReturn(violations);

        ApiCallResponse postAdResponse = mock(ApiCallResponse.class);
        when(postAdResponse.isErrorResponse()).thenReturn(false);
        when(apiCallExecutor.call(any(ValidateAdApiCall.class))).thenReturn(postAdResponse);

        ApiCallResponse registerUserResponse = mock(ApiCallResponse.class);
        when(registerUserResponse.isErrorResponse()).thenReturn(true);
        when(apiCallExecutor.call(any(ValidateRegistrationFormApiCall.class))).thenReturn(registerUserResponse);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        assertThat(advertEditorImpl.isValid(), equalTo(false));
    }

    @Test
    public void isValidReturnsTrueWhenValidatingApiBeanReturnsSuccessResponse() {
        Set violations = new HashSet();

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setCategoryId(10L);
        postAdDetail.setManualLocation(5L);

        when(validator.validate(postAdDetail)).thenReturn(violations);

        ApiCallResponse response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(false);
        when(apiCallExecutor.call(any(ValidateAdApiCall.class))).thenReturn(response);

        Location location = mock(Location.class);
        when(location.getId()).thenReturn(5);
        when(postAdLocationService.get(5L)).thenReturn(location);
        when(postAdLocationService.hasZoomIn(location)).thenReturn(false);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        assertThat(advertEditorImpl.isValid(), equalTo(true));
    }

    @Test
    public void supportsChangeVisibleOnMapTrueWhenInEditModeAndAndValidLocationSelected() {
        when(postAdLocationService.lookupPostcode(anyString())).thenReturn(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_RECOGNISED, "TW9 1EH"));

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setAdvertId(1L);

        advertEditorImpl = getAdvertEditor(postAdDetail);
        advertEditorImpl.getAdvertDetail().setPostcodeLocation(postAdLocationService.lookupPostcode("TW9 1EH"));

        assertThat(advertEditorImpl.supportsChangeVisibleOnMap(), equalTo(true));
    }

    @Test
    public void supportsChangeVisibleOnMapTrueWhenInCreateModeAndValidLocationSelected() {
        when(postAdLocationService.lookupPostcode(anyString())).thenReturn(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_RECOGNISED, "TW9 1EH"));

        advertEditorImpl = getAdvertEditor(new PostAdDetail());
        advertEditorImpl.getAdvertDetail().setPostcodeLocation(postAdLocationService.lookupPostcode("TW9 1EH"));

        assertThat(advertEditorImpl.supportsChangeVisibleOnMap(), equalTo(true));
    }

    @Test
    public void verbIsCorrectForCreateMode() {
        advertEditorImpl = getAdvertEditor(new PostAdDetail());
        assertThat(advertEditorImpl.getDisplayActionVerb(), equalTo("Post"));
    }

    @Test
    public void verbIsCorrectForEditMode() {
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setAdvertId(10L);
        advertEditorImpl = getAdvertEditor(postAdDetail);
        assertThat(advertEditorImpl.getDisplayActionVerb(), equalTo("Update"));
    }

    @Test
    public void supportsChangeCategoryWhenInPostNewAdvertMode() {
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setAdvertId(null);
        advertEditorImpl = getAdvertEditor(postAdDetail);
        assertThat(advertEditorImpl.supportsChangeCategory(), equalTo(true));
    }

    @Test
    public void supportsChangeLocationWhenInPostNewAdvertMode() {
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setAdvertId(null);
        advertEditorImpl = getAdvertEditor(postAdDetail);
        assertThat(advertEditorImpl.supportsChangeLocation(), equalTo(true));
    }

    @Test
    public void doesNotSupportChangeCategoryWhenInEditAdvertMode() {
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setAdvertId(10L);
        advertEditorImpl = getAdvertEditor(postAdDetail);
        assertThat(advertEditorImpl.supportsChangeCategory(), equalTo(false));
    }

    @Test
    public void doesNotSupportChangeLocationWhenInEditAdvertMode() {
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setAdvertId(10L);
        advertEditorImpl = getAdvertEditor(postAdDetail);
        assertThat(advertEditorImpl.supportsChangeLocation(), equalTo(false));
    }

    @Test
    public void toApiBeanPassesPostAdBeanBuilderToLocationSelectionModel() {
        advertEditorImpl.getAdvertDetail().setPostcodeLocation(postAdLocationService.lookupPostcode("TW9 1EH"));
        advertEditorImpl.getAdvertDetail().setCategoryId(10L);

        PostAdvertBean bean = advertEditorImpl.toApiBean();

        assertThat(bean.getPostcode(), equalTo("TW9 1EH"));
    }

    @Test
    public void toApiBeanClearsWebsiteUrlIfNoWebsiteUrlFeature() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setFeatures(new HashMap<>());
        formBean.setWebsiteUrl("http://www.google.com");
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().getWebsiteUrl(), nullValue());
    }

    @Test
    public void toApiBeanDoesNotWebsiteUrlIfWebsiteUrlFeaturePreviouslyPurchased() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setFeatures(new HashMap<>());
        formBean.setWebsiteUrl("http://www.google.com");
        advertEditorImpl.getAdvertDetail().addExistingFeature(ProductType.WEBSITE_URL, DateTime.now().plusDays(30));
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().getWebsiteUrl(), equalTo("http://www.google.com"));
    }

    @Test
    public void toApiBeanClearsWebsiteUrlIfDisabled() {
        FeatureBean websiteFeature = new FeatureBean();
        websiteFeature.setSelected(null);
        websiteFeature.setProductName("WEBSITE_URL");

        Map<ProductType, FeatureBean> features = Maps.newHashMap();
        features.put(ProductType.WEBSITE_URL, websiteFeature);

        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setFeatures(features);
        formBean.setWebsiteUrl("http://www.google.com");
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().getWebsiteUrl(), nullValue());
    }

    @Test
    public void toApiBeanClearsWebsiteUrlIfNotSelected() {
        FeatureBean websiteFeature = new FeatureBean();
        websiteFeature.setSelected(false);
        websiteFeature.setProductName("WEBSITE_URL");

        Map<ProductType, FeatureBean> features = Maps.newHashMap();
        features.put(ProductType.WEBSITE_URL, websiteFeature);

        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setFeatures(features);
        formBean.setWebsiteUrl("http://www.google.com");
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().getWebsiteUrl(), nullValue());
    }

    @Test
    public void toApiBeanWebsiteUrlNotClearedIfFeatureSelected() {
        FeatureBean websiteFeature = new FeatureBean();
        websiteFeature.setSelected(true);
        websiteFeature.setProductName("WEBSITE_URL");

        Map<ProductType, FeatureBean> features = Maps.newHashMap();
        features.put(ProductType.WEBSITE_URL, websiteFeature);

        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setFeatures(features);
        formBean.setWebsiteUrl("http://www.google.com");
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().getWebsiteUrl(), equalTo("http://www.google.com"));
    }

    @Test
    public void toApiBeanClearsContactUrlIfContactUrlNotSupported() {
        // given
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(10L);
        formBean.setContactUrl("http://www.contact.com");
        formBean.setUseUrl(true);
        advertEditorImpl.setPostAdFormBean(formBean);

        // when & then
        assertThat(advertEditorImpl.supportsContactUrl(), is(false)); // just to make sure
        assertThat(advertEditorImpl.toApiBean().getContactUrl(), nullValue());
    }

    @Test
    public void toApiBeanPassesContactUrlIfContactUrlSupported() {
        // given
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(10L);
        formBean.setContactUrl("http://www.contact.com");
        formBean.setUseUrl(true);
        advertEditorImpl.setPostAdFormBean(formBean);

        // and
        User user = Fixtures.user().build();
        user.setType(UserType.PRO);
        when(authenticatedUserSession.getUser()).thenReturn(user);
        when(categoryModel.getL1CategoryFor(10L)).thenReturn(Optional.of(Fixtures.JOBS_REAL_CATEGORY));

        // when & then
        assertThat(advertEditorImpl.supportsContactUrl(), is(true)); // just to make sure
        assertThat(advertEditorImpl.toApiBean().getContactUrl(), equalTo("http://www.contact.com"));
    }

    @Test
    public void toApiBeanConvertsLocalArea() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setArea("My local area");
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().getArea(), equalTo("My local area"));
    }

    @Test
    public void toApiBeanSetPlatform() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().getPlatform(), equalTo("seller"));
    }

    @Test
    public void toApiBeanSetExtendFieldsByAdStatus() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        advertEditorImpl.getAdvertDetail().setStatus(AdStatus.AWAITING_PHONE_VERIFIED);
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().getExtendFields().get("AdvertStatus"), equalTo(AdStatus.AWAITING_PHONE_VERIFIED.getName()));
    }
    public void toApiBeanConvertsVisibleOnMapWhenNull() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setVisibleOnMap(null);
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void toApiBeanConvertsVisibleOnMapWhenTrueAndPostCodeNotEmpty() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setPostcode("TW9 1EH");
        formBean.setVisibleOnMap(true);
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().isVisibleOnMap(), equalTo(true));
    }

    @Test
    public void toApiBeanConvertsVisibleOnMapWhenTrueAndPostCodeNull() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setPostcode(null);
        formBean.setVisibleOnMap(true);
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void toApiBeanConvertsVisibleOnMapWhenTrueAndPostCodeEmpty() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setPostcode("");
        formBean.setCategoryId(4615L);
        formBean.setVisibleOnMap(true);
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void toApiBeanConvertsVisibleOnMapWhenFalse() {
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(4615L);
        formBean.setVisibleOnMap(false);
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.toApiBean().isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void toApiBeanAddsZeroPriceAttributeWhenCategoryIsFreebiesAndNoPriceInForm() {
        // given
        PostAdFormBean formBean = getPostAdFormBeanWithPriceSupport(FREEBIES_CATEGORY_ID, true);

        // when
        advertEditorImpl.setPostAdFormBean(formBean);

        // then
        assertThat(advertEditorImpl.toApiBean().getAttributes(),
                equalTo(Collections.singletonList(new ApiAttribute(CategoryConstants.Attribute.PRICE.getName(), "0"))));
    }

    @Test
    public void toApiBeanAddsZeroPriceAttributeWhenCategoryIsFreebiesAndWrongPriceExistsInForm() {
        // given
        PostAdFormBean formBean = getPostAdFormBeanWithPriceSupport(FREEBIES_CATEGORY_ID, true);
        formBean.getAttributes().put(CategoryConstants.Attribute.PRICE.getName(), "2");

        // when
        advertEditorImpl.setPostAdFormBean(formBean);

        // then
        assertThat(advertEditorImpl.toApiBean().getAttributes(),
                equalTo(Collections.singletonList(new ApiAttribute(CategoryConstants.Attribute.PRICE.getName(), "0"))));
    }

    @Test
    public void toApiBeanDoesntAddZeroPriceAttributeWhenCategoryIsNotFreebiesAndNoPriceInForm() {
        // given
        PostAdFormBean formBean = getPostAdFormBeanWithPriceSupport(SOFA_CATEGORY_ID, true);

        // when
        advertEditorImpl.setPostAdFormBean(formBean);

        // then
        assertThat(advertEditorImpl.toApiBean().getAttributes(), equalTo(Collections.emptyList()));
    }

    @Test
    public void toApiBeanDoesntAddZeroPriceAttributeWhenCategoryIsFreebiesAndPriceAttributeIsNotSupported() {
        // given
        PostAdFormBean formBean = getPostAdFormBeanWithPriceSupport(FREEBIES_CATEGORY_ID, false);

        // when
        advertEditorImpl.setPostAdFormBean(formBean);

        // then
        assertThat(advertEditorImpl.toApiBean().getAttributes(), equalTo(Collections.emptyList()));
    }

    private PostAdFormBean getPostAdFormBeanWithPriceSupport(Long categoryId, boolean priceSupport) {
        Category freebiesCategory = mock(Category.class);
        Optional<Category> freebiesCategoryOptional = Optional.of(freebiesCategory);
        when(categoryModel.getCategory(categoryId)).thenReturn(freebiesCategoryOptional);
        when(freebiesCategory.getId()).thenReturn(categoryId);
        when(freebiesCategory.findAttribute(CategoryConstants.Attribute.PRICE.getName())).thenReturn(
                Optional.of(AttributeMetadata.builder().build()));
        when(categoryService.exists(categoryId)).thenReturn(true);
        when(categoryService.getById(categoryId)).thenReturn(freebiesCategoryOptional);
        when(categoryModel.isSupportedAttribute(CategoryConstants.Attribute.PRICE.getName(), categoryId))
                .thenReturn(priceSupport);
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(categoryId);
        return formBean;
    }

    @Test
    public void toApiBeanIncludesVehicleHistoryCheckAttributesCategoryHasVrnAttribute() {

        Long carsCategoryId = CategoryConstants.CARS_ID;
        String vrm = "AB12CDE";
        String vrmAttributeName = CategoryConstants.Attribute.VRN.getName();
        String vhcAttributeName = CategoryConstants.Attribute.VEHICLE_VHC_CHECKED.getName();

        Map<String, AttributeMetadata> attributeMetadataMap = Fixtures.carsAttributeMetaData();
        when(categoryModel.findAttributesByNameForGivenCategory(vrmAttributeName, carsCategoryId))
                .thenReturn(Optional.of(attributeMetadataMap.get(vrmAttributeName)));

        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(carsCategoryId);
        formBean.addAttribute(vrmAttributeName, vrm);
        advertEditorImpl.setPostAdFormBean(formBean);

        Map<String, String> vehicleHistoryCheckResult = Stream.of(
                        new AbstractMap.SimpleEntry<>(vrmAttributeName, vrm),
                        new AbstractMap.SimpleEntry<>(vhcAttributeName, "true"))
                .collect(Collectors.toMap((se) -> se.getKey(), (se) -> se.getValue()));

        when(motorsApiClient.standardiseVehicleData(formBean.getCategoryId(), formBean.getAttributes())).thenReturn(vehicleHistoryCheckResult);
        List<String> attributeNames = advertEditorImpl.toApiBean().getAttributes().stream()
                .map(apiAttribute -> apiAttribute.getName()).collect(Collectors.toList());
        List<String> vhcAttributeNames = Stream.of(vrmAttributeName, vhcAttributeName).collect(Collectors.toList());
        assertThat(attributeNames, equalTo(vhcAttributeNames));
    }

    @Test
    public void toApiBeanUseOriginalAttributesWhenCategoryDoesntHaveVrnAttribute() {

        Long carsCategoryId = CategoryConstants.CARS_ID;
        String model = "model";
        String vehicleModelAttribName = CategoryConstants.Attribute.VEHICLE_MODEL.getName();
        String vrnAttributeName = CategoryConstants.Attribute.VRN.getName();

        Map<String, AttributeMetadata> attributeMetadataMap = Fixtures.carsAttributeMetaData();
        when(categoryModel.findAttributesByNameForGivenCategory(vehicleModelAttribName, carsCategoryId))
                .thenReturn(Optional.of(attributeMetadataMap.get(vehicleModelAttribName)));
        when(categoryModel.findAttributesByNameForGivenCategory(vrnAttributeName, carsCategoryId))
                .thenReturn(Optional.absent());

        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(carsCategoryId);
        formBean.addAttribute(vehicleModelAttribName, model);
        advertEditorImpl.setPostAdFormBean(formBean);

        List<String> attributeNames = advertEditorImpl.toApiBean().getAttributes().stream()
                .map(apiAttribute -> apiAttribute.getName()).collect(Collectors.toList());
        List<String> standardisedAttributeNames = Stream.of(vehicleModelAttribName).collect(Collectors.toList());
        assertThat(attributeNames, equalTo(standardisedAttributeNames));
        verifyZeroInteractions(motorsApiClient);
    }

    @Test
    public void toApiBeanRemovesUnselectedAdditionalFeaturesBeforeIncludingVehicleHistoryCheckAttributes() {
        // given
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(CategoryConstants.CARS_ID);
        formBean.addAttribute(CategoryConstants.Attribute.VRN.getName(), "AB12CDE");
        formBean.addAttribute(CategoryConstants.Attribute.TRACTION_CONTROL.getName(), "Y"); // boolean 'yes' selected
        formBean.addAttribute(CategoryConstants.Attribute.TRIP_COMPUTER.getName(), null); // boolean not selected

        // and
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(formBean);
        advertEditorImpl = getAdvertEditorWithStubbedCategoryModel(postAdDetail);

        // and
        when(motorsApiClient.standardiseVehicleData(formBean.getCategoryId(), formBean.getAttributes())).thenReturn(new HashMap<>());

        // when
        advertEditorImpl.toApiBean();

        // then
        verify(motorsApiClient).standardiseVehicleData(formBean.getCategoryId(), ImmutableMap.of(
                CategoryConstants.Attribute.VRN.getName(), "AB12CDE",
                CategoryConstants.Attribute.TRACTION_CONTROL.getName(), "Y"
        ));
    }

    @Test
    public void toApiBeanRemovesNonPopulatedNonBooleanAttributesBeforeIncludingVehicleHistoryCheckAttributes() {
        // given
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(CategoryConstants.CARS_ID);
        formBean.addAttribute(CategoryConstants.Attribute.VRN.getName(), "AB12CDE");
        formBean.addAttribute(CategoryConstants.Attribute.TRACTION_CONTROL.getName(), "Y"); // boolean 'yes' selected
        formBean.addAttribute(CategoryConstants.Attribute.VEHICLE_AGE.getName(), null); // non boolean

        // and
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(formBean);
        advertEditorImpl = getAdvertEditorWithStubbedCategoryModel(postAdDetail);

        // and
        when(motorsApiClient.standardiseVehicleData(formBean.getCategoryId(), formBean.getAttributes())).thenReturn(new HashMap<>());

        // when
        advertEditorImpl.toApiBean();

        // then
        verify(motorsApiClient).standardiseVehicleData(formBean.getCategoryId(), ImmutableMap.of(
                CategoryConstants.Attribute.VRN.getName(), "AB12CDE",
                CategoryConstants.Attribute.TRACTION_CONTROL.getName(), "Y"
        ));
    }

    @Test
    public void postAdFormBeanIsInitialisedCorrectlyForCurrentUser() {
        User user = authenticatedUserSession.getUser();
        when(contactEmailService.getPreferred(user)).thenReturn("<EMAIL>");
        advertEditorImpl.initNew(null);
        PostAdFormBean formBean = advertEditorImpl.getPostAdFormBean();
        assertThat(formBean.getContactEmail(), equalTo("<EMAIL>"));
        assertThat(formBean.getContactTelephone(), equalTo("0800 50 50 50"));
        assertThat(formBean.getPostcode(), equalTo("TW9 1EH"));
        assertThat(formBean.getLocationId(), equalTo(5L));
    }

    @Test
    public void postAdFormBeanIsNotReinitialisedIfAlreadyPresent() {
        when(categoryService.getById(10L)).thenReturn(createCategory(10L));
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setCategoryId(10L);
        advertEditorImpl.setPostAdFormBean(formBean);
        assertThat(advertEditorImpl.getPostAdFormBean(), sameInstance(formBean));
        verifyZeroInteractions(bushfireApi);
        verify(authenticatedUserSession, never()).getUsername();
    }

    @Test
    public void attributesAreAddedToMapOnFormBeanInitialisationForChosenCategory() {
        // given
        when(categoryService.exists(1L)).thenReturn(true);
        advertEditorImpl.getPostAdFormBean().setCategoryId(1L);

        PostAdFormBean formBean = advertEditorImpl.getPostAdFormBean();

        // when
        advertEditorImpl.setPostAdFormBean(formBean);

        // then
        Map<String, String> attributes = formBean.getAttributes();

        assertThat(attributes.size(), equalTo(4));
        assertThat(attributes.containsKey("attribute1"), equalTo(true));
        assertThat(attributes.containsKey("attribute2"), equalTo(true));
        assertThat(attributes.containsKey("attribute3"), equalTo(true));
        assertThat(attributes.containsKey("attribute4"), equalTo(true));
    }

    @Test
    public void onCategoryChangeNoLongerRelevantAttributesAreRemovedFromAttributeMap() {
        when(categoryService.exists(2L)).thenReturn(true);
        when(categoryService.getById(2L)).thenReturn(Optional.<Category>absent());
        when(categoryService.exists(3L)).thenReturn(true);
        PostAdFormBean formBean = advertEditorImpl.getPostAdFormBean();
        formBean.setCategoryId(3L);
        advertEditorImpl.setPostAdFormBean(formBean);
        formBean.setCategoryId(2L);
        advertEditorImpl.setPostAdFormBean(formBean);
        Map<String, String> attributes = advertEditorImpl.getPostAdFormBean().getAttributes();
        assertThat(attributes.size(), equalTo(4));
        assertThat(attributes.containsKey("attribute1"), equalTo(true));
        assertThat(attributes.containsKey("attribute2"), equalTo(true));
        assertThat(attributes.containsKey("attribute5"), equalTo(true));
        assertThat(attributes.containsKey("attribute6"), equalTo(true));
    }

    @Test
    public void onCategoryChangeStillRelevantAttributeValuesArePreservedInAttributeMap() {
        when(categoryService.getById(2L)).thenReturn(createCategory(2L));
        when(categoryService.getById(3L)).thenReturn(createCategory(3L));
        PostAdFormBean formBean = advertEditorImpl.getPostAdFormBean();
        formBean.setCategoryId(3L);
        advertEditorImpl.setPostAdFormBean(formBean);
        formBean.getAttributes().put("attribute1", "a value for 1");
        formBean.getAttributes().put("attribute2", "a value for 2");
        formBean.setCategoryId(2L);
        advertEditorImpl.setPostAdFormBean(formBean);
        Map<String, String> attributes = advertEditorImpl.getPostAdFormBean().getAttributes();
        assertThat(attributes.get("attribute1"), equalTo("a value for 1"));
        assertThat(attributes.get("attribute2"), equalTo("a value for 2"));
    }

    private Optional<Category> createCategory(Long id) {
        return Optional.of(new Category(id, "seoName", "name"));
    }

    @Test
    public void loadsAdvertIdFromApiAd() {

        Ad ad = createDefaultApiAd();
        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());
        assertThat(advertEditorImpl.getAdvertDetail().getAdvertId(), equalTo(100L));
    }

    @Test
    public void requiresBumpUpEqualsTrueWhenLoadedAdIsExpired() {

        Ad ad = createDefaultApiAd();
        ad.setStatus(AdStatus.EXPIRED);
        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());
        assertThat(advertEditorImpl.requiresBumpUp(), equalTo(true));
    }

    @Test
    public void requiresBumpUpEqualsFalseWhenLoadedAdIsNotExpired() {

        for (AdStatus status : AdStatus.values()) {
            ReflectionTestUtils.setField(advertEditorImpl.getAdvertDetail(), "advertId", null);
            if (status != AdStatus.EXPIRED) {
                Ad ad = createDefaultApiAd();
                ad.setStatus(status);

                when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
                advertEditorImpl.loadAdvert(ad.getId());
                assertThat(advertEditorImpl.requiresBumpUp(), equalTo(false));
            }
        }
    }

    @Test
    public void supportsBumpUpIsFalseInCreateMode() {
        ReflectionTestUtils.setField(advertEditorImpl.getAdvertDetail(), "advertId", null);
        assertThat(advertEditorImpl.supportsBumpUp(), equalTo(false));
    }

    @Test
    public void supportsBumpUpIsFalseInEditModeWhenAdvertNeverPublished() {
        ReflectionTestUtils.setField(advertEditorImpl.getAdvertDetail(), "advertId", null);
        Ad ad = createDefaultApiAd();
        ad.setStatus(AdStatus.AWAITING_CS_REVIEW);
        ad.setLiveDate(null);
        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());
        assertThat(advertEditorImpl.supportsBumpUp(), equalTo(false));
    }

    @Test
    public void supportsBumpUpIsFalseInEditModeWhenAdvertIsDraft() {
        ReflectionTestUtils.setField(advertEditorImpl.getAdvertDetail(), "advertId", null);
        Ad ad = createDefaultApiAd();
        ad.setStatus(AdStatus.DRAFT);
        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());
        assertThat(advertEditorImpl.supportsBumpUp(), equalTo(false));
    }

    @Test
    public void supportsBumpUpIsTrueInEditModeWhenAdvertIsNotDraft() {
        Category category = mock(Category.class);
        when(category.getParentId()).thenReturn(42L);
        when(categoryService.getById(10L)).thenReturn(Optional.of(category));
        when(insertionPrice.isFree()).thenReturn(true);
        for (AdStatus status : AdStatus.values()) {
            advertEditorImpl.getAdvertDetail().setAdvertId(null);
            if (status != AdStatus.DRAFT) {
                Ad ad = createDefaultApiAd();
                ad.setStatus(status);
                when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
                advertEditorImpl.loadAdvert(ad.getId());
                assertThat(advertEditorImpl.supportsBumpUp(), equalTo(true));
            }
        }
    }

    @Test
    public void supportsBumpUpInEditModeForExpiredAdvertInPaidCategory() {
        Category category = mock(Category.class);
        when(category.getParentId()).thenReturn(42L);
        when(categoryService.getById(10L)).thenReturn(Optional.of(category));
        when(insertionPrice.isFree()).thenReturn(false);
        for (AdStatus status : AdStatus.values()) {
            advertEditorImpl.getAdvertDetail().setAdvertId(null);
            if (status == AdStatus.EXPIRED) {
                Ad ad = createDefaultApiAd();
                ad.setStatus(status);
                when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
                advertEditorImpl.loadAdvert(ad.getId());
                assertThat(advertEditorImpl.supportsBumpUp(), equalTo(true));
            }
        }
    }

    @Test
    public void loadsCategoryIdFromApiAd() {

        Ad ad = createDefaultApiAd();
        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        when(categoryService.getById(ad.getCategoryId())).thenReturn(createCategory(ad.getCategoryId()));
        advertEditorImpl.loadAdvert(ad.getId());
        assertThat(advertEditorImpl.getCategoryId(), equalTo(10L));
    }

    @Test
    public void loadsTitleFromApiAd() {


        Ad ad = createDefaultApiAd();
        ad.setTitle("My Title");

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.getTitle(), equalTo("My Title"));
    }

    @Test
    public void loadsDescriptionFromApiAd() {


        Ad ad = createDefaultApiAd();
        ad.setDescription("My Description");

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.getDescription(), equalTo("My Description"));
    }

    @Test
    public void loadsYoutubeLinkFromApiAd() {

        Ad ad = createDefaultApiAd();
        ad.setYoutubeLink("http://www.youtube.com/watch?v=dLpqIw0Wjoo");

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.getYoutubeLink(), equalTo("http://www.youtube.com/watch?v=dLpqIw0Wjoo"));
    }

    @Test
    public void loadsContactEmailFromApiAd() {

        Ad ad = createDefaultApiAd();
        ad.setRepliesEmail("<EMAIL>");

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.getContactEmail(), equalTo("<EMAIL>"));
    }

    @Test
    public void loadsContactTelephoneFromApiAd() {

        Ad ad = createDefaultApiAd();
        ad.setPhoneNumber("01632960012");

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.getContactTelephone(), equalTo("01632960012"));
    }

    @Test
    public void loadsContactNameFromApiAd() {

        Ad ad = createDefaultApiAd();
        ad.setContactName("Cuthbert");

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.getContactName(), equalTo("Cuthbert"));
    }

    @Test
    public void loadsContactUrlFromApiAd() {

        Ad ad = createDefaultApiAd();
        ad.setReplyLink("http://www.hello.com/reply");

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.getContactUrl(), equalTo("http://www.hello.com/reply"));
    }

    @Test
    public void useEmailIsTrueWhenContactEmailNotNull() {

        Ad ad = createDefaultApiAd();
        ad.setRepliesEmail("<EMAIL>");

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.isUseEmail(), equalTo(true));
    }

    @Test
    public void useEmailIsFalseWhenContactEmailIsNull() {

        Ad ad = createDefaultApiAd();
        ad.setRepliesEmail(null);

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.isUseEmail(), equalTo(false));
    }

    @Test
    public void usePhoneIsTrueWhenContactTelephoneIsNotNull() {

        Ad ad = createDefaultApiAd();
        ad.setPhoneNumber("01632960012");

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.isUsePhone(), equalTo(true));
    }

    @Test
    public void usePhoneIsFalseWhenContactTelephoneIsNull() {

        Ad ad = createDefaultApiAd();
        ad.setPhoneNumber(null);

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.isUsePhone(), equalTo(false));
    }

    @Test
    public void attributesAreEmptyWhenApiAdAttributesAreNull() {

        Ad ad = createDefaultApiAd();
        ad.setAttributes(null);

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.getAttributes().size(), equalTo(0));
    }

    @Test
    public void attributesAreEmptyWhenApiAdAttributesAreEmpty() {

        Ad ad = createDefaultApiAd();
        ad.setAttributes(new Attribute[0]);
        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        assertThat(postAdFormBean.getAttributes().size(), equalTo(0));
    }

    @Test
    public void submittedPhoneAttributesValidation() {

        Ad ad = createDefaultApiAd();

        Attribute att1 = new Attribute();
        att1.setName("mobile_condition");
        att1.setValue("new");

        Attribute att2 = new Attribute();
        att2.setName("mobile_colour");
        att2.setValue("blue");

        Attribute att3 = new Attribute();
        att3.setName("mobile_storage_capacity");
        att3.setValue("8_gb");

        ad.setAttributes(new Attribute[]{att1, att2, att3});

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();
        Map<String, String> attributes = postAdFormBean.getAttributes();
        Long categoryId = 10206L;
        postAdFormBean.setCategoryId(categoryId);

        when(categoryModel.isChildOrEqual(4660L, categoryId)).thenReturn(true);
        AttributeMetadata attributeMetadata = new AttributeMetadata();
        attributeMetadata.setValues(Arrays.asList(
                new AttributeValueMetadata("new", "New"),
                new AttributeValueMetadata("8_gb", "8 GB"),
                new AttributeValueMetadata("blue", "Blue")
        ));
        when(categoryModel.findAttributesByNameForGivenCategory("mobile_condition", categoryId))
                .thenReturn(Optional.of(attributeMetadata));
        when(categoryModel.findAttributesByNameForGivenCategory("mobile_colour", categoryId))
                .thenReturn(Optional.of(attributeMetadata));
        when(categoryModel.findAttributesByNameForGivenCategory("mobile_storage_capacity", categoryId))
                .thenReturn(Optional.of(attributeMetadata));

        boolean result = phoneAttributesValidationService.isValidPhoneValuePassed(categoryId, attributes);
        assertTrue(result);

        attributes.put("mobile_condition",null);
        boolean result2 = phoneAttributesValidationService.isValidPhoneValuePassed(categoryId, attributes);
        assertFalse(result2);

        attributes.put("mobile_condition", "invalidValue");
        boolean result3 = phoneAttributesValidationService.isValidPhoneValuePassed(categoryId, attributes);
        assertFalse(result3);
    }

    /**
     * Validates the submitted mobile model attributes for an advertisement.
     * This test method sets up a mock advertisement with specific mobile model attributes,
     * configures the necessary mocks for the advert API, category model, and loads the advertisement
     * into the advert editor. It then tests the phone attributes validation service with different
     * scenarios of mobile model attribute values.
     *
     * Scenarios tested:
     * 1. Valid mobile model values for both Apple and Samsung.
     * 2. Using the "other" value for the Apple mobile model.
     * 3. Setting the Apple mobile model to null.
     * 4. Setting an invalid value for the Samsung mobile model.
     */
    @Test
    public void submittedMobileModelValidationForRightValue(){
        Map<String, String> newAttributes = new HashMap<>();
        // Set the Apple mobile model attribute to right value
        newAttributes.put("mobile_model_apple", "iphone_4");
        // Assert that the validation result is true
        assertTrue(submittedMobileModelValidation(newAttributes));
    }

    @Test
    public void submittedMobileModelValidationForOtherValue(){
        Map<String, String> newAttributes = new HashMap<>();
        // Set the Apple mobile model attribute to "other"
        newAttributes.put("mobile_model_apple", "other");
        // Test the phone attributes validation service with a other value
        // Assert that the validation result is true
        assertTrue(submittedMobileModelValidation(newAttributes));
    }

    @Test
    public void submittedMobileModelValidationForNullValue(){
        Map<String, String> newAttributes = new HashMap<>();
        // Set the Apple mobile model attribute to null
        newAttributes.put("mobile_model_apple", null);
        // Test the phone attributes validation service with a null value
        // Assert that the validation result is false
        assertFalse(submittedMobileModelValidation(newAttributes));
    }

    @Test
    public void submittedMobileModelValidationForInvalidValue(){
        Map<String, String> newAttributes = new HashMap<>();
        // Set an invalid value for the Samsung mobile model attribute
        newAttributes.put("mobile_model_samsung", "invalidValue");
        // Test the phone attributes validation service with an invalid value
        // Assert that the validation result is false
        assertFalse(submittedMobileModelValidation(newAttributes));
    }

    public boolean submittedMobileModelValidation(Map<String, String> newAttributes) {
        // Create a default advertisement object
        Ad ad = createDefaultApiAd();

        // Create an attribute for Apple mobile model and set its name and value
        Attribute att1 = new Attribute();
        att1.setName("mobile_model_apple");
        att1.setValue("iphone_4");

        // Create an attribute for Samsung mobile model and set its name and value
        Attribute att2 = new Attribute();
        att2.setName("mobile_model_samsung");
        att2.setValue("galaxy_ace");

        // Set the created attributes to the advertisement
        ad.setAttributes(new Attribute[]{att1, att2});

        // Mock the behavior of the advert API to return the created advertisement when its ID is requested
        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        // Load the advertisement into the advert editor using its ID
        advertEditorImpl.loadAdvert(ad.getId());

        // Get the post - ad form bean from the advert editor
        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();
        // Get the attributes map from the post - ad form bean
        Map<String, String> attributes = postAdFormBean.getAttributes();
        // Create an attribute metadata object
        AttributeMetadata attributeMetadata = new AttributeMetadata();
        // Set the possible values for the attribute metadata
        attributeMetadata.setValues(Arrays.asList(
                new AttributeValueMetadata("iphone_4", "iPhone 4"),
                new AttributeValueMetadata("galaxy_ace", "Galaxy Ace"),
                new AttributeValueMetadata("other", "Other")
        ));
        // Set the category ID for the post - ad form bean
        Long categoryId = 10205L;
        postAdFormBean.setCategoryId(categoryId);
        // Mock the behavior of the category model to return true when checking if a category is a child or equal
        when(categoryModel.isChildOrEqual(4660L, categoryId)).thenReturn(true);

        // Mock the behavior of the category model to return the attribute metadata when finding attributes by name for the given category
        when(categoryModel.findAttributesByNameForGivenCategory("mobile_model_apple", categoryId))
                .thenReturn(Optional.of(attributeMetadata));
        when(categoryModel.findAttributesByNameForGivenCategory("mobile_model_samsung", categoryId))
                .thenReturn(Optional.of(attributeMetadata));

        if ((newAttributes != null && !newAttributes.isEmpty())){
            attributes.putAll(newAttributes);
        }
        // Test the phone attributes validation service with valid values
        return phoneAttributesValidationService.isValidPhoneValuePassed(categoryId, attributes);
    }

    @Test
    public void attributesArePopulatedWhenApiAdAttributesDefined() {

        Ad ad = createDefaultApiAd();

        Attribute att1 = new Attribute();
        att1.setName("att1");
        att1.setValue("value1");

        Attribute att2 = new Attribute();
        att2.setName("att2");
        att2.setValue("value2");

        ad.setAttributes(new Attribute[]{att1, att2});

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        Map<String, String> attributes = postAdFormBean.getAttributes();

        assertThat(attributes.size(), equalTo(2));
        assertThat(attributes.get("att1"), equalTo("value1"));
        assertThat(attributes.get("att2"), equalTo("value2"));
    }

    @Test
    public void dateAttributeIsConvertedToPresentationDateFormatFromApiFormatOnLoad() {
        Ad ad = createDefaultApiAd();
        ad.setAttributes(new Attribute[]{new Attribute(CategoryConstants.Attribute.EVENT_DATE.getName(), "20121015")});

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        when(categoryModel.getAttributeType(CategoryConstants.Attribute.EVENT_DATE.getName()))
                .thenReturn(Optional.of(AttributeType.DATETIME));

        // when
        advertEditorImpl.loadAdvert(ad.getId());

        // then
        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        Map<String, String> attributes = postAdFormBean.getAttributes();

        assertThat(attributes.size(), equalTo(1));
        assertThat(attributes.get(CategoryConstants.Attribute.EVENT_DATE.getName()), equalTo("15/10/2012"));
    }

    @Test
    public void dateAttributeIsEmptyIfCannotConvertToPresentationDateFormat() {
        Ad ad = createDefaultApiAd();

        Attribute att1 = new Attribute();
        att1.setName(CategoryConstants.Attribute.EVENT_DATE.getName());
        att1.setValue("invalid date");

        ad.setAttributes(new Attribute[]{att1});

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        when(categoryModel.getAttributeType(CategoryConstants.Attribute.EVENT_DATE.getName()))
                .thenReturn(Optional.of(AttributeType.DATETIME));

        // then
        advertEditorImpl.loadAdvert(ad.getId());

        // when
        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        Map<String, String> attributes = postAdFormBean.getAttributes();

        assertThat(attributes.size(), equalTo(1));
        assertThat(attributes.get(CategoryConstants.Attribute.EVENT_DATE.getName()), equalTo(""));
    }

    @Test
    public void dateAttributeIsEmptyIfNull() {
        Ad ad = createDefaultApiAd();
        ad.setAttributes(new Attribute[]{new Attribute(CategoryConstants.Attribute.EVENT_DATE.getName(), null)});

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        when(categoryModel.getAttributeType(CategoryConstants.Attribute.EVENT_DATE.getName()))
                .thenReturn(Optional.of(AttributeType.DATETIME));

        // when
        advertEditorImpl.loadAdvert(ad.getId());

        // then
        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        Map<String, String> attributes = postAdFormBean.getAttributes();
        assertThat(attributes.size(), equalTo(1));
        assertThat(attributes.get(CategoryConstants.Attribute.EVENT_DATE.getName()), equalTo(""));
    }

    @Test
    public void priceAttributeIsEmptyIfNull() {
        Attribute att1 = new Attribute();
        att1.setName(CategoryConstants.Attribute.PRICE.getName());
        att1.setValue(null);

        Ad ad = createDefaultApiAd();
        ad.setAttributes(new Attribute[]{att1});

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        when(categoryModel.getAttributeType(CategoryConstants.Attribute.PRICE.getName()))
                .thenReturn(Optional.of(AttributeType.CURRENCY));

        // when
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        Map<String, String> attributes = postAdFormBean.getAttributes();

        assertThat(attributes.size(), equalTo(1));
        assertThat(attributes.get(CategoryConstants.Attribute.PRICE.getName()), equalTo(""));
    }

    @Test
    public void priceAttributeIsConvertedToPoundsCorrectly() {

        Attribute att1 = new Attribute();
        att1.setName(CategoryConstants.Attribute.PRICE.getName());
        att1.setValue("1099");

        Ad ad = createDefaultApiAd();
        ad.setAttributes(new Attribute[]{att1});

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        when(categoryModel.getAttributeType(CategoryConstants.Attribute.PRICE.getName()))
                .thenReturn(Optional.of(AttributeType.CURRENCY));

        // when
        advertEditorImpl.loadAdvert(ad.getId());

        // then
        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        Map<String, String> attributes = postAdFormBean.getAttributes();

        assertThat(attributes.size(), equalTo(1));
        assertThat(attributes.get(CategoryConstants.Attribute.PRICE.getName()), equalTo("10.99"));
    }

    @Test
    public void priceAttributeIsEmptyIfCannotConvertPriceToPounds() {
        Attribute att1 = new Attribute();
        att1.setName(CategoryConstants.Attribute.PRICE.getName());
        att1.setValue("invalid price");

        Ad ad = createDefaultApiAd();
        ad.setAttributes(new Attribute[]{att1});

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        when(categoryModel.getAttributeType(CategoryConstants.Attribute.PRICE.getName()))
                .thenReturn(Optional.of(AttributeType.CURRENCY));

        // when
        advertEditorImpl.loadAdvert(ad.getId());

        // then
        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        Map<String, String> attributes = postAdFormBean.getAttributes();

        assertThat(attributes.size(), equalTo(1));
        assertThat(attributes.get(CategoryConstants.Attribute.PRICE.getName()), equalTo(""));
    }

    @Test
    public void imagesEmptyWhenApiAdvertHasNullImages() {

        Ad ad = createDefaultApiAd();
        ad.setImages(null);

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        assertThat(advertEditorImpl.getImages().size(), equalTo(0));
    }

    @Test
    public void imagesEmptyWhenApiAdvertHasEmptyImages() {

        Ad ad = createDefaultApiAd();
        ad.setImages(new ArrayList<>());

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        assertThat(advertEditorImpl.getImages().size(), equalTo(0));
    }

    @Test
    public void imagesArePopulatedAndMainImageIdSetWhenApiAdHasImages() {

        Ad ad = createDefaultApiAd();
        ad.setImages(createTestImages());
        ad.setMainImageId(2L);

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();

        List<Image> images = advertEditorImpl.getImages();

        assertThat(images.size(), equalTo(3));
        assertThat(images.get(0).getId(), equalTo(1L));
        assertThat(images.get(0).getUrl(), equalTo("image1Size1Url"));
        assertThat(images.get(1).getId(), equalTo(2L));
        assertThat(images.get(1).getUrl(), equalTo("image2Size1Url"));
        assertThat(images.get(2).getId(), equalTo(3L));
        assertThat(images.get(2).getUrl(), equalTo("image3Size1Url"));
        assertThat(postAdFormBean.getMainImageId(), equalTo(2L));
    }

    @Test
    public void imagesArePopulatedWhenACloudFlareUrl() {
        List<Map<String, LegacyImage>> imageMap = createSingleTestImage();
        String mockUpdatedUrl = "http://the.updated.url";

        Ad ad = createDefaultApiAd();
        ad.setImages(imageMap);
        ad.setMainImageId(1L);

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);

        String legacyImageUrl = imageMap.get(0).get("image1Size1").getUrl();
        when(cdnImageUrlProvider.isCloudFlareUrl(legacyImageUrl)).thenReturn(true);
        when(cdnImageUrlProvider.buildCFImageUrl(legacyImageUrl, ImageSize.MAIN.getId())).thenReturn(mockUpdatedUrl);

        advertEditorImpl.loadAdvert(ad.getId());

        List<Image> images = advertEditorImpl.getImages();

        assertThat(images.size(), equalTo(1));
        assertThat(images.get(0).getUrl(), equalTo(mockUpdatedUrl));
    }

    @Test
    public void imagesArePopulatedWhenNotACloudFlareUrl() {

        List<Map<String, LegacyImage>> imageMap = createSingleTestImage();

        Ad ad = createDefaultApiAd();
        ad.setImages(imageMap);
        ad.setMainImageId(1L);

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);

        String legacyImageUrl = imageMap.get(0).get("image1Size1").getUrl();
        when(cdnImageUrlProvider.isCloudFlareUrl(legacyImageUrl)).thenReturn(false);
        advertEditorImpl.loadAdvert(ad.getId());

        List<Image> images = advertEditorImpl.getImages();

        assertThat(images.size(), equalTo(1));
        assertThat(images.get(0).getUrl(), equalTo(legacyImageUrl));

        verify(cdnImageUrlProvider, never()).buildCFImageUrl(legacyImageUrl, ImageSize.MAIN.getId());
    }

    @Test
    public void whenExistingAdHasNullFeaturesThenPriceContextMethodsBehaveCorrectly() {

        Ad ad = createDefaultApiAd();
        ad.setFeatures(null);

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        for (ProductType productType : ProductType.values()) {
            assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).isProductActive(productType), equalTo(false));
            assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).getProductExpiryDate(productType), equalTo(null));
        }
    }

    @Test
    public void whenExistingAdHasEmptyFeaturesThenPriceContextMethodsBehaveCorrectly() {

        Ad ad = createDefaultApiAd();
        ad.setFeatures(new ArrayList<>());

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        for (ProductType productType : ProductType.values()) {
            assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).isProductActive(productType), equalTo(false));
            assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).getProductExpiryDate(productType), equalTo(null));
        }
    }

    @Test
    public void whenExistingAdHasFeaturesThenPriceContextMethodsBehaveCorrectly() {

        AdFeature urgentFeature = createAdFeature(ProductName.URGENT, new DateTime(1010101010L));
        AdFeature featuredFeature = createAdFeature(ProductName.FEATURE_3_DAY, new DateTime(2020202020L));
        AdFeature spotlightFeature = createAdFeature(ProductName.HOMEPAGE_SPOTLIGHT, new DateTime(3030303030L));

        Ad ad = createDefaultApiAd();
        ad.setFeatures(Arrays.asList(urgentFeature, featuredFeature, spotlightFeature));

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        // Urgent
        final PricingContextImpl pricingContext = new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class));
        assertThat(pricingContext.isProductActive(ProductType.URGENT), equalTo(true));
        assertThat(pricingContext.getProductExpiryDate(ProductType.URGENT), equalTo(new DateTime(1010101010L)));

        // Featured
        assertThat(pricingContext.isProductActive(ProductType.FEATURED), equalTo(true));
        assertThat(pricingContext.getProductExpiryDate(ProductType.FEATURED), equalTo(new DateTime(2020202020L)));

        // Spotlight
        assertThat(pricingContext.isProductActive(ProductType.SPOTLIGHT), equalTo(true));
        assertThat(pricingContext.getProductExpiryDate(ProductType.SPOTLIGHT), equalTo(new DateTime(3030303030L)));
    }

    @Test
    public void whenExistingAdHas7DayFeatureThenPriceContextMethodsBehaveCorrectly() {

        AdFeature featuredFeature = createAdFeature(ProductName.FEATURE_7_DAY, new DateTime(2020202020L));

        Ad ad = createDefaultApiAd();
        ad.setFeatures(Collections.singletonList(featuredFeature));

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        // Featured
        assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).isProductActive(ProductType.FEATURED), equalTo(true));
        assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).getProductExpiryDate(ProductType.FEATURED), equalTo(new DateTime
                (2020202020L)));
    }

    @Test
    public void whenExistingAdHas14DayFeatureThenPriceContextMethodsBehaveCorrectly() {

        AdFeature featuredFeature = createAdFeature(ProductName.FEATURE_14_DAY, new DateTime(2020202020L));

        Ad ad = createDefaultApiAd();
        ad.setFeatures(Collections.singletonList(featuredFeature));

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        // Featured
        assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).isProductActive(ProductType.FEATURED), equalTo(true));
        assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).getProductExpiryDate(ProductType.FEATURED), equalTo(new DateTime
                (2020202020L)));
    }

    @Test
    public void whenExistingAdHasNonActionedFeatureThenPriceContextMethodsBehaveCorrectly() {
        ProductNamesSearchResponse productNamesSearchResponse = new ProductNamesSearchResponse();
        productNamesSearchResponse.setProductNames(Collections.singletonList(ProductName.FEATURE_7_DAY));
        productNamesSearchResponse.setAdvertId(100L);


        when(orderApi.searchPaidProductNamesByAccountId(anyLong())).thenReturn(productNamesSearchResponse);

        Ad ad = createDefaultApiAd();

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        // Featured
        assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).isProductActive(ProductType.FEATURED), equalTo(true));
        assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).getProductExpiryDate(ProductType.FEATURED), nullValue());
    }

    @Test
    public void whenExistingAdHasNonActionedAndActionedFeatureThenPriceContextMethodsBehaveCorrectly() {
        ProductNamesSearchResponse productNamesSearchResponse = new ProductNamesSearchResponse();
        productNamesSearchResponse.setProductNames(Collections.singletonList(ProductName.FEATURE_7_DAY));
        productNamesSearchResponse.setAdvertId(100L);


        when(orderApi.searchPaidProductNamesByAccountId(anyLong())).thenReturn(productNamesSearchResponse);

        AdFeature featuredFeature = createAdFeature(ProductName.FEATURE_7_DAY, new DateTime(2020202020L));

        Ad ad = createDefaultApiAd();
        ad.setFeatures(Collections.singletonList(featuredFeature));

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        advertEditorImpl.loadAdvert(ad.getId());

        // Featured
        assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).isProductActive(ProductType.FEATURED), equalTo(true));
        assertThat(new PricingContextImpl(advertEditorImpl, mock(CategoryModel.class)).getProductExpiryDate(ProductType.FEATURED), equalTo(new DateTime
                (2020202020L)));
    }

    @Test
    public void populateShoppingCartAddsInsertionProductInCreateMode() {

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(1L);
        postAdDetail.setStatus(AdStatus.DRAFT);
        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        advertEditorImpl.populateShoppingCart(cart);

        verify(cart).addProduct(ProductName.INSERTION);
    }

    @Test
    public void populateShoppingCartDoesNotAddInsertionProductInEditMode() {
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(1L);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.AWAITING_CS_REVIEW);
        when(insertionPrice.isFree()).thenReturn(true);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        advertEditorImpl.populateShoppingCart(cart);

        verify(cart, never()).addProduct(ProductName.INSERTION);
    }

    @Test
    public void shoppingCartAddsInsertionWhenEditingExpiredAdInJobCategory() {

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(2553L);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.EXPIRED);
        when(insertionPrice.isFree()).thenReturn(true);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        advertEditorImpl.populateShoppingCart(cart);

        verify(cart).addProduct(ProductName.BUMP_UP);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void shoppingCartAddsBumpUpWhenEditingExpiredAdInPaidCategory() {

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(2553L);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.EXPIRED);
        when(insertionPrice.isFree()).thenReturn(false);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        advertEditorImpl.populateShoppingCart(cart);

        verify(cart).addProduct(ProductName.BUMP_UP);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void populateShoppingCartAddsInsertionProductWhenEditingADraftAd() {

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(1L);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.DRAFT);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        advertEditorImpl.populateShoppingCart(cart);

        verify(cart).addProduct(ProductName.INSERTION);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void populateShoppingCartDoesNotAddBumpUpProductWhenEditingANonExpiredAd() {

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(1L);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.LIVE);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        advertEditorImpl.populateShoppingCart(cart);

        verify(cart, never()).addProduct(ProductName.BUMP_UP);
    }

    @Test
    public void whenInNewUserModeNoDefaultAccountImageIsLoaded() {
        reset(userSecurityManager);
        when(authenticatedUserSession.getSelectedAccountId()).thenReturn(null);
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(1L);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        assertThat(advertEditorImpl.getImages().size(), equalTo(0));
    }

    @Test
    public void populateShoppingCartPopulatesSelectedFeaturesAndIgnoresNonSelectedFeatures() {
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(1L);
        postAdDetail.setStatus(AdStatus.DRAFT);
        FeatureBean featured3Day = new FeatureBean();
        featured3Day.setProductName(ProductName.FEATURE_3_DAY.name());
        featured3Day.setSelected(true);

        FeatureBean urgent = new FeatureBean();
        urgent.setProductName(ProductName.URGENT.name());
        urgent.setSelected(false);

        FeatureBean spotlight = new FeatureBean();
        spotlight.setProductName(ProductName.HOMEPAGE_SPOTLIGHT.name());
        spotlight.setSelected(null);

        Map<ProductType, FeatureBean> featuresMap = new HashMap<>();
        featuresMap.put(ProductType.FEATURED, featured3Day);
        featuresMap.put(ProductType.URGENT, urgent);
        featuresMap.put(ProductType.SPOTLIGHT, spotlight);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        advertEditorImpl.getPostAdFormBean().setFeatures(featuresMap);

        ShoppingCart cart = mock(ShoppingCart.class);

        advertEditorImpl.populateShoppingCart(cart);

        verify(cart).addProduct(ProductName.INSERTION);
        verify(cart).addProduct(ProductName.FEATURE_3_DAY);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void populateShoppingCartIgnoresUnrecognisedProductName() {
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(1L);
        postAdDetail.setStatus(AdStatus.DRAFT);
        FeatureBean featured3Day = new FeatureBean();
        featured3Day.setProductName(ProductName.FEATURE_3_DAY.name());
        featured3Day.setSelected(true);

        FeatureBean unknownFeature = new FeatureBean();
        unknownFeature.setProductName("UnknownProduct");
        unknownFeature.setSelected(true);

        Map<ProductType, FeatureBean> featuresMap = new HashMap<ProductType, FeatureBean>();
        featuresMap.put(ProductType.FEATURED, featured3Day);
        featuresMap.put(ProductType.SPOTLIGHT, unknownFeature);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        advertEditorImpl.getPostAdFormBean().setFeatures(featuresMap);

        ShoppingCart cart = mock(ShoppingCart.class);

        advertEditorImpl.populateShoppingCart(cart);

        verify(cart).addProduct(ProductName.INSERTION);
        verify(cart).addProduct(ProductName.FEATURE_3_DAY);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void imagesAreEmptyWhenSelectedAccountDoesNotHaveADefaultImageInCreateMode() {
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.getPostAdFormBean().setCategoryId(1L);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        assertThat(advertEditorImpl.getImages().size(), equalTo(0));
    }

    @Test
    public void whenCarsCategoryThenHiddenMakeCategoryShouldBeSubstituted() {

        Long carsCategoryId = CategoryConstants.CARS_ID;
        String vehicleMakeAttribName = CategoryConstants.Attribute.VEHICLE_MAKE.getName();
        String vehicleModelAttribName = CategoryConstants.Attribute.VEHICLE_MODEL.getName();
        String vehicleMilageAttribName = CategoryConstants.Attribute.VEHICLE_MILEAGE.getName();
        String vehicleModel = "z3";
        String vehicleMake = "vauxhall";
        String vehicleMilage = "10000";

        Map<String, AttributeMetadata> attributeMetadataMap = Fixtures.carsAttributeMetaData();

        Collection<Category> visbleChildCategories = new ArrayList<>();
        when(carsCategory.getId()).thenReturn(carsCategoryId);
        when(carsCategory.visibleChildCategories()).thenReturn(visbleChildCategories);
        when(categoryModel.findAttributesByNameForGivenCategory(vehicleMakeAttribName, carsCategoryId))
                .thenReturn(Optional.of(attributeMetadataMap.get(vehicleMakeAttribName)));
        when(categoryModel.findAttributesByNameForGivenCategory(vehicleModelAttribName, carsCategoryId))
                .thenReturn(Optional.of(attributeMetadataMap.get(vehicleModelAttribName)));
        when(categoryModel.findAttributesByNameForGivenCategory(vehicleMilageAttribName, carsCategoryId))
                .thenReturn(Optional.of(attributeMetadataMap.get(vehicleMilageAttribName)));

        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.addAttribute(vehicleModelAttribName, vehicleModel);
        postAdFormBean.addAttribute(vehicleMakeAttribName, vehicleMake);
        postAdFormBean.addAttribute(vehicleMilageAttribName, vehicleMilage);
        postAdFormBean.setCategoryId(carsCategoryId);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setAdvertId(null);
        advertEditorImpl = getAdvertEditor(postAdDetail);
        assertThat(advertEditorImpl.toApiBean().getCategoryId(), equalTo(10322L));

    }

    @Test
    public void whenLeafCategoryThenDontAttemptToSubstituteCategoryId() {

        Long bmwCategoryId = 10303L;
        String vehicleMakeAttribName = CategoryConstants.Attribute.VEHICLE_MAKE.getName();
        String vehicleMake = "bmw";

        Category bmwCategory = mock(Category.class);
        when(categoryModel.getCategory(bmwCategoryId)).thenReturn(Optional.of(bmwCategory));
        when(bmwCategory.isLeaf()).thenReturn(true);

        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.addAttribute(vehicleMakeAttribName, vehicleMake);
        postAdFormBean.setCategoryId(bmwCategoryId);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setAdvertId(null);
        advertEditorImpl = getAdvertEditor(postAdDetail);
        assertThat(advertEditorImpl.toApiBean().getCategoryId(), equalTo(bmwCategoryId));
    }

    @Test
    public void whenNotLeafCategoryButHasVisibleChildrenThenDontAttemptToSubstituteCategoryId() {

        Long forSaleCategoryId = CategoryConstants.FOR_SALE_ID;

        Category forSaleCategory = mock(Category.class);
        Category audioCategory = mock(Category.class);
        when(categoryModel.getCategory(forSaleCategoryId)).thenReturn(Optional.of(forSaleCategory));
        when(forSaleCategory.isLeaf()).thenReturn(false);
        Collection<Category> children = new ArrayList<>();
        children.add(audioCategory);
        when(forSaleCategory.visibleChildCategories()).thenReturn(children);

        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setCategoryId(forSaleCategoryId);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setAdvertId(null);
        advertEditorImpl = getAdvertEditor(postAdDetail);
        assertThat(advertEditorImpl.toApiBean().getCategoryId(), equalTo(forSaleCategoryId));
    }

    @Test
    public void whenAdvertHasNoImagesRemoveImageShouldFailSilently() {
        assertThat(advertEditorImpl.getPostAdFormBean().getImageIds(), is(empty()));
        advertEditorImpl.removeImage(1L);
        assertThat(advertEditorImpl.getPostAdFormBean().getImageIds(), is(empty()));
        assertThat(advertEditorImpl.getImages(), is(empty()));
    }

    @Test
    public void whenAdvertHasOneImageRemoveImageShouldWorkOK() {
        Long imageId = 1L;
        Image image = new Image();
        image.setId(imageId);
        advertDetail.getPostAdFormBean().setImageIds(Lists.newArrayList(imageId));
        advertDetail.setImages(Lists.newArrayList(image));
        assertThat(advertEditorImpl.getPostAdFormBean().getImageIds(), is(not(empty())));
        advertEditorImpl.removeImage(1L);
        assertThat(advertEditorImpl.getPostAdFormBean().getImageIds(), is(empty()));
        assertThat(advertEditorImpl.getImages(), is(empty()));
    }

    @Test
    public void whenAdvertHasMoreThanOneImageShouldBeDeleteOneImageOK() {
        Long imageId = 1L;
        Image image = new Image();
        image.setId(imageId);
        Long imageId2 = 2L;
        Image image2 = new Image();
        image2.setId(imageId2);

        advertDetail.getPostAdFormBean().setImageIds(Lists.newArrayList(imageId, imageId2));
        advertDetail.setImages(Lists.newArrayList(image, image2));
        assertThat(advertEditorImpl.getPostAdFormBean().getImageIds(), is(not(empty())));
        assertThat(advertEditorImpl.getPostAdFormBean().getImageIds(), iterableWithSize(equalTo(2)));
        advertEditorImpl.removeImage(1L);
        assertThat(advertEditorImpl.getPostAdFormBean().getImageIds(), iterableWithSize(equalTo(1)));
        assertThat(advertEditorImpl.getPostAdFormBean().getImageIds(), contains(2L));
        assertThat(advertEditorImpl.getImages(), iterableWithSize(equalTo(1)));
        assertThat(advertEditorImpl.getImages(), contains(image2));
    }


    /*
     *  supportsContactUrl
     */

    @Test
    public void supportsContactUrlIsTrueIfProUserAndJobs() {
        // given
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setCategoryId(10L);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        // and
        User user = Fixtures.user().build();
        user.setType(UserType.PRO);
        when(authenticatedUserSession.getUser()).thenReturn(user);
        when(categoryModel.getL1CategoryFor(10L)).thenReturn(Optional.of(Fixtures.JOBS_REAL_CATEGORY));

        // when & then
        assertThat(advertEditorImpl.supportsContactUrl(), is(true));
    }

    @Test
    public void supportsContactUrlIsFalseIfStandardUserAndServices() {
        // given
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setCategoryId(10L);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        // and
        User user = Fixtures.user().build();
        user.setType(UserType.STANDARD);
        when(authenticatedUserSession.getUser()).thenReturn(user);
        when(categoryModel.getL1CategoryFor(10L)).thenReturn(Optional.of(Fixtures.JOBS_REAL_CATEGORY));

        // when & then
        assertThat(advertEditorImpl.supportsContactUrl(), is(false));
    }

    @Test
    public void supportsContactUrlIsFalseIfProUserAndNeitherJobsNorServices() {
        // given
        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        postAdDetail.setCategoryId(10L);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        // and
        User user = Fixtures.user().build();
        user.setType(UserType.PRO);
        when(authenticatedUserSession.getUser()).thenReturn(user);
        when(categoryModel.getL1CategoryFor(10L)).thenReturn(Optional.of(Fixtures.MOTORS_REAL_CATEGORY));

        // when & then
        assertThat(advertEditorImpl.supportsContactUrl(), is(false));
    }

    @Test
    public void populateShoppingCartExpiredJobsAd() {
        // given
        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setCategoryId(2553L);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.EXPIRED);

        when(insertionPrice.isFree()).thenReturn(true);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        // when
        advertEditorImpl.populateShoppingCart(cart);

        // then
        verify(cart).addProduct(ProductName.BUMP_UP);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void populateShoppingCartExpiredForSaleAdRelistForFree() {
        // given
        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.addAttribute("seller_type", "private");
        postAdFormBean.setCategoryId(2549L);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.EXPIRED);

        when(insertionPrice.isFree()).thenReturn(true);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        //when
        advertEditorImpl.populateShoppingCart(cart);

        //then
        verify(cart).addProduct(ProductName.INSERTION);
        verifyNoMoreInteractions(cart);
    }


    @Test
    public void populateShoppingCartExpiredCommunityAdRelistForFree() {
        //given
        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setCategoryId(1234L);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.EXPIRED);

        when(insertionPrice.isFree()).thenReturn(true);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        // when
        advertEditorImpl.populateShoppingCart(cart);

        //then
        verify(cart).addProduct(ProductName.INSERTION);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void populateShoppingCartExpiredForSaleAdRelistNotForFree() {
        // given
        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.addAttribute("seller_type", "trade");
        postAdFormBean.setCategoryId(2549L);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.EXPIRED);

        when(insertionPrice.isFree()).thenReturn(true);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);
        // when
        advertEditorImpl.populateShoppingCart(cart);

        // then
        verify(cart).addProduct(ProductName.BUMP_UP);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void populateShoppingCartExpiredMotorAdInsertionFreePrivateSeller() {
        //given
        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.addAttribute("seller_type", "private");
        postAdFormBean.setCategoryId(2551L);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.EXPIRED);

        when(insertionPrice.isFree()).thenReturn(true);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        // when
        advertEditorImpl.populateShoppingCart(cart);

        // then
        verify(cart).addProduct(ProductName.INSERTION);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void populateShoppingCartExpiredMotorAdInseriontNotFreePrivateSeller() {
        // given
        // Motors category - private seller with already a live ads in motor, relisting an expired ad
        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.addAttribute("seller_type", "private");
        postAdFormBean.setCategoryId(2551L);
        postAdDetail.setPostAdFormBean(postAdFormBean);
        postAdDetail.setAdvertId(1L);
        postAdDetail.setStatus(AdStatus.EXPIRED);

        when(insertionPrice.isFree()).thenReturn(false);

        advertEditorImpl = getAdvertEditor(postAdDetail);

        ShoppingCart cart = mock(ShoppingCart.class);

        // when
        advertEditorImpl.populateShoppingCart(cart);

        // then
        verify(cart).addProduct(ProductName.BUMP_UP);
        verifyNoMoreInteractions(cart);
    }

    @Test
    public void shouldReturnFalseInAutobizFlowForNew(){
        // Given
        Location location = mock(Location.class);
        Category carCategory = mock(Category.class);
        Category currentCategory = mock(Category.class);
        when(userPostcodeLookupService.getLastUsedPostcode(24L))
                .thenReturn(Optional.of("CB10 1NU")); // Essex
        when(postAdLocationService.lookupPostcode("CB10 1NU"))
                .thenReturn(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_RECOGNISED, "CB10 1NU", ESSEX_CB10_1NU));
        when(postAdLocationService.get(ESSEX_CB10_1NU)).thenReturn(location);

        when(postAdLocationService.isInArea(location, ESSEX.intValue())).thenReturn(true);
        when(categoryService.getById(CARS_CATEGORY_ID)).thenReturn(Optional.of(carCategory));
        when(categoryService.getById(USED_FIAT_CATEGORY_ID)).thenReturn(Optional.of(currentCategory));
        when(categoryService.isChild(currentCategory, carCategory)).thenReturn(true);


        // When
        advertEditorImpl.initNew(USED_FIAT_CATEGORY_ID); // Used Fiat for sale
        PostAdFormBean formBean = advertEditorImpl.getPostAdFormBean();


        // Then
        // Seller type is not set yet
        assertThat(formBean.getInAutobizFlow(), is(false));
    }

    @Test
    public void shouldReturnTrueInAutobizFlowForLoad(){
        // Given
        when(postAdLocationService.isInArea(any(), any())).thenReturn(true);
        Ad advert = loadAdvertAutobiz(USED_FIAT_CATEGORY_ID, ESSEX_CB10_1NU, SellerType.PRIVATE);
        when(advertApi.getAdvert(advert.getId())).thenReturn(advert);

        // When
        advertEditorImpl.loadAdvert(advert.getId());

        // Then
        assertThat(advertEditorImpl.getPostAdFormBean().getInAutobizFlow(), is(true));
    }

    @Test
    public void shouldReturnFalseInAutobizFlowForLoadWrongCategory(){
        // Given
        Ad advert = loadAdvertAutobiz(SOFA_CATEGORY_ID, ESSEX_CB10_1NU, SellerType.PRIVATE);

        // When
        advertEditorImpl.loadAdvert(advert.getId());

        // Then
        assertThat(advertEditorImpl.getPostAdFormBean().getInAutobizFlow(), is(false));
    }

    @Test
    public void shouldReturnFalseInAutobizFlowForLoadWrongPostcode(){
        // Given
        Ad advert = loadAdvertAutobiz(USED_FIAT_CATEGORY_ID, RICHMOND_TW10_6LW, SellerType.PRIVATE);

        // When
        advertEditorImpl.loadAdvert(advert.getId());

        // Then
        assertThat(advertEditorImpl.getPostAdFormBean().getInAutobizFlow(), is(false));
    }

    @Test
    public void shouldReturnFalseInAutobizFlowForLoadWrongSellerType(){
        // Given
        Ad advert = loadAdvertAutobiz(USED_FIAT_CATEGORY_ID, ESSEX_CB10_1NU, SellerType.TRADE);

        // When
        advertEditorImpl.loadAdvert(advert.getId());

        // Then
        assertThat(advertEditorImpl.getPostAdFormBean().getInAutobizFlow(), is(false));
    }

    @Test
    public void shouldReturnTrueInAutobizFlowForUpdate(){
        // Given
        PostAdFormBean formBean = createPostAdFormBean(USED_FIAT_CATEGORY_ID, ESSEX_CB10_1NU, SellerType.PRIVATE);
        when(postAdLocationService.isInArea(any(), any())).thenReturn(true);

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        formBean = advertEditorImpl.getPostAdFormBean();


        // Then
        assertThat(formBean.getInAutobizFlow(), is(true));
    }

    @Test
    public void shouldReturnFalseInAutobizFlowForUpdateWhenWrongCategory(){
        // Given
        PostAdFormBean formBean = createPostAdFormBean(SOFA_CATEGORY_ID, ESSEX_CB10_1NU, SellerType.PRIVATE);

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        formBean = advertEditorImpl.getPostAdFormBean();


        // Then
        assertThat(formBean.getInAutobizFlow(), is(false));
    }

    @Test
    public void shouldReturnFalseInAutobizFlowForUpdateWhenWrongLocation(){
        // Given
        PostAdFormBean formBean = createPostAdFormBean(USED_FIAT_CATEGORY_ID, RICHMOND_TW10_6LW, SellerType.PRIVATE);

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        formBean = advertEditorImpl.getPostAdFormBean();


        // Then
        assertThat(formBean.getInAutobizFlow(), is(false));
    }

    @Test
    public void shouldReturnFalseInAutobizFlowForUpdateWhenWrongSellerType(){
        // Given
        PostAdFormBean formBean = createPostAdFormBean(USED_FIAT_CATEGORY_ID, ESSEX_CB10_1NU, SellerType.TRADE);

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        formBean = advertEditorImpl.getPostAdFormBean();


        // Then
        assertThat(formBean.getInAutobizFlow(), is(false));
    }

    @Test(expected = BreedNotFoundException.class)
    public void shouldThrowExceptionWhenInvalidDogBreedsHasBeenPassed(){
        // Given
        Map<String, String> attributes = new HashMap<>();
        Map<String, AttributeMetadata> attributeMetadataMap = Fixtures.dogBreedAttributeMetadata();
        attributes.put("dog_breed","bagadbilla");
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setLocationId(ESSEX_CB10_1NU);
        formBean.setCategoryId(9389L);
        formBean.setAttributes(attributes);
        when(categoryModel.findAttributesByNameForGivenCategory("dog_breed",9389L ))
                .thenReturn(Optional.of(attributeMetadataMap.get("dog_breed")));

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        advertEditorImpl.toApiBean();
    }

    @Test
    public void shouldNotThrowExceptionWhenValidDogBreedsHasBeenPassed(){
        // Given
        Map<String, String> attributes = new HashMap<>();
        Map<String, AttributeMetadata> attributeMetadataMap = Fixtures.dogBreedAttributeMetadata();
        attributes.put("dog_breed","Chihuahua");
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setLocationId(ESSEX_CB10_1NU);
        formBean.setCategoryId(9389L);
        formBean.setAttributes(attributes);

        when(categoryModel.findAttributesByNameForGivenCategory("dog_breed",9389L ))
                .thenReturn(Optional.of(attributeMetadataMap.get("dog_breed")));

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        PostAdvertBean pab = advertEditorImpl.toApiBean();

        Assert.assertNotNull(pab);
    }

    @Test(expected = BreedNotFoundException.class)
    public void shouldThrowExceptionWhenEmptyDogBreedsHasBeenPassed(){
        // Given
        Map<String, String> attributes = new HashMap<>();
        Map<String, AttributeMetadata> attributeMetadataMap = Fixtures.dogBreedAttributeMetadata();
        attributes.put("dog_breed","");
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setLocationId(ESSEX_CB10_1NU);
        formBean.setCategoryId(9389L);
        formBean.setAttributes(attributes);

        when(categoryModel.findAttributesByNameForGivenCategory("dog_breed",9389L ))
                .thenReturn(Optional.of(attributeMetadataMap.get("dog_breed")));

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        PostAdvertBean pab = advertEditorImpl.toApiBean();

        Assert.assertNotNull(pab);
    }

    @Test
    public void shouldNotThrowExceptionWhenValidCatBreedsHasBeenPassed(){
        // Given
        Map<String, String> attributes = new HashMap<>();
        Map<String, AttributeMetadata> attributeMetadataCats = Fixtures.catBreedAttributeMetadata();
        attributes.put("cat_breed","american_curl");
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setLocationId(ESSEX_CB10_1NU);
        formBean.setCategoryId(9390L);
        formBean.setAttributes(attributes);
        Optional<AttributeMetadata>  metadataOptional = Optional.of(attributeMetadataCats.get("cat_breed"));

        when(categoryModel.findAttributesByNameForGivenCategory("cat_breed",9390L ))
                .thenReturn(metadataOptional);

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        PostAdvertBean pab = advertEditorImpl.toApiBean();

        Assert.assertNotNull(pab);

        Assert.assertTrue(pab.getAttributes().stream().anyMatch( e -> "american_curl".equals(e.getValue())));
    }

    @Test(expected = BreedNotFoundException.class)
    public void shouldThrowExceptionWhenInValidCatBreedsHasBeenPassed(){
        // Given
        Map<String, String> attributes = new HashMap<>();
        Map<String, AttributeMetadata> attributeMetadataCats = Fixtures.catBreedAttributeMetadata();
        attributes.put("cat_breed","bagadbilli");
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setLocationId(ESSEX_CB10_1NU);
        formBean.setCategoryId(9390L);
        formBean.setAttributes(attributes);
        Optional<AttributeMetadata>  metadataOptional = Optional.of(attributeMetadataCats.get("cat_breed"));

        when(categoryModel.findAttributesByNameForGivenCategory("cat_breed",9390L ))
                .thenReturn(metadataOptional);

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        advertEditorImpl.toApiBean();
    }

    @Test(expected = BreedNotFoundException.class)
    public void shouldThrowExceptionWhenEmptyCatBreedsHasBeenPassed(){
        // Given
        Map<String, String> attributes = new HashMap<>();
        Map<String, AttributeMetadata> attributeMetadataCats = Fixtures.catBreedAttributeMetadata();
        attributes.put("cat_breed","");
        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setLocationId(ESSEX_CB10_1NU);
        formBean.setCategoryId(9390L);
        formBean.setAttributes(attributes);
        Optional<AttributeMetadata>  metadataOptional = Optional.of(attributeMetadataCats.get("cat_breed"));

        when(categoryModel.findAttributesByNameForGivenCategory("cat_breed",9390L ))
                .thenReturn(metadataOptional);

        // When
        advertEditorImpl.setPostAdFormBean(formBean);
        advertEditorImpl.toApiBean();
    }



    @Test
    public void submittedValidationForInvalidValue(){
        Map<String, String> newAttributes = new HashMap<>();
        newAttributes.put(SearchBeanRequestParams.DIY_TOOLS_MATERIALS_CONDITION.getName(), "invalidValue");
        assertFalse(submittedDiyConditionAttributeValidation(10683L,10684L,SearchBeanRequestParams.DIY_TOOLS_MATERIALS_CONDITION,newAttributes));
    }


    @Test
    public void submittedValidationForRightValue(){
        Map<String, String> newAttributes = new HashMap<>();
        newAttributes.put(SearchBeanRequestParams.DIY_TOOLS_MATERIALS_CONDITION.getName(), "as_good_as_new");
        assertTrue(submittedDiyConditionAttributeValidation(10683L,10684L,SearchBeanRequestParams.DIY_TOOLS_MATERIALS_CONDITION,newAttributes));
    }



    public boolean submittedDiyConditionAttributeValidation(Long configedCategoryId, Long adCategoryId, SearchBeanRequestParams searchBeanRequestParams, Map<String, String> newAttributes) {
        // Create a default advertisement object
        Ad ad = createDefaultApiAd();

        // Mock the behavior of the advert API to return the created advertisement when its ID is requested
        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);
        // Load the advertisement into the advert editor using its ID
        advertEditorImpl.loadAdvert(ad.getId());

        // Get the post - ad form bean from the advert editor
        PostAdFormBean postAdFormBean = advertEditorImpl.getPostAdFormBean();
        // Get the attributes map from the post - ad form bean
        Map<String, String> attributes = postAdFormBean.getAttributes();
        // Create an attribute metadata object

        AttributeMetadata attributeMetadata = new AttributeMetadata();
        attributeMetadata.setValues(Arrays.asList(
                new AttributeValueMetadata("as_good_as_new", "As good as new"),
                new AttributeValueMetadata("good", "Good condition")
        ));

        // Set the category ID for the post - ad form bean
        Long categoryId = adCategoryId;
        postAdFormBean.setCategoryId(categoryId);
        // Mock the behavior of the category model to return true when checking if a category is a child or equal
        when(categoryModel.isChildOrEqual(configedCategoryId, categoryId)).thenReturn(true);

        // Mock the behavior of the category model to return the attribute metadata when finding attributes by name for the given category
        when(categoryModel.findAttributesByNameForGivenCategory(searchBeanRequestParams.getName(), categoryId))
                .thenReturn(Optional.of(attributeMetadata));

        if ((newAttributes != null && !newAttributes.isEmpty())){
            attributes.putAll(newAttributes);
        }
        // Test the phone attributes validation service with valid values
        return commonAttributesValidationService.isValidAttributeValuePassed(categoryId, attributes);
    }

    /*
     *  helper methods
     */
    private void setupAutobiz(Long categoryId, Long locationId){

        Location location = mock(Location.class);
        Category carCategory = mock(Category.class);
        Category currentCategory = mock(Category.class);

        when(postAdLocationService.get(locationId)).thenReturn(location);
        when(categoryService.getById(CARS_CATEGORY_ID)).thenReturn(Optional.of(carCategory));
        when(categoryService.getById(categoryId)).thenReturn(Optional.of(currentCategory));
        when(categoryService.isChild(currentCategory, carCategory)).thenReturn(categoryId==USED_FIAT_CATEGORY_ID);
        when(postAdLocationService.isInArea(location,ESSEX.intValue())).thenReturn(locationId==ESSEX_CB10_1NU);
    }

    private Ad loadAdvertAutobiz(Long categoryId, Long locationId, SellerType sellerType){

        setupAutobiz(categoryId, locationId);

        Ad ad = createDefaultApiAd();
        ad.setCategoryId(categoryId);
        ad.setLocationId(locationId);
        Attribute attribute = new Attribute();
        attribute.setName("seller_type");
        attribute.setValue(sellerType == SellerType.PRIVATE?"private":"trade");
        ad.setAttributes(new Attribute[]{attribute});

        when(advertApi.getAdvert(ad.getId())).thenReturn(ad);

        return ad;
    }

    private PostAdFormBean createPostAdFormBean(Long categoryId, Long locationId, SellerType sellerType){

        setupAutobiz(categoryId, locationId);

        Map<String,String> attributes = new HashMap<>();
        attributes.put("seller_type", sellerType == SellerType.PRIVATE?"private":"trade");

        PostAdFormBean formBean = new PostAdFormBean();
        formBean.setLocationId(locationId);
        formBean.setCategoryId(categoryId);
        formBean.setAttributes(attributes);

        return formBean;
    }

    private AdFeature createAdFeature(ProductName product, DateTime expiry) {
        AdFeature urgentFeature = new AdFeature();
        urgentFeature.setProductName(product);
        urgentFeature.setEndDate(expiry);
        return urgentFeature;
    }

    private Ad createDefaultApiAd() {
        Ad ad = new Ad();
        ad.setAccountId(24L);
        ad.setId(100L);
        ad.setCategoryId(10L);
        ad.setLocationId(5L);
        ad.setLiveDate(new DateTime());
        when(categoryService.getById(ad.getCategoryId())).thenReturn(createCategory(ad.getCategoryId()));
        Location location = mock(Location.class);
        when(postAdLocationService.get(ad.getLocationId())).thenReturn(location);
        when(location.getId()).thenReturn(ad.getLocationId().intValue());
        ad.setStatus(AdStatus.DRAFT);
        return ad;
    }

    private List<Map<String, LegacyImage>> createTestImages() {

        List<Map<String, LegacyImage>> imageList = new ArrayList<>();

        Map<String, LegacyImage> image1Map = new LinkedHashMap<>();
        image1Map.put("image1Size1", createImage(1L, "image1Size1Url"));
        image1Map.put("image1Size2", createImage(1L, "image1Size2Url"));
        image1Map.put("image1Size3", createImage(1L, "image1Size3Url"));

        Map<String, LegacyImage> image2Map = new LinkedHashMap<>();
        image2Map.put("image2Size1", createImage(2L, "image2Size1Url"));
        image2Map.put("image2Size2", createImage(2L, "image2Size2Url"));
        image2Map.put("image2Size3", createImage(2L, "image2Size3Url"));

        Map<String, LegacyImage> image3Map = new LinkedHashMap<>();
        image3Map.put("image3Size1", createImage(3L, "image3Size1Url"));
        image3Map.put("image3Size2", createImage(3L, "image3Size2Url"));
        image3Map.put("image3Size3", createImage(3L, "image3Size3Url"));

        imageList.addAll(Arrays.asList(image1Map, image2Map, image3Map));

        return imageList;
    }

    private List<Map<String, LegacyImage>> createSingleTestImage() {

        List<Map<String, LegacyImage>> imageList = new ArrayList<>();

        Map<String, LegacyImage> image1Map = new LinkedHashMap<>();
        image1Map.put("image1Size1", createImage(1L, "image1Size1Url/79"));
        image1Map.put("image1Size2", createImage(1L, "image1Size2Url/80"));
        image1Map.put("image1Size3", createImage(1L, "image1Size3Url/81"));

        imageList.addAll(Arrays.asList(image1Map));

        return imageList;
    }

    private LegacyImage createImage(Long id, String url) {
        LegacyImage image = new LegacyImage();
        image.setId(id);
        image.setUrl(url);
        return image;
    }

    private List<PostAdAttributeGroup> createTestAttributeSetOne() {
        List<PostAdAttributeGroup> attributeGroups = new ArrayList<>();
        List<PostAdAttribute> attributes = new ArrayList<>();

        attributes.add(anAttribute()
                .id("attribute1")
                .label("Attribute1")
                .mandatory(true)
                .type(PostAdAttributeType.DROPDOWN)
                .withValue("1val1", "1Value1")
                .withValue("1val2", "1Value2")
                .build());

        attributes.add(anAttribute()
                .id("attribute2")
                .label("Attribute2")
                .mandatory(false)
                .type(PostAdAttributeType.DROPDOWN)
                .withValue("2val1", "2Value1")
                .withValue("2val2", "2Value2")
                .build());

        attributes.add(anAttribute()
                .id("attribute3")
                .label("Attribute3")
                .mandatory(false)
                .type(PostAdAttributeType.DROPDOWN)
                .withValue("3val1", "3Value1")
                .withValue("3val2", "3Value2")
                .build());


        attributes.add(anAttribute()
                .id("attribute4")
                .label("Attribute4")
                .mandatory(false)
                .type(PostAdAttributeType.TEXTFIELD)
                .build());

        attributeGroups.add(
                PostAdAttributeGroup.builder()
                        .setId("test_group1")
                        .setLabel("Test Group One")
                        .setPanelId("panel1")
                        .setHighPriority(false)
                        .setAttributes(attributes, new HashMap<>())
                        .build());

        return attributeGroups;
    }

    private List<PostAdAttributeGroup> createTestAttributeSetTwo() {

        List<PostAdAttributeGroup> attributeGroups = new ArrayList<>();
        List<PostAdAttribute> attributes = new ArrayList<>();

        attributes.add(anAttribute()
                .id("attribute1")
                .label("Attribute1")
                .mandatory(true)
                .type(PostAdAttributeType.DROPDOWN)
                .withValue("1val1", "1Value1")
                .withValue("1val2", "1Value2")
                .build());

        attributes.add(anAttribute()
                .id("attribute2")
                .label("Attribute2")
                .mandatory(false)
                .type(PostAdAttributeType.DROPDOWN)
                .withValue("2val1", "2Value1")
                .withValue("2val2", "2Value2")
                .build());

        attributes.add(anAttribute()
                .id("attribute5")
                .label("Attribute5")
                .mandatory(false)
                .type(PostAdAttributeType.DROPDOWN)
                .withValue("5val1", "5Value1")
                .withValue("5val2", "5Value2")
                .build());


        attributes.add(anAttribute()
                .id("attribute6")
                .label("Attribute6")
                .mandatory(false)
                .type(PostAdAttributeType.TEXTFIELD)
                .build());

        attributeGroups.add(
                PostAdAttributeGroup.builder()
                        .setId("test_group1")
                        .setLabel("Test Group One")
                        .setPanelId("panel1")
                        .setHighPriority(false)
                        .setAttributes(attributes, new HashMap<>())
                        .build());

        return attributeGroups;
    }

    private PostAdAttribute.Builder anAttribute() {
        return new PostAdAttribute.Builder();
    }
}
