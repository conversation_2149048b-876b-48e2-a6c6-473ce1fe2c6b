package com.gumtree.web.seller.page.postad.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsNull.nullValue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class ValidateAdApiCallTest {

    private BushfireApi bushfireApi;

    private AdvertApi advertApi;

    private PostAdvertBean postAdvertBean;

    private ValidateAdApiCall apiCall;

    @Before
    public void init() {
        bushfireApi = mock(BushfireApi.class);
        advertApi = mock(AdvertApi.class);
        postAdvertBean = new PostAdvertBean();
        apiCall = new ValidateAdApiCall(postAdvertBean);

        when(bushfireApi.advertApi()).thenReturn(advertApi);
    }

    @Test
    public void callsValidateAdOnApi() {
        assertThat(apiCall.execute(bushfireApi), nullValue());
        verify(advertApi).validateAd(postAdvertBean);
    }
}
