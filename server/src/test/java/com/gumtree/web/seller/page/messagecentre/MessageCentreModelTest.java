package com.gumtree.web.seller.page.messagecentre;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.gumtree.web.common.domain.messagecentre.Advert;
import com.gumtree.web.common.domain.messagecentre.Conversation;
import com.gumtree.web.common.domain.messagecentre.ConversationGroup;
import com.gumtree.web.common.domain.messagecentre.Conversations;
import com.gumtree.web.common.domain.messagecentre.Message;
import com.gumtree.web.common.domain.messagecentre.MessageDirection;
import com.gumtree.web.common.domain.messagecentre.Messages;
import com.gumtree.web.common.domain.messagecentre.PollingFrequencies;
import com.gumtree.web.seller.page.messagecentre.model.ReviewableConversation;
import org.junit.Test;

import java.util.Collections;
import java.util.Optional;

import static org.fest.assertions.api.Assertions.assertThat;

public class MessageCentreModelTest {

    @Test
    public void shouldSerializeConversationsToJsonCorrectly() throws JsonProcessingException {
        // given
        ConversationGroup group = new ConversationGroup(
                Optional.of(new Advert.Builder().setId(99L).setCategoryId(1026L).setProAccount(false).build()),
                Lists.newArrayList(Conversation.builder().setId("cid").build()));

        Conversations conversation = Conversations.builder().setNumUnreadConversations(1)
                .setPollingFrequencies(new PollingFrequencies(1000, 1000))
                .setConversationGroups(Lists.newArrayList(group))
                .build();

        // when
        MessageCentreModel messageCentreModel = new MessageCentreModel.Builder()
                .withConversations(conversation)
                .build(null);

        // then
        assertThat(messageCentreModel.getConversationsAsJsonString())
                .isEqualTo("{\"numUnreadConversations\":1,\"numConversations\":0,\"pollingFrequencies\":{\"pollingVisibleInterval\":1000,\"pollingHiddenInterval\":1000},\"conversationGroups\":[{\"advert\":{\"id\":99,\"categoryId\":1026,\"proAccount\":false,\"replyType\":\"GUMTREE\",\"markedAsSold\":false},\"conversations\":[{\"id\":\"cid\",\"unread\":false}],\"lastConversationId\":\"cid\"}],\"lastConversationId\":\"cid\"}");
    }

    @Test
    public void shouldSerializeConversationsToXssSafeJson() throws JsonProcessingException {
        // given
        Message.Builder messageBuilder = Message.builder()
                .setId("123")
                .setDirection(MessageDirection.INBOUND)
                .setTime("0");
        Message xssInfectedMessage = messageBuilder
                .setBody("<script>Some dubious message</script>")
                .build();

        ConversationGroup group = new ConversationGroup(
                Optional.of(new Advert.Builder().setId(99L).setCategoryId(1026L).setProAccount(false).build()),
                Lists.newArrayList(Conversation.builder().setId("cid").setLastMessage(xssInfectedMessage).build()));

        Conversations conversation = Conversations.builder().setNumUnreadConversations(1)
                .setPollingFrequencies(new PollingFrequencies(1000, 1000))
                .setConversationGroups(Lists.newArrayList(group))
                .build();

        // when
        MessageCentreModel messageCentreModel = new MessageCentreModel.Builder()
                .withConversations(conversation)
                .build(null);

        // then
        assertThat(messageCentreModel.getConversationsAsJsonString())
                .isEqualTo("{\"numUnreadConversations\":1,\"numConversations\":0,\"pollingFrequencies\":{\"pollingVisibleInterval\":1000,\"pollingHiddenInterval\":1000},\"conversationGroups\":[{\"advert\":{\"id\":99,\"categoryId\":1026,\"proAccount\":false,\"replyType\":\"GUMTREE\",\"markedAsSold\":false},\"conversations\":[{\"id\":\"cid\",\"unread\":false,\"lastMessage\":{\"id\":\"123\",\"direction\":\"INBOUND\",\"time\":\"0\",\"body\":\"<script>Some dubious message<\\/script>\"}}],\"lastConversationId\":\"cid\"}],\"lastConversationId\":\"cid\"}");
    }

    @Test
    public void shouldSerializeMessagesToXssSafeJson() throws JsonProcessingException {
        // given
        Message.Builder messageBuilder = Message.builder()
                .setId("123")
                .setDirection(MessageDirection.INBOUND)
                .setTime("0");
        Message xssInfectedMessage = messageBuilder
                .setBody("üäö🥺" + System.lineSeparator() + "<script attr=\"test\">Some dubious message</script> \n")
                .build();
        Messages messages = Messages.builder()
                .setMessages(Collections.singletonList(xssInfectedMessage))
                .setAdvert(Optional.empty())
                .build();
        ReviewableConversation conversation = ReviewableConversation.builder().withMessages(messages).build();

        // when
        MessageCentreModel messageCentreModel = new MessageCentreModel.Builder()
                .withMessages(conversation)
                .build(null);

        // then
        assertThat(messageCentreModel.getMessagesAsJsonString())
                .isEqualTo("{\"messages\":[{\"id\":\"123\",\"direction\":\"INBOUND\",\"time\":\"0\",\"body\":\"üäö\uD83E\uDD7A\\n<script attr=\\x22test\\x22>Some dubious message<\\/script> \\n\"}],\"reviewAllowed\":false,\"converseeRating\":{\"total\":0,\"average\":0.0,\"breakdown\":{\"1\":0,\"2\":0,\"3\":0,\"4\":0,\"5\":0},\"formattedAverage\":\"0.0\"}}");
    }

}