package com.gumtree.web.seller.storage.ratelimit;

import com.google.common.collect.Maps;
import com.gumtree.web.storage.CassandraClient;
import com.gumtree.web.storage.CassandraKeyValueRepository;
import com.gumtree.web.storage.ratelimit.CassandraRateLimiterPersister;
import com.gumtree.web.storage.ratelimit.RateCheckResult;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;

import java.util.Map;
import java.util.Optional;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.mock;

public class CassandraRateLimiterPersisterTest {
    private static final String CREDENTIAL = "<EMAIL>";
    private static final String CREDENTIAL_IS_EXPIRED = "<EMAIL>";
    private static final String FAILING_CREDENTIAL = "<EMAIL>";

    private MockCassandraKeyValueRepository cassandraRepository;
    private CassandraRateLimiterPersister persister;
    private int limitPerInterval = 2;
    private int intervalInSeconds = 60;


    @Before
    public void setup() {
        cassandraRepository = new MockCassandraKeyValueRepository();

        persister = new CassandraRateLimiterPersister(cassandraRepository);
    }

    @Test
    public void registersAndReturnsZeroWhenFirstTimeForCredential() {
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL), equalTo(RateCheckResult.EMPTY));
    }

    @Test
    public void createRateCounterOnFirstIncrement() {
        // when
        persister.incRateCounter(intervalInSeconds, CREDENTIAL);

        // then
        assertThat(cassandraRepository.get(CREDENTIAL), equalTo(Optional.of("1")));
    }

    @Test
    public void returnsZeroWhenCredentialIsStillWithinLimit() {
        cassandraRepository.set(CREDENTIAL, "1", 60);

        // when + then
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL), equalTo(new RateCheckResult(1, 0)));
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL).isRateLimitExceeded(), equalTo(false));
    }

    @Test
    public void incrementRateCounterWhenCounterExists() {
        cassandraRepository.set(CREDENTIAL, "1", 60);

        // when
        persister.incRateCounter(intervalInSeconds, CREDENTIAL);

        // then
        assertThat(cassandraRepository.get(CREDENTIAL), equalTo(Optional.of("2")));
    }

    @Test
    public void returnsWaitWhenCredentialIsAtLimit() {
        cassandraRepository.set(CREDENTIAL, "3", 60);
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL), equalTo(new RateCheckResult(3, 30)));
    }

    @Test
    public void returnsWaitWhenCredentialIsBeyondLimit() {
        cassandraRepository.set(CREDENTIAL, "3", 60);
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL), equalTo(new RateCheckResult(3, 30)));
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL).isRateLimitExceeded(), equalTo(true));
    }

//    @Test
//    public void setsNewEntryIfExpiredInRedisBetweenReadAndTTL() {
//        // given
//        redisTemplate.set(CREDENTIAL_IS_EXPIRED, "3", 60);
//
//        // when
//        persister.incRateCounter(intervalInSeconds, CREDENTIAL_IS_EXPIRED);
//
//        // then
//        assertThat(redisTemplate.get(CREDENTIAL_IS_EXPIRED), equalTo("1"));
//    }

    private class MockCassandraKeyValueRepository extends CassandraKeyValueRepository<String, String> {
        private Map<String, String> entries;

        public MockCassandraKeyValueRepository() {
            super(mock(CassandraClient.class), "x", "y", "z", String.class);
            entries = Maps.newHashMap();
        }

        @Override
        public Optional<String> get(String key) {
            checkAndThrowException(key);
            return Optional.ofNullable(entries.get(key));
        }

        @Override
        public Optional<Pair<String, Integer>> getWithTTL(String key) {
            checkAndThrowException(key);
            return get(key).map(val -> Pair.of(val, 30));
        }

        @Override
        public void set(String key, String value, int ttl) {
            checkAndThrowException(key);
            entries.put(key, value);
        }

        @Override
        public void expire(String key, int ttl) {
            checkAndThrowException(key);
        }

        @Override
        public void delete(String key) {
            checkAndThrowException(key);
            entries.remove(key);
        }

        private void checkAndThrowException(String key) {
            if (FAILING_CREDENTIAL.equals(key)) {
                throw new IllegalStateException();
            }
        }
    }
}
