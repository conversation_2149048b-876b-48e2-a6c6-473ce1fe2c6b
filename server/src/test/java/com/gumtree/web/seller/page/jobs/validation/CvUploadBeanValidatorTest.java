package com.gumtree.web.seller.page.jobs.validation;

import com.gumtree.web.seller.page.jobs.model.CvUploadBean;
import org.junit.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertThat;

public class CvUploadBeanValidatorTest {

    CvUploadBeanValidator validator = new CvUploadBeanValidator();

    @Test
    public void shouldSucceedForValidFile() {
        //given
        MultipartFile file = new MockMultipartFile("cv", "test.pdf", "application/pdf", new byte[5]);
        CvUploadBean bean = new CvUploadBean();
        bean.setFile(file);
        BindingResult result = new BeanPropertyBindingResult(bean, "form");

        //when
        validator.validate(bean, result);

        //then
        assertThat(result.hasErrors(), is(false));
    }

    @Test
    public void shouldFailIfFileTooLarge() {
        //given
        MultipartFile file = new MockMultipartFile("cv", "test.pdf", "application/pdf", new byte[8 * 1024 * 1024 + 1]);
        CvUploadBean bean = new CvUploadBean();
        bean.setFile(file);
        BindingResult result = new BeanPropertyBindingResult(bean, "form");

        //when
        validator.validate(bean, result);

        //then
        assertThat(result.hasErrors(), is(true));
        assertThat(result.getGlobalErrorCount(), is(0));
        assertThat(result.getFieldErrorCount(), is(1));
        assertThat(result.getFieldError("file").getCode(), equalTo("upload.cv.file.too.large"));
    }

    @Test
    public void shouldFailIfInvalidFileType() {
        //given
        MultipartFile file = new MockMultipartFile("cv", "test.exe", "application/x-msdownload", new byte[5]);
        CvUploadBean bean = new CvUploadBean();
        bean.setFile(file);
        BindingResult result = new BeanPropertyBindingResult(bean, "form");

        //when
        validator.validate(bean, result);

        //then
        assertThat(result.hasErrors(), is(true));
        assertThat(result.getGlobalErrorCount(), is(0));
        assertThat(result.getFieldErrorCount(), is(1));
        assertThat(result.getFieldError("file").getCode(), equalTo("upload.cv.invalid.type"));
    }

    @Test
    public void shouldFailIfFileEmpty() {
        //given
        MultipartFile file = new MockMultipartFile("cv", "test.pdf", "application/pdf", new byte[0]);
        CvUploadBean bean = new CvUploadBean();
        bean.setFile(file);
        BindingResult result = new BeanPropertyBindingResult(bean, "form");

        //when
        validator.validate(bean, result);

        //then
        assertThat(result.hasErrors(), is(true));
        assertThat(result.getGlobalErrorCount(), is(0));
        assertThat(result.getFieldErrorCount(), is(1));
        assertThat(result.getFieldError("file").getCode(), equalTo("upload.cv.empty.file"));
    }

}