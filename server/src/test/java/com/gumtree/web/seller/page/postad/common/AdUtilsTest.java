package com.gumtree.web.seller.page.postad.common;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.Attribute;
import org.junit.Test;

import java.util.Optional;

import static org.fest.assertions.api.Assertions.assertThat;


public class AdUtilsTest {

    @Test
    public void findAttributeReturnsOk() {
        // given
        Ad ad = advertWithAttributes(new Attribute("price", "10"), new Attribute("vrn", "abc"));

        // when
        Optional<Attribute> price = AdUtils.findAttribute(ad, "price");

        // then
        assertThat(price.isPresent()).isTrue();
        assertThat(price.get().getName()).isEqualTo("price");
        assertThat(price.get().getValue()).isEqualTo("10");
    }

    @Test
    public void findAttributeReturnsEmptyWhenNoAttributes() {
        // given
        Ad ad = advertWithAttributes();

        // when
        Optional<Attribute> price = AdUtils.findAttribute(ad, "price");

        // then
        assertThat(price.isPresent()).isFalse();
    }

    @Test
    public void findAttributeReturnsEmptyWhenAttributeIsNotFound() {
        // given
        Ad ad = advertWithAttributes(new Attribute("model", "x"), new Attribute("vrn", "abc"));

        // when
        Optional<Attribute> price = AdUtils.findAttribute(ad, "price");

        // then
        assertThat(price.isPresent()).isFalse();
    }


     /*
        utility methods
     */

    private static Ad advertWithAttributes(Attribute... attrs) {
        Ad ad = new Ad();
        ad.setId(1L);
        ad.setStatus(AdStatus.LIVE);
        if (attrs.length > 0) {
            ad.setAttributes(attrs);
        }
        return ad;
    }
}

