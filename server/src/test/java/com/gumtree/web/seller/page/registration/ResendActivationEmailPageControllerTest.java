package com.gumtree.web.seller.page.registration;

import com.gumtree.api.Bushfire<PERSON>pi<PERSON>ey;
import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCall;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.UserApiErrors;
import com.gumtree.user.service.model.UserResponse;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.login.controller.LoginController;
import com.gumtree.web.seller.page.registration.model.ConfirmationModel;
import com.gumtree.web.seller.page.registration.model.ResendActivationModel;
import com.gumtree.web.seller.service.user.forgotpassword.PasswordResetService;
import com.gumtree.web.zeno.userregistration.UserActivationBeginZenoEvent;
import com.gumtree.zeno.core.service.ZenoService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.ui.ExtendedModelMap;
import org.springframework.ui.Model;
import org.springframework.validation.support.BindingAwareModelMap;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.mvc.support.RedirectAttributesModelMap;
import org.springframework.web.servlet.view.RedirectView;

import java.io.IOException;
import java.util.Optional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertFalse;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;

@RunWith(MockitoJUnitRunner.class)
public class ResendActivationEmailPageControllerTest extends BaseSellerControllerTest {

    private ResendActivationEmailPageController controller;

    @Mock
    private UserApi mockUserApi;
    @Mock
    private RedirectAttributes redirectAttributes;
    @Mock
    private ApiCallResponse apiCallResponse;
    @Mock
    private Model model;
    @Mock
    private UserSession authenticatedUserSession;
    @Mock
    private BushfireApi bushfireApi;
    @Mock
    private ZenoService zenoService;
    @Mock
    private PasswordResetService passwordResetService;

    @Mock
    private UserServiceFacade userServiceFacade;

    @Mock
    private ApiResponse<UserResponse> userResponse;

    public static final String EXPERIMENT_PAGE_PATH = "/new-resend-activation";
    public static final String EMAIL = "<EMAIL>\"";

    private User user;

    @Before
    public void init() {
        BushfireApiKey apiKey = new BushfireApiKey();
        user = new User();
        user.setStatus(UserStatus.AWAITING_ACTIVATION);

        when(authenticatedUserSession.getApiKey()).thenReturn(apiKey);
        when(authenticatedUserSession.getUsername()).thenReturn(EMAIL);
        ReflectionTestUtils.setField(user, "email", EMAIL);
        ReflectionTestUtils.setField(user, "id", 123L);
        when(authenticatedUserSession.getUser()).thenReturn(user);

        when(bushfireApi.create(UserApi.class)).thenReturn(mockUserApi);
        when(bushfireApi.userApi()).thenReturn(mockUserApi);
        when(mockUserApi.getUser(user.getEmail())).thenReturn(user);
        when(mockUserApi.getUser(user.getId())).thenReturn(user);

        when(apiCallExecutor.call(Matchers.<ApiCall<Object>>any())).thenReturn(apiCallResponse);
        when(apiCallResponse.getResponseObject()).thenReturn(user);

        messageResolver = mock(ErrorMessageResolver.class);
        when(messageResolver.getMessage(anyString(), anyString())).thenAnswer(invocationOnMock -> invocationOnMock.getArguments()[0]);

        controller = new ResendActivationEmailPageController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme,
                authenticatedUserSession, userSessionService, bushfireApi, zenoService, passwordResetService,userServiceFacade);

        autowireAbExperimentsService(controller);
    }

    @Test
    public void returnLoginFormAction() throws IOException {
        //given-when
        String loginFormAction = controller.loginFormAction();

        //then
        assertThat(loginFormAction, equalTo(LoginController.PAGE_PATH));
    }

    @Test
    public void whenResendSuccessfulModelIsPopulated() throws IOException {

        //Given
        when(apiCallResponse.isErrorResponse()).thenReturn(false);
        when(mockUserApi.getUser(user.getEmail())).thenReturn(user);
        ArgumentCaptor<ApiCall> apiCallCaptor = ArgumentCaptor.forClass(ApiCall.class);
        when(userServiceFacade.getUserById(Matchers.any()))
                .thenReturn(ApiResponse.of(new UserResponse().userId(123L).userEmail(EMAIL)));

        //When
        model = new BindingAwareModelMap();
        ModelAndView view = requestPageAndFollowRedirect(Optional.empty());

        //Then call to the BAPI has been done
        verify(apiCallExecutor).call(apiCallCaptor.capture());
        ApiCall apiCall = apiCallCaptor.getValue();
        apiCall.execute(bushfireApi);

        //And proper model and view has been built
        assertThat(view.getViewName(), equalTo(Page.RegistrationConfirmation.getTemplateName()));
        ConfirmationModel model = (ConfirmationModel) view.getModel().get("model");
        assertThat(model.getEmailAddress(), equalTo(user.getEmail()));

        verify(zenoService, times(1)).logEvent(new UserActivationBeginZenoEvent(user));
        verifyNoMoreInteractions(zenoService);
    }

    @Test
    public void whenResendWithEmailSuccessfulModelIsPopulated() throws IOException {

        //Given
        when(apiCallResponse.isErrorResponse()).thenReturn(false);
        ArgumentCaptor<ApiCall> apiCallCaptor = ArgumentCaptor.forClass(ApiCall.class);
        when(userServiceFacade.getUserById(Matchers.any()))
                .thenReturn(ApiResponse.of(new UserResponse().userId(123L).userEmail(EMAIL)));

        //When
        model = new BindingAwareModelMap();
        ModelAndView view = requestPageAndFollowRedirect(Optional.of(user.getId()));

        //Then call to the BAPI has been done
        verify(apiCallExecutor).call(apiCallCaptor.capture());
        ApiCall apiCall = apiCallCaptor.getValue();
        apiCall.execute(bushfireApi);

        //And proper model and view has been built
        assertThat(view.getViewName(), equalTo(Page.RegistrationConfirmation.getTemplateName()));
        ConfirmationModel model = (ConfirmationModel) view.getModel().get("model");
        assertThat(model.getEmailAddress(), equalTo(user.getEmail()));
    }

    @Test
    public void userReturnedToLoginPageWhenResendFails() throws IOException {

        //Given the BAPI service fails
        when(apiCallResponse.isErrorResponse()).thenReturn(true);
        when(userServiceFacade.getUserById(Matchers.any()))
                .thenReturn(ApiResponse.of(new UserResponse().userId(123L).userEmail(EMAIL)));

        //When
        View view = requestPageAndFollowRedirect(Optional.empty()).getView();

        //Then errors has been reported to error reporter
        ArgumentCaptor<ReportableErrorsMessageResolvingErrorSource> errorReporterCaptor = ArgumentCaptor
                .forClass(ReportableErrorsMessageResolvingErrorSource.class);
        verify(model).addAttribute(eq("errors"), errorReporterCaptor.capture());
        ReportableErrorsMessageResolvingErrorSource errorReporter = errorReporterCaptor.getValue();
        verify(apiCallResponse).report(errorReporter);

        //And proper model and view has been built
        verifyZeroInteractions(redirectAttributes);

        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(LoginController.PAGE_PATH));
    }

    @Test
    public void userReturnedToLoginPageWhenResendWithEmailFails() throws IOException {

        //Given the BAPI service fails
        when(apiCallResponse.isErrorResponse()).thenReturn(true);
        when(userServiceFacade.getUserById(Matchers.any()))
                .thenReturn(ApiResponse.of(new UserResponse().userId(123L).userEmail(EMAIL)));

        //When
        View view = requestPageAndFollowRedirect(Optional.of(user.getId())).getView();

        //Then errors has been reported to error reporter
        ArgumentCaptor<ReportableErrorsMessageResolvingErrorSource> errorReporterCaptor = ArgumentCaptor
                .forClass(ReportableErrorsMessageResolvingErrorSource.class);
        verify(model).addAttribute(eq("errors"), errorReporterCaptor.capture());
        ReportableErrorsMessageResolvingErrorSource errorReporter = errorReporterCaptor.getValue();
        verify(apiCallResponse).report(errorReporter);

        //And proper model and view has been built
        verifyZeroInteractions(redirectAttributes);

        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(LoginController.PAGE_PATH));
    }

    @Test
    public void passwordResetIsSentIfUserIsAlreadyActive() throws Exception {
        //Given the BAPI service informs that the user is already active
        when(apiCallResponse.isErrorResponse()).thenReturn(true);
        when(apiCallResponse.getErrorCode()).thenReturn(ApiErrorCode.ALREADY_ACTIVATED);
        when(userServiceFacade.getUserById(Matchers.any()))
                .thenReturn(ApiResponse.of(new UserResponse().userId(123L).userEmail(EMAIL)));

        //When
        ModelAndView modelAndView = requestPageAndFollowRedirect(Optional.of(user.getId()));

        //Then the proper model and view has been built
        assertThat(modelAndView.getViewName(), equalTo(Page.RegistrationConfirmation.getTemplateName()));
        ConfirmationModel model = (ConfirmationModel) modelAndView.getModel().get("model");
        assertThat(model.getEmailAddress(), equalTo(user.getEmail()));
        //And the password reset service is called for the user
        verify(passwordResetService).resetPassword(user.getEmail());
        verify(zenoService).logEvent(Mockito.any());
    }

    @Test
    public void resultsPageLogsRegistrationEventIfUserIsAwaitingActivation() throws Exception {
        //Given
        when(apiCallResponse.isErrorResponse()).thenReturn(false);
        when(userServiceFacade.getUserById(Matchers.any()))
                .thenReturn(ApiResponse.of(new UserResponse().userId(123L).userEmail(EMAIL)));

        //When
        requestPageAndFollowRedirect(Optional.of(user.getId()));

        //Then Zeno event is logged
        verify(zenoService).logEvent(new UserActivationBeginZenoEvent(user));
        verify(passwordResetService, never()).resetPassword(user.getEmail());
    }

    @Test
    public void resendActivationFailsWhenUserResponseNotDefined() throws Exception {
        //Given
        when(apiCallResponse.isErrorResponse()).thenReturn(false);
        when(userServiceFacade.getUserById(Matchers.any()))
                .thenReturn(ApiResponse.error(new UserApiErrors().errorCode("Error1")));

        //When
        requestPageAndFollowRedirect(Optional.of(user.getId()));

        //Then Zeno event is logged
        verifyZeroInteractions(zenoService);
        verifyZeroInteractions(passwordResetService);
        verifyZeroInteractions(bushfireApi);
    }

    private ModelAndView requestPageAndFollowRedirect(Optional<Long> userId) throws IOException {
        RedirectAttributesModelMap map = new RedirectAttributesModelMap();
        ModelAndView result;
        if (userId.isPresent()) {
            result = controller.resendActivation(userId.get(),model, map);
        } else {
            result = controller.resendActivation(model, map);
        }


        //and following HTTP redirect
        RedirectView view = (RedirectView) result.getView();
        if (view.getUrl().equals("/resend-activation-result")) {
            Model model = new ExtendedModelMap();
            model.addAllAttributes(map.getFlashAttributes());

            return controller.resendActivationResult(request, model);
        } else {
            return result;
        }
    }

    @Test
    public void newResendActivationLogsRegistrationEventIfUserIsAwaitingActivation() throws Exception {
        //Given
        when(apiCallResponse.isErrorResponse()).thenReturn(false);

        //When
        ResponseEntity<ResendActivationModel> responseEntity = controller.newResendActivation(user.getId());

        //Then
        verify(zenoService).logEvent(new UserActivationBeginZenoEvent(user));
        verify(passwordResetService, never()).resetPassword(user.getEmail());
        ResendActivationModel  resendActivationModel = responseEntity.getBody();
        assertThat(resendActivationModel.getResendPath(), equalTo(buildResendPath(user.getId())));
        assertThat(responseEntity.getStatusCode(), equalTo(HttpStatus.OK));
    }

    @Test
    public void newResendActivationPasswordResetIsSentIfUserIsAlreadyActive() throws Exception {
        //Given the BAPI service informs that the user is already active
        when(apiCallResponse.isErrorResponse()).thenReturn(true);
        when(apiCallResponse.getErrorCode()).thenReturn(ApiErrorCode.ALREADY_ACTIVATED);
        when(userServiceFacade.getUserById(Matchers.any()))
                .thenReturn(ApiResponse.of(new UserResponse().userId(123L).userEmail(EMAIL)));

        //When
        ResponseEntity<ResendActivationModel> responseEntity = controller.newResendActivation(user.getId());

        //Then
        ResendActivationModel  resendActivationModel = responseEntity.getBody();
        assertThat(resendActivationModel.getResendPath(), equalTo(buildResendPath(user.getId())));
        verify(passwordResetService).resetPassword(user.getEmail());
        assertThat(responseEntity.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
    }

    @Test
    public void newResendErrorsReturnedWhenCallingBapiApi() throws Exception {
        //Given
        when(apiCallResponse.isErrorResponse()).thenReturn(true);
        when(apiCallResponse.getErrorCode()).thenReturn(ApiErrorCode.NOT_FOUND);
        when(userServiceFacade.getUserById(Matchers.any()))
                .thenReturn(ApiResponse.of(new UserResponse().userId(123L).userEmail(EMAIL)));

        //When
        ResponseEntity<ResendActivationModel> responseEntity = controller.newResendActivation(user.getId());

        //Then
        ResendActivationModel  resendActivationModel = responseEntity.getBody();
        assertThat(resendActivationModel.getResendPath(), equalTo(buildResendPath(user.getId())));

        assertThat(responseEntity.getStatusCode(), equalTo(HttpStatus.NOT_FOUND));

        assertFalse(resendActivationModel.getErrors().isEmpty());
        verifyZeroInteractions(passwordResetService);
        verifyZeroInteractions(zenoService);
    }

    @Test
    public void newResendReturnBadRequestIfUserIdMissing() throws Exception {
        //Given
        Long userId = null;

        //When
        ResponseEntity<ResendActivationModel> responseEntity = controller.newResendActivation(userId);

        //Then
        assertThat(responseEntity.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
    }

    private String buildResendPath(Long userId){
        return EXPERIMENT_PAGE_PATH + "/" + userId;
    }

}
