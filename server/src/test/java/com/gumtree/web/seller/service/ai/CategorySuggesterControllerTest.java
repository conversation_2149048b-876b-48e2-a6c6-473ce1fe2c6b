package com.gumtree.web.seller.service.ai;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.util.HeadersExtensions;
import com.gumtree.web.abtest.growthbook.ClientExperiments;
import com.gumtree.web.seller.page.ajax.category.CategorySuggesterController;
import com.gumtree.web.seller.page.ajax.category.SuggestedCategories;
import com.gumtree.web.seller.page.ajax.category.SuggestedCategory;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.service.category.CategorySuggesterService;
import com.gumtree.web.seller.service.jobs.JobsSuggesterService;
import com.gumtree.web.seller.storage.DraftAdvertService;
import io.micrometer.core.instrument.Timer;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CategorySuggesterControllerTest {
    @InjectMocks
    private CategorySuggesterController controller;
    @Mock
    private CustomMetricRegistry metrics;
    @Mock
    private CategoryModel categoryModel;
    @Mock
    private JobsSuggesterService jobsSuggesterService;
    @Mock
    private CategorySuggesterService categorySuggesterService;
    @Mock
    private AISuggestService aiSuggestService;
    @Mock
    private DraftAdvertService draftAdvertService;
    @Mock
    private HttpServletRequest request;
    @Mock
    Timer mockTimer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testAISuggestCategory_titleType() {
        String input = "car";
        String type = "title";
        List<Long> catIds = Arrays.asList(1L, 2L);
        when(aiSuggestService.getSuggestedCategories(input, type)).thenReturn(catIds);
        Category cat1 = mock(Category.class);
        when(cat1.getId()).thenReturn(1L);
        when(cat1.getDisplayName()).thenReturn("Car");
        when(cat1.getEnabled()).thenReturn(true);
        when(cat1.getReadOnly()).thenReturn(false);
        when(cat1.isHidden()).thenReturn(false);
        when(cat1.isAdPostingPermitted()).thenReturn(true);
        when(categoryModel.getCategory(1L)).thenReturn(Optional.of(cat1));
        when(categoryModel.getBreadcrumb(1L, ",")).thenReturn("Motors,Car");
        Category cat2 = mock(Category.class);
        when(cat2.getId()).thenReturn(2L);
        when(cat2.getDisplayName()).thenReturn("Truck");
        when(cat2.getEnabled()).thenReturn(true);
        when(cat2.getReadOnly()).thenReturn(false);
        when(cat2.isHidden()).thenReturn(false);
        when(cat2.isAdPostingPermitted()).thenReturn(true);
        when(categoryModel.getCategory(2L)).thenReturn(Optional.of(cat2));
        when(categoryModel.getBreadcrumb(2L, ",")).thenReturn("Motors,Truck");

        when(metrics.AIResponseTimer(anyString())).thenReturn(mockTimer);
        SuggestedCategories result = controller.AISuggestCategory(input, type);
        assertThat(result.getCategories()).hasSize(2);
        assertThat(result.getCategories().get(0).getDisplayName()).isEqualTo("Car");
        assertThat(result.getCategories().get(1).getDisplayName()).isEqualTo("Truck");
    }

    @Test
    public void testAISuggestCategoryAttributes_successAndPersist() {
        String input = "car";
        String type = "title";
        String categoryName = "Cars";
        Map<String, String> attr = new HashMap<>();
        attr.put("color", "red");
        Timer timer = mock(Timer.class);
        when(metrics.AIResponseTimer(anyString())).thenReturn(timer);

        when(aiSuggestService.getSuggestedAttributes(input, type, categoryName)).thenReturn(attr);
        when(draftAdvertService.persist(any(PostAdDetail.class))).thenReturn(true);
    }

    @Test
    public void testAISuggestCategoryAttributes_noDraft() {
        String input = "car";
        String type = "title";
        String categoryName = "Cars";
        Map<String, String> attr = new HashMap<>();
        attr.put("color", "red");
        when(metrics.AIResponseTimer(anyString())).thenReturn(mockTimer);
        when(aiSuggestService.getSuggestedAttributes(input, type, categoryName)).thenReturn(attr);
        when(draftAdvertService.retrieve()).thenReturn(Optional.absent());
        Map<String, String> result = controller.AISuggestCategoryAttributes(input, type, categoryName);
        assertThat(result).isNull();
        verify(draftAdvertService, never()).persist(any(PostAdDetail.class));
    }

    @Test
    public void testAISuggestCategoryAttributes_withExistingDraft_shouldReturnAttributesAndPersist() {
        // 准备测试数据
        String input = "test input";
        String type = "title";
        String categoryName = "test category";

        Map<String, String> suggestedAttributes = new HashMap<>();
        suggestedAttributes.put("brand", "Nike");
        suggestedAttributes.put("color", "red");

        PostAdDetail postAdDetail = new PostAdDetail();
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdDetail.setPostAdFormBean(postAdFormBean);

        // 配置mock行为
        when(metrics.AIResponseTimer(anyString())).thenReturn(mockTimer);
        when(aiSuggestService.getSuggestedAttributes(input, type, categoryName)).thenReturn(suggestedAttributes);
        when(draftAdvertService.retrieve()).thenReturn(Optional.of(postAdDetail));
        when(draftAdvertService.persist(any(PostAdDetail.class))).thenReturn(true);

        // 执行测试
        Map<String, String> result = controller.AISuggestCategoryAttributes(input, type, categoryName);

        // 验证结果
//        assertNotNull(result);
//        assertEquals(suggestedAttributes, result);
//        assertEquals(suggestedAttributes, postAdDetail.getPostAdFormBean().getAttributes());
        verify(draftAdvertService).persist(postAdDetail);
    }

    @Test
    public void testDeduplicateByIdKeepFirst() {
        List<SuggestedCategory> list = new ArrayList<>();
        list.add(new SuggestedCategory(1L, "A", "a"));
        list.add(new SuggestedCategory(2L, "B", "b"));
        list.add(new SuggestedCategory(1L, "A2", "a2"));
        List<SuggestedCategory> result = CategorySuggesterController.deduplicateByIdKeepFirst(list);
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(1).getId()).isEqualTo(2L);
    }

    @Test
    public void testIsAdPostingPermitted_true() {
        Category cat = mock(Category.class);
        when(cat.getEnabled()).thenReturn(true);
        when(cat.getReadOnly()).thenReturn(false);
        when(cat.isHidden()).thenReturn(false);
        when(cat.isAdPostingPermitted()).thenReturn(true);
        boolean permitted = CategorySuggesterController.isAdPostingPermitted(cat);
        assertThat(permitted).isTrue();
    }

    @Test
    public void testIsAdPostingPermitted_false() {
        Category cat = mock(Category.class);
        when(cat.getEnabled()).thenReturn(false);
        when(cat.getReadOnly()).thenReturn(true);
        when(cat.isHidden()).thenReturn(true);
        when(cat.isAdPostingPermitted()).thenReturn(false);
        boolean permitted = CategorySuggesterController.isAdPostingPermitted(cat);
        assertThat(permitted).isFalse();
    }

//    @Test
//    public void testSuggestCategory_experimentD() {
//        // Arrange
//        String input = "car";
//        int top = 5;
//        List<SuggestedCategory> allNameList = Arrays.asList(
//                new SuggestedCategory(1L, "A", "a"),
//                new SuggestedCategory(2L, "B", "b")
//        );
//        List<SuggestedCategory> textMatchList = Arrays.asList(
//                new SuggestedCategory(3L, "C", "c")
//        );
//        List<SuggestedCategory> predictList = Arrays.asList(
//                new SuggestedCategory(4L, "D", "d")
//        );
//        // Mock实验分组D
//        ClientExperiments clientExperiments = mock(ClientExperiments.class);
//        when(request.getHeader(anyString())).thenReturn("syi_cate_sug_flag.D");
//        when(clientExperiments.isD(any())).thenReturn(true);
//        when(clientExperiments.toGamExperimentsString()).thenReturn("syi_cate_sug_flag.D");
//        mock(HeadersExtensions.class);
//        when(HeadersExtensions.getClientExperiments(request)).thenReturn(clientExperiments);
//        // Mock service
//        when(categorySuggesterService.getSuggestedCategoriesAllName(input, 1)).thenReturn(allNameList);
//        when(categorySuggesterService.getSuggestedTextMatchCategories(input, false, top)).thenReturn(textMatchList);
//        when(categorySuggesterService.getCategoryPredictApi(input, top)).thenReturn(predictList);
//        // Act
//        SuggestedCategories result = controller.suggestCategory(input, request);
//        // Assert
//        assertThat(result.getCategories()).hasSize(4);
//        assertThat(result.getCategories().get(0).getDisplayName()).isEqualTo("A");
//        assertThat(result.getCategories().get(1).getDisplayName()).isEqualTo("B");
//        assertThat(result.getCategories().get(2).getDisplayName()).isEqualTo("C");
//        assertThat(result.getCategories().get(3).getDisplayName()).isEqualTo("D");
//    }
}
