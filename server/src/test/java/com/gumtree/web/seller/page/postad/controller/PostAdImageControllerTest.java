package com.gumtree.web.seller.page.postad.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.gumtree.api.Image;
import com.gumtree.api.Images;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.config.SellerProperty;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.converter.PostAdImageConverter;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdImage;
import com.gumtree.web.seller.page.postad.model.PostAdImageUpload;
import com.gumtree.web.seller.service.PostAdWorkspace;
import com.gumtree.web.seller.service.image.ImageMeterRegistry;
import com.gumtree.web.seller.service.image.ImageUploadService;
import com.gumtree.web.seller.service.image.error.BapiLegacyImageException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PostAdImageControllerTest {

    public static final String USER_AGENT_OF_SOME_SORT = "user agent of some sort";

    @Mock
    private MultipartFile multipartFile;
    @Mock
    private PostAdWorkspace postAdWorkspace;
    @Mock
    private ImageUploadService imageUploadService;
    @Mock
    private AdvertEditor advertEditor;

    @Mock
    private PostAdImageConverter postAdImageConverter;
    @Mock
    private ImageMeterRegistry imageMetricRegistry;

    @InjectMocks
    private PostAdImageController postAdImageController;

    private final Long imageId = 1L;
    private final String editorId = "editorId";
    private final String size = "2000000";
    private final String thumbnailUrl = "http://i.sandbox.ebayimg.com/t.jpg";
    private final String url = "http://i.sandbox.ebayimg.com/u.jpg";
    private final String filename = "a.jpg";
    private Image image;
    private PostAdFormBean postAdFormBean;
    private final ObjectMapper objectMapper = new ObjectMapper();

    static {
        GtPropManager.setProperty(SellerProperty.MESSAGE_CENTRE_ENABLED.getPropertyName(), true);
        GtPropManager.setProperty(SellerProperty.MESSAGE_CENTRE_LINK_ENABLED.getPropertyName(), true);
        GtPropManager.setProperty(SellerProperty.MAX_IMAGE_UPLOAD_SIZE.getPropertyName(), "1000");
    }

    @Before
    public void before() {

        CookieResolver cookieResolver = null;
        CategoryModel categoryModel = null;
        ApiCallExecutor apiCallExecutor = null;
        ErrorMessageResolver messageResolver = null;
        UrlScheme urlScheme = null;
        UserSession authenticatedUserSession = null;
        CategoryService categoryService = null;
        GumtreePageContext pageContext = null;
        LocationService locationService = null;
        UserSessionService userSessionService = null;
        postAdFormBean = new PostAdFormBean();

        postAdImageController = new PostAdImageController(cookieResolver, categoryModel,
                apiCallExecutor, messageResolver, urlScheme,
                postAdWorkspace, authenticatedUserSession, categoryService, imageUploadService, postAdImageConverter,
                pageContext, locationService, userSessionService, imageMetricRegistry);

        when(postAdWorkspace.getEditor(editorId)).thenReturn(advertEditor);
        when(advertEditor.getPostAdFormBean()).thenReturn(postAdFormBean);
        PostAdDetail detail = new PostAdDetail();
        detail.setPostAdFormBean(postAdFormBean);
        when(advertEditor.getAdvertDetail()).thenReturn(detail);
        when(multipartFile.getOriginalFilename()).thenReturn(filename);
        when(advertEditor.getEditorId()).thenReturn(editorId);

        image = new Image();
        image.setId(imageId);
    }

    @Test
    public void shouldRejectUploadingMoreThanMaxImages() throws IOException {
        //given
        when(multipartFile.getSize()).thenReturn(100L);
        List<Image> images = Lists.newArrayList(image,image,image,image,image,image,image,image,image,
                image,image,image,image,image,image,image,image,image,image,image);
        when(postAdWorkspace.getEditor(editorId)).thenReturn(advertEditor);
        when(advertEditor.getImages()).thenReturn(images);

        int position = 20;

        //when
        ResponseEntity<String> response
                = postAdImageController.uploadImages(multipartFile, position, editorId, USER_AGENT_OF_SOME_SORT);

        //then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.PRECONDITION_FAILED);
        assertThat(response.getBody().isEmpty()).isFalse();

        verify(imageMetricRegistry).tooMany();
    }

    @Test
    public void shouldFailToUploadLargeImage() throws IOException {

        when(multipartFile.getSize()).thenReturn(10000L);

        int position = 0;

        when(advertEditor.getImages()).thenReturn(Collections.emptyList());
        when(advertEditor.getImages()).thenReturn(Lists.newArrayList(image));

        PostAdImage.Builder postAdImageBuilder = new PostAdImage.Builder().id(imageId).size(size)
                .thumbnailUrl(thumbnailUrl).url(url);
        when(postAdImageConverter.convertImageToPostAdImage(image)).thenReturn(postAdImageBuilder.build());

        ResponseEntity<String> response
                = postAdImageController.uploadImages(multipartFile, position, editorId, USER_AGENT_OF_SOME_SORT);

        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.REQUEST_ENTITY_TOO_LARGE);

        verify(imageMetricRegistry).tooLarge();
        verifyZeroInteractions(advertEditor);
    }

    @Test
    public void shouldFailToUploadLargeBAPIErrors() throws IOException {

        int position = 0;

        when(advertEditor.getImages()).thenReturn(Collections.emptyList());
        when(imageUploadService.postImage(multipartFile)).thenThrow(new RuntimeException());
        when(advertEditor.getImages()).thenReturn(Lists.newArrayList(image));

        PostAdImage.Builder postAdImageBuilder = new PostAdImage.Builder().id(imageId).size(size)
                .thumbnailUrl(thumbnailUrl).url(url);
        when(postAdImageConverter.convertImageToPostAdImage(image)).thenReturn(postAdImageBuilder.build());

        ResponseEntity<String> response
                = postAdImageController.uploadImages(multipartFile, position, editorId, USER_AGENT_OF_SOME_SORT);

        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_GATEWAY);

        verify(imageMetricRegistry).storageFailed();
    }

    @Test
    public void shouldUploadAnImageToNewAdvert() throws IOException {
        //given
        int position = 0;

        when(advertEditor.getImages()).thenReturn(Collections.emptyList());
        when(imageUploadService.postImage(multipartFile)).thenReturn(image);
        when(advertEditor.getImages()).thenReturn(Lists.newArrayList(image));

        PostAdImage.Builder postAdImageBuilder = new PostAdImage.Builder().id(imageId).size(size)
                .thumbnailUrl(thumbnailUrl).url(url);
        when(postAdImageConverter.convertImageToPostAdImage(image)).thenReturn(postAdImageBuilder.build());

        //when
        ResponseEntity<String> response
                = postAdImageController.uploadImages(multipartFile, position, editorId, PostAdImageController.MSIE_9_0);

        //then
        verify(postAdWorkspace, times(1)).updateEditor(editorId, advertEditor);

        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        PostAdImageUpload postAdImageUpload = objectMapper.readValue(response.getBody(), PostAdImageUpload.class);
        assertThat(postAdImageUpload.getId()).isEqualTo(imageId);
        assertThat(postAdImageUpload.getFileName()).isEqualTo(filename);
        assertThat(postAdImageUpload.getPosition()).isEqualTo(position);
        assertThat(postAdImageUpload.getThumbnailUrl()).isEqualTo(thumbnailUrl);
        assertThat(postAdImageUpload.getUrl()).isEqualTo("//i.sandbox.ebayimg.com/u.jpg");

        verify(imageMetricRegistry).uploaded();
    }

    @Test
    public void shouldAppendNewImageToAdvertWithExistingAdverts() throws IOException {
        //given
        Image newImage = new Image();
        long newImageId = imageId + 1;
        newImage.setId(newImageId);

        when(advertEditor.getImages()).thenReturn(Lists.newArrayList(image));
        when(imageUploadService.postImage(multipartFile)).thenReturn(newImage);
        when(advertEditor.getImages()).thenReturn(Lists.newArrayList(image, newImage));
        int position = 1;

        PostAdImage.Builder postAdImageBuilder = new PostAdImage.Builder().id(newImageId)
                .size(size).thumbnailUrl(thumbnailUrl).url(url);

        when(postAdImageConverter.convertImageToPostAdImage(newImage)).thenReturn(postAdImageBuilder.build());

        //when
        ResponseEntity<String> response
                = postAdImageController.uploadImages(multipartFile, position, editorId, USER_AGENT_OF_SOME_SORT);

        //then
        verify(postAdWorkspace, times(1)).updateEditor(editorId, advertEditor);

        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        PostAdImageUpload postAdImageUpload1 = objectMapper.readValue(response.getBody(), PostAdImageUpload.class);
        assertThat(postAdImageUpload1.getId()).isEqualTo(newImageId);
        assertThat(postAdImageUpload1.getFileName()).isEqualTo(filename);
        assertThat(postAdImageUpload1.getPosition()).isEqualTo(position);
        assertThat(postAdImageUpload1.getThumbnailUrl()).isEqualTo(thumbnailUrl);
        assertThat(postAdImageUpload1.getUrl()).isEqualTo("//i.sandbox.ebayimg.com/u.jpg");

        verify(imageMetricRegistry).uploaded();
    }

    @Test
    public void shouldInsertNewImageToAdvertWithExistingAdverts() throws IOException {
        //given
        Image newImage = new Image();
        long newImageId = imageId + 1;
        newImage.setId(newImageId);

        when(advertEditor.getImages()).thenReturn(Lists.newArrayList(image));
        when(imageUploadService.postImage(multipartFile)).thenReturn(newImage);
        when(advertEditor.getImages()).thenReturn(Lists.newArrayList(image, newImage));
        int position = 0; // insert before existing image

        PostAdImage.Builder postAdImageBuilder = new PostAdImage.Builder().id(newImageId)
                .size(size).thumbnailUrl(thumbnailUrl).url(url);

        when(postAdImageConverter.convertImageToPostAdImage(newImage)).thenReturn(postAdImageBuilder.id(newImageId).build());

        //when
        ResponseEntity<String> response
                = postAdImageController.uploadImages(multipartFile, position, editorId, USER_AGENT_OF_SOME_SORT);

        //then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);

        verify(postAdWorkspace, times(1)).updateEditor(editorId, advertEditor);

        PostAdImageUpload postAdImageUpload1 = objectMapper.readValue(response.getBody(), PostAdImageUpload.class);
        assertThat(postAdImageUpload1.getId()).isEqualTo(newImageId);
        assertThat(postAdImageUpload1.getFileName()).isEqualTo(filename);
        assertThat(postAdImageUpload1.getPosition()).isEqualTo(position);
        assertThat(postAdImageUpload1.getThumbnailUrl()).isEqualTo(thumbnailUrl);
        assertThat(postAdImageUpload1.getUrl()).isEqualTo("//i.sandbox.ebayimg.com/u.jpg");

        verify(imageMetricRegistry).uploaded();
    }


    @Test
    public void shouldDeleteImage() {
        //given
        Image image = new Image();
        image.setId(imageId);

        when(advertEditor.getImages()).thenReturn(Lists.newArrayList(image));

        Images images = new Images();
        images.setImages(Lists.newArrayList(image));
        PostAdImageController.DeleteImageRequest deleteImageRequest = new PostAdImageController.DeleteImageRequest();
        deleteImageRequest.setId(String.valueOf(imageId));

        //when
        ResponseEntity<PostAdImageController.DeleteImageResponse> response
                = postAdImageController.deleteImage(editorId, deleteImageRequest);

        //then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getMessage()).isNull();
        assertThat(response.getBody().getId()).isEqualTo(imageId);
        assertThat(postAdFormBean.getImageIds()).isEmpty();
        verify(advertEditor, times(1)).removeImage(imageId);
        verify(postAdWorkspace, times(1)).updateEditor(editorId, advertEditor);
    }

    @Test
    public void shouldOrderImages() {
        //given
        Long imageId1 = 1L;
        Long imageId2 = 2L;
        Long imageId3 = 3L;

        List<String> imgIds = Lists.newArrayList(String.valueOf(imageId3), String.valueOf(imageId1),
                String.valueOf(imageId2));

        PostAdImageController.OrderImagesRequest orderImagesRequest = new PostAdImageController.OrderImagesRequest();
        orderImagesRequest.setImgIds(imgIds);

        //when
        ResponseEntity<String> response = postAdImageController.orderImages(editorId, orderImagesRequest);

        //then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(postAdFormBean.getImageIds()).containsExactly(imageId3, imageId1, imageId2);
        verify(postAdWorkspace, times(1)).updateEditor(editorId, advertEditor);

    }

    @Test
    public void shouldReturnErrorCodeWhenUnexpectedExceptionOccur() throws IOException {
        // given
        List<Image> images = Lists.newArrayList(image,image,image,image,image,image,image,image);
        when(postAdWorkspace.getEditor(editorId)).thenReturn(advertEditor);
        when(advertEditor.getImages()).thenThrow(Exception.class);

        int position = 9;

        //when
        ResponseEntity<String> response
                = postAdImageController.uploadImages(multipartFile, position, editorId, USER_AGENT_OF_SOME_SORT);

        //then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        PostAdImageUpload postAdImageUpload = objectMapper.readValue(response.getBody(), PostAdImageUpload.class);
        assertThat(postAdImageUpload.getError().getCode()).isEqualTo(102);

        verify(imageMetricRegistry).storageFailed();
    }
}
