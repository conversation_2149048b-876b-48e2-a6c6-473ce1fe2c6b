package com.gumtree.web.seller.page.reviews.service;

import com.google.common.collect.ImmutableMap;
import com.gumtree.userreviewsservice.client.ReviewsReadApi;
import com.gumtree.userreviewsservice.client.model.ApiRatingBreakdownItem;
import com.gumtree.userreviewsservice.client.model.GetSummaryRatingReceivedResponse;
import com.gumtree.web.seller.page.reviews.model.UserRating;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import rx.Single;

import java.util.Map;
import java.util.Optional;

import static com.google.common.collect.Lists.newArrayList;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserReviewsServiceTest {
    @InjectMocks
    private UserReviewsService service;
    @Mock private ReviewsReadApi reviewsReadApi;
    private ReadReviewsMocks readReviewsMocks;

    @SuppressWarnings("Guava")
    @Before
    public void beforeEach() {
        readReviewsMocks = new ReadReviewsMocks();
    }

    @Test
    public void shouldGetSellerRating() {
        // given
        Long accountId = 100L;
        readReviewsMocks.givenRatingFoundForAccountId(accountId);

        // when
        Optional<UserRating> userRating = service.getUserRating(accountId).toBlocking().value();

        // then
        assertThat(userRating.get().getBreakdown()).isEqualTo(readReviewsMocks.getBreakDown());
        assertThat(userRating.get().getAverage()).isEqualTo(readReviewsMocks.getAverage());
        assertThat(userRating.get().getTotal()).isEqualTo(readReviewsMocks.getTotalReceived());
        assertThat(userRating.get().getFormattedAverage()).isEqualTo(readReviewsMocks.getFormattedAverage());
    }

    @Test
    public void shouldFormatAvgRating() {
        assertThat(UserReviewsService.formatAvgRating(4.6f)).isEqualTo("4.6");
        assertThat(UserReviewsService.formatAvgRating(4.64f)).isEqualTo("4.6");
        assertThat(UserReviewsService.formatAvgRating(4.65f)).isEqualTo("4.7");
        assertThat(UserReviewsService.formatAvgRating(4.66f)).isEqualTo("4.7");
        assertThat(UserReviewsService.formatAvgRating(null)).isEqualTo("0");
    }

    private ApiRatingBreakdownItem createRatingItem(Integer rating, Integer count) {
        ApiRatingBreakdownItem item = new ApiRatingBreakdownItem();
        item.setRating(rating);
        item.setNumber(count);
        return item;
    }

    private class ReadReviewsMocks {
        private Map<Integer, Integer> breakDown = ImmutableMap.<Integer, Integer>builder()
                .put(1, 10)
                .put(2, 20)
                .put(3, 30)
                .put(4, 40)
                .put(5, 50)
                .build();

        private Float average = 3.7f;
        private Integer totalReceived = 10;

        public ReadReviewsMocks givenRatingFoundForAccountId(Long accountId) {
            GetSummaryRatingReceivedResponse response = new GetSummaryRatingReceivedResponse();
            response.setAverageRating(average);
            response.setTotalReceived(totalReceived);
            response.setBreakdown(newArrayList(
                    createRatingItem(1, 10),
                    createRatingItem(2, 20),
                    createRatingItem(3, 30),
                    createRatingItem(4, 40),
                    createRatingItem(5, 50)
            ));
            when(reviewsReadApi.findRatingReceivedByRevieweeId(accountId)).thenReturn(Single.just(response));
            return this;
        }

        public Map<Integer, Integer> getBreakDown() {
            return breakDown;
        }

        public Integer getTotalReceived() {
            return totalReceived;
        }

        public Float getAverage() {
            return average;
        }

        public String getFormattedAverage() {
            return average.toString();
        }
    }
}
