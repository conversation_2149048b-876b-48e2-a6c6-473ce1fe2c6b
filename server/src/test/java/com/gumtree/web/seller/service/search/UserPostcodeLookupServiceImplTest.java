package com.gumtree.web.seller.service.search;

import com.google.common.base.Optional;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.fulladsearch.model.FullAdFlatAd;
import com.gumtree.fulladsearch.model.FullAdSearchResult;
import com.gumtree.web.seller.service.postad.UserPostcodeLookupServiceImpl;
import org.junit.Test;
import org.mockito.Mockito;
import rx.Single;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class UserPostcodeLookupServiceImplTest {

    @Test
    public void testGetLastUSedPostcodeForNoResponse() {
        FullAdsSearchApi fullAdsSearchApi = mock(FullAdsSearchApi.class);
        UserPostcodeLookupServiceImpl userPostcodeLookupService = new UserPostcodeLookupServiceImpl(fullAdsSearchApi);
        FullAdSearchResult searchResult = new FullAdSearchResult();
        searchResult.total(0L);
        when(fullAdsSearchApi.search(Mockito.any())).thenReturn(Single.just(searchResult));
        //Call to original method
        Optional<String> postcode = userPostcodeLookupService.getLastUsedPostcode(123L);
        //Reponse should be absent as the Id sent has no reponse to return.
        assertEquals(Optional.absent(), postcode);
    }

    @Test
    public void testGetLastUSedPostcodeForResponse() {
        FullAdsSearchApi fullAdsSearchApi = mock(FullAdsSearchApi.class);
        UserPostcodeLookupServiceImpl userPostcodeLookupService = new UserPostcodeLookupServiceImpl(fullAdsSearchApi);

        FullAdFlatAd flatAd = new FullAdFlatAd();
        flatAd.setPostcode("TW91EJ");
        List<FullAdFlatAd> flatAddList = new ArrayList<>();
        flatAddList.add(flatAd);

        FullAdSearchResult searchResult = new FullAdSearchResult();
        searchResult.setTotal(1L);
        searchResult.setItems(flatAddList);

        when(fullAdsSearchApi.search(Mockito.any())).thenReturn(Single.just(searchResult));

        //Call to original method
        Optional<String> postcode = userPostcodeLookupService.getLastUsedPostcode(123L);
        //Reponse should be absent as the Id sent has no reponse to return.
        assertEquals("TW91EJ", postcode.get());
    }

}