package com.gumtree.web.seller.page;

import com.gumtree.web.security.UserSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ResponsiveGroupRequestTest {

    private ResponsiveGroupRequest switcher;

    @Mock
    private HttpServletRequest request;

    @Mock
    private UserSession userSession;

    @Before
    public void setUp() {
        switcher = new ResponsiveGroupRequest(userSession);
    }

    @Test
    public void testGetGroup() {
        when(userSession.isProUser()).thenReturn(false);
        assertEquals("responsive", switcher.getGroup());
    }

    @Test
    public void testGetGroupProUser() {
        when(userSession.isProUser()).thenReturn(true);
        assertEquals("non-responsive", switcher.getGroup());
    }

    @Test
    public void testIsResponsive() {
        when(userSession.isProUser()).thenReturn(false);
        assertTrue(switcher.isResponsiveGroup());
    }

    @Test
    public void testIsProNotResponsive() {
        when(userSession.isProUser()).thenReturn(true);
        assertFalse(switcher.isResponsiveGroup());
    }

}
