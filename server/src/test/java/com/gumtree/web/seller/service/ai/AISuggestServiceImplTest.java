package com.gumtree.web.seller.service.ai;

import com.gumtree.util.HttpUtils;
import com.gumtree.util.url.UrlScheme;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.io.IOException;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AISuggestServiceImplTest {

    @InjectMocks
    private AISuggestServiceImpl aiSuggestService;

    @Mock
    private UrlScheme urlScheme;

    @Mock
    private HttpUtils httpUtils;

    @Before
    public void setUp() throws Exception {
        java.lang.reflect.Field field = AISuggestServiceImpl.class.getDeclaredField("urlScheme");
        java.lang.reflect.Field field1 = AISuggestServiceImpl.class.getDeclaredField("httpUtils");
        field.setAccessible(true);
        field.set(aiSuggestService, urlScheme);
        field1.setAccessible(true);
        field1.set(aiSuggestService, httpUtils);
    }

    @Test
    public void testGetSuggestedAttributes_allEmptyParams_returnsEmptyMap() {
        // Arrange
        String input = "";
        String type = "";
        String categoryName = "";

        // Act
        Map<String, String> result = aiSuggestService.getSuggestedAttributes(input, type, categoryName);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetSuggestedCategories_withImageUrls_returnsValidList() throws Exception {
        String input = "https://example.com/image1.jpg,https://example.com/image2.jpg";
        String type = "img";

        JSONObject mockResponse = new JSONObject();
        List<Long> dataList = new ArrayList<>();
        dataList.add(10205L);
        dataList.add(157L);
        mockResponse.put("code", 200);
        mockResponse.put("data", dataList);

        when(urlScheme.urlforPredictCate()).thenReturn("https://ai.ok.com/api/predictCate");
        when(httpUtils.sendJsonPost(anyString(), any(JSONObject.class))).thenReturn(mockResponse);

        List<Long> result = aiSuggestService.getSuggestedCategories(input, type);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(10205L));
        assertTrue(result.contains(157L));
    }

    @Test
    public void testGetSuggestedCategories_withEmptyInput_returnsEmptyList() {
        List<Long> result = aiSuggestService.getSuggestedCategories("", "img");
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetSuggestedAttributes_httpNullResponse_returnsEmptyMap() throws Exception {
        // Arrange
        String input = "test input";
        String type = "title";
        String categoryName = "houses";

        when(urlScheme.urlforAttributeSuggest()).thenReturn("https://ai.example.com/api/attributeSuggest");
        when(httpUtils.sendJsonPost(anyString(), any(JSONObject.class))).thenReturn(null);

        // Act
        Map<String, String> result = aiSuggestService.getSuggestedAttributes(input, type, categoryName);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetSuggestedAttributes_invalidJsonFormat_returnsEmptyMap() throws Exception {
        // Arrange
        String input = "test input";
        String type = "title";
        String categoryName = "houses";

        // Create a mock response with invalid JSON format
        JSONObject mockResponse = mock(JSONObject.class);
        when(mockResponse.toString()).thenThrow(new RuntimeException("Invalid JSON format"));

        when(urlScheme.urlforAttributeSuggest()).thenReturn("https://ai.example.com/api/attributeSuggest");
        when(httpUtils.sendJsonPost(anyString(), any(JSONObject.class))).thenReturn(mockResponse);

        // Act
        Map<String, String> result = aiSuggestService.getSuggestedAttributes(input, type, categoryName);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetSuggestedAttributes_httpNon200Status_returnsEmptyMap() throws Exception {
        // Arrange
        String input = "test input";
        String type = "title";
        String categoryName = "houses";

        JSONObject mockResponse = new JSONObject();
        mockResponse.put("code", 500); // Non-success status code
        mockResponse.put("data", new JSONObject());

        when(urlScheme.urlforAttributeSuggest()).thenReturn("https://ai.example.com/api/attributeSuggest");
        when(httpUtils.sendJsonPost(anyString(), any(JSONObject.class))).thenReturn(mockResponse);

        // Act
        Map<String, String> result = aiSuggestService.getSuggestedAttributes(input, type, categoryName);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetSuggestedAttributes_emptyData_returnsEmptyMap() throws Exception {
        // Arrange
        String input = "test input";
        String type = "title";
        String categoryName = "houses";

        JSONObject mockResponse = new JSONObject();
        mockResponse.put("code", 200);
        mockResponse.put("data", new JSONObject()); // Empty data

        when(urlScheme.urlforAttributeSuggest()).thenReturn("https://ai.example.com/api/attributeSuggest");
        when(httpUtils.sendJsonPost(anyString(), any(JSONObject.class))).thenReturn(mockResponse);

        // Act
        Map<String, String> result = aiSuggestService.getSuggestedAttributes(input, type, categoryName);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    @Test
    public void testGetSuggestedCategories_withInvalidType_returnsEmptyList() {
        List<Long> result = aiSuggestService.getSuggestedCategories("some-input", "invalid-type");
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetSuggestedCategories_httpError_returnsEmptyList() throws Exception {
        String input = "https://example.com/image1.jpg";
        String type = "img";

        when(urlScheme.urlforPredictCate()).thenReturn("https://ai.ok.com/api/predictCate");
        doThrow(new RuntimeException("Network error"))
                .when(httpUtils).sendJsonPost(anyString(), any(JSONObject.class));

        List<Long> result = aiSuggestService.getSuggestedCategories(input, type);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetSuggestedTitles_withValidInput() throws Exception {
        String input = "iPhone 14 Pro Max";
        JSONObject mockResponse = new JSONObject();
        List<String> dataList = new ArrayList<>();
        dataList.add("iPhone 14 Pro Max");
        dataList.add("iPhone 14");
        dataList.add("iPhone 13 Pro");
        mockResponse.put("code", 200);
        mockResponse.put("data", dataList);

        when(urlScheme.urlforTitleSuggest()).thenReturn("https://ai.ok.com/api/titleSuggest");
        when(httpUtils.sendJsonGet(anyString(), anyString(), anyString())).thenReturn(mockResponse);

        List<String> result = aiSuggestService.getSuggestedTitles(input);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("iPhone 14 Pro Max"));
        assertTrue(result.contains("iPhone 14"));
        assertTrue(result.contains("iPhone 13 Pro"));
    }

    @Test
    public void testGetSuggestedAttributes_withValidData() throws Exception {
        String categoryName = "Electronics";
        String title = "iPhone 14";
        String images = "https://example.com/image1.jpg";

        JSONObject data = new JSONObject();
        data.put("brand", "Apple");
        data.put("condition", "New");
        data.put("price", "1000");

        JSONObject mockResponse = new JSONObject();
        mockResponse.put("code", 200);
        mockResponse.put("data", data);

        when(urlScheme.urlforDescSuggest()).thenReturn("https://ai.ok.com/api/descSuggest");
        when(httpUtils.sendJsonPost(anyString(), any(JSONObject.class))).thenReturn(mockResponse);

        Map<String, String> result = aiSuggestService.getSuggestedAttributes(categoryName, title, images);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("Apple", result.get("brand"));
        assertEquals("New", result.get("condition"));
        assertEquals("1000", result.get("price"));
    }

    @Test
    public void testGetSuggestedTitleAndDesc_withValidInput() throws Exception {
        String categoryName = "Electronics";
        String title = "iPhone 14 Pro Max";
        String desc = "Brand new iPhone 14 Pro Max with case and charger.";
        String images = "https://example.com/image1.jpg";

        JSONObject data = new JSONObject();
        data.put("title", "Buy New iPhone 14 Pro Max");
        data.put("description", "Great condition. Comes with original box and accessories.");

        JSONObject mockResponse = new JSONObject();
        mockResponse.put("code", 200);
        mockResponse.put("data", data);

        when(urlScheme.urlforDescSuggest()).thenReturn("https://ai.ok.com/api/descSuggest");
        when(httpUtils.sendJsonPost(anyString(), any(JSONObject.class))).thenReturn(mockResponse);

        Map<String, String> result = aiSuggestService.getSuggestedTitleAndDesc(categoryName, null, title, desc, images);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Buy New iPhone 14 Pro Max", result.get("title"));
        assertEquals("Great condition. Comes with original box and accessories.", result.get("description"));
    }

    @Test
    public void testToStringMap_withMixedValues() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("key1", "value1");
        jsonObject.put("key2", 123);  // Integer value should be ignored

        Map<String, String> result = AISuggestServiceImpl.toStringMap(jsonObject);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("value1", result.get("key1"));
        assertNull(result.get("key3"));
    }
}
