package com.gumtree.web.seller.page.password.controller;

import com.gumtree.api.client.request.EmailRequest;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.password.model.ForgottenPasswordModel;
import com.gumtree.web.seller.page.password.model.ForgottenPasswordResult;
import com.gumtree.web.seller.service.user.forgotpassword.PasswordResetService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ForgottenPasswordControllerTest extends BaseSellerControllerTest {

    public static final String EMAIL = "<EMAIL>";

    private ForgottenPasswordController controller;
    private ResetPasswordFormBean resetPasswordFormBean;
    private EmailRequest emailRequest;

    @Mock private BindingResult result;
    @Mock private ApiResponse<Boolean> resetPasswordResponse;
    @Mock private PasswordResetService passwordResetService;

    @Before
    public void init() {
        resetPasswordFormBean = new ResetPasswordFormBean();
        emailRequest = new EmailRequest();
        controller = new ForgottenPasswordController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme,
                zenoService, userSessionService, passwordResetService, parameterEncryption);

        autowireAbExperimentsService(controller);
    }

    //given an invalid email address when perform forgot password stay on ForgotPassword page
    @Test
    public void testEmailValidationFail() {
        //given
        resetPasswordFormBean.setUsername("");
        when(result.hasErrors()).thenReturn(true);

        ForgottenPasswordModel forgottenPasswordModel = new ForgottenPasswordModel();
        forgottenPasswordModel.setForm(resetPasswordFormBean);

        //when
        ModelAndView modelAndView = controller.sendEmail(forgottenPasswordModel, result, request);

        //then
        assertThat(modelAndView.getViewName(), equalTo(Page.ForgottenPassword.getTemplateName()));
        assertThat(modelAndView.getView(), is(nullValue()));
    }

    //given a user when perform forgot password it redirect to ResetPassword Confirmation page and send the email
    @Test
    public void testEmailValidationPassesBushFireApiPass() {
        //given
        resetPasswordFormBean.setUsername(EMAIL);
        when(result.hasErrors()).thenReturn(false);

        ForgottenPasswordModel forgottenPasswordModel = new ForgottenPasswordModel();
        forgottenPasswordModel.setForm(resetPasswordFormBean);

        emailRequest.setEmail(EMAIL);
        when(resetPasswordResponse.isDefined()).thenReturn(true);

        //when
        View view = controller.sendEmail(forgottenPasswordModel, result, request).getView();

        //then
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(ForgottenPasswordEmailConfirmationController.PAGE_PATH));
        verify(passwordResetService).resetPassword(EMAIL);
    }

    //given a user when perform forgot password it fails bushfire and fails legacy
    @Test
    public void testEmailValidationPassesBushFireApiFail() {
        //given
        resetPasswordFormBean.setUsername("");
        when(result.hasErrors()).thenReturn(false);

        ForgottenPasswordModel forgottenPasswordModel = new ForgottenPasswordModel();
        forgottenPasswordModel.setForm(resetPasswordFormBean);

        emailRequest.setEmail("");
        when(resetPasswordResponse.isDefined()).thenReturn(false);

        //when
        View view = controller.sendEmail(forgottenPasswordModel, result, request).getView();

        //then
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(ForgottenPasswordEmailConfirmationController.PAGE_PATH));
    }

    //given a user when perform forgot password it fails bushfire because inactive user
    @Test
    public void testAccountNotActivatedErrorOnBushfireResponse() {
        //given
        resetPasswordFormBean.setUsername("");
        when(result.hasErrors()).thenReturn(false);

        ForgottenPasswordModel forgottenPasswordModel = new ForgottenPasswordModel();
        forgottenPasswordModel.setForm(resetPasswordFormBean);

        emailRequest.setEmail("");
        when(resetPasswordResponse.isDefined()).thenReturn(false);

        //when
        View view = controller.sendEmail(forgottenPasswordModel, result, request).getView();

        //then
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(ForgottenPasswordEmailConfirmationController.PAGE_PATH));
    }

    //given a user when perform forgot password it fails bushfire
    @Test
    public void not_bushfire_fail_forgot_password() {
        //given
        resetPasswordFormBean.setUsername(EMAIL);
        when(result.hasErrors()).thenReturn(false);
        emailRequest.setEmail(EMAIL);

        ForgottenPasswordModel forgottenPasswordModel = new ForgottenPasswordModel();
        forgottenPasswordModel.setForm(resetPasswordFormBean);

        when(resetPasswordResponse.isDefined()).thenReturn(false);

        //when
        View view = controller.sendEmail(forgottenPasswordModel, result, request).getView();

        //then
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(ForgottenPasswordEmailConfirmationController.PAGE_PATH));
    }

    @Test
    public void newResetEmailSuccessfullySent() {
        //given
        resetPasswordFormBean.setUsername(EMAIL);
        when(result.hasErrors()).thenReturn(false);

        ForgottenPasswordModel forgottenPasswordModel = new ForgottenPasswordModel();
        forgottenPasswordModel.setForm(resetPasswordFormBean);

        emailRequest.setEmail(EMAIL);
        when(resetPasswordResponse.isDefined()).thenReturn(true);

        //when
        ResponseEntity<ForgottenPasswordResult> response = controller.sendForgottenPasswordEmail(forgottenPasswordModel,
                result);

        //then
        assertThat(response.getStatusCode(), equalTo(HttpStatus.OK));
        assertThat(response.getBody().getResetPasswordFormBean(), equalTo(resetPasswordFormBean));
        verify(passwordResetService).resetPassword(EMAIL);
    }

    @Test
    public void newResetEmailValidationFail() {
        //given
        resetPasswordFormBean.setUsername("");
        when(result.hasErrors()).thenReturn(true);

        ForgottenPasswordModel forgottenPasswordModel = new ForgottenPasswordModel();
        forgottenPasswordModel.setForm(resetPasswordFormBean);

        ResponseEntity<ForgottenPasswordResult> response = controller.sendForgottenPasswordEmail(forgottenPasswordModel,
                result);

        //then
        assertThat(response.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
        assertThat(response.getBody().getResetPasswordFormBean(), equalTo(resetPasswordFormBean));
        assertFalse(response.getBody().getResetPasswordFormBean().getFormErrors().isEmpty());
    }
}
