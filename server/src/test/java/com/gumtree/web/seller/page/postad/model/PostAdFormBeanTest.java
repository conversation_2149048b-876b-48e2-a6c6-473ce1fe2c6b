package com.gumtree.web.seller.page.postad.model;

import com.google.common.collect.Lists;
import com.gumtree.api.User;
import org.hamcrest.Matchers;
import org.junit.Test;

import static com.gumtree.web.seller.page.postad.controller.PostAdSubmitController.IS_PHONE_VERIFICATION_MANDATORY;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

/**
 */
public class PostAdFormBeanTest {

    @Test
    public void shouldAddNewImageOnExistingPosition() {
        //given
        PostAdFormBean bean = new PostAdFormBean(createUser(null, "01632960012"), null);
        bean.setImageIds(Lists.newArrayList(1L, null, 2L));

        //when
        bean.addImageOnProperPosition(2, 3L);

        //then
        assertThat(bean.getImageIds().size(), equalTo(4));
        assertThat(bean.getImageIds().get(0), equalTo(1L));
        assertThat(bean.getImageIds().get(1), equalTo(null));
        assertThat(bean.getImageIds().get(2), equalTo(2L));
        assertThat(bean.getImageIds().get(3), equalTo(3L));
    }

    @Test
    public void shouldAddNewImageOnNewPosition() {
        //given
        PostAdFormBean bean = new PostAdFormBean(createUser(null, "01632960012"), null);

        //when
        bean.addImageOnProperPosition(2, 1L);

        //then
        assertThat(bean.getImageIds().size(), equalTo(3));
        assertThat(bean.getImageIds().get(0), equalTo(null));
        assertThat(bean.getImageIds().get(1), equalTo(null));
        assertThat(bean.getImageIds().get(2), equalTo(1L));
    }

    @Test
    public void shouldAddNewImageOnEmptyPosition() {
        //given
        PostAdFormBean bean = new PostAdFormBean(createUser(null, "01632960012"), null);
        bean.addImageOnProperPosition(2, 1L);

        //when
        bean.addImageOnProperPosition(0, 2L);

        //then
        assertThat(bean.getImageIds().size(), equalTo(3));
        assertThat(bean.getImageIds().get(0), equalTo(2L));
        assertThat(bean.getImageIds().get(1), equalTo(null));
        assertThat(bean.getImageIds().get(2), equalTo(1L));
    }

    @Test
    public void shouldNotChangeImagePositionIfWeKnowIt() {
        //given
        PostAdFormBean bean = new PostAdFormBean(createUser(null, "01632960012"), null);
        bean.setImageIds(Lists.newArrayList(1L, null, 2L));

        //when
        bean.addImageOnProperPosition(1, 1L);

        //then
        assertThat(bean.getImageIds().size(), equalTo(3));
        assertThat(bean.getImageIds().get(0), equalTo(1L));
        assertThat(bean.getImageIds().get(1), equalTo(null));
        assertThat(bean.getImageIds().get(2), equalTo(2L));
    }

    @Test
    public void shouldDeleteImage() {
        //given
        PostAdFormBean bean = new PostAdFormBean(createUser(null, "01632960012"), null);
        bean.setImageIds(Lists.newArrayList(1L, null, 2L));

        //when
        bean.removeImage(2L);

        //then
        assertThat(bean.getImageIds().size(), equalTo(2));
        assertThat(bean.getImageIds().get(0), equalTo(1L));
    }

    @Test
    public void usePhoneIsTrueWhenCreatedWithUserWithTelephoneNumber() {
        PostAdFormBean bean = new PostAdFormBean(createUser(null, "01632960012"), null);
        assertThat(bean.isUsePhone(), equalTo(false));
        assertThat(bean.getContactTelephone(), equalTo("01632960012"));
    }

    @Test
    public void useEmailIsTrueWhenPreferredEmailAddressExists() {
        PostAdFormBean bean = new PostAdFormBean(createUser("<EMAIL>", null), "<EMAIL>");
        assertThat(bean.isUseEmail(), equalTo(true));
        assertThat(bean.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(bean.getContactEmail(), equalTo("<EMAIL>"));
    }

    @Test
    public void useEmailIsFalseWhenPreferredEmailAddressNotExists() {
        PostAdFormBean bean = new PostAdFormBean(createUser("<EMAIL>", null), null);
        assertThat(bean.isUseEmail(), equalTo(false));
        assertThat(bean.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(bean.getContactEmail(), nullValue());
    }

    @Test
    public void usePhoneIsFalseWhenCreatedWithUserWithoutTelephoneNumber() {
        PostAdFormBean bean = new PostAdFormBean(createUser(null, null), null);
        assertThat(bean.isUsePhone(), equalTo(false));
        assertThat(bean.getContactTelephone(), Matchers.nullValue());
    }

    @Test
    public void shouldTrimPostcodeWhenLeadingAndTrailingWhiteSpace() {
        PostAdFormBean bean = new PostAdFormBean(createUser(null, null), null);
        String postCode = "W4 5DD";
        bean.setPostcode(" " + postCode +  " ");
        assertThat(bean.isUsePhone(), equalTo(false));
        assertThat(bean.getContactTelephone(), Matchers.nullValue());
        assertThat(bean.getPostcode(), equalTo(postCode));
    }

    @Test
    public void shouldNotAttemptToTrimPostcodeWhenPostCodeIsNull() {
        PostAdFormBean bean = new PostAdFormBean(createUser(null, null), null);
        String postCode = null;
        bean.setPostcode(postCode);
        assertThat(bean.isUsePhone(), equalTo(false));
        assertThat(bean.getContactTelephone(), Matchers.nullValue());
        assertThat(bean.getPostcode(), equalTo(postCode));
    }

    @Test
    public void useEmailIsFalseWhenCreatedWithUserWithoutEmailAddress() {
        PostAdFormBean bean = new PostAdFormBean(createUser(null, null), null);
        assertThat(bean.isUseEmail(), equalTo(false));
        assertThat(bean.getEmailAddress(), Matchers.nullValue());
        assertThat(bean.getContactEmail(), Matchers.nullValue());
    }

    @Test
    public void optInDefaultsToTrue() {
        PostAdFormBean bean = new PostAdFormBean(createUser("<EMAIL>", null), "<EMAIL>");
        assertThat(bean.isUseEmail(), equalTo(true));
        assertThat(bean.isOptInMarketing(), equalTo(true));
        assertThat(bean.getEmailAddress(), equalTo("<EMAIL>"));
        assertThat(bean.getContactEmail(), equalTo("<EMAIL>"));
    }

    private User createUser(String emailAddress, String telephone) {
        User user = new User();
        user.setEmail(emailAddress);
        user.setPhone(telephone);
        return user;
    }

    /**
     * Test case for when the key exists in the fields map.
     * Expected behavior: The method should return the value associated with the key.
     */
    @Test
    public void getUnknownProperties_KeyExists_ReturnsValue() {
        // Initialize the PostAdFormBean instance before each test
        PostAdFormBean postAdFormBean = new PostAdFormBean();

        // Arrange: Add a key-value pair to the fields map
        postAdFormBean.handleUnknownProperties(IS_PHONE_VERIFICATION_MANDATORY, true);

        // Act: Call the method with the existing key
        Object result = postAdFormBean.getUnknownProperties(IS_PHONE_VERIFICATION_MANDATORY);

        // Assert: Verify that the returned value matches the expected value
        assertTrue((Boolean) result);
    }

    /**
     * Test case for when the key does not exist in the fields map.
     * Expected behavior: The method should return null.
     */
    @Test
    public void getUnknownProperties_KeyDoesNotExist_ReturnsNull() {
        // Initialize the PostAdFormBean instance before each test
        PostAdFormBean postAdFormBean = new PostAdFormBean();

        // Act: Call the method with a non-existent key
        Object result = postAdFormBean.getUnknownProperties("nonExistentKey");

        // Assert: Verify that the returned value is null
        assertNull(result);
    }
}
