package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;

public class AdTitleValidatorBase {
    
    protected static final String ERR_MSG_PN = "This field cannot contain phone numbers";
    protected static final String ERR_MSG_WL = "This field cannot contain website link";
    protected static final String ERR_MSG_XSS = "Please remove script or html tags from title: eg. //, <, >";
    protected static final String ERR_MSG_CODE_PN = "postad.title.phonenumber.invalid";
    protected static final String ERR_MSG_CODE_XSS = "postad.title.xss.opentag";
    protected static final String TITLE_WITH_TEXT = "Some title with number ";
    protected static final String TITLE_WITH_PHONE_NUMBER_1 = TITLE_WITH_TEXT + "+44 1632960001";
    protected static final String TITLE_WITH_PHONE_NUMBER_2 = TITLE_WITH_TEXT + "+441632960001";
    protected static final String TITLE_WITH_PHONE_NUMBER_3 = TITLE_WITH_TEXT + "(01632) 960 001";
    protected static final String TITLE_WITH_PHONE_NUMBER_4 = TITLE_WITH_TEXT + "01632 960001";
    protected static final String TITLE_WITH_PHONE_NUMBER_5 = TITLE_WITH_TEXT + "01632960001";

    protected PostAdFormBean makeTestBean(String title) {
        PostAdFormBean bean = new PostAdFormBean();
        bean.setTitle(title);
        return bean;
    }

    protected PostAdDetail makeTestTitleDetail(String title) {
        PostAdDetail detail = new PostAdDetail();
        PostAdFormBean bean = new PostAdFormBean();
        bean.setTitle(title);
        detail.setPostAdFormBean(bean);
        return detail;
    }
}
