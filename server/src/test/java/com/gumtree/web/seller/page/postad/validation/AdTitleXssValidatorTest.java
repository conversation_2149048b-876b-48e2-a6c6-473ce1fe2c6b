package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintValidatorContext;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class AdTitleXssValidatorTest extends AdTitleValidatorBase {

    private AdTitleXssValidation validation;
    private AdTitleXssValidator validator;
    private ConstraintValidatorContext context;

    @Before
    public void setup() {
        ErrorMessageResolver resolver = mock(ErrorMessageResolver.class);
        when(resolver.getMessage(ERR_MSG_CODE_XSS, "")).thenReturn(ERR_MSG_XSS);
        context = mock(ConstraintValidatorContext.class);
        validator = new AdTitleXssValidator(resolver);
        validation = mock(AdTitleXssValidation.class);
        when(validation.message()).thenReturn(ERR_MSG_CODE_XSS);
        when(validation.fieldList()).thenReturn(new String[]{"title"});
        validator.initialize(validation);
        doNothing().when(context).disableDefaultConstraintViolation();

        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.class);
        ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext nodeContext =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext.class);

        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(builder);
        when(builder.addPropertyNode(anyString())).thenReturn(nodeContext);
        when(builder.addConstraintViolation()).thenReturn(context);
    }

    @Test
    public void isNotValidWithCommentCode() throws Exception {
        PostAdDetail detail = makeTestTitleDetail("Some title with // ...");

        boolean result = validator.isValid(detail, context);
        assertFalse(result);
        verify(context).buildConstraintViolationWithTemplate(ERR_MSG_XSS);
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    public void isValidWithoutCommentCode() throws Exception {
        PostAdDetail detail = makeTestTitleDetail("Some title with / \\...");

        boolean result = validator.isValid(detail, context);
        assertTrue(result);
    }

    @Test
    public void isNotValidWithOpenTag() throws Exception {
        PostAdDetail detail = makeTestTitleDetail("Some title with <tag...");

        boolean result = validator.isValid(detail, context);
        assertFalse(result);
        verify(context).buildConstraintViolationWithTemplate(ERR_MSG_XSS);
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    public void isNotValidWithOpenTagAndComment() throws Exception {
        PostAdDetail detail = makeTestTitleDetail("Some title with <svg/onload=alert(document.domain);// > remaining code");

        boolean result = validator.isValid(detail, context);
        assertFalse(result);
        verify(context).buildConstraintViolationWithTemplate(ERR_MSG_XSS);
        verify(context).disableDefaultConstraintViolation();
    }
}
