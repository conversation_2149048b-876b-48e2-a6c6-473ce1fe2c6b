package com.gumtree.web.seller.page.payment.controller;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.OrderInvoiceEmailBean;
import com.gumtree.seller.domain.order.status.OrderStatus;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.service.CheckoutContainer;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SuccessfulCheckoutManagerTest {

    @Mock
    private BushfireApi api;

    @Mock
    private OrderApi orderApi;

    @Mock
    private UserSession userSession;

    @Mock
    private CheckoutContainer checkoutContainer;


    @Mock
    private SuccessfulCheckoutManager checkoutManager;

    private ApiOrder inputApiOrder;

    private ApiOrder outputApiOrder;

    @Before
    public void setUp() {
        inputApiOrder = new ApiOrder();
        outputApiOrder = new ApiOrder();

        when(api.create(Mockito.eq(OrderApi.class), Mockito.any(BushfireApiKey.class))).thenReturn(orderApi);
        when(orderApi.executeTransaction(anyLong())).thenReturn(outputApiOrder);

        checkoutManager = new SuccessfulCheckoutManager(api, userSession, checkoutContainer);
    }

    @Test
    public void paymentConfirmedIfApiOrderIdIsNull() {
        assertThat(checkoutManager.paymentConfirmed(inputApiOrder), is(true));
    }

    @Test
    public void paymentConfirmedIfApiOrderIdNotNullButPaymentStatusIsPayed() {
        inputApiOrder.setId(123L);
        inputApiOrder.setStatus(OrderStatus.PAID);
        assertThat(checkoutManager.paymentConfirmed(inputApiOrder), is(true));

    }

    @Test
    public void paymentNotConfirmedIfApiOrderIdNotNullButPaymentStatusIsUnpaid() {
        inputApiOrder.setId(123L);
        inputApiOrder.setStatus(OrderStatus.UNPAID);
        assertThat(checkoutManager.paymentConfirmed(inputApiOrder), is(false));
    }

    @Test
    public void processPaymentIfIdIsNull(){
        assertThat(checkoutManager.processPayment(inputApiOrder), is(inputApiOrder));
    }

    @Test
    public void processPaymentIfIdIsNotNull(){
        inputApiOrder.setId(123L);
        outputApiOrder.setStatus(OrderStatus.PAID);

        outputApiOrder = checkoutManager.processPayment(inputApiOrder);
        assertThat(outputApiOrder.getStatus(), is(OrderStatus.PAID));
    }

    @Test
    public void nothingEverSendsInvoiceEmail() {
        inputApiOrder.setId(123L);
        inputApiOrder.setStatus(OrderStatus.PAID);
        checkoutManager.paymentConfirmed(inputApiOrder);

        checkoutManager.processPayment(inputApiOrder);

        Checkout checkout = mock(Checkout.class);
        when(checkout.getKey()).thenReturn("123");
        checkoutManager.finishCheckout(checkout);

        verify(orderApi, never()).sendInvoiceEmail(any(OrderInvoiceEmailBean.class));
    }
}
