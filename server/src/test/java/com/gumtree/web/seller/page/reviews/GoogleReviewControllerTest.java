package com.gumtree.web.seller.page.reviews;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.reviews.model.ExternalReviewManagement;
import com.gumtree.web.seller.page.reviews.model.ReviewCommonResponse;
import com.gumtree.web.seller.page.reviews.model.google.AsyncResult;
import com.gumtree.web.seller.page.reviews.model.google.GoogleRelationListResponse;
import com.gumtree.web.seller.page.reviews.model.google.oauth.GoogleOAuthWebRequest;
import com.gumtree.web.seller.page.reviews.service.ExternalReviewsService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GoogleReviewControllerTest {

    @InjectMocks
    private GoogleReviewController googleReviewController;

    @Mock
    private ExternalReviewsService externalReviewsService;

    @Mock
    private UserSession userSession;

    @Mock
    private CategoryService categoryService;

    private static final Long ACCOUNT_ID = 12345L;

    @Before
    public void setUp() {
        when(userSession.getSelectedAccountId()).thenReturn(ACCOUNT_ID);
    }

    @Test
    public void getSummary_withCategory_shouldReturnSummaryForServiceCategory() {
        // given
        String serviceCategoryId = "1000";
        Category l0 = new Category(Categories.ALL.getId(), "root", "root");
        Category l1 = new Category(Categories.SERVICES.getId(), "services", "services");

        HashMap<Integer, Category> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put(0, l0);
        objectObjectHashMap.put(1, l1);
        when(categoryService.getLevelHierarchy(Long.parseLong(serviceCategoryId))).thenReturn(objectObjectHashMap);

        ExternalReviewManagement mockSummary = new ExternalReviewManagement(new com.gumtree.web.seller.page.reviews.model.google.GoogleReviews());
        when(externalReviewsService.getSummary(ACCOUNT_ID, false)).thenReturn(mockSummary);

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary(serviceCategoryId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isEqualTo(mockSummary);
    }

    @Test
    public void getSummary_withCategory_shouldReturnNullForNonServiceCategory() {
        // given
        String nonServiceCategoryId = "2000";
        Category l0 = new Category(Categories.ALL.getId(), "root", "root");
        Category l1 = new Category(Categories.MOTORS.getId(), "motors", "motors");
        HashMap<Integer, Category> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put(0, l0);
        objectObjectHashMap.put(1, l1);

        when(categoryService.getLevelHierarchy(Long.parseLong(nonServiceCategoryId))).thenReturn(objectObjectHashMap);

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary(nonServiceCategoryId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isNull();
    }

    @Test
    public void getSummary_shouldReturnSummary() {
        // given
        ExternalReviewManagement mockSummary = new ExternalReviewManagement(new com.gumtree.web.seller.page.reviews.model.google.GoogleReviews());
        when(externalReviewsService.getSummary(ACCOUNT_ID, true)).thenReturn(mockSummary);

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary();

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isEqualTo(mockSummary);
    }

    @Test
    public void getSummary_shouldHandleException() {
        // given
        when(externalReviewsService.getSummary(ACCOUNT_ID, true)).thenThrow(new RuntimeException("Test Exception"));

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary();

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("get-summary error");
    }

    @Test
    public void doAuth_shouldReturnAsyncResult() {
        // given
        GoogleOAuthWebRequest request = new GoogleOAuthWebRequest();
        AsyncResult asyncResult = new AsyncResult();
        when(externalReviewsService.doGoogleAuth(ACCOUNT_ID, request)).thenReturn(asyncResult);

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.doAuth(request);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isEqualTo(asyncResult);
    }

    @Test
    public void setDisplay_shouldReturnRelationId() {
        // given
        String relationId = "rel-123";
        Integer display = 1;
        when(externalReviewsService.setGoogleDisplay(ACCOUNT_ID, relationId, display)).thenReturn(relationId);

        // when
        ResponseEntity<ReviewCommonResponse<String>> response = googleReviewController.setDisplay(relationId, display);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isEqualTo(relationId);
    }

    @Test
    public void reSync_shouldReturnAsyncResult() {
        // given
        String authId = "auth-123";
        AsyncResult asyncResult = new AsyncResult();
        when(externalReviewsService.reSyncGoogleData(ACCOUNT_ID, authId)).thenReturn(asyncResult);

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.reSync(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isEqualTo(asyncResult);
    }

    @Test
    public void getRelationList_shouldReturnRelationList() {
        // given
        String authId = "auth-123";
        GoogleRelationListResponse listResponse = new GoogleRelationListResponse();
        when(externalReviewsService.getGoogleRelationList(ACCOUNT_ID, authId)).thenReturn(listResponse);

        // when
        ResponseEntity<ReviewCommonResponse<GoogleRelationListResponse>> response = googleReviewController.getRelationList(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isEqualTo(listResponse);
    }

    @Test
    public void setRelation_shouldReturnRelationId() {
        // given
        String relationId = "rel-123";
        when(externalReviewsService.setGoogleRelation(ACCOUNT_ID, relationId)).thenReturn(relationId);

        // when
        ResponseEntity<ReviewCommonResponse<String>> response = googleReviewController.setRelation(relationId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isEqualTo(relationId);
    }

    @Test
    public void setRelation_shouldHandleException() {
        // given
        String relationId = "rel-123";
        when(externalReviewsService.setGoogleRelation(ACCOUNT_ID, relationId)).thenThrow(new RuntimeException("Test Exception"));

        // when
        ResponseEntity<ReviewCommonResponse<String>> response = googleReviewController.setRelation(relationId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("set-relation error");
    }

    @Test
    public void route_shouldReturnSuccess() {
        // when
        String response = googleReviewController.route(null);

        // then
        assertThat(response).isEqualTo("Success!");
    }

    @Test
    public void doAuth_shouldHandleException() {
        // given
        GoogleOAuthWebRequest request = new GoogleOAuthWebRequest();
        when(externalReviewsService.doGoogleAuth(ACCOUNT_ID, request)).thenThrow(new RuntimeException("Test Exception"));

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.doAuth(request);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("do-auth error");
    }

    @Test
    public void setDisplay_shouldHandleException() {
        // given
        String relationId = "rel-123";
        Integer display = 1;
        when(externalReviewsService.setGoogleDisplay(ACCOUNT_ID, relationId, display)).thenThrow(new RuntimeException("Test Exception"));

        // when
        ResponseEntity<ReviewCommonResponse<String>> response = googleReviewController.setDisplay(relationId, display);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("set-display error");
    }

    @Test
    public void reSync_shouldHandleException() {
        // given
        String authId = "auth-123";
        when(externalReviewsService.reSyncGoogleData(ACCOUNT_ID, authId)).thenThrow(new RuntimeException("Test Exception"));

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.reSync(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("re-sync error");
    }

    @Test
    public void getRelationList_shouldHandleException() {
        // given
        String authId = "auth-123";
        when(externalReviewsService.getGoogleRelationList(ACCOUNT_ID, authId)).thenThrow(new RuntimeException("Test Exception"));

        // when
        ResponseEntity<ReviewCommonResponse<GoogleRelationListResponse>> response = googleReviewController.getRelationList(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("get-relation-list error");
    }

    @Test
    public void getSummary_withCategory_shouldHandleException() {
        // given
        String categoryId = "1000";
        when(categoryService.getLevelHierarchy(Long.parseLong(categoryId))).thenThrow(new RuntimeException("Test Exception"));

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary(categoryId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("get-summary error");
    }

    @Test
    public void getSummary_withCategory_shouldHandleNullCategory() {
        // given
        String categoryId = "3000";
        when(categoryService.getLevelHierarchy(Long.parseLong(categoryId))).thenReturn(null);

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary(categoryId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isNull();
    }

    @Test
    public void getSummary_withCategory_shouldHandleEmptyHierarchy() {
        // given
        String categoryId = "4000";
        HashMap<Integer, Category> emptyHierarchy = new HashMap<>();
        when(categoryService.getLevelHierarchy(Long.parseLong(categoryId))).thenReturn(emptyHierarchy);

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary(categoryId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isNull();
    }

    @Test
    public void getSummary_withCategory_shouldHandleInvalidCategoryId() {
        // given
        String invalidCategoryId = "invalid";

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary(invalidCategoryId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("get-summary error");
    }

    @Test
    public void setDisplay_shouldHandleZeroDisplay() {
        // given
        String relationId = "rel-456";
        Integer display = 0;
        when(externalReviewsService.setGoogleDisplay(ACCOUNT_ID, relationId, display)).thenReturn(relationId);

        // when
        ResponseEntity<ReviewCommonResponse<String>> response = googleReviewController.setDisplay(relationId, display);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isEqualTo(relationId);
    }

    @Test
    public void doAuth_shouldHandleNullRequest() {
        // given
        GoogleOAuthWebRequest request = null;
        when(externalReviewsService.doGoogleAuth(ACCOUNT_ID, request)).thenThrow(new NullPointerException("Request cannot be null"));

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.doAuth(request);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("do-auth error");
    }

    @Test
    public void doAuth_shouldHandleEmptyRequest() {
        // given
        GoogleOAuthWebRequest request = new GoogleOAuthWebRequest();
        request.setAuthCode("");
        request.setRedirectUrl("");
        request.setClientId("");
        request.setCodeVerifier("");
        
        AsyncResult asyncResult = new AsyncResult();
        asyncResult.setStatus("FAILED");
        asyncResult.setMessage("Invalid request");
        when(externalReviewsService.doGoogleAuth(ACCOUNT_ID, request)).thenReturn(asyncResult);

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.doAuth(request);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData().getStatus()).isEqualTo("FAILED");
        assertThat(response.getBody().getData().getMessage()).isEqualTo("Invalid request");
    }

    @Test
    public void setDisplay_shouldHandleNullDisplay() {
        // given
        String relationId = "rel-789";
        Integer display = null;
        when(externalReviewsService.setGoogleDisplay(ACCOUNT_ID, relationId, display)).thenThrow(new IllegalArgumentException("Display value cannot be null"));

        // when
        ResponseEntity<ReviewCommonResponse<String>> response = googleReviewController.setDisplay(relationId, display);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("set-display error");
    }

    @Test
    public void reSync_shouldHandleEmptyAuthId() {
        // given
        String authId = "";
        when(externalReviewsService.reSyncGoogleData(ACCOUNT_ID, authId)).thenThrow(new IllegalArgumentException("AuthId cannot be empty"));

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.reSync(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("re-sync error");
    }

    @Test
    public void getRelationList_shouldHandleNullAuthId() {
        // given
        String authId = null;
        when(externalReviewsService.getGoogleRelationList(ACCOUNT_ID, authId)).thenThrow(new NullPointerException("AuthId cannot be null"));

        // when
        ResponseEntity<ReviewCommonResponse<GoogleRelationListResponse>> response = googleReviewController.getRelationList(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("get-relation-list error");
    }

    @Test
    public void setRelation_shouldHandleEmptyRelationId() {
        // given
        String relationId = "";
        when(externalReviewsService.setGoogleRelation(ACCOUNT_ID, relationId)).thenThrow(new IllegalArgumentException("RelationId cannot be empty"));

        // when
        ResponseEntity<ReviewCommonResponse<String>> response = googleReviewController.setRelation(relationId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("set-relation error");
    }

    @Test
    public void getSummary_shouldHandleNullUserSession() {
        // given
        when(userSession.getSelectedAccountId()).thenReturn(null);
        when(externalReviewsService.getSummary(null, true)).thenThrow(new IllegalArgumentException("AccountId cannot be null"));

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary();

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("get-summary error");
    }

    @Test
    public void getSummary_withCategory_shouldHandleMissingL1Category() {
        // given
        String categoryId = "5000";
        Category l0 = new Category(Categories.ALL.getId(), "root", "root");
        // L1 category is missing
        HashMap<Integer, Category> hierarchy = new HashMap<>();
        hierarchy.put(0, l0);
        when(categoryService.getLevelHierarchy(Long.parseLong(categoryId))).thenReturn(hierarchy);

        // when
        ResponseEntity<ReviewCommonResponse<ExternalReviewManagement>> response = googleReviewController.getSummary(categoryId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isNull();
    }

    @Test
    public void unBind_shouldReturnAsyncResult() {
        // given
        String authId = "auth-123";
        AsyncResult asyncResult = new AsyncResult();
        asyncResult.setStatus("SUCCESS");
        when(externalReviewsService.unBind(ACCOUNT_ID, authId)).thenReturn(asyncResult);

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.unBind(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getData()).isEqualTo(asyncResult);
    }

    @Test
    public void unBind_shouldHandleException() {
        // given
        String authId = "auth-123";
        when(externalReviewsService.unBind(ACCOUNT_ID, authId)).thenThrow(new RuntimeException("Test Exception"));

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.unBind(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("un-bind error");
    }

    @Test
    public void unBind_shouldHandleEmptyAuthId() {
        // given
        String authId = "";
        when(externalReviewsService.unBind(ACCOUNT_ID, authId)).thenThrow(new IllegalArgumentException("AuthId cannot be empty"));

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.unBind(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("un-bind error");
    }

    @Test
    public void unBind_shouldHandleNullAuthId() {
        // given
        String authId = null;
        when(externalReviewsService.unBind(ACCOUNT_ID, authId)).thenThrow(new NullPointerException("AuthId cannot be null"));

        // when
        ResponseEntity<ReviewCommonResponse<AsyncResult>> response = googleReviewController.unBind(authId);

        // then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody().getMsg()).isEqualTo("un-bind error");
    }
} 