package com.gumtree.web.seller.page.postad.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static com.gumtree.mobile.test.Fixtures.SELLER_TYPE;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PanelFactoryImplTest extends TestCase {

    public static final long MOTORS_CAT_ID = 24L;
    public static final long FLATS_HOUSES_CAT_ID = 10201L;
    public static final long RANDOM_CAT_ID = 123L;

    @InjectMocks
    private PanelFactoryImpl panelFactory;

    @Mock
    private CategoryModel categoryModel;

    @Mock
    private BushfireApi bushfireApi;

    @Mock
    private AdvertEditor advertEditor;

    @Mock
    AccountApi accountApi;

    @Before
    public void beforeEach() {
        when(bushfireApi.accountApi()).thenReturn(accountApi);
        when(categoryModel.isChild(CategoryConstants.MOTORS_ID, MOTORS_CAT_ID)).thenReturn(true);
        when(categoryModel.isChild(CategoryConstants.FLATS_AND_HOUSES_ID, FLATS_HOUSES_CAT_ID)).thenReturn(true);
    }

    @Test
    public void shouldReturnMandatoryPanelIfAdvertIsPostedInMotorsCategory() throws Exception {
        //given
        when(advertEditor.getCategoryId()).thenReturn(MOTORS_CAT_ID);

        //when
        SellerTypePanel sellerTypePanel = panelFactory.createSellerTypePanel(advertEditor, SELLER_TYPE);

        //then
        assertTrue("should be mandatory seller panel instance", sellerTypePanel instanceof MandatorySellerTypePanel);
        assertThat(sellerTypePanel.getId()).isEqualTo("seller-type-group");
    }

    @Test
    public void shouldReturnMandatoryPanelIfAdvertIsPostedInPropertiesCategory() throws Exception {
        //given
        when(advertEditor.getCategoryId()).thenReturn(FLATS_HOUSES_CAT_ID);

        //when
        SellerTypePanel sellerTypePanel = panelFactory.createSellerTypePanel(advertEditor, SELLER_TYPE);

        //then
        assertTrue("should be mandatory seller panel instance", sellerTypePanel instanceof MandatorySellerTypePanel);
        assertThat(sellerTypePanel.getId()).isEqualTo("seller-type-group");
    }

    @Test
    public void shouldReturnForSalesPanelIfNotInMotorsCategory() throws Exception {
        //given
        when(advertEditor.getCategoryId()).thenReturn(RANDOM_CAT_ID);

        //when
        SellerTypePanel sellerTypePanel = panelFactory.createSellerTypePanel(advertEditor, SELLER_TYPE);

        //then
        assertTrue("should be a for sale panel instance", sellerTypePanel instanceof SellerTypePanelForSales);
    }
}