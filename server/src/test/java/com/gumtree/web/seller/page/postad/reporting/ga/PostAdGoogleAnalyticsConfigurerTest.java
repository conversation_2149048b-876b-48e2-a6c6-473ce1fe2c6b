package com.gumtree.web.seller.page.postad.reporting.ga;

import com.gumtree.api.category.domain.Category;
import com.gumtree.common.test.hamcrest.Answers;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEvent;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PostAdGoogleAnalyticsConfigurerTest {

    private GoogleAnalyticsReportBuilder reportBuilder;
    private ThirdPartyRequestContext ctx;
    private PostAdGoogleAnalyticsConfigurer configurer;

    @Before
    public void setUp() {
        reportBuilder = mock(GoogleAnalyticsReportBuilder.class, Answers.fluentInterfaceAnswer());
        ctx = mock(ThirdPartyRequestContext.class);
        when(ctx.getCategory()).thenReturn(mock(Category.class));
        when(ctx.getLocation()).thenReturn(mock(Location.class));
        when(ctx.getPageType()).thenReturn(PageType.PostAd);
        configurer = new PostAdGoogleAnalyticsConfigurer();
    }

    @Test
    public void configurePageType() {
        // given
        Category l1Category = mock(Category.class);
        when(ctx.getL1Category()).thenReturn(l1Category);

        // when
        configurer.configure(reportBuilder, ctx);

        //then
        verify(reportBuilder).pageType(PageType.PostAd);
    }

    @Test
    public void configureL1Category() {
        // given
        Category l1Category = mock(Category.class);
        when(ctx.getL1Category()).thenReturn(l1Category);

        // when
        configurer.configure(reportBuilder, ctx);

        //then
        verify(reportBuilder).l1Category(l1Category);
    }

    @Test
    public void configureCategory() {
        // given
        Category category = mock(Category.class);
        when(ctx.getCategory()).thenReturn(category);

        // when
        configurer.configure(reportBuilder, ctx);

        //then
        verify(reportBuilder).category(category);
    }

    @Test
    public void configureCounty() {
        // given
        Location location = mock(Location.class);
        when(ctx.getCounty()).thenReturn(location);

        // when
        configurer.configure(reportBuilder, ctx);

        //then
        verify(reportBuilder).county(location);
    }

    @Test
    public void trackEvents() {
        // given
        when(ctx.getCategory().getId()).thenReturn(23L);
        when(ctx.getLocation().getId()).thenReturn(42);
        when(ctx.getSearchTerm()).thenReturn("test");
        when(ctx.hasContent()).thenReturn(true);

        // when
        configurer.configure(reportBuilder, ctx);

        // then
        ArgumentCaptor<GoogleAnalyticsTrackEvent> captor = ArgumentCaptor.forClass(GoogleAnalyticsTrackEvent.class);
        verify(reportBuilder, times(3)).addTrackEvent(captor.capture());
        List<GoogleAnalyticsTrackEvent> values = captor.getAllValues();
        assertThat(values.get(0).getAction(), equalTo("PostAdPreview"));
        assertThat(values.get(1).getAction(), equalTo("PostAdFreeAttempt"));
        assertThat(values.get(2).getAction(), equalTo("PostAdBegin"));
    }

}
