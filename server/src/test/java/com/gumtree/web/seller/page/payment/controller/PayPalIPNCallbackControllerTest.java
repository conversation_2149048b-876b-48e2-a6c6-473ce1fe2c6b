package com.gumtree.web.seller.page.payment.controller;

import com.codahale.metrics.Counter;
import com.gumtree.api.client.spec.PaymentApi;
import com.gumtree.api.domain.payment.ApiPayPalIPNBean;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.filter.paypal.PayPalIPNRequest;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.payment.converter.PayPalIPNRequestToApiPayPalIPNBeanConverter;
import org.jboss.resteasy.client.ClientResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import javax.ws.rs.core.Response;

import static com.codahale.metrics.MetricRegistry.name;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class PayPalIPNCallbackControllerTest extends BaseSellerControllerTest {

    @Mock
    private PaymentApi paymentApi;

    @Mock
    private ClientResponse<Void> clientResponse;

    @Mock
    private PayPalIPNRequest payPalIPNRequest;

    @Mock
    private ApiPayPalIPNBean apiPayPalIPNBean;

    @Mock
    private PayPalIPNRequestToApiPayPalIPNBeanConverter converter;

    @Mock
    private Counter ipnCallbackCounter;

    @Mock
    private Counter ipnCallbackSuccessCounter;

    @Mock
    private Counter ipnCallbackFailureCounter;

    private PayPalIPNCallbackController paypalIPNCallbackController;

    @Before
    public void setUp() throws Exception {
        when(converter.convert(payPalIPNRequest)).thenReturn(apiPayPalIPNBean);
        when(bushfireApi.paymentApi()).thenReturn(paymentApi);
        when(paymentApi.processIPNCallback(apiPayPalIPNBean)).thenReturn(clientResponse);
        when(metricRegistry.counter(name(PayPalIPNCallbackController.class, "ipn", "callbacks")))
                .thenReturn(ipnCallbackCounter);
        when(metricRegistry.counter(name(PayPalIPNCallbackController.class, "ipn", "callbacks", "success")))
                .thenReturn(ipnCallbackSuccessCounter);
        when(metricRegistry.counter(name(PayPalIPNCallbackController.class, "ipn", "callbacks", "failure")))
                .thenReturn(ipnCallbackFailureCounter);
        paypalIPNCallbackController = new PayPalIPNCallbackController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme,
                bushfireApi, metricRegistry, payPalIPNRequest, converter, mock(UserSessionService.class));
    }

    @Test
    public void successfulIPNCallbackReturnsOKResponse() throws Exception {
        when(clientResponse.getResponseStatus()).thenReturn(Response.Status.OK);

        ResponseEntity<String> response = paypalIPNCallbackController.processIPNCallback();

        assertThat(response.getBody(), equalTo(""));
        assertThat(response.getStatusCode(), equalTo(HttpStatus.OK));
        verify(ipnCallbackCounter).inc();
        verify(ipnCallbackSuccessCounter).inc();
    }

    @Test
    public void unsuccessfulIPNCallbackReturnsFailureResponse() throws Exception {
        when(clientResponse.getResponseStatus()).thenReturn(Response.Status.INTERNAL_SERVER_ERROR);

        ResponseEntity<String> response = paypalIPNCallbackController.processIPNCallback();

        assertThat(response.getStatusCode(), equalTo(HttpStatus.INTERNAL_SERVER_ERROR));
        verify(ipnCallbackCounter).inc();
        verify(ipnCallbackFailureCounter).inc();
    }

    @Test
    public void bapiFailureReturnsFailureResponse() throws Exception {
        when(paymentApi.processIPNCallback(apiPayPalIPNBean)).thenThrow(new RuntimeException());

        ResponseEntity<String> response = paypalIPNCallbackController.processIPNCallback();

        assertThat(response.getStatusCode(), equalTo(HttpStatus.INTERNAL_SERVER_ERROR));
        verify(ipnCallbackCounter).inc();
        verify(ipnCallbackFailureCounter).inc();
    }
}
