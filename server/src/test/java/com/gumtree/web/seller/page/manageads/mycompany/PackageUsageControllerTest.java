package com.gumtree.web.seller.page.manageads.mycompany;

import com.gumtree.api.Account;
import com.gumtree.api.CreditPackage;
import com.gumtree.api.PackageUsage;
import com.gumtree.api.PackageUsageSearchResponse;
import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.SelectableValue;
import com.gumtree.web.seller.page.common.SimpleSelectableValue;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.page.manageads.mycompany.model.DisplayCreditPackageUsage;
import com.gumtree.web.seller.page.manageads.mycompany.model.PackageStatusFilter;
import com.gumtree.web.seller.page.manageads.mycompany.model.PackageUsageModel;
import com.gumtree.web.seller.page.manageads.mycompany.service.PackageUsageService;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.List;

import static com.google.common.collect.Lists.newArrayList;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.samePropertyValuesAs;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PackageUsageControllerTest extends BaseSellerControllerTest {
    private PackageUsageService packageUsageService;

    private PackageUsageController controller;
    private Account account;

    @Before
    public void init() {
        packageUsageService = mock(PackageUsageService.class);
        UserSession userSession = mock(UserSession.class);
        ManageAdsHelper manageAdsHelper = mock(ManageAdsHelper.class);

        Clock clock = new StoppedClock(new DateTime(2012, 2, 1, 0, 0, 0, 0));

        when(userSession.getSelectedAccountId()).thenReturn(1L);

        account = new Account();
        account.setId(1L);
        when(packageUsageService.getAccount(1L)).thenReturn(account);

        CreditPackage cp1 = new CreditPackage();
        cp1.setPackageTypeId(1L);
        cp1.setName("CP1");

        CreditPackage cp2 = new CreditPackage();
        cp2.setPackageTypeId(2L);
        cp2.setName("CP2");

        when(packageUsageService.getCreditPackagesForAccount(1L, PackageStatusFilter.ALL)).thenReturn(newArrayList(cp1, cp2));

        controller = new PackageUsageController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme,
                userSession, packageUsageService, clock, manageAdsHelper, userSessionService);

        autowireAbExperimentsService(controller);
    }

    @Test
    public void lightBoxModelShouldContainEmptyListWhenResponseContainsEmptyList() {
        //given
        PackageUsageSearchResponse searchResponse = new PackageUsageSearchResponse();
        searchResponse.setTotalCount(0);
        when(packageUsageService.getCreditUsagesForPackage(1L, 10L, 2, 1000)).thenReturn(searchResponse);

        //when
        ModelAndView mav = controller.showLightboxPage(request, 10L, 2, 1000);

        //then
        assertThat(mav.getViewName(), equalTo("pages/manage-ads/package-usage-light"));

        PackageUsageModel model = extractModel(mav, PackageUsageModel.class);

        assertThat(model.getPackageUsages().isEmpty(), equalTo(true));
        assertThat(model.getAccount(), equalTo(account));
        assertThat(model.getPage(), equalTo(2));
        assertThat(model.getPageSize(), equalTo(1000));
        assertThat(model.isLightbox(), equalTo(true));
    }

    @Test
    public void lightBoxModelShouldCouldContainCorrectDisplayModelWhenResultsExist() {
        //given
        PackageUsageSearchResponse searchResponse = new PackageUsageSearchResponse();
        searchResponse.setTotalCount(0);
        populateTestData(searchResponse);
        when(packageUsageService.getCreditUsagesForPackage(1L, 10L, 2, 100)).thenReturn(searchResponse);

        //when
        ModelAndView mav = controller.showLightboxPage(request, 10L, 2, 100);

        //then
        assertThat(mav.getViewName(), equalTo("pages/manage-ads/package-usage-light"));

        PackageUsageModel model = extractModel(mav, PackageUsageModel.class);

        assertModelData(model.getPackageUsages());
        assertThat(model.getAccount(), equalTo(account));
        assertThat(model.getPage(), equalTo(2));
        assertThat(model.getPageSize(), equalTo(100));
        assertThat(model.isLightbox(), equalTo(true));

    }

    @Test
    public void fullPageModelShouldContainEmptyListWhenResponseContainsEmptyList() {
        //given
        PackageUsageSearchResponse searchResponse = new PackageUsageSearchResponse();
        searchResponse.setTotalCount(0);
        when(packageUsageService.getCreditUsagesForAccount(1L, 10L, dateTime(2012, 10, 10), dateTime(2012, 10, 12),
                PackageStatusFilter.ALL, 2, 1000)).thenReturn(searchResponse);

        //when
        ModelAndView mav =
                controller.showFullPage(request, 10L, "10/10/2012", "12/10/2012", PackageStatusFilter.ALL, 2, 1000);

        //then
        assertThat(mav.getViewName(), equalTo("pages/manage-ads/package-usage"));

        PackageUsageModel model = extractModel(mav, PackageUsageModel.class);

        assertThat(model.getPackageUsages().isEmpty(), equalTo(true));
        assertThat(model.getAccount(), equalTo(account));
        assertThat(model.getPage(), equalTo(2));
        assertThat(model.getPageSize(), equalTo(1000));
        assertThat(model.isLightbox(), equalTo(false));
    }

    @Test
    public void fullPageModelShouldContainCorrectDisplayModelWhenResultsExist() {
        //given
        PackageUsageSearchResponse searchResponse = new PackageUsageSearchResponse();
        searchResponse.setTotalCount(0);
        populateTestData(searchResponse);
        when(packageUsageService.getCreditUsagesForAccount(1L, 10L, dateTime(2012, 10, 10), dateTime(2012, 10, 12),
                PackageStatusFilter.ALL, 2, 1000)).thenReturn(searchResponse);

        //when
        ModelAndView mav = controller.showFullPage(request, 10L, "10/10/2012", "12/10/2012", PackageStatusFilter.ALL, 2, 1000);

        //then
        assertThat(mav.getViewName(), equalTo("pages/manage-ads/package-usage"));

        PackageUsageModel model = extractModel(mav, PackageUsageModel.class);

        assertThat(model.getPackageTypeId(), equalTo(10L));
        assertThat(model.getFrom(), equalTo("10/10/2012"));
        assertThat(model.getTo(), equalTo("12/10/2012"));
        assertThat(model.getStatus(), equalTo(PackageStatusFilter.ALL.getValue()));
        assertSelectableCreditPackages(model.getPackageTypes());
        assertModelData(model.getPackageUsages());
        assertThat(model.getAccount(), equalTo(account));
        assertThat(model.getPage(), equalTo(2));
        assertThat(model.getPageSize(), equalTo(1000));
        assertThat(model.isLightbox(), equalTo(false));
    }

    @Test
    public void fullPageModelShouldDefaultDatesForInvalidDateFormats() {
        //given
        PackageUsageSearchResponse searchResponse = new PackageUsageSearchResponse();
        searchResponse.setTotalCount(0);
        populateTestData(searchResponse);
        when(packageUsageService.getCreditUsagesForAccount(1L, 10L, new DateTime(2012, 1, 1, 0, 0, 0, 0),
                new DateTime(2012, 2, 1, 0, 0, 0, 0), PackageStatusFilter.ALL, 2, 1000)).thenReturn(searchResponse);

        //when
        ModelAndView mav = controller.showFullPage(request, 10L, "", "", PackageStatusFilter.ALL, 2, 1000);

        //then
        assertThat(mav.getViewName(), equalTo("pages/manage-ads/package-usage"));

        PackageUsageModel model = extractModel(mav, PackageUsageModel.class);

        assertThat(model.getPackageTypeId(), equalTo(10L));
        assertThat(model.getFrom(), equalTo("01/01/2012"));
        assertThat(model.getTo(), equalTo("01/02/2012"));
        assertThat(model.getStatus(), equalTo(PackageStatusFilter.ALL.getValue()));
        assertSelectableCreditPackages(model.getPackageTypes());
        assertModelData(model.getPackageUsages());
        assertThat(model.getAccount(), equalTo(account));
        assertThat(model.getPage(), equalTo(2));
        assertThat(model.getPageSize(), equalTo(1000));
        assertThat(model.isLightbox(), equalTo(false));
    }

    private void assertModelData(List<DisplayCreditPackageUsage> usages) {
        assertThat(usages.size(), equalTo(5));
        assertCreditPackageUsage(usages.get(0), "01/01/2012", 1, "Ad1", "<EMAIL>", "1", "P1");
        assertCreditPackageUsage(usages.get(1), "02/01/2012", 1, "Ad2", "<EMAIL>", null, "P2");
        assertCreditPackageUsage(usages.get(2), "03/01/2012", 1, "Ad3", "<EMAIL>", "3", "P3");
        assertCreditPackageUsage(usages.get(3), "04/01/2012", 1, "Ad4", "<EMAIL>", "4", "P4");
        assertCreditPackageUsage(usages.get(4), "05/01/2012", 1, "Ad5", "<EMAIL>", "5", "P5");
    }

    private void populateTestData(PackageUsageSearchResponse searchResponse) {
        searchResponse.addPackage(createPackageUsage(new DateTime(2012, 1, 1, 1, 0, 0, 0), 1, "Ad1", "<EMAIL>", "1", "P1"));
        searchResponse.addPackage(createPackageUsage(new DateTime(2012, 1, 2, 1, 0, 0, 0), 1, "Ad2", "<EMAIL>", null, "P2"));
        searchResponse.addPackage(createPackageUsage(new DateTime(2012, 1, 3, 1, 0, 0, 0), 1, "Ad3", "<EMAIL>", "3", "P3"));
        searchResponse.addPackage(createPackageUsage(new DateTime(2012, 1, 4, 1, 0, 0, 0), 1, "Ad4", "<EMAIL>", "4", "P4"));
        searchResponse.addPackage(createPackageUsage(new DateTime(2012, 1, 5, 1, 0, 0, 0), 1, "Ad5", "<EMAIL>", "5", "P5"));
    }

    private void assertCreditPackageUsage(
            DisplayCreditPackageUsage usage,
            String appliedDate,
            long adId,
            String adTitle,
            String appliedBy,
            String contactTelephone,
            String packageTypes) {

        assertThat(usage.getAppliedDate(), equalTo(appliedDate));
        assertThat(usage.getAdvertId(), equalTo(adId));
        assertThat(usage.getAdvertTitle(), equalTo(adTitle));
        assertThat(usage.getAppliedBy(), equalTo(appliedBy));
        if (contactTelephone != null) {
            assertThat(usage.getContactDetails(), equalTo(Arrays.asList(appliedBy, contactTelephone)));
        } else {
            assertThat(usage.getContactDetails(), equalTo(Arrays.asList(appliedBy)));
        }
        assertThat(usage.getPackageType(), equalTo(packageTypes));
    }

    private PackageUsage createPackageUsage(
            DateTime appliedDate,
            long adId,
            String adTitle,
            String appliedBy,
            String contactTelephone,
            String packageType) {

        PackageUsage packageUsage = new PackageUsage();
        packageUsage.setAppliedDate(appliedDate);
        packageUsage.setAdvertId(adId);
        packageUsage.setAdvertTitle(adTitle);
        packageUsage.setAppliedBy(appliedBy);
        packageUsage.setContactEmail(appliedBy);
        packageUsage.setContactTelephone(contactTelephone);
        packageUsage.setPackageType(packageType);
        return packageUsage;
    }

    private void assertSelectableCreditPackages(List<SelectableValue> input) {
        assertThat(input.size(), equalTo(3));
        assertThat(input.get(0), samePropertyValuesAs(new SimpleSelectableValue("", "All products")));
        assertThat(input.get(1), samePropertyValuesAs(new SimpleSelectableValue("1", "CP1")));
        assertThat(input.get(2), samePropertyValuesAs(new SimpleSelectableValue("2", "CP2")));
    }

    private DateTime dateTime(int year, int month, int day) {
        return new DateTime(year, month, day, 0, 0, 0, 0);
    }
}
