package com.gumtree.web.seller.service.category;

import com.gumtree.web.seller.service.category.model.CategorySuggesterScoreModel;
import com.gumtree.web.seller.service.category.model.CategoryTextMatcher;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.*;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class CategoryTextMatcherTest {
    private  CategoryTextMatcher matcher;
    private final List<CategorySuggesterScoreModel> sampleCategories = Arrays.asList(
            createCategory(1L, "Mobile Phone", "Electronics;Phones"),
            createCategory(2L, "Phone Case", "Electronics;Accessories"),
            createCategory(3L, "Laptop", "Electronics;Computers"),
            createCategory(4L, "Other", "Miscellaneous")
    );

    @Before
    public void setup() {
        // Initialize the matcher with test parameters
        matcher = new CategoryTextMatcher(
                "mobile phone",
                sampleCategories,
                10,      // maxCategorySize
                0.6,     // fuzzyThreshold
                3,       // editThreshold
                0.7      // cosineThreshold
        );
    }
    private CategorySuggesterScoreModel createCategory(Long id, String name, String tree) {
        return new CategorySuggesterScoreModel(id, name, tree);
    }

    //===== Test text processing =====//
    @Test
    public void normalizeWord_shouldCacheResults() {
        String result1 = matcher.normalizeWord("Running");
        String result2 = matcher.normalizeWord("Running");
        String result3 = matcher.normalizeWord("");
        assertEquals(result1, result2);
        assertEquals("running", result1);
        assertEquals("", result3);
    }

    @Test
    public void processInput_handlesSpecialCharacters() {
        List<String> tokens = matcher.processInput("Hello, world! $100");
        assertIterableEquals(Arrays.asList("hello", "world", "100"), tokens);
    }

    //===== Matching algorithm test =====//
    @Test
    public void exactMatch_findsFullMatch() {
        List<Long> matches = matcher.exactMatch();
        assertTrue(matches.contains(1L));
    }

    @Test
    public void fuzzyMatch_aboveThreshold() {
        List<Long> matches = matcher.fuzzyMatch(0.3);
        assertTrue(matches.contains(1L) && matches.contains(2L));
    }

    @Test
    public void editMatch_withinDistance() {
        CategoryTextMatcher localMatcher = new CategoryTextMatcher(
                "mobl",
                Collections.singletonList(createCategory(5L, "Mobile", "Electronics;Mobile Phone")),
                10, 0.6, 3, 0.7
        );
        assertFalse(localMatcher.editMatch(3).isEmpty());
    }

    @Test
    public void prefixMatch_findsPartial() {
        CategoryTextMatcher localMatcher = new CategoryTextMatcher(
                "pho",
                sampleCategories,
                10, 0.6, 3, 0.7
        );
        List<Long> matches = localMatcher.prefixMatch();
        assertTrue(matches.contains(1L) && matches.contains(2L));
    }

    //===== Boundary condition testing =====//
    @Test
    public void emptyQuery_returnsEmpty() {
        CategoryTextMatcher emptyMatcher = new CategoryTextMatcher(
                "",
                sampleCategories,
                10, 0.6, 3, 0.7
        );
        assertTrue(emptyMatcher.doTextMatch().isEmpty());
    }

    @Test
    public void shortWord_ignoredInTrie() {
        CategoryTextMatcher localMatcher = new CategoryTextMatcher(
                "a",
                sampleCategories,
                10, 0.6, 3, 0.7
        );
        assertTrue(localMatcher.prefixMatch().isEmpty());
    }

    //===== Multithreaded security testing =====//
    @Test
    public void concurrentNormalization_preventsDuplicates() throws Exception {
        // Create a new matcher instance to avoid shared state pollution.
        CategoryTextMatcher localMatcher = new CategoryTextMatcher(
                "",
                Collections.emptyList(),
                10, 0.6, 3, 0.7
        );

        // Use reflection to obtain cache fields for validation
        Field cacheField = CategoryTextMatcher.class.getDeclaredField("normalizationCache");
        cacheField.setAccessible(true);

        // Ensure that the initial cache is empty
        ConcurrentHashMap<?, ?> cache = (ConcurrentHashMap<?, ?>) cacheField.get(localMatcher);
        assertEquals(0, cache.size(), "the initial cache should be empty");

        final int THREAD_COUNT = 10;
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);

        // All threads process the same words
        String testWord = "Concurrent";
        for (int i = 0; i < THREAD_COUNT; i++) {
            executor.submit(() -> {
                localMatcher.normalizeWord(testWord);
                latch.countDown();
            });
        }

        assertTrue(latch.await(2, TimeUnit.SECONDS), "All threads should completed within 2 seconds.");
        executor.shutdown();

        // Re-acquire cache reference
        cache = (ConcurrentHashMap<?, ?>) cacheField.get(localMatcher);

        // Verify the cache size and content
        assertEquals(1, cache.size(), "The cache should have only one entry.");
        assertTrue(cache.containsKey(testWord), "The cache should contain test words");
        String cachedValue = (String) cache.get(testWord);
        assertNotNull(cachedValue, "The cache should not be empty");
        assertTrue(cachedValue.length() > 0, "The cached value should be a valid string");
    }

    //===== Resource management test =====//
    @Test
    public void largeInput_avoidsMemoryLeak() {
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            longText.append("word").append(i).append(" ");
        }

        CategoryTextMatcher heavyMatcher = new CategoryTextMatcher(
                longText.toString(),
                sampleCategories,
                10, 0.6, 3, 0.7
        );

        assertDoesNotThrow(heavyMatcher::doTextMatch);
    }

    //===== Integrated process testing =====//
    @Test
    public void doTextMatch_ordersResultsCorrectly() {
        List<CategorySuggesterScoreModel> results = matcher.doTextMatch();
        // Verify that the exact match is at the front
        assertEquals("exact_match", results.get(0).getCategoryCrumb().get("recall_type"));
        // Verify that the "Other" category is at the end
        assertEquals("Other", results.get(results.size()-1).getDisplayName());
    }

    @Test
    public void doTextMatch_respectsMaxSize() {
        CategoryTextMatcher smallMatcher = new CategoryTextMatcher(
                "phone",
                sampleCategories,
                2,  // maxCategorySize
                0.6, 3, 0.7
        );
        assertEquals(2, smallMatcher.doTextMatch().size());
    }

    private List<String> getSortedTrees(List<CategorySuggesterScoreModel> categories) {
        List<String> result = new ArrayList<>();
        for (CategorySuggesterScoreModel c : categories) {
            result.add(c.getTree());
        }
        return result;
    }

    @Test
    public void testNormalSorting() {
        List<CategorySuggesterScoreModel> categories = Arrays.asList(
                createCategory(1L, "Sedan","Motors;Car;Sedan"),
                createCategory(2L, "Home","Services;Cleaning;Home"),
                createCategory(3L, "TV","For Sale;Electronics;TV"),
                createCategory(4L, "Items","Other;Misc;Items"),
                createCategory(5L, "Tech","Services;Repair;Tech")
        );

        List<CategorySuggesterScoreModel> sorted = matcher.sortCategories(categories);

        List<String> expectedTrees = Arrays.asList(
                "For Sale;Electronics;TV",
                "Services;Cleaning;Home",
                "Services;Repair;Tech",
                "Motors;Car;Sedan",
                "Other;Misc;Items"
        );

        assertEquals(expectedTrees, getSortedTrees(sorted));
    }

    @Test
    public void testEmptyList() {
        List<CategorySuggesterScoreModel> emptyList = Collections.emptyList();
        List<CategorySuggesterScoreModel> result = matcher.sortCategories(emptyList);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testSingleElement() {
        CategorySuggesterScoreModel single = createCategory(1L, "Furniture","For Sale;Home;Furniture");
        List<CategorySuggesterScoreModel> result = matcher.sortCategories(Collections.singletonList(single));

        assertEquals(1, result.size());
        assertEquals("For Sale;Home;Furniture", result.get(0).getTree());
    }

    @Test
    public void testAllSameCategory() {
        List<CategorySuggesterScoreModel> categories = Arrays.asList(
                createCategory(1L,"A", "Services;A"),
                createCategory(2L,"B", "Services;B"),
                createCategory(3L,"C", "Services;C")
        );
        List<CategorySuggesterScoreModel> sorted = matcher.sortCategories(categories);
        List<String> expectedTrees = Arrays.asList("Services;A", "Services;B", "Services;C");
        assertEquals(expectedTrees, getSortedTrees(sorted));
    }

    @Test
    public void testNullTree() {
        List<CategorySuggesterScoreModel> categories = Arrays.asList(
                createCategory(1L,"", null),
                createCategory(2L, "Item","For Sale;Item"),
                createCategory(3L,"", "")
        );
        List<CategorySuggesterScoreModel> sorted = matcher.sortCategories(categories);
        List<String> expectedTrees = Arrays.asList(
                "For Sale;Item",
                null,
                ""
        );
        assertEquals(expectedTrees, getSortedTrees(sorted));
    }

    @Test
    public void testNoSemicolon() {
        List<CategorySuggesterScoreModel> categories = Arrays.asList(
                createCategory(1L,"", "SingleCategory"),
                createCategory(2L,"WithSemicolon", "For Sale;WithSemicolon")
        );
        List<CategorySuggesterScoreModel> sorted = matcher.sortCategories(categories);
        List<String> expectedTrees = Arrays.asList(
                "For Sale;WithSemicolon",
                "SingleCategory"
        );
        assertEquals(expectedTrees, getSortedTrees(sorted));
    }

    @Test
    public void testPriorityEdgeCases() {
        List<CategorySuggesterScoreModel> categories = Arrays.asList(
                createCategory(1L,"First", "Services;First"),
                createCategory(2L,"Second", "For Sale;Second"),
                createCategory(3L,"Third", "For Sale;Third")
        );
        List<CategorySuggesterScoreModel> sorted = matcher.sortCategories(categories);
        List<String> expectedTrees = Arrays.asList(
                "For Sale;Second",
                "For Sale;Third",
                "Services;First"
        );
        assertEquals(expectedTrees, getSortedTrees(sorted));
    }

    @Test
    public void testLargeListSorting() {
        List<CategorySuggesterScoreModel> categories = new ArrayList<>();
        for (int i = 0; i < 300; i++) {
            categories.add(createCategory((long) i, String.valueOf(i),"Other;Category;" + i));
        }
        for (int i = 300; i < 600; i++) {
            categories.add(createCategory((long)i, String.valueOf(i),"Services;Category;" + i));
        }
        for (int i = 600; i < 1000; i++) {
            categories.add(createCategory((long)i, String.valueOf(i), "For Sale;Category;" + i));
        }

        long startTime = System.currentTimeMillis();
        List<CategorySuggesterScoreModel> sorted = matcher.sortCategories(categories);
        long duration = System.currentTimeMillis() - startTime;

        assertTrue(sorted.get(0).getTree().startsWith("For Sale"));
        assertTrue(sorted.get(400).getTree().startsWith("Services"));
        assertTrue(sorted.get(700).getTree().startsWith("Other"));

        for (int i = 600; i < 999; i++) {
            assertTrue(sorted.get(i - 600).getId() < sorted.get(i - 599).getId());
        }

        assertTrue(duration < 100, "Sorting took too long: " + duration + "ms");
    }

    @Test
    public void testExtractFirstLevel() {
        assertEquals("For Sale", matcher.extractFirstLevel("For Sale;Home;Furniture"));
        assertEquals("Services", matcher.extractFirstLevel("Services;"));
        assertEquals("OnlyOne", matcher.extractFirstLevel("OnlyOne"));
        assertEquals("", matcher.extractFirstLevel(""));
        assertEquals("", matcher.extractFirstLevel(null));
        assertEquals("First", matcher.extractFirstLevel("First;Second;Third"));
    }

    @Test
    public void testGetPriority() {
        assertEquals(1, matcher.getPriority("For Sale"));
        assertEquals(2, matcher.getPriority("Services"));
        assertEquals(3, matcher.getPriority("Other"));
        assertEquals(3, matcher.getPriority(""));
        assertEquals(3, matcher.getPriority(null));
        assertEquals(3, matcher.getPriority("For Sale "));
        assertEquals(3, matcher.getPriority("for sale"));
    }
}