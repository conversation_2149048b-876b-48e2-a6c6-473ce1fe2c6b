package com.gumtree.web.seller.page.postad.model;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class PostAdImageTest {

    @Test
    public void shouldBuildSuccessfully() {

        Long id = 1L;
        String size = "2";
        String thumbnailUrl = "http://i.sandbox.ebayimg.com/t.jpg";
        String url = "http://i.sandbox.ebayimg.com/u.jpg";

        PostAdImage postAdImage = new PostAdImage.Builder()
                .thumbnailUrl(thumbnailUrl).id(id).size(size).url(url).build();

        assertThat(postAdImage.getId()).isEqualTo(id);
        assertThat(postAdImage.getSize()).isEqualTo(size);
        assertThat(postAdImage.getThumbnailUrl()).isEqualTo(thumbnailUrl);
        assertThat(postAdImage.getUrl()).isEqualTo("//i.sandbox.ebayimg.com/u.jpg");
    }

}
