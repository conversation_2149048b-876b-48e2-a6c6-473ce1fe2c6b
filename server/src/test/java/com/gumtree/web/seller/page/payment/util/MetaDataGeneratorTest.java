package com.gumtree.web.seller.page.payment.util;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.seller.domain.order.status.OrderStatus;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;
import com.gumtree.web.seller.page.postad.model.meta.PagePaymentType;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 * Created by mdivilioglu on 6/18/17.
 */
public class MetaDataGeneratorTest {
    private MetaDataGenerator generator;

    @Before
    public void init() {
        generator = new MetaDataGeneratorImpl();
    }

    @Test
    public void shouldReturnRelist() {
        Checkout checkout = new CheckoutImpl();

        assertThat(generator.getPageAction(checkout, AdStatus.EXPIRED), equalTo(PageActionType.RELIST));
    }

    @Test
    public void shouldReturnRelistForDeletedUser() {
        Checkout checkout = new CheckoutImpl();

        assertThat(generator.getPageAction(checkout, AdStatus.DELETED_USER), equalTo(PageActionType.RELIST));
    }

    @Test
    public void shouldReturnUpdateForExistingUser() {
        Ad ad = getAd(AdStatus.LIVE);
        Checkout checkout = new CheckoutImpl();

        assertThat(generator.getPageAction(checkout, AdStatus.LIVE), equalTo(PageActionType.UPDATE));
    }

    @Test
    public void shouldReturnPostForNewAd() {
        Checkout checkout = new CheckoutImpl();

        ApiOrder order = createOrderWithProducts(new ArrayList<ApiOrderItem>(Arrays.asList(createItem(ProductName.BUMP_UP, true), createItem(ProductName.INSERTION, true))));
        checkout.setOrder(order);
        checkout.setCreateOrEdit(true);
        assertThat(generator.getPageAction(checkout, null), equalTo(PageActionType.POST));
    }

    @Test
    public void shouldReturnFeatureAndInsertionForPayment() {
        Checkout checkout = new CheckoutImpl();

        ApiOrder order = createOrderWithProducts(new ArrayList<ApiOrderItem>(Arrays.asList(createItem(ProductName.BUMP_UP, true), createItem(ProductName.INSERTION, true))));
        checkout.setOrder(order);
        checkout.setCreateOrEdit(true);
        assertThat(generator.getPaymentTypes(checkout), equalTo(new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE))));
    }

    @Test
    public void shouldNotReturnFeatureAndInsertionForPaymentIfItemPricesZero() {
        Checkout checkout = new CheckoutImpl();

        ApiOrder order = createOrderWithProducts(new ArrayList<ApiOrderItem>(Arrays.asList(createItem(ProductName.BUMP_UP, false), createItem(ProductName.INSERTION, false))));
        checkout.setOrder(order);
        checkout.setCreateOrEdit(true);
        assertThat(generator.getPaymentTypes(checkout), equalTo(new ArrayList<PagePaymentType>(Arrays.asList())));
    }

    @Test
    public void shouldNotReturnFeatureAndInsertionForPaymentIfOrderStatusisPaid() {
        Checkout checkout = new CheckoutImpl();

        ApiOrder order = createOrderWithProducts(new ArrayList<ApiOrderItem>(Arrays.asList(createItem(ProductName.BUMP_UP, true), createItem(ProductName.INSERTION, true))));
        order.setStatus(OrderStatus.PAID);
        checkout.setOrder(order);
        checkout.setCreateOrEdit(true);
        assertThat(generator.getPaymentTypes(checkout), equalTo(new ArrayList<PagePaymentType>(Arrays.asList())));
    }

    @Test
    public void shouldReturnFeatureAndInsertionForItems() {
        Checkout checkout = new CheckoutImpl();

        ApiOrder order = createOrderWithProducts(new ArrayList<ApiOrderItem>(Arrays.asList(createItem(ProductName.BUMP_UP, true), createItem(ProductName.INSERTION, true))));
        checkout.setOrder(order);
        checkout.setCreateOrEdit(true);
        assertThat(generator.getPageFeatureTypes(checkout), equalTo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP))));
    }

    @Test
    public void shouldNotReturnFeatureAndInsertionForItemsItemPricesZero() {
        Checkout checkout = new CheckoutImpl();

        ApiOrder order = createOrderWithProducts(new ArrayList<ApiOrderItem>(Arrays.asList(createItem(ProductName.BUMP_UP, false), createItem(ProductName.INSERTION, true))));
        checkout.setOrder(order);
        checkout.setCreateOrEdit(true);
        assertThat(generator.getPageFeatureTypes(checkout), equalTo(new ArrayList<ProductType>()));
    }

    @Test
    public void shouldNotReturnFeatureAndInsertionForItemsIfOrderStatusisPaid() {
        Checkout checkout = new CheckoutImpl();

        ApiOrder order = createOrderWithProducts(new ArrayList<ApiOrderItem>(Arrays.asList(createItem(ProductName.BUMP_UP, true), createItem(ProductName.INSERTION, true))));
        order.setStatus(OrderStatus.PAID);
        checkout.setOrder(order);
        checkout.setCreateOrEdit(true);
        assertThat(generator.getPageFeatureTypes(checkout), equalTo(new ArrayList<ProductType>(Arrays.asList())));
    }

    private CheckoutAdvert getCheckoutAdvert(Ad ad) {
        return CheckoutAdvert.Builder.builder()
                .categoryId(ad.getCategoryId())
                .locationId(ad.getLocationId())
                .id(ad.getId()).build();
    }

    private Ad getAd(AdStatus status) {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setStatus(status);

        return ad;
    }

    private ApiOrderItem createItem(ProductName name, boolean withPrice) {
        ApiOrderItem item = new ApiOrderItem();
        item.setProductName(name);
        if (withPrice)
            item.setPriceIncVat(100L);

        return item;
    }

    private ApiOrder createOrderWithProducts(List<ApiOrderItem> items) {
        ApiOrder order = new ApiOrder();
        order.setItems(items);
        return order;
    }
}
