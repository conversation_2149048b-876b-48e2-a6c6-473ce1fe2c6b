package com.gumtree.web.seller.security.apiauthentication;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.UserType;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jose.crypto.impl.RSAKeyUtils;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.text.ParseException;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class JsonWebTokenServiceTest {

    private JsonWebTokenService tokenService;

    private String SHARED_SECRET = "Kxj38gTS6crvdRGKbZBkN3dUSrz3Cr8P";

    @Before
    public void init() {
        String privateKey = readStringFromFile("testkeys/private.txt");
        JsonWebTokenProperties properties = new JsonWebTokenProperties("seller", privateKey, 2, 900, SHARED_SECRET);
        tokenService = new JsonWebTokenService(properties);
    }

    @Test
    public void tokenCreatedWithAllUserInfo() throws Exception {
        //given
        User user = getUser();

        //when
        String token = tokenService.generateTokenForUser(user);

        //then
        assertTrue(StringUtils.isNotBlank(token));

        //and
        SignedJWT signedJWT = SignedJWT.parse(token);
        JWTClaimsSet claimsSet = signedJWT.getJWTClaimsSet();
        assertEquals(claimsSet.getIssuer(), "seller");
        assertTrue(claimsSet.getExpirationTime().after(new Date()));

        Map<String, Object> claims = claimsSet.getClaims();
        assertEquals(claims.get("userId"), 1L);
        assertEquals(claims.get("email"), "<EMAIL>");
        assertEquals(claims.get("firstName"), "testname");
        assertEquals(claims.get("status"), UserStatus.ACTIVE.toString());
        assertEquals(claims.get("apiKey"), "accessKey");
        assertEquals(claims.get("userType"), UserType.STANDARD.toString());
    }

    @Test
    public void tokenCreatedWithUserNull() throws Exception {
        //given
        //when
        String token = tokenService.generateTokenForUser(null);

        //then
        assertTrue(StringUtils.isNotBlank(token));

        //and
        SignedJWT signedJWT = SignedJWT.parse(token);
        JWTClaimsSet claimsSet = signedJWT.getJWTClaimsSet();
        assertEquals(claimsSet.getIssuer(), "seller");
        assertTrue(claimsSet.getExpirationTime().after(new Date()));

        Map<String, Object> claims = claimsSet.getClaims();
        assertNull(claims.get("userId"));
        assertNull(claims.get("email"));
        assertNull(claims.get("firstName"));
        assertNull(claims.get("status"));
        assertNull(claims.get("apiKey"));
        assertNull(claims.get("userType"));
    }

    @Test
    public void jwtSignedWithValidPairOfKeys() throws Exception {
        //given
        User user = getUser();
        String publicKey = readStringFromFile("testkeys/public.txt");;

        //when
        String token = tokenService.generateTokenForUser(user);

        //then
        assertTrue(StringUtils.isNotBlank(token));

        //and
        SignedJWT signedJWT = SignedJWT.parse(token);
        assertTrue(verifySignature(signedJWT, publicKey));
    }

    @Test
    public void jwtNotVerifiedInvalidPublicKey() throws Exception {
        //given
        User user = getUser();
        String publicKey = "invalid key";

        //when
        String token = tokenService.generateTokenForUser(user);

        //then
        assertTrue(StringUtils.isNotBlank(token));

        //and
        SignedJWT signedJWT = SignedJWT.parse(token);
        assertFalse(verifySignature(signedJWT, publicKey));
    }

    private User getUser() {
        User user = User.builder().withId(1L)
                .withEmail("<EMAIL>")
                .withApiKey(new BushfireApiKey("accessKey", "privateKey"))
                .withFirstName("testname").build();
        user.setStatus(UserStatus.ACTIVE);
        user.setType(UserType.STANDARD);
        return user;
    }

    private boolean verifySignature(SignedJWT signedJWT, String publicKey) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");

            String pb = publicKey.replaceAll("\\n", "")
                    .replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "");
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(pb));
            RSAPublicKey rsaPublicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);

            JWSVerifier verifier = new RSASSAVerifier(rsaPublicKey);
            return signedJWT.verify(verifier);
        } catch (Exception e) {
            return false;
        }
    }

    private String readStringFromFile(String fileName) {
        InputStream is = getClass().getClassLoader().getResourceAsStream(fileName);
        return new BufferedReader(
                new InputStreamReader(is, StandardCharsets.UTF_8)).lines()
                .collect(Collectors.joining("\n"));
    }
}
