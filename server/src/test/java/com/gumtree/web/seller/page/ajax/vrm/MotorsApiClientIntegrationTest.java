package com.gumtree.web.seller.page.ajax.vrm;

import au.com.dius.pact.consumer.Pact;
import au.com.dius.pact.consumer.PactProviderRule;
import au.com.dius.pact.consumer.PactVerification;
import au.com.dius.pact.consumer.dsl.PactDslWithProvider;
import au.com.dius.pact.model.PactFragment;
import com.google.common.collect.ImmutableMap;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.ApiClient;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.VehicleDataApi;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardisedVehicleDataResponse;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.VehicleAttribute;
import org.apache.commons.httpclient.HttpStatus;
import org.junit.Rule;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_VHC_CHECKED;
import static java.lang.String.format;
import static java.util.stream.Collectors.toList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class MotorsApiClientIntegrationTest {
    private static final String VRM = "vrm";
    private static final Long CAR_CATEGORY_ID = 9311L;
    private static final String ATTRIBUTE_NAME = "attribute_name";
    private static final String ATTRIBUTE_VALUE = "attribute_value";

    private Map<String, String> defaultAttributes = ImmutableMap.<String, String>builder()
            .put(ATTRIBUTE_NAME, ATTRIBUTE_VALUE)
            .build();

    @Rule
    public PactProviderRule mockTestProvider = new PactProviderRule("motors_api_server", this);
    private final String hostname = mockTestProvider.getConfig().getHostname();
    private final int port = mockTestProvider.getConfig().getPort();
    private ApiClient apiClient = new ApiClient().setBasePath(format("http://%s:%d", hostname, port));
    private MotorsApiClient motorsApiClient = new MotorsApiClient(new VehicleDataApi(apiClient));


    @Pact(provider = "motors_api_server", consumer = "seller")
    public PactFragment vehicleDataFound(PactDslWithProvider builder) {
        Map<String, String> requestHeaders = new HashMap();
        requestHeaders.put("Client-Id", "seller");

        Map<String, String> responseHeaders = new HashMap();
        responseHeaders.put("Content-Type", "application/json");

        return builder
                .given("vehicle data available for vrm")
                .uponReceiving("lookup finds vehicle data")
                .path(format("/api/vehicleData/%s", VRM))
                .query(format("category_id=%d", CAR_CATEGORY_ID))
                .method("GET")
                .headers(requestHeaders)
                .willRespondWith()
                .status(HttpStatus.SC_OK)
                .headers(responseHeaders)
                .body(responseBody(defaultAttributes))
                .toFragment();
    }

    @Pact(provider = "motors_api_server", consumer = "seller")
    public PactFragment vehicleDataNotFound(PactDslWithProvider builder) {
        Map<String, String> requestHeaders = new HashMap();
        requestHeaders.put("Client-Id", "seller");

        return builder
                .given("lookup does not find vehicle data")
                .uponReceiving("vehicle data lookup")
                .path(format("/api/vehicleData/%s", VRM))
                .query(format("category_id=%d", CAR_CATEGORY_ID))
                .method("GET")
                .headers(requestHeaders)
                .willRespondWith()
                .status(HttpStatus.SC_NOT_FOUND)
                .toFragment();
    }

    @Pact(provider = "motors_api_server", consumer = "seller")
    public PactFragment serverBadState(PactDslWithProvider builder) {
        Map<String, String> requestHeaders = new HashMap();
        requestHeaders.put("Client-Id", "seller");

        return builder
                .given("bad state")
                .uponReceiving("server error during lookup")
                .path(format("/api/vehicleData/%s", VRM))
                .query(format("category_id=%d", CAR_CATEGORY_ID))
                .method("GET")
                .headers(requestHeaders)
                .willRespondWith()
                .status(HttpStatus.SC_INTERNAL_SERVER_ERROR)
                .toFragment();
    }

    @Pact(provider = "motors_api_server", consumer = "seller")
    public PactFragment standardiseVehicleData(PactDslWithProvider builder) {
        Map<String, String> requestHeaders = new HashMap();
        requestHeaders.put("Client-Id", "seller");
        requestHeaders.put("Content-Type", "application/json");

        Map<String, String> responseHeaders = new HashMap();
        responseHeaders.put("Content-Type", "application/json");

        return builder
                .given("vehicle data with valid vrm")
                .uponReceiving("standardise vehicle data")
                .path(format("/api/vehicleData"))
                .body(requestBody(defaultAttributes))
                .method("POST")
                .headers(requestHeaders)
                .willRespondWith()
                .status(HttpStatus.SC_OK)
                .headers(responseHeaders)
                .body(responseBody(ImmutableMap.<String, String>builder().putAll(defaultAttributes).put(VEHICLE_VHC_CHECKED.getName(), "true").build()))
                .toFragment();
    }

    @Pact(provider = "motors_api_server", consumer = "seller")
    public PactFragment standardiseVehicleDataWithError(PactDslWithProvider builder) {
        Map<String, String> requestHeaders = new HashMap();
        requestHeaders.put("Client-Id", "seller");
        requestHeaders.put("Content-Type", "application/json");

        Map<String, String> responseHeaders = new HashMap();
        responseHeaders.put("Content-Type", "application/json");

        return builder
                .given("bad state")
                .uponReceiving("server error during standardise")
                .path(format("/api/vehicleData"))
                .body(requestBody(defaultAttributes))
                .method("POST")
                .headers(requestHeaders)
                .willRespondWith()
                .status(HttpStatus.SC_INTERNAL_SERVER_ERROR)
                .toFragment();
    }

    @Test
    @PactVerification(value = "motors_api_server", fragment = "vehicleDataFound")
    public void vehicleDataLookupShouldReturnVehicleDataWhenFound() throws Exception {
        assertThat(motorsApiClient.lookupVehicleData(VRM, CAR_CATEGORY_ID),
                equalTo(Optional.of(responseObject(defaultAttributes))));
    }

    @Test
    @PactVerification(value = "motors_api_server", fragment = "vehicleDataNotFound")
    public void vehicleDataLookupShouldReturnNoVehicleDataWhenNotFound() throws Exception {
        assertThat(motorsApiClient.lookupVehicleData(VRM, CAR_CATEGORY_ID), equalTo(Optional.empty()));
    }

    @Test
    @PactVerification(value = "motors_api_server", fragment = "serverBadState")
    public void vehicleDataLookupShouldReturnNoVehicleDataWhenServerInBadState() throws Exception {
        assertThat(motorsApiClient.lookupVehicleData(VRM, CAR_CATEGORY_ID), equalTo(Optional.empty()));
    }

    @Test
    @PactVerification(value = "motors_api_server", fragment = "standardiseVehicleData")
    public void standardiseVehicleDataShouldReturnVehicleDataWhenStandardised() throws Exception {
        assertThat(motorsApiClient
                        .standardiseVehicleData(CAR_CATEGORY_ID, defaultAttributes),
                equalTo(ImmutableMap.<String, String>builder().putAll(defaultAttributes).put(VEHICLE_VHC_CHECKED.getName(), "true").build()));
    }

    @Test
    @PactVerification(value = "motors_api_server", fragment = "standardiseVehicleDataWithError")
    public void standardiseVehicleDataShouldReturnOriginalAndCheckedFalseWhenError() throws Exception {
        assertThat(motorsApiClient
                        .standardiseVehicleData(CAR_CATEGORY_ID, defaultAttributes),
                equalTo(ImmutableMap.<String, String>builder().putAll(defaultAttributes).put(VEHICLE_VHC_CHECKED.getName(), "false").build()));
    }

    private StandardisedVehicleDataResponse responseObject(final Map<String, String> expectedAttributes) {
        return new StandardisedVehicleDataResponse()
                .categoryId(CAR_CATEGORY_ID.intValue())
                .attributes(expectedAttributes.entrySet().stream()
                        .map(entry -> new VehicleAttribute().name(entry.getKey()).value(entry.getValue())).collect(toList()));
    }

    private String responseBody(final Map<String, String> expectedAttributes) {
        try {
            return apiClient.getObjectMapper()
                    .writeValueAsString(responseObject(expectedAttributes));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String requestBody(final Map<String, String> expectedAttributes) {
        return responseBody(expectedAttributes);
    }
}
