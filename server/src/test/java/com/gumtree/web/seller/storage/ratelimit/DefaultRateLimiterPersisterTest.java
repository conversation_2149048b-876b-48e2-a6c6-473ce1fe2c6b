package com.gumtree.web.seller.storage.ratelimit;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.google.common.collect.Maps;
import com.gumtree.web.storage.CassandraClient;
import com.gumtree.web.storage.CassandraKeyValueRepository;
import com.gumtree.web.storage.RedisTemplate;
import com.gumtree.web.storage.ratelimit.DefaultRateLimiterPersister;
import com.gumtree.web.storage.ratelimit.RateCheckResult;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.util.Map;
import java.util.Optional;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class DefaultRateLimiterPersisterTest {
    private static final String CREDENTIAL = "<EMAIL>";
    private static final String CREDENTIAL_IS_EXPIRED = "<EMAIL>";
    private static final String FAILING_CREDENTIAL = "<EMAIL>";

    private RedisTemplate redisTemplate;
    private MockCassandraKeyValueRepository cassandraRepository;
    private DefaultRateLimiterPersister persister;
    private int limitPerInterval = 2;
    private int intervalInSeconds = 60;
    private Counter cassandraFailureCounter;
    private MeterRegistry meterRegistry;


    @Before
    public void setup() {
        MetricRegistry metricRegistry = mock(MetricRegistry.class);
        meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());
        cassandraFailureCounter = mock(Counter.class);
        when(metricRegistry.counter(anyString())).thenReturn(cassandraFailureCounter);

        redisTemplate = new RedisTemplate(new MockJedisPool(new MockJedis()), null, null, meterRegistry);
        cassandraRepository = new MockCassandraKeyValueRepository();

        persister = new DefaultRateLimiterPersister(redisTemplate, cassandraRepository, metricRegistry);
    }

    @Test
    public void registersAndReturnsZeroWhenFirstTimeForCredential() {
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL), equalTo(RateCheckResult.EMPTY));
    }

    @Test
    public void createRateCounterOnFirstIncrement() {
        // when
        persister.incRateCounter(intervalInSeconds, CREDENTIAL);

        // then
        assertThat(redisTemplate.get(CREDENTIAL), equalTo("1"));
        assertThat(cassandraRepository.get(CREDENTIAL), equalTo(Optional.of("1")));
    }

    @Test
    public void returnsZeroWhenCredentialIsStillWithinLimit() {
        redisTemplate.set(CREDENTIAL, "1", 60);
        cassandraRepository.set(CREDENTIAL, "1", 60);

        // when + then
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL), equalTo(new RateCheckResult(1, 0)));
    }

    @Test
    public void incrementRateCounterWhenCounterExists() {
        redisTemplate.set(CREDENTIAL, "1", 60);
        cassandraRepository.set(CREDENTIAL, "1", 60);

        // when
        persister.incRateCounter(intervalInSeconds, CREDENTIAL);

        // then
        assertThat(redisTemplate.get(CREDENTIAL), equalTo("2"));
        assertThat(cassandraRepository.get(CREDENTIAL), equalTo(Optional.of("2")));
    }

    @Test
    public void returnsWaitWhenCredentialIsAtLimit() {
        redisTemplate.set(CREDENTIAL, "3", 60);
        cassandraRepository.set(CREDENTIAL, "3", 60);
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL), equalTo(new RateCheckResult(3, 30)));
    }

    @Test
    public void returnsWaitWhenCredentialIsBeyondLimit() {
        redisTemplate.set(CREDENTIAL, "3", 60);
        cassandraRepository.set(CREDENTIAL, "3", 60);
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL), equalTo(new RateCheckResult(3, 30)));
        assertThat(persister.checkRate(limitPerInterval, CREDENTIAL).isRateLimitExceeded(), equalTo(true));
    }


    @Test
    public void setsNewEntryIfExpiredInRedisBetweenReadAndTTL() {
        // given
        redisTemplate.set(CREDENTIAL_IS_EXPIRED, "3", 60);

        // when
        persister.incRateCounter(intervalInSeconds, CREDENTIAL_IS_EXPIRED);

        // then
        assertThat(redisTemplate.get(CREDENTIAL_IS_EXPIRED), equalTo("1"));
    }

    @Test
    public void incRateCounterWorksIfCassandraFails() {
        // when
        persister.incRateCounter(intervalInSeconds, FAILING_CREDENTIAL);

        // then
        assertThat(redisTemplate.get(FAILING_CREDENTIAL), equalTo("1"));
        verify(cassandraFailureCounter, times(2)).inc();
    }

    @Test
    public void checkRateWorksIfCassandraFails() {
        redisTemplate.set(FAILING_CREDENTIAL, "1", 60);

        // when + then
        RateCheckResult result = persister.checkRate(limitPerInterval, FAILING_CREDENTIAL);
        assertThat(result, equalTo(new RateCheckResult(1, 0)));
        assertThat(result.isRateLimitExceeded(), equalTo(false));
        verify(cassandraFailureCounter).inc();
    }

    private class MockJedisPool extends Pool<Jedis> {

        private Jedis jedis;

        public MockJedisPool(Jedis jedis) {
            this.jedis = jedis;
        }

        @Override
        public void returnResource(final Jedis resource) {

        }

        @Override
        public Jedis getResource() {
            return jedis;
        }
    }

    private class MockJedis extends Jedis {

        private Map<String, String> entries;

        public MockJedis() {
            super("");
            entries = Maps.newHashMap();
        }

        @Override
        public String get(String key) {
            return entries.get(key);
        }

        @Override
        public String set(String key, String value) {
            return entries.put(key, value);
        }

        @Override
        public Long expire(String key, int seconds) {
            return 0L;
        }

        @Override
        public Long ttl(String key) {
            if (CREDENTIAL_IS_EXPIRED.equals(key)) {
                return -2L;
            }
            return entries.containsKey(key) ? 30L : 0L;
        }

        @Override
        public Long del(String key) {
            entries.remove(key);
            return 0L;
        }

        @Override
        public Long del(String... keys) {
            for (String key : keys) {
                entries.remove(key);
            }

            return 0L;
        }
    }

    private class MockCassandraKeyValueRepository extends CassandraKeyValueRepository<String, String> {
        private Map<String, String> entries;

        public MockCassandraKeyValueRepository() {
            super(mock(CassandraClient.class), "x", "y", "z", String.class);
            entries = Maps.newHashMap();
        }

        @Override
        public Optional<String> get(String key) {
            checkAndThrowException(key);
            return Optional.ofNullable(entries.get(key));
        }

        @Override
        public Optional<Pair<String, Integer>> getWithTTL(String key) {
            checkAndThrowException(key);
            return get(key).map(val -> Pair.of(val, 30));
        }

        @Override
        public void set(String key, String value, int ttl) {
            checkAndThrowException(key);
            entries.put(key, value);
        }

        @Override
        public void expire(String key, int ttl) {
            checkAndThrowException(key);
        }

        @Override
        public void delete(String key) {
            checkAndThrowException(key);
            entries.remove(key);
        }

        private void checkAndThrowException(String key) {
            if (FAILING_CREDENTIAL.equals(key)) {
                throw new IllegalStateException();
            }
        }
    }
}
