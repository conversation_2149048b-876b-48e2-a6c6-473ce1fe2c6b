package com.gumtree.web.seller.service.image;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.common.ConsoleNotifier;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.gumtree.api.Image;
import com.gumtree.bapi.model.ApiError;
import com.gumtree.bapi.model.ApiErrorCode;
import com.gumtree.bapi.model.ApiErrors;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.config.SellerProperty;
import com.gumtree.config.api.BapiContractConfig;
import com.gumtree.hystrix.ApiKeyHystrixRequestVariable;
import com.gumtree.web.seller.service.image.error.BapiException;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.springframework.http.HttpStatus;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThrows;

public class BapiImageGatewayTest {

    private BapiImageGateway bapiImageGateway;
    private HystrixRequestContext context;

    @Rule
    public WireMockRule wireMockRule = new WireMockRule(wireMockConfig().dynamicPort().notifier(new ConsoleNotifier(true)));

    @Before
    public void setUp() {

        GtPropManager.setProperty(SellerProperty.BAPI_CONNECTION_TIMEOUT.getPropertyName(), "1000");
        GtPropManager.setProperty(SellerProperty.BAPI_SOCKET_TIMEOUT.getPropertyName(), "1000");
        GtPropManager.setProperty(SellerProperty.BAPI_HOST.getPropertyName(), wireMockRule.baseUrl());
        GtPropManager.setProperty(SellerProperty.BAPI_MAX_ATTEMPTS.getPropertyName(), "2");
        GtPropManager.setProperty(SellerProperty.BAPI_SECRET_HEADER.getPropertyName(), "gumtree");

        BapiContractConfig bapiContractImageConfig = new BapiContractConfig(new SimpleMeterRegistry());
        bapiImageGateway = new BapiImageGateway(bapiContractImageConfig.getBapiContractImageApi());

        this.context = HystrixRequestContext.initializeContext();
    }

    @After
    public void after() {
        this.context.shutdown();
    }

    @Test
    public void getImage() throws JsonProcessingException {
        String apiKey = "abc123";
        Long imageId = 1L;
        String testPath = String.format("/api/images/%d?apiKey=%s", imageId, apiKey);

        ApiKeyHystrixRequestVariable.set(apiKey);

        Image image = new Image();
        image.setId(1L);
        image.setUrl("http://eps.com/1.jpg");

        ObjectMapper objectMapper = new ObjectMapper();

        stubFor(get(urlEqualTo(testPath))
                .willReturn(aResponse()
                        .withBody(objectMapper.writeValueAsString(image))
                        .withStatus(HttpStatus.OK.value())));

        Image response = bapiImageGateway.get(1L);
        assertThat(response.getId(), is(image.getId()));
        assertThat(response.getUrl(), is(image.getUrl()));
    }

    @Test
    public void failToGetImageHasApiErrors() throws JsonProcessingException {
        String apiKey = "abc123";
        Long imageId = 1L;
        String testPath = String.format("/api/images/%d?apiKey=%s", imageId, apiKey);

        ApiKeyHystrixRequestVariable.set(apiKey);

        ApiErrors apiErrors = new ApiErrors()
                .errorCode(ApiErrorCode._2F)
                .addErrorsItem(new ApiError().messageCode("bapi-001").field("id"));


        ObjectMapper objectMapper = new ObjectMapper();

        stubFor(get(urlEqualTo(testPath))
                .willReturn(aResponse()
                        .withBody(objectMapper.writeValueAsString(apiErrors))
                        .withStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())));

        BapiException e = assertThrows(BapiException.class, () -> bapiImageGateway.get(1L));
        assertThat(e.getApiErrors().getErrorCode().getValue(), is("2F"));
        assertThat(e.getApiMethod(), is("ImageApi#getImage(Long)"));
        assertThat(e.getStatus(), is(HttpStatus.INTERNAL_SERVER_ERROR.value()));
    }

    @Test
    public void failToGetImageNoApiErrors() {
        String apiKey = "abc123";
        Long imageId = 1L;
        String testPath = String.format("/api/images/%d?apiKey=%s", imageId, apiKey);

        ApiKeyHystrixRequestVariable.set(apiKey);

        stubFor(get(urlEqualTo(testPath))
                .willReturn(aResponse()
                        .withStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())));

        BapiException e = assertThrows(BapiException.class, () -> bapiImageGateway.get(1L));
        assertThat(e.getApiErrors().getErrorCode().getValue(), is("2F"));
        assertThat(e.getApiMethod(), is("ImageApi#getImage(Long)"));
        assertThat(e.getStatus(), is(HttpStatus.INTERNAL_SERVER_ERROR.value()));
    }

}
