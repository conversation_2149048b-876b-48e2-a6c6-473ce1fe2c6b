package com.gumtree.web.seller.page.login.type;

import com.gumtree.web.seller.page.common.model.Page;
import org.fest.assertions.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Optional;

@RunWith(MockitoJUnitRunner.class)
public class LoginPageTypeUtilTest {

    @Mock private LoginPageResolver anyOtherLoginResolver;

    @Mock private LoginPageResolver messageCentreLoginResolver;

    private LoginPageResolverUtil loginPageResolverUtil;

    @Before
    public void setup() {
        when(messageCentreLoginResolver.resolve(any(LoginPageResolverData.class)))
                .thenReturn(Optional.of(Page.Login_MessageCentreReview));
        loginPageResolverUtil = new LoginPageResolverUtil(Arrays.asList(anyOtherLoginResolver, messageCentreLoginResolver));
    }

    @Test
    public void shouldTraversAndReturnFirstMatch() {
        // given
        when(anyOtherLoginResolver.resolve(any(LoginPageResolverData.class)))
                .thenReturn(Optional.empty());

        // when
        Page page = loginPageResolverUtil.resolve(LoginPageResolverData.builder().build());

        // then
        Assertions.assertThat(page).isEqualTo(Page.Login_MessageCentreReview);
        verify(anyOtherLoginResolver).resolve(any(LoginPageResolverData.class));
        verify(messageCentreLoginResolver).resolve(any(LoginPageResolverData.class));
    }

    @Test
    public void shouldReturnDefaultIfNoMatch() {
        // given
        when(anyOtherLoginResolver.resolve(any(LoginPageResolverData.class)))
                .thenReturn(Optional.empty());
        when(messageCentreLoginResolver.resolve(any(LoginPageResolverData.class)))
                .thenReturn(Optional.empty());

        // when
        Page page = loginPageResolverUtil.resolve(LoginPageResolverData.builder().build());

        // then
        Assertions.assertThat(page).isEqualTo(Page.Login);
        verify(anyOtherLoginResolver).resolve(any(LoginPageResolverData.class));
        verify(messageCentreLoginResolver).resolve(any(LoginPageResolverData.class));
    }

}