package com.gumtree.web.seller.page.postad.model.location;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class PostcodeLookupResponseTest {

    @Test
    public void shouldBeOutcodeRecognised() {
        PostcodeLookupResponse postCodeLookupResponse =
                new PostcodeLookupResponse(PostcodeSelectionState.OUTCODE_RECOGNISED, "DY2 8JL");
        assertThat(postCodeLookupResponse.isOutcodeRecognised()).isTrue();
    }

    @Test
    public void shouldBeNotOutcodeRecognised() {
        PostcodeLookupResponse postCodeLookupResponse =
                new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_RECOGNISED, "W4 5DD");
        assertThat(postCodeLookupResponse.isOutcodeRecognised()).isFalse();

    }
}
