package com.gumtree.web.seller.page.ajax.userConsent;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.api.User;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.user.service.ThirdPartyConsentService;
import com.gumtree.user.service.model.ThirdPartyConsentPreference;
import com.gumtree.web.security.UserSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import java.util.Arrays;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThirdPartyConsentControllerTest {

    @Mock
    ThirdPartyConsentService thirdPartyConsentService;
    @Mock
    UserSession userSession;
    @InjectMocks
    ThirdPartyConsentController thirdPartyConsentController;
    ObjectMapper objectMapper;
    User user;

    @Before
    public void init() {
        user = new User();
        user.setFirstName("test");
        user.setLastName("Test");
        user.setEmail("<EMAIL>");
        user.setAccountIds(Arrays.asList(1L, 2L, 3L));
        user.setStatus(UserStatus.ACTIVE);
        user.setId(1L);

        objectMapper = new ObjectMapper();
    }

    @Test
    public void shouldReturn200OnGetForValidLoggedInUser() throws Exception {

        //given
        ThirdPartyConsentPreference partyConsentPreference = new ThirdPartyConsentPreference().status(ThirdPartyConsentPreference.StatusEnum.ACCEPTED);
        when(userSession.getUser()).thenReturn(user);
        when(thirdPartyConsentService.getUserThirdPartyResponse(1L))
                .thenReturn(partyConsentPreference);

        //when
        ResponseEntity<ThirdPartyConsentPreference> response = thirdPartyConsentController.getThirdPartyPreference();

        //then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("{\"status\":\"ACCEPTED\"}", objectMapper.writeValueAsString(response.getBody()));

    }

    @Test
    public void shouldReturnNotYetConsentedOnGetIfUserNotRegistered() throws Exception {

        //given
        ThirdPartyConsentPreference partyConsentPreference = new ThirdPartyConsentPreference().status(ThirdPartyConsentPreference.StatusEnum.NOT_YET_CONSENTED);
        when(userSession.getUser()).thenReturn(user);
        when(thirdPartyConsentService.getUserThirdPartyResponse(1L))
                .thenReturn(partyConsentPreference);

        //when
        ResponseEntity<ThirdPartyConsentPreference> response = thirdPartyConsentController.getThirdPartyPreference();

        //then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("{\"status\":\"NOT_YET_CONSENTED\"}", objectMapper.writeValueAsString(response.getBody()));

    }

    @Test(expected = ThirdPartyConsentException.class)
    public void shouldReturnExceptionOnGetIfUserIsNotLoggedIn() {

        //given
        when(userSession.getUser()).thenReturn(null);

        //when
       thirdPartyConsentController.getThirdPartyPreference();

    }

    @Test
    public void shouldReturn200OnPutIfUserRegistered() throws Exception {

        //given
        ThirdPartyConsentPreference partyConsentPreference = new ThirdPartyConsentPreference().status(ThirdPartyConsentPreference.StatusEnum.ACCEPTED);
        when(userSession.getUser()).thenReturn(user);
        when(thirdPartyConsentService.putUserThirdPartyResponse(partyConsentPreference, 1L))
                .thenReturn(partyConsentPreference);

        //when
        ResponseEntity<ThirdPartyConsentPreference> response = thirdPartyConsentController.createOrUpdateThirdPartyPreference(partyConsentPreference);

        //then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("{\"status\":\"ACCEPTED\"}", objectMapper.writeValueAsString(response.getBody()));

    }

    @Test(expected = ThirdPartyConsentException.class)
    public void shouldReturnExceptionOnPutIfUserIsNotLoggedIn() {

        //given
        when(userSession.getUser()).thenReturn(null);

        //when
        thirdPartyConsentController.getThirdPartyPreference();

    }

}
