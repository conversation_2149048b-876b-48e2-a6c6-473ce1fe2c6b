package com.gumtree.web.seller.service.presentation.config;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.category.domain.matcher.ValueMatchMode;
import com.gumtree.api.category.domain.syi.AttributeSyiMetadata;
import com.gumtree.api.category.domain.syi.SyiAttributeValueMetadata;
import com.gumtree.web.seller.page.postad.model.PostAdAttribute;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeType;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeValue;
import org.fest.assertions.api.Assertions;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static com.gumtree.web.seller.service.presentation.config.PresentableAttribute.aPresentableAttribute;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class PresentableAttributeTest {

    @Test(expected = IllegalArgumentException.class)
    public void postAdAttributeCreationFailsWhenMetadataDoesNotMatchId() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        target.toPostAdAttribute(anAttributeMetadata().withName("not-the-same-attribute").build(), null);
    }

    @Test
    public void postAdAttributeContainsLabelFromMetadataWhenExistingLabelIsNull() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .withLabel("metadata-label")
                .ofType(AttributeType.STRING)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getId(), equalTo("att-id"));
        assertThat(attribute.getLabel(), equalTo("metadata-label"));
    }

    @Test
    public void postAdAttributeContainsLabelFromMetadataWhenExistingLabelIsEmpty() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .withLabel("metadata-label")
                .ofType(AttributeType.STRING)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getId(), equalTo("att-id"));
        assertThat(attribute.getLabel(), equalTo("metadata-label"));
    }

    @Test
    public void postAdAttributeContainsMandatorySettingFromMetadataWhenFalse() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.STRING)
                .flaggedAsMandatory(false)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.isMandatory(), equalTo(false));
    }

    @Test
    public void postAdAttributeContainsMandatorySettingFromMetadataWhenTrue() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.STRING)
                .flaggedAsMandatory(true)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.isMandatory(), equalTo(true));
    }

    @Test
    public void postAdAttributeTypeForBooleanIsRadio() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.BOOLEAN)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getType(), equalTo(PostAdAttributeType.RADIO));
    }

    @Test
    public void postAdAttributeTypeForStringIsTextField() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.STRING)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getType(), equalTo(PostAdAttributeType.TEXTFIELD));
    }

    @Test
    public void postAdAttributeTypeForEnumIsDropdown() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.ENUM)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getType(), equalTo(PostAdAttributeType.DROPDOWN));
    }

    @Test
    public void postAdAttributeTypeForDateTimeIsDate() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.DATETIME)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getType(), equalTo(PostAdAttributeType.DATE));
    }

    @Test
    public void postAdAttributeTypeForLongIsTextField() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.LONG)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getType(), equalTo(PostAdAttributeType.NUMBERFIELD));
    }

    @Test
    public void postAdAttributeTypeForYearIsDropdown() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.YEAR)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getType(), equalTo(PostAdAttributeType.DROPDOWN));
    }

    @Test
    public void postAdAttributeTypeForCurrencyIsCurrency() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.CURRENCY)
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getType(), equalTo(PostAdAttributeType.CURRENCY));
    }

    @Test
    public void attributeTypeForEnumAttributeWithTwoSupportedSearchValuesIsRadio() {

        PresentableAttribute target = aPresentableAttribute()
                .withId(CategoryConstants.Attribute.SELLER_TYPE.getName())
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName(CategoryConstants.Attribute.SELLER_TYPE.getName())
                .ofType(AttributeType.ENUM)
                .withValue("private", "private")
                .withValue("trade", "trade")
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getType(), equalTo(PostAdAttributeType.RADIO));
    }

    @Test
    public void postAdAttributeValueTakenFromMetadataWhenNotDefined() {

        PresentableAttribute target = aPresentableAttribute().withId("att-id").build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.ENUM)
                .withValue("val1", "Value from metadata")
                .build();

        // when
        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        // then
        Assertions.assertThat(attribute.getType()).isEqualTo(PostAdAttributeType.DROPDOWN);
        assertThat(attribute.getValues().get(1).getValue(), equalTo("val1"));
        assertThat(attribute.getValues().get(1).getDisplayValue(), equalTo("Value from metadata"));
    }

    @Test
    public void postAdAttributeValueTakenFromMetadataShouldAlsoPickUpThePriceSensitiveMetadata() {

        PresentableAttribute target = aPresentableAttribute().withId("seller_type").build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("seller_type")
                .ofType(AttributeType.ENUM)
                .withValue("val1", "Value from metadata")
                .withPriceSensitive(true)
                .build();

        // when
        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        // then
        assertThat(attribute.getPriceSensitive(), equalTo(true));

        // and
        Assertions.assertThat(attribute.getType()).isEqualTo(PostAdAttributeType.DROPDOWN);
        Assertions.assertThat(attribute.getValues()).hasSize(2);
        assertThat(attribute.getValues().get(1).getValue(), equalTo("val1"));
        assertThat(attribute.getValues().get(1).getDisplayValue(), equalTo("Value from metadata"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void mergeFailsIfIdsAreDifferent() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        PresentableAttribute mergeSource = aPresentableAttribute()
                .withId("different-att-id")
                .build();

        target.merge(mergeSource);
    }

    @Test
    public void dropDownTypeHasPleaseSelectAppendedAsFirstValueInList() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.ENUM)
                .withValue("val1", "value 1")
                .withValue("val2", "value 2")
                .withValue("val3", "value 3")
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Maps.newHashMap());

        assertThat(attribute.getType(), equalTo(PostAdAttributeType.DROPDOWN));
        Assertions.assertThat(attribute.getValues()).hasSize(4);
        assertThat(attribute.getValues().get(0).getValue(), equalTo(""));
        assertThat(attribute.getValues().get(0).getDisplayValue(), equalTo("Please select..."));
    }

    @Test
    public void radioTypeDoesNotHavePleaseSelectAppendedAsFirstValueInList() {

        PresentableAttribute target = aPresentableAttribute()
                .withId(CategoryConstants.Attribute.SELLER_TYPE.getName())
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName(CategoryConstants.Attribute.SELLER_TYPE.getName())
                .ofType(AttributeType.ENUM)
                .withValue("val1", "value 1")
                .withValue("val2", "value 2")
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, new HashMap<>());

        assertThat(attribute.getValues().size(), equalTo(2));
        assertThat(attribute.getValues().get(0).getValue(), equalTo("val1"));
        assertThat(attribute.getValues().get(1).getValue(), equalTo("val2"));
    }

    @Test
    public void shouldMarkValueAsSelectedIfCurrentValueIsMatchesOneOfValues() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.STRING)
                .withValue("val1", "value 1")
                .withValue("val2", "value 2")
                .build();

        // when
        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Collections.singletonMap("att-id", "val2"));

        // then
        assertThat(attribute.getId(), equalTo("att-id"));
        assertThat(attribute.getValues(), equalTo(Lists.newArrayList(
                new PostAdAttributeValue("val1",  "value 1", false, false),
                new PostAdAttributeValue("val2",  "value 2", true, false))));
    }

    @Test
    public void shouldMarkNoValueAsSelectedIfCurrentValueIsDoesNotMatchAnyOfValues() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.STRING)
                .withValue("val1", "value 1")
                .withValue("val2", "value 2")
                .build();

        // when
        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Collections.singletonMap("att-id", "no-match"));

        // then
        assertThat(attribute.getValues(), equalTo(Lists.newArrayList(
                new PostAdAttributeValue("val1",  "value 1", false, false),
                new PostAdAttributeValue("val2",  "value 2", false, false))));
    }

    @Test
    public void shouldUseCurrentValueAsSelectedAttributeValueValue() {

        PresentableAttribute target = aPresentableAttribute()
                .withId(CategoryConstants.Attribute.SELLER_TYPE.getName())
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName(CategoryConstants.Attribute.SELLER_TYPE.getName())
                .ofType(AttributeType.ENUM)
                .withValue("val1", "value 1")
                .withValue("val2", "value 2")
                .build();

        PostAdAttribute attribute = target.toPostAdAttribute(metadata, new HashMap<>());

        assertThat(attribute.getValues().size(), equalTo(2));
        assertThat(attribute.getValues().get(0).getValue(), equalTo("val1"));
        assertThat(attribute.getValues().get(1).getValue(), equalTo("val2"));
    }

    @Test
    public void yearTypeHasDropdownValueListCreatedCorrectly() {

        PresentableAttribute target = aPresentableAttribute()
                .withId("att-id")
                .build();

        AttributeMetadata metadata = anAttributeMetadata()
                .withName("att-id")
                .ofType(AttributeType.STRING)
                .withValue("val1", "lab1")
                .withValue("val5", "lab5")
                .withValueMatchMode(ValueMatchMode.SAME_OR_CLOSEST_HIGHER)
                .build();

        // when
        PostAdAttribute attribute = target.toPostAdAttribute(metadata, Collections.singletonMap("att-id", "val3"));

        // then
        assertThat(attribute.getValues(), equalTo(Lists.newArrayList(
                new PostAdAttributeValue("val1", "lab1", false, false),
                // notice there is 'val3' instead of 'val5' which is what is expected
                new PostAdAttributeValue("val3", "lab5", true, false))));
    }

    public static AttributeMetadataBuilder anAttributeMetadata() {
        return new AttributeMetadataBuilder();
    }

    public static class AttributeMetadataBuilder {

        private AttributeMetadata metadata = new AttributeMetadata();
        private List<SyiAttributeValueMetadata> syiValues = new ArrayList<>();
        private ValueMatchMode valueMatchMode;

        public AttributeMetadataBuilder ofType(AttributeType type) {
            metadata.setType(type);
            return this;
        }

        public AttributeMetadataBuilder withName(String name) {
            metadata.setName(name);
            return this;
        }

        public AttributeMetadataBuilder withLabel(String label) {
            metadata.setLabel(label);
            return this;
        }

        public AttributeMetadataBuilder flaggedAsMandatory(boolean mandatory) {
            metadata.setRequired(mandatory);
            return this;
        }

        public AttributeMetadataBuilder withValue(String val, String label) {
            syiValues.add(new SyiAttributeValueMetadata(val, label));
            return this;
        }

        public AttributeMetadataBuilder withValueMatchMode(ValueMatchMode valueMatchMode) {
            this.valueMatchMode = valueMatchMode;
            return this;
        }

        public AttributeMetadataBuilder withPriceSensitive(Boolean value) {
            metadata.setPriceSensitive(true);
            return this;
        }

        public AttributeMetadata build() {
            if (!syiValues.isEmpty()) {
                AttributeSyiMetadata.Builder syiBuilder = AttributeSyiMetadata.builder().setValues(syiValues);
                if (valueMatchMode != null) {
                    syiBuilder.setValueMatchMode(valueMatchMode);
                }
                metadata.setSyi(syiBuilder.build());
            }
            return metadata;
        }
    }
}
