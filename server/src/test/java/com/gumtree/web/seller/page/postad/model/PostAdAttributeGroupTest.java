package com.gumtree.web.seller.page.postad.model;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.gumtree.web.seller.page.common.model.SummaryAttribute;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

import static org.fest.assertions.api.Assertions.assertThat;

public class PostAdAttributeGroupTest {

    @Test
    public void builderShouldPreserverAttributeOrder() {
        // given
        PostAdAttribute aAttr = PostAdAttribute.builder().id("a").build();
        PostAdAttribute bAttr = PostAdAttribute.builder().id("b").build();
        PostAdAttribute cAttr = PostAdAttribute.builder().id("c").build();
        PostAdAttribute dAttr = PostAdAttribute.builder().id("d").build();

        ArrayList<PostAdAttribute> attributes = Lists.newArrayList(aAttr, dAttr, cAttr, bAttr);

        // when
        PostAdAttributeGroup group = PostAdAttributeGroup.builder().setAttributes(attributes, null).build();

        // then
        assertThat(group.getAttributes().keySet().stream().collect(Collectors.toList()))
                .isEqualTo(attributes.stream().map(PostAdAttribute::getId).collect(Collectors.toList()));

        assertThat(group.getAttributes().entrySet().stream().map(Map.Entry::getKey).collect(Collectors.toList()))
                .isEqualTo(attributes.stream().map(PostAdAttribute::getId).collect(Collectors.toList()));
    }

    @Test
    public void shouldBuildSummaryAttributeForAttributeWithoutSelectedValue() {
        // given
        PostAdAttribute aAttr = PostAdAttribute.builder().id("a").label("a-label").build();
        PostAdAttribute bAttr = PostAdAttribute.builder().id("b").label("b-label").build();

        ArrayList<PostAdAttribute> attributes = Lists.newArrayList(aAttr, bAttr);
        ImmutableMap<String, String> formAttributes = ImmutableMap.of("a", "a-val", "b", "b-val");

        // when
        PostAdAttributeGroup group = PostAdAttributeGroup.builder().setAttributes(attributes, formAttributes).build();

        // then
        assertThat(group.getFormAttributes().get("a")).isEqualTo(
                SummaryAttribute.builder()
                        .setAttributeName("a").setAttributeDisplayName("a-label").setValue("a-val").setValueDisplayName("a-val").build());
    }

    @Test
    public void shouldBuildSummaryAttributeForAttributeWithSelectedValue() {
        // given
        PostAdAttribute aAttr = PostAdAttribute.builder().id("a").label("a-l")
                .withValues(Lists.newArrayList(
                        new PostAdAttributeValue("a-v1", "a-v1-disp", false, false),
                        new PostAdAttributeValue("a-v2", "a-v2-disp", true, false)))
                .build();

        PostAdAttribute bAttr = PostAdAttribute.builder().id("b").label("b-label").build();

        ArrayList<PostAdAttribute> attributes = Lists.newArrayList(aAttr, bAttr);
        ImmutableMap<String, String> formAttributes = ImmutableMap.of("a", "a-val2", "b", "b-val");

        // when
        PostAdAttributeGroup group = PostAdAttributeGroup.builder().setAttributes(attributes, formAttributes).build();

        // then
        assertThat(group.getFormAttributes().get("a")).isEqualTo(
                SummaryAttribute.builder()
                        .setAttributeName("a").setAttributeDisplayName("a-l").setValue("a-v2").setValueDisplayName("a-v2-disp").build());
    }

}