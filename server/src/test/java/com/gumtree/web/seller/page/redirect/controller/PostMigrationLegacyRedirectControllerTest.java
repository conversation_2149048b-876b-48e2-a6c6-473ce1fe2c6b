package com.gumtree.web.seller.page.redirect.controller;

import com.gumtree.util.model.Actions;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.login.controller.LoginForm;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.view.RedirectView;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PostMigrationLegacyRedirectControllerTest extends BaseSellerControllerTest {

    private PostMigrationLegacyRedirectController controller;

    @Before
    public void init() {
        when(urlScheme.urlFor(Actions.BUSHFIRE_LOGIN)).thenReturn("http://gumtree.com/login/url");

        controller = new PostMigrationLegacyRedirectController(cookieResolver, categoryModel, apiCallExecutor,
                messageResolver, urlScheme, mock(UserSessionService.class));
    }

    @Test
    public void whenEmailAndPasswordNotSpecifiedThenRedirectToLogin() {
        RedirectView result = controller.handleLegacyLogin();
        assertEquals("http://gumtree.com/login/url", result.getUrl());
    }

    @Test
    public void whenLoginToLegacyRedirectsToLogin() {
        LoginForm expectedLoginForm = new LoginForm();
        expectedLoginForm.setUsername("<EMAIL>");
        expectedLoginForm.setPassword("somePassword");

        RedirectView result = controller.handleLegacyLogin();
        assertEquals("http://gumtree.com/login/url", result.getUrl());
    }

}
