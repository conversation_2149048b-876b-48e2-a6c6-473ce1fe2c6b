package com.gumtree.web.seller.page.payment.reporting.ga.events;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class OrderCancelTest {

    @Test
    public void checkValues() {
        ThirdPartyRequestContext ctx = mock(ThirdPartyRequestContext.class);
        Category category = mock(Category.class);
        Location location = mock(Location.class);
        when(ctx.getPageType()).thenReturn(PageType.PostAdForm);
        when(ctx.getCategory()).thenReturn(category);
        when(ctx.getLocation()).thenReturn(location);
        when(category.getId()).thenReturn(23L);
        when(location.getId()).thenReturn(42);

        OrderCancel event = new OrderCancel(ctx);

        assertThat(event.getAction(), equalTo("PostAdPaidCancel"));
        assertThat(event.getBindEvent(), equalTo("click"));
        assertThat(event.getBindSelector(), equalTo("[ga-event=\\'order-cancel\\']"));
        assertThat(event.getCategory(), equalTo("Post Ad Form"));
        assertThat(event.getLabel(), equalTo("catID=23;locID=42"));
    }

}
