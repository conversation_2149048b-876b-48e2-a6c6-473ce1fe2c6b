package com.gumtree.web.seller.page.postad.controller.steps;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.bapi.ListingCapApi;
import com.gumtree.bapi.model.SellerTypeEnum;
import com.gumtree.bapi.model.SellerTypeResponse;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.seller.page.postad.model.PostAdFormStatus;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.VrmStatus;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import rx.Single;

import static com.gumtree.web.seller.page.postad.model.PostAdFormPanel.CATEGORY;
import static com.gumtree.web.seller.page.postad.model.PostAdFormPanel.BUMP;
import static com.gumtree.web.seller.page.postad.model.PostAdFormPanel.CONTINUE;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class CategorySelectPostAdStepTest{

    @Mock
    private CategoryService categoryService;

    @Mock
    private AdvertEditor advertEditor;

    @Mock
    private CategoryModel categoryModel;

    @Mock
    private ListingCapApi listingCapApi;

    @InjectMocks
    private CategorySelectPostAdStep categorySelectPostAdStep;

    private PostAdFormBean postAdFormBean;
    private PostAdSubmitModel.Builder modelBuilder;

    private static final Long CAT_ID = 1L;
    private static final Long ACC_ID = 1L;

    @Before
    public void beforeEach() {
        postAdFormBean = new PostAdFormBean();
        postAdFormBean.setVrmStatus(VrmStatus.VRM_VALID);
        modelBuilder = PostAdSubmitModel.builder();

        // editor
        when(advertEditor.getPostAdFormBean()).thenReturn(postAdFormBean);
        when(advertEditor.getCategoryId()).thenReturn(CAT_ID);
        when(advertEditor.getAccountId()).thenReturn(ACC_ID);
        when(advertEditor.supportsChangeCategory()).thenReturn(true);

        when(categoryService.getCategoryModel()).thenReturn(categoryModel);
        when(categoryModel.isChild(CategoryConstants.FLATS_AND_HOUSES_ID, CAT_ID)).thenReturn(false);

        SellerTypeResponse expected = null;
        when(listingCapApi.getSellerType(ACC_ID, CAT_ID)).thenReturn(Single.just(expected));
    }

    @Test
    public void executeCategoryStepWithNoValidCategorySelected() {
        //given
        when(advertEditor.isValidCategorySelected()).thenReturn(Boolean.FALSE);

        // when
        boolean doNext = categorySelectPostAdStep.execute(modelBuilder, advertEditor);

        // then
        assertThat(doNext).isFalse();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.CATEGORY);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), CONTINUE.getId()));
        assertThat(model.getForm().getDefaultSellerType()).isNull();
        assertThat(model.getForm().getAdvertLimit()).isNull();
        assertThat(model.getForm().getAdvertCount()).isNull();
    }

    @Test
    public void executeCategoryStepNoDraftMode() {
        //given
        when(advertEditor.isValidCategorySelected()).thenReturn(Boolean.TRUE);
        when(advertEditor.isDraftMode()).thenReturn(Boolean.FALSE);

        Category category = new Category();
        when(categoryService.getById(CAT_ID)).thenReturn(Optional.of(category));
        when(categoryService.getLevelHierarchy(category)).thenReturn(Maps.newHashMap());

        // when
        boolean doNext = categorySelectPostAdStep.execute(modelBuilder, advertEditor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.CATEGORY);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(BUMP.getId(), CATEGORY.getId()));
    }

    @Test
    public void executeCategoryStepNoCreateMode() {
        //given
        when(advertEditor.isValidCategorySelected()).thenReturn(Boolean.TRUE);
        when(advertEditor.isCreateMode()).thenReturn(Boolean.FALSE);

        Category category = new Category();
        when(categoryService.getById(CAT_ID)).thenReturn(Optional.of(category));
        when(categoryService.getLevelHierarchy(category)).thenReturn(Maps.newHashMap());

        // when
        boolean doNext = categorySelectPostAdStep.execute(modelBuilder, advertEditor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.CATEGORY);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(BUMP.getId(), CATEGORY.getId()));
    }

    @Test
    public void executeCategoryStepWithNullCategoryId() {
        //given
        when(advertEditor.getCategoryId()).thenReturn(null);
        //given
        when(advertEditor.isValidCategorySelected()).thenReturn(Boolean.FALSE);

        // when
        boolean doNext = categorySelectPostAdStep.execute(modelBuilder, advertEditor);

        // then
        assertThat(doNext).isFalse();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.CATEGORY);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), CONTINUE.getId()));
        assertThat(model.getForm().getDefaultSellerType()).isNull();
        assertThat(model.getForm().getAdvertLimit()).isNull();
        assertThat(model.getForm().getAdvertCount()).isNull();
    }

    @Test
    public void executeCategoryStepCreateModeNoCrumbs() {
        //given
        when(advertEditor.isValidCategorySelected()).thenReturn(Boolean.TRUE);
        when(advertEditor.isCreateMode()).thenReturn(Boolean.TRUE);

        when(categoryService.getById(CAT_ID)).thenReturn(Optional.absent());

        // when
        boolean doNext = categorySelectPostAdStep.execute(modelBuilder, advertEditor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.CATEGORY);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId()));
    }

    @Test
    public void executeCategoryStepForPropertiesCategory() {
        //given
        when(categoryModel.isChild(CategoryConstants.FLATS_AND_HOUSES_ID, CAT_ID)).thenReturn(Boolean.TRUE);

        when(advertEditor.isValidCategorySelected()).thenReturn(Boolean.TRUE);
        when(advertEditor.isCreateMode()).thenReturn(Boolean.TRUE);
        when(categoryService.getById(CAT_ID)).thenReturn(Optional.absent());

        SellerTypeResponse expected = new SellerTypeResponse().sellerType(SellerTypeEnum.TRADE)
                .categoryId(CategoryConstants.FLATS_AND_HOUSES_ID)
                .adverts(0)
                .advertsLimit(2);
        when(listingCapApi.getSellerType(ACC_ID, CAT_ID)).thenReturn(Single.just(expected));

        // when
        boolean doNext = categorySelectPostAdStep.execute(modelBuilder, advertEditor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.CATEGORY);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId()));
        assertThat(model.getForm().getDefaultSellerType()).isEqualTo("TRADE");
        assertThat(model.getForm().getAdvertLimit()).isEqualTo(2);
        assertThat(model.getForm().getAdvertCount()).isEqualTo(0);
    }

   @Test
    public void executeCategoryStepForPropertiesIdWithNullSellerTypeResponse() {
        //given
        when(categoryModel.isChild(CategoryConstants.FLATS_AND_HOUSES_ID, CAT_ID)).thenReturn(Boolean.TRUE);

        when(advertEditor.isValidCategorySelected()).thenReturn(Boolean.TRUE);
        when(advertEditor.isCreateMode()).thenReturn(Boolean.TRUE);
        when(categoryService.getById(CAT_ID)).thenReturn(Optional.absent());

        SellerTypeResponse expected = null;
        when(listingCapApi.getSellerType(ACC_ID, CAT_ID)).thenReturn(Single.just(expected));

        // when
        boolean doNext = categorySelectPostAdStep.execute(modelBuilder, advertEditor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.CATEGORY);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId()));
        assertThat(model.getForm().getDefaultSellerType()).isNull();
        assertThat(model.getForm().getAdvertLimit()).isNull();
        assertThat(model.getForm().getAdvertCount()).isNull();
    }

    @Test
    public void executeCategoryStepForPropertiesIdWithNoSellerType() {
        //given
        when(categoryModel.isChild(CategoryConstants.FLATS_AND_HOUSES_ID, CAT_ID)).thenReturn(Boolean.TRUE);

        when(advertEditor.isValidCategorySelected()).thenReturn(Boolean.TRUE);
        when(advertEditor.isCreateMode()).thenReturn(Boolean.TRUE);
        when(categoryService.getById(CAT_ID)).thenReturn(Optional.absent());

        SellerTypeResponse expected = new SellerTypeResponse()
                .categoryId(CategoryConstants.FLATS_AND_HOUSES_ID)
                .adverts(0)
                .advertsLimit(2);
        when(listingCapApi.getSellerType(ACC_ID, CAT_ID)).thenReturn(Single.just(expected));

        // when
        boolean doNext = categorySelectPostAdStep.execute(modelBuilder, advertEditor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.CATEGORY);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId()));
        assertThat(model.getForm().getDefaultSellerType()).isNull();
        assertThat(model.getForm().getAdvertLimit()).isNull();
        assertThat(model.getForm().getAdvertCount()).isNull();
    }

}
