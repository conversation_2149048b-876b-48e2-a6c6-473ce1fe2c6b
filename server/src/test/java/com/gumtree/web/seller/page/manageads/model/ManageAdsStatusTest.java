package com.gumtree.web.seller.page.manageads.model;

import com.google.common.collect.Sets;
import com.gumtree.api.AdStatus;
import org.junit.Test;

import java.util.Set;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

public class ManageAdsStatusTest {

    @Test
    public void activeAdsMapsToCorrectSetOfAdStatuses() {
        assertTrue(ManageAdStatus.ACTIVE_ADS.getStatuses().contains(AdStatus.LIVE));
        assertTrue(ManageAdStatus.ACTIVE_ADS.getStatuses().contains(AdStatus.AWAITING_ACTIVATION));
        assertTrue(ManageAdStatus.ACTIVE_ADS.getStatuses().contains(AdStatus.NEEDS_EDITING));
        assertTrue(ManageAdStatus.ACTIVE_ADS.getStatuses().contains(AdStatus.AWAITING_SCREENING));
        assertTrue(ManageAdStatus.ACTIVE_ADS.getStatuses().contains(AdStatus.AWAITING_CS_REVIEW));
    }

    @Test
    public void processingMapsToCorrectSetOfAdStatuses() {
        assertTrue(ManageAdStatus.PROCESSING.getStatuses().contains(AdStatus.AWAITING_SCREENING));
        assertTrue(ManageAdStatus.PROCESSING.getStatuses().contains(AdStatus.AWAITING_CS_REVIEW));
    }

    @Test
    public void inactiveAdsMapsToCorrectSetOfAdStatuses() {
        assertTrue(ManageAdStatus.INACTIVE_ADS.getStatuses().contains(AdStatus.DELETED_CS));
        assertTrue(ManageAdStatus.INACTIVE_ADS.getStatuses().contains(AdStatus.DELETED_USER));
        assertTrue(ManageAdStatus.INACTIVE_ADS.getStatuses().contains(AdStatus.EXPIRED));
    }

    @Test
    public void activeAndInactiveAdsMapsToCorrectSetOfAdStatuses() {
        assertTrue(ManageAdStatus.ACTIVE_AND_INACTIVE_ADS.getStatuses().contains(AdStatus.LIVE));
        assertTrue(ManageAdStatus.ACTIVE_AND_INACTIVE_ADS.getStatuses().contains(AdStatus.AWAITING_ACTIVATION));
        assertTrue(ManageAdStatus.ACTIVE_AND_INACTIVE_ADS.getStatuses().contains(AdStatus.NEEDS_EDITING));
        assertTrue(ManageAdStatus.ACTIVE_AND_INACTIVE_ADS.getStatuses().contains(AdStatus.AWAITING_SCREENING));
        assertTrue(ManageAdStatus.ACTIVE_AND_INACTIVE_ADS.getStatuses().contains(AdStatus.AWAITING_CS_REVIEW));
        assertTrue(ManageAdStatus.ACTIVE_AND_INACTIVE_ADS.getStatuses().contains(AdStatus.DELETED_CS));
        assertTrue(ManageAdStatus.ACTIVE_AND_INACTIVE_ADS.getStatuses().contains(AdStatus.DELETED_USER));
        assertTrue(ManageAdStatus.ACTIVE_AND_INACTIVE_ADS.getStatuses().contains(AdStatus.EXPIRED));
    }

    @Test
    public void allAdStatusesIncludedInActiveAndInactive() {
        Set<AdStatus> activeAndInactiveUnion = Sets.newHashSet();
                
        Sets.union(
                ManageAdStatus.ACTIVE_ADS.getStatuses(),
                ManageAdStatus.INACTIVE_ADS.getStatuses())
                .copyInto(activeAndInactiveUnion);

        Set<AdStatus> universalSet = Sets.newHashSet(AdStatus.values());
        universalSet.remove(AdStatus.AWAITING_PAYMENT);   //hack for legacy

        assertThat(activeAndInactiveUnion,equalTo(universalSet));
    }

    @Test
    public void correctMappingFromAdStatus() {
        assertThat(ManageAdStatus.valueOf(AdStatus.LIVE),equalTo(ManageAdStatus.LIVE));
        assertThat(ManageAdStatus.valueOf(AdStatus.DRAFT),equalTo(ManageAdStatus.DRAFT));
        assertThat(ManageAdStatus.valueOf(AdStatus.AWAITING_ACTIVATION),equalTo(ManageAdStatus.AWAITING_ACTIVATION));
        assertThat(ManageAdStatus.valueOf(AdStatus.NEEDS_EDITING),equalTo(ManageAdStatus.NEEDS_EDITING));
        assertThat(ManageAdStatus.valueOf(AdStatus.AWAITING_SCREENING),equalTo(ManageAdStatus.PROCESSING));
        assertThat(ManageAdStatus.valueOf(AdStatus.AWAITING_CS_REVIEW),equalTo(ManageAdStatus.PROCESSING));
        assertThat(ManageAdStatus.valueOf(AdStatus.DELETED_USER),equalTo(ManageAdStatus.DELETED));
        assertThat(ManageAdStatus.valueOf(AdStatus.DELETED_CS),equalTo(ManageAdStatus.REMOVED));
        assertThat(ManageAdStatus.valueOf(AdStatus.EXPIRED),equalTo(ManageAdStatus.EXPIRED));
    }

    @Test
    public void mappingFromAdStatusCoversAllAdStatusValues() {

        Set<AdStatus> universalSet = Sets.newHashSet(AdStatus.values());
        universalSet.remove(AdStatus.AWAITING_PAYMENT);   //hack for legacy

        for(AdStatus status : universalSet) {
            assertNotNull(ManageAdStatus.valueOf(status));
        }
    }
}
