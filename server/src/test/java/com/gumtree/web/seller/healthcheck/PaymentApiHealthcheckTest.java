package com.gumtree.web.seller.healthcheck;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.gumtree.healthcheck.core.Health;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertThat;

public class PaymentApiHealthcheckTest {

    private static final Integer MOCK_SERVER_PORT = 8484;
    private static WireMockServer mockServer;

    private PaymentApiHealthcheck paymentApiHealthcheck;

    @BeforeClass
    public static void setupServer() {
        mockServer = new WireMockServer(wireMockConfig().port(MOCK_SERVER_PORT));
    }

    @AfterClass
    public static void serverShutdown() {
        mockServer.stop();
    }

    @Before
    public void setup() throws Exception {
        mockServer.start();

        paymentApiHealthcheck = new PaymentApiHealthcheck("localhost", MOCK_SERVER_PORT, 10000, 20);
    }

    @Test
    public void idShouldBePaymentApi() throws Exception {
        assertThat(paymentApiHealthcheck.getId(), equalTo("payment-api"));
    }

    @Test
    public void remoteShouldBePaymentApiHostAndPort() throws Exception {
        assertThat(paymentApiHealthcheck.getRemote(), equalTo("http://localhost:8484"));
    }

    @Test
    public void shouldBeHealthyWhenPaymentApiConnectionSucceeds() throws Exception {
        assertThat(paymentApiHealthcheck.getHealth(), equalTo(Health.healthy()));
    }

    @Test
    public void shouldBeUnhealthyWhenPaymentApiConnectionSucceeds() throws Exception {
        mockServer.stop();
        Health health = paymentApiHealthcheck.getHealth();
        assertFalse(health.isHealthy());
        assertThat(health.getError(), equalTo("Error connecting to Payment API: http://localhost:8484"));
    }
}