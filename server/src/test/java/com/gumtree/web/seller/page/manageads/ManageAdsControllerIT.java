package com.gumtree.web.seller.page.manageads;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.CommonProperty;
import com.gumtree.config.SellerProperty;
import com.gumtree.web.seller.mvctests.SellerMvcJSONModelTest;
import com.gumtree.web.seller.page.reviews.service.ExternalReviewsService;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import rx.Single;
import java.util.*;

import com.gumtree.adcounters.CountersApi;
import com.gumtree.api.*;
import com.gumtree.api.client.StubBushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.client.spec.PriceApi;
import com.gumtree.config.DefaultPropertyInitializer;
import com.gumtree.google.authservice.GoogleAuthService;
import com.gumtree.mobile.test.Fixtures;
import com.gumtree.mobile.test.SecurityTestHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.ResultActions;

import static org.hamcrest.Matchers.*;
import static org.mockito.Matchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.mockito.MockitoAnnotations.initMocks;
import static org.mockito.Mockito.when;

@Configuration
class ManageAdsTestConfig {
    @Bean
    @Primary
    public CountersApi countersApi() {
        return Mockito.mock(CountersApi.class);
    }

    @Bean
    @Qualifier("adCountersGoogleAuthService")
    public GoogleAuthService googleAuthService() {
        return Mockito.mock(GoogleAuthService.class);
    }
}

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = DefaultPropertyInitializer.class)
@Import(ManageAdsTestConfig.class)
public class ManageAdsControllerIT extends SellerMvcJSONModelTest {
    @Autowired
    private ManageAdsController controller;

    @Autowired
    private CountersApi mockCountersApi;

    @Autowired
    private StubBushfireApi bushfireApi;

    @Mock
    private AdvertApi advertApi;

    @Mock
    private PriceApi priceApi;

    @Mock
    private ExternalReviewsService externalReviewsService;

    @Before
    public void beforeEach() {
        initMocks(this);

        bushfireApi.setPriceApi(priceApi);
        bushfireApi.setAdvertApi(advertApi);
        when(externalReviewsService.getGoogleReviewSwitchAsync(anyLong())).thenReturn(Single.just(false));
        super.beforeEach();
        mockMvc = initialiseWithJSONModelHandler(controller);
    }

    @Test
    public void testFilterAdverts_noSearchTerms() throws Exception {
        SecurityTestHelper.loginAs(Fixtures.USER_EMAIL);

        AdSearchResponse adSearchResponse = new AdSearchResponse();

        Ad mockAd = createMockAd();

        Ad[] adList = { mockAd };
        adSearchResponse.setPostings(adList);
        adSearchResponse.setTotalCount(1L);

        when(advertApi.search(777000L, Arrays.asList("DRAFT"), 1, 10, false)).thenReturn(
                adSearchResponse
        );

        Map<String, Integer> searchCountersMap = new HashMap<String, Integer>();
        searchCountersMap.put("777000", 5);

        when(mockCountersApi.searchCounters(any(), any(), any(), any())).thenReturn(Single.just(searchCountersMap));

        ResultActions result = mockMvc.perform(
            get("/manage/ads?mad-filter-search=Update&status=DRAFT&page=1").header("x-gt-get-model", "gumtree")
        );

        result.andExpect(status().isOk());

        result.andExpect(jsonPath("$.model.advertCount", is(1)));
        result.andExpect(jsonPath("$.model.currentPageNumber", is(1)));
        result.andExpect(jsonPath("$.model.totalNumberOfPages", is(1)));

        result.andExpect(jsonPath("$.model.core.jobsConfig.postAdUrl", is(GtProps.getStr(CommonProperty.MADGEX_WEB_POSTAD_URL))));
        assertAdPayloadCorrect(result, mockAd);
    }

    @Test
    public void testRecentlyExpiredAdverts_advertWithinWindow() throws Exception {
        SecurityTestHelper.loginAs(Fixtures.USER_EMAIL);

        AdSearchResponse adSearchResponse = new AdSearchResponse();

        Ad mockAd = createMockAd();
        mockAd.setExpiryDate(LocalDate.now().minusDays(29).toDateTimeAtStartOfDay());

        Ad[] adList = { mockAd };
        adSearchResponse.setPostings(adList);
        adSearchResponse.setTotalCount(1L);

        when(advertApi.search(777000L, Arrays.asList("EXPIRED"), 1, 10, false)).thenReturn(
                adSearchResponse
        );

        ResultActions result = mockMvc.perform(
                get("/manage/ads/recently-expired")
        );

        result.andExpect(jsonPath("$", hasSize(1)));
        result.andExpect(jsonPath("$[0].id", is(mockAd.getId().intValue())));
        result.andExpect(jsonPath("$[0].title", is(mockAd.getTitle())));
        result.andExpect(jsonPath("$[0].status", is(mockAd.getStatus().toString())));
    }

    @Test
    public void testRecentlyExpiredAdverts_advertOutsideOfWindow() throws Exception {
        SecurityTestHelper.loginAs(Fixtures.USER_EMAIL);

        AdSearchResponse adSearchResponse = new AdSearchResponse();

        Ad mockAd = createMockAd();
        mockAd.setExpiryDate(LocalDate.now().minusDays(30).toDateTimeAtStartOfDay());

        Ad[] adList = { mockAd };
        adSearchResponse.setPostings(adList);
        adSearchResponse.setTotalCount(1L);

        when(advertApi.search(777000L, Arrays.asList("EXPIRED"), 1, 10)).thenReturn(
                adSearchResponse
        );

        ResultActions result = mockMvc.perform(
                get("/manage/ads/recently-expired")
        );

        result.andExpect(jsonPath("$", hasSize(0)));
    }

    @Test
    public void testRecentlyExpiredAdverts_advertWithNoExpiryDate() throws Exception {
        SecurityTestHelper.loginAs(Fixtures.USER_EMAIL);

        AdSearchResponse adSearchResponse = new AdSearchResponse();

        Ad mockAd = createMockAd();
        mockAd.setExpiryDate(null);

        Ad[] adList = { mockAd };
        adSearchResponse.setPostings(adList);
        adSearchResponse.setTotalCount(1L);

        when(advertApi.search(777000L, Arrays.asList("EXPIRED"), 1, 10)).thenReturn(
                adSearchResponse
        );

        ResultActions result = mockMvc.perform(
                get("/manage/ads/recently-expired")
        );

        result.andExpect(jsonPath("$", hasSize(0)));
    }

    private void assertJsonValueCorrect(ResultActions result, Ad ad, String advertPath, Object expectedOutput) throws Exception {
        String jsonSelector = String.format("$.model.adverts.%s.%s", ad.getId(), advertPath);
        result.andExpect(jsonPath(jsonSelector, is(expectedOutput)));
    }

    private void assertAdPayloadCorrect(ResultActions result, Ad mockAd) throws Exception {
        assertJsonValueCorrect(result, mockAd, "adId", mockAd.getId().toString());
        assertJsonValueCorrect(result, mockAd, "rootImgUrl", "http://inte-test.imagedelivery.net/my-picture.jpg/78");
        assertJsonValueCorrect(result, mockAd, "advertUrl", GtProps.getStr(SellerProperty.BUYER_BASE_URL) + "/p/all/test-ad/102030");
        assertJsonValueCorrect(result, mockAd, "editAdvertUrl", GtProps.getStr(SellerProperty.SELLER_SECURE_BASE_URL) + "/postad?advertId=102030");
        assertJsonValueCorrect(result, mockAd, "altTag", "Test Ad Suffolk");
        assertJsonValueCorrect(result, mockAd, "status", mockAd.getStatus().toString());
        assertJsonValueCorrect(result, mockAd, "displayPrice", "£1.23");
        assertJsonValueCorrect(result, mockAd, "title", mockAd.getTitle());
        assertJsonValueCorrect(result, mockAd, "l1CategoryId", mockAd.getCategoryId().intValue());
        assertJsonValueCorrect(result, mockAd, "expires", Collections.emptyMap());
        assertJsonValueCorrect(result, mockAd, "canBeUrgent", !mockAd.getUrgent());
        assertJsonValueCorrect(result, mockAd, "canBeHomepageSpotlight", true);
        assertJsonValueCorrect(result, mockAd, "canBeBumpedUp", false);
        assertJsonValueCorrect(result, mockAd, "markedAsSold", false);
        assertJsonValueCorrect(result, mockAd, "urgent", mockAd.getUrgent());
        assertJsonValueCorrect(result, mockAd, "featured", mockAd.getFeatured());
        assertJsonValueCorrect(result, mockAd, "deletable", true);
        assertJsonValueCorrect(result, mockAd, "readOnlyCategory", false);
        assertJsonValueCorrect(result, mockAd, "featurable", !mockAd.getFeatured());
        assertJsonValueCorrect(result, mockAd, "editable", true);
        assertJsonValueCorrect(result, mockAd, "displayLocationText", "Suffolk");
    }

    private Ad createMockAd() {
        Ad ad = new Ad();

        List<Map<String, LegacyImage>> images = new ArrayList<Map<String, LegacyImage>>();
        Map<String, LegacyImage> imageMap = new HashMap<String, LegacyImage>();
        LegacyImage legacyImage = new LegacyImage();
        legacyImage.setId(123L);
        legacyImage.setUrl("http://inte-test.imagedelivery.net/my-picture.jpg/100");
        imageMap.put("thumb", legacyImage);
        images.add(imageMap);

        ad.setImages(images);
        ad.setMainImageId(123L);

        ad.setId(102030L);
        ad.setTitle("Test Ad");
        ad.setCategoryId(1L);
        ad.setStatus(AdStatus.DRAFT);
        ad.setUrgent(false);
        ad.setFeatured(false);
        ad.setPrice(new Price(123L));

        ad.setLocationId(11000039L);

        return ad;
    }
}
