package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.api.Image;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class SpotlightFeatureValidatorTest {

    private SpotlightFeatureValidator spotlightFeatureValidator;

    private ConstraintValidatorContext context;

    private PostAdDetail postAdDetail;

    private PostAdFormBean postAdFormBean;

    private SpotlightFeatureValidation spotlightFeatureValidation;

    @Before
    public void init() {
        spotlightFeatureValidator = new SpotlightFeatureValidator();
        postAdDetail = new PostAdDetail();
        spotlightFeatureValidation = mock(SpotlightFeatureValidation.class);
        context = mock(ConstraintValidatorContext.class);

        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.class);
        ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderDefinedContext nodeContext =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderDefinedContext.class);

        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(builder);
        when(builder.addNode(anyString())).thenReturn(nodeContext);

        when(spotlightFeatureValidation.fieldList()).thenReturn(new String[]{"features['SPOTLIGHT'].selected"});
        when(spotlightFeatureValidation.message()).thenReturn("postad.spotlight.needs_image");

        spotlightFeatureValidator.initialize(spotlightFeatureValidation);

        postAdFormBean = new PostAdFormBean();
    }


    @Test
    public void testNoImageSpotlightSelected() {
        FeatureBean bean = new FeatureBean();
        bean.setProductName("HOMEPAGE_SPOTLIGHT");
        bean.setSelected(true);
        postAdFormBean.getFeatures().put(ProductType.SPOTLIGHT,
                bean);
        postAdDetail.setPostAdFormBean(postAdFormBean);

        assertThat(spotlightFeatureValidator.isValid(postAdDetail, context), equalTo(false));
    }

    @Test
    public void testImageSpotLightSelected() {
        postAdDetail.setImages(Arrays.asList(new Image()));
        FeatureBean bean = new FeatureBean();
        bean.setProductName("HOMEPAGE_SPOTLIGHT");
        bean.setSelected(true);
        postAdFormBean.getFeatures().put(ProductType.SPOTLIGHT,
                bean);
        postAdDetail.setPostAdFormBean(postAdFormBean);

        assertThat(spotlightFeatureValidator.isValid(postAdDetail, context), equalTo(true));
    }

    @Test
    public void testNoSpotLightSelected() {
        FeatureBean bean = new FeatureBean();
        bean.setProductName("HOMEPAGE_SPOTLIGHT");
        bean.setSelected(false);
        postAdFormBean.getFeatures().put(ProductType.SPOTLIGHT,
                bean);
        postAdDetail.setPostAdFormBean(postAdFormBean);

        assertThat(spotlightFeatureValidator.isValid(postAdDetail, context), equalTo(true));
    }

}
