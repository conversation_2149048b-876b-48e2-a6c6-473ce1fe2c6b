package com.gumtree.web.seller.page.manageads;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.domain.advert.DeleteReason;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.manageads.api.GumtreeSuccessApiCall;
import com.gumtree.web.seller.page.manageads.api.NewAdvertStatusApiCall;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import junit.framework.TestCase;
import org.fest.assertions.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Matchers;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.AbstractUrlBasedView;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.StringContains.containsString;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DeleteAdControllerTest extends BaseSellerControllerTest {
    private UserSession authenticatedUserSession;
    private BushfireApiKey apiKey;

    @Test
    public void testDeleteAdvertRedirectsToManageAds() throws Exception {
        ModelAndView redirect = controller().deleteAdvert("1", null);


        assertThat(getUrl(redirect), containsString("manage/ads"));
        TestCase.assertTrue(redirect.getModel().containsKey("advertId"));
        Assertions.assertThat(redirect.getModel().get("advertId")).isEqualTo("1");
        verify(apiCallExecutor).call(Matchers.any(NewAdvertStatusApiCall.class));
    }

    @Test
    public void testShouldDeleteAdvertAndRedirectToManageAdsFromEmail() {
        // Given & When
        ModelAndView redirect = controller().deleteAdvertThroughEmail("1");

        // Then
        assertThat(getUrl(redirect), containsString("manage/ads"));

        Assertions.assertThat(redirect.getModel().get("responsive")).isEqualTo("true");
        Assertions.assertThat(redirect.getModel().get("mad-filter-search")).isEqualTo("Update");
        Assertions.assertThat(redirect.getModel().get("status")).isEqualTo("INACTIVE_ADS");

        verify(apiCallExecutor).call(Matchers.any(NewAdvertStatusApiCall.class));
    }

    @Test
    public void testShouldNotDeleteAdvertWhenUserIsNotOwner() {

        // Given
        ApiCallResponse apiCallResponse = mock(ApiCallResponse.class);
        when(apiCallResponse.isErrorResponse()).thenReturn(true);
        when(apiCallResponse.getErrorCode()).thenReturn(ApiErrorCode.UNAUTHORIZED_ACTION);

        when(apiCallExecutor.call(any())).thenReturn(apiCallResponse);

        // When
        ModelAndView redirect = controller().deleteAdvertThroughEmail("1");

        // Then
        assertThat(getUrl(redirect), containsString("manage/ads"));

        Assertions.assertThat(redirect.getModel().get("responsive")).isEqualTo("true");
        Assertions.assertThat(redirect.getModel().get("mad-filter-search")).isEqualTo("Update");
        Assertions.assertThat(redirect.getModel().get("status")).isEqualTo("INACTIVE_ADS");

        verify(apiCallExecutor).call(Matchers.any(NewAdvertStatusApiCall.class));
        verify(apiCallExecutor, times(1)).call(any());
    }


    @Test
    public void testDeleteAdvertGumtreeSuccess() {
        controller().deleteAdvertGumtreeSuccess("1", DeleteReason.GT_SUCCESS_YES);

        verify(apiCallExecutor).call(Matchers.any(GumtreeSuccessApiCall.class));
    }

    private DeleteAdController controller() {

        authenticatedUserSession = mock(UserSession.class);
        apiKey = new BushfireApiKey();
        when(authenticatedUserSession.getApiKey()).thenReturn(apiKey);


        apiCallExecutor = mock(ApiCallExecutor.class);
        categoryModel = mock(CategoryModel.class);
        cookieResolver = mock(CookieResolver.class);

        DeleteAdController controller = new DeleteAdController(cookieResolver, categoryModel, apiCallExecutor, messageResolver,
                urlScheme,authenticatedUserSession, mock(UserSessionService.class), mock(CustomMetricRegistry.class));

        return controller;
    }

    private String getUrl(ModelAndView modelAndView) {
        return ((AbstractUrlBasedView) modelAndView.getView()).getUrl();
    }
}
