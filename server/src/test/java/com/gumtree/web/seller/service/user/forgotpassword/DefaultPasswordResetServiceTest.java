package com.gumtree.web.seller.service.user.forgotpassword;

import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.support.factory.ResetPasswordRequestFactory;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.zeno.core.event.user.sellerside.password.PasswordResetEmailSent;
import com.gumtree.zeno.core.service.ZenoService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultPasswordResetServiceTest {

    @Mock
    private UserServiceFacade userServiceFacade;
    @Mock
    private ZenoService zenoService;
    @InjectMocks
    private DefaultPasswordResetService passwordResetService;

    @Test
    public void logsZenoEventIfResetPasswordResponseIsSuccessful() {
        String username = "<EMAIL>";
        when(userServiceFacade.resetPassword(ResetPasswordRequestFactory.createResetPasswordRequest(username))).thenReturn(ApiResponse.of(true));

        passwordResetService.resetPassword(username);

        verify(zenoService).logEvent(new String[0], Page.ForgottenPassword.getTemplateName(), PasswordResetEmailSent.class);
    }

}