package com.gumtree.web.seller.page.redirect.controller;

import com.gumtree.util.model.Actions;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.view.RedirectView;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SellerRedirectControllerTest extends BaseSellerControllerTest {

    private SellerRedirectController controller;

    @Before
    public void setUp() throws Exception {
        when(urlScheme.urlFor(Actions.USER_HOME)).thenReturn("http://abc.com/");
        controller = new SellerRedirectController(cookieResolver, categoryModel, apiCallExecutor, messageResolver,
                urlScheme, mock(UserSessionService.class));
    }

    @Test
    public void testHandleSlash() throws Exception {
        RedirectView result = controller.handleSlash();
        assertEquals("http://abc.com/", result.getUrl());
    }
}
