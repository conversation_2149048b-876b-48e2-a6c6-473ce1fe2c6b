package com.gumtree.web.seller.config;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.UnfilteredCategoryModel;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerConfig;
import com.gumtree.config.servlet.SellerServletConfig;
import com.gumtree.seller.infrastructure.driven.user.UserAdPreferenceServiceApi;
import com.gumtree.userapi.client.config.UserApiPropNames;
import com.gumtree.userapi.stub.config.UserApiStubSettings;
import com.gumtree.userapi.stub.config.UserServiceStubConfig;
import com.gumtree.web.seller.service.postad.UserPostcodeLookupService;
import com.gumtree.web.seller.service.postad.UserPostcodeLookupServiceImpl;
import com.gumtree.zeno.core.EventHandler;
import com.gumtree.zeno.core.InMemoryEventHandler;
import com.gumtree.zeno.core.sampling.SamplingStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import static org.mockito.Mockito.mock;

@Configuration
@Import({
        SellerConfig.class,
        SellerServletConfig.class,
        UserServiceStubConfig.class,
        StubRedisConfig.class,
        MotorsApiStubConfig.class,
        PriceGuidanceApiMockServerConfig.class
})
public class SellerMvcTestConfig extends WebMvcConfigurerAdapter {

    @Autowired
    private SamplingStrategy samplingStrategy;

    /**
     * Overrides default event handler with in memory event handler which make it easier to inspect events logged
     * by zeno
     * @return the in memory event handler
     */
    @Bean
    public EventHandler zenoEventHandler() {
        return new InMemoryEventHandler(samplingStrategy);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        super.addResourceHandlers(registry);
        registry.setOrder(-1);

        // mapping for js resources
        registry.addResourceHandler("/static/**")       // configured in 'gumtree.static.assets.seller.host' in seller-server-overrides.properties
                .addResourceLocations("/responsive");   // configured in SellerMvcContextLoader
    }

    @Bean
    public UserApiStubSettings userApiStubSettings() {
        UserApiStubSettings settings = new UserApiStubSettings();
        String url = GtProps.getStr(UserApiPropNames.BASE_URL);
        String[] parts = url.split("localhost:");
        if (parts.length != 2) {
            throw new IllegalArgumentException(
                    UserApiPropNames.BASE_URL + " property needs to be in format: http://localhost:<PORT> but is: " + url);
        }
        settings.setPort(Integer.valueOf(parts[1]));
        return settings;
    }

    @Bean
    public ViewResolver mockViewResolver() {
        return new MockViewResolver();
    }

    @Bean
    public UserPostcodeLookupService userPostcodeLookupService() {
        return (Long accountId) -> Optional.absent();
    }

    @Bean
    public UserAdPreferenceServiceApi  userAdPreferenceServiceApi() {
        return mock(UserAdPreferenceServiceApi.class);
    }

    @Bean
    public UnfilteredCategoryModel unfilteredCategoryModel(CategoryModel categoryModel) {
        return new UnfilteredCategoryModel(categoryModel);
    }
}
