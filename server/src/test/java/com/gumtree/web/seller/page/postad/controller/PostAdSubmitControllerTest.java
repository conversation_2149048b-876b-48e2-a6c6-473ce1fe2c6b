package com.gumtree.web.seller.page.postad.controller;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.security.phone.number.authenticator.model.AuthenticationStatus;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.controller.steps.*;
import com.gumtree.web.seller.page.postad.converter.PostAdImageConverter;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService;
import com.gumtree.web.seller.service.PostAdWorkspace;
import com.gumtree.web.seller.service.phoneverify.PhoneVerifyService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.*;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PostAdSubmitControllerTest {
    @Mock
    private CookieResolver cookieResolver;
    @Mock private CategoryModel categoryModel;
    @Mock private ApiCallExecutor apiCallExecutor;
    @Mock private ErrorMessageResolver messageResolver;
    @Mock private UrlScheme urlScheme;
    @Mock private PostAdWorkspace postAdWorkspace;
    @Mock private UserSession userSession;
    @Mock private CategoryService categoryService;
    @Mock private GumtreePageContext pageContext;
    @Mock private LocationService locationService;
    @Mock private UserSessionService userSessionService;
    @Mock private PhoneVerifyService phoneVerifyService;
    @Mock private AttributePresentationService attributePresentationService;
    @Mock private PostAdImageConverter postAdImageConverter;
    @Mock private PostAdFormDescriptionHintService descriptionHintService;
    @Mock private RemoteIP remoteIP;
    @Mock private HttpServletRequest request;
    @Mock private AdvertEditor advertEditor;
    @Mock private ThreatMetrixCookie threatMetrixCookie;
    @Spy
    private CustomMetricRegistry customMetricRegistry = new CustomMetricRegistry(new SimpleMeterRegistry());

    private PostAdSubmitModel.Builder postAdSubmitModelBuilder;
    private PostAdSubmitController controller;

    private static final Long USER_ID = 987125L;
    private static final User USER = User.builder().withId(USER_ID).build();


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        controller = new PostAdSubmitController(
                cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, postAdWorkspace, userSession,
                categoryService, pageContext, locationService, userSessionService, customMetricRegistry, phoneVerifyService,
                attributePresentationService, postAdImageConverter, descriptionHintService);
        // 注入postAdSteps
        List<PostAdStep> steps = new ArrayList<>();
        PostAdStep step1 = mock(PostAdStep.class);
        when(step1.getOrder()).thenReturn(1);
        steps.add(step1);
        ReflectionTestUtils.setField(controller, "postAdSteps", steps);

        // set: user logged in
        when(userSessionService.getUser()).thenReturn(Optional.of(USER));

        List<PostAdFormPanel> panels = new ArrayList<PostAdFormPanel>(){{
            add(PostAdFormPanel.CATEGORY);
            add(PostAdFormPanel.LOCATION);
            add(PostAdFormPanel.AD_TITLE);
        }};
        postAdSubmitModelBuilder = new PostAdSubmitModel.Builder().addPanels(panels);
//        when(postAdSubmitModelBuilder.addPanels(panels)).thenReturn(postAdSubmitModelBuilder);
//        when(postAdSubmitModelBuilder.build()).thenReturn(mock(PostAdSubmitModel.class));

    }

//    @Test
//    public void testPostAdvert_removeDraft() {
//        PostAdSubmitController.PostAdFormBeanWithAction bean = new PostAdSubmitController.PostAdFormBeanWithAction();
//        bean.setRemoveDraft(true);
//        bean.setCategoryId(1L);
//        bean.setExtendFields(new HashMap<>());
//        when(userSession.getUserType()).thenReturn(UserLoginStatus.NEW_REGISTERED);
//        when(postAdWorkspace.getEditor(anyString())).thenReturn(advertEditor);
//        when(advertEditor.getPostAdFormBean()).thenReturn(bean);
//        when(categoryService.getCategoryModel()).thenReturn(categoryModel);
//
//        bean.setEmailAddress("<EMAIL>");
//        when(postAdWorkspace.resetEditor(anyString(), any(), any(), any())).thenReturn(advertEditor);
//        when(advertEditor.getCategoryId()).thenReturn(1L);
//        when(categoryModel.getL1CategoryFor(1L)).thenReturn(Optional.absent());
//        customMetricRegistry.postAdSubmitPageTimer("resetEditor");
//        PostAdSubmitModel result = controller.postAdvert(bean, "editorId", remoteIP, request);
//        assertThat(result).isNotNull();
//    }

    @Test
    public void testPostAdvert_attributeStep_true() {
        PostAdSubmitController.PostAdFormBeanWithAction bean = new PostAdSubmitController.PostAdFormBeanWithAction();
        bean.setRemoveDraft(false);
        bean.setCategoryId(1L);
        bean.setAttributeStep(true);
        bean.setExtendFields(new HashMap<>());
        when(userSession.getUserType()).thenReturn(UserLoginStatus.EXISTING);
        when(postAdWorkspace.getEditor(anyString())).thenReturn(advertEditor);
        when(advertEditor.getPostAdFormBean()).thenReturn(bean);
        when(categoryService.getCategoryModel()).thenReturn(categoryModel);
        when(advertEditor.getCategoryId()).thenReturn(1L);
        when(categoryModel.getL1CategoryFor(1L)).thenReturn(Optional.absent());
        List<AttributeMetadata> attrList = new ArrayList<>();
        when(categoryModel.getCategoryAttributes(1L)).thenReturn(Optional.of(attrList));
        PostAdStep attrStep = mock(PostAdStep.class);
        when(attrStep.getOrder()).thenReturn(1);
        when(attrStep.execute(any(), any())).thenReturn(true);
        List<PostAdStep> steps = Arrays.asList(attrStep);
        ReflectionTestUtils.setField(controller, "postAdSteps", steps);
        customMetricRegistry.postAdSubmitPageTimer("updateEditor");
        PostAdSubmitModel result = controller.postAdvert(bean, "editorId", remoteIP, request);
        assertThat(result).isNotNull();
    }

    @Test
    public void testPostAdvert_attributeStep_true_Ai_Flow() {
        PostAdSubmitController.PostAdFormBeanWithAction bean = new PostAdSubmitController.PostAdFormBeanWithAction();
        bean.setRemoveDraft(false);
        bean.setCategoryId(1L);
        bean.setAttributeStep(true);
        bean.setAiFlowType("B");
        bean.setExtendFields(new HashMap<>());
        when(userSession.getUserType()).thenReturn(UserLoginStatus.EXISTING);
        when(postAdWorkspace.getEditor(anyString())).thenReturn(advertEditor);
        when(advertEditor.getPostAdFormBean()).thenReturn(bean);
        when(categoryService.getCategoryModel()).thenReturn(categoryModel);
        when(advertEditor.getCategoryId()).thenReturn(1L);
        when(categoryModel.getL1CategoryFor(1L)).thenReturn(Optional.absent());
        List<AttributeMetadata> attrList = new ArrayList<>();
        when(categoryModel.getCategoryAttributes(1L)).thenReturn(Optional.of(attrList));
        PostAdStep attrStep = mock(PostAdStep.class);
        when(attrStep.getOrder()).thenReturn(1);
        when(attrStep.execute(any(), any())).thenReturn(true);
        List<PostAdStep> steps = Arrays.asList(attrStep);
        ReflectionTestUtils.setField(controller, "postAdSteps", steps);
        customMetricRegistry.postAdSubmitPageTimer("updateEditor");
        PostAdSubmitModel result = controller.postAdvert(bean, "editorId", remoteIP, request);
        assertThat(result).isNotNull();
    }

    @Test
    public void testPostAdvert_attributeStep_true_Ai_Flow_C() {
        PostAdSubmitController.PostAdFormBeanWithAction bean = new PostAdSubmitController.PostAdFormBeanWithAction();
        bean.setRemoveDraft(false);
        bean.setCategoryId(1L);
        bean.setAttributeStep(true);
        bean.setAiFlowType("C");
        bean.setExtendFields(new HashMap<>());
        Map<String, String> attributeMap = new HashMap<>();
        attributeMap.put("price", "100");
        bean.setAttributes(attributeMap);
        when(userSession.getUserType()).thenReturn(UserLoginStatus.EXISTING);
        when(postAdWorkspace.getEditor(anyString())).thenReturn(advertEditor);
        when(advertEditor.getPostAdFormBean()).thenReturn(bean);
        when(categoryService.getCategoryModel()).thenReturn(categoryModel);
        when(advertEditor.getCategoryId()).thenReturn(1L);
        when(categoryModel.getL1CategoryFor(1L)).thenReturn(Optional.absent());
        List<AttributeMetadata> attrList = new ArrayList<>();
        when(categoryModel.getCategoryAttributes(1L)).thenReturn(Optional.of(attrList));
        PostAdStep attrStep = mock(PostAdStep.class);
        when(attrStep.getOrder()).thenReturn(1);
        when(attrStep.execute(any(), any())).thenReturn(true);
        List<PostAdStep> steps = Arrays.asList(attrStep);
        ReflectionTestUtils.setField(controller, "postAdSteps", steps);
        customMetricRegistry.postAdSubmitPageTimer("updateEditor");
        PostAdSubmitModel result = controller.postAdvert(bean, "editorId", remoteIP, request);
        assertThat(result).isNotNull();
    }

//    @Test
//    public void testPostAdvert_attributeStep_false() {
//        PostAdSubmitController.PostAdFormBeanWithAction bean = new PostAdSubmitController.PostAdFormBeanWithAction();
//        bean.setRemoveDraft(false);
//        bean.setCategoryId(1L);
//        bean.setAttributeStep(false);
//        bean.setExtendFields(new HashMap<>());
//        when(userSession.getUserType()).thenReturn(UserLoginStatus.EXISTING);
//        when(postAdWorkspace.getEditor(anyString())).thenReturn(advertEditor);
//        when(advertEditor.getPostAdFormBean()).thenReturn(bean);
//
//        when(advertEditor.getCategoryId()).thenReturn(1L);
//        when(categoryModel.getL1CategoryFor(1L)).thenReturn(Optional.absent());
//        when(categoryModel.getCategoryAttributes(1L)).thenReturn(Optional.absent());
//        PostAdStep step = mock(PostAdStep.class);
//        when(step.getOrder()).thenReturn(1);
//        when(step.execute(any(), any())).thenReturn(true);
//        List<PostAdStep> steps = Arrays.asList(step);
//        ReflectionTestUtils.setField(controller, "postAdSteps", steps);
//        customMetricRegistry.postAdSubmitPageTimer("resetEditor");
//        PostAdSubmitModel result = controller.postAdvert(bean, "editorId", remoteIP, request);
//        assertThat(result).isNotNull();
//    }

//    @Test
//    public void testPostAdvert_phoneVerificationRequired() {
//        PostAdSubmitController.PostAdFormBeanWithAction bean = new PostAdSubmitController.PostAdFormBeanWithAction();
//        bean.setRemoveDraft(false);
//        bean.setCategoryId(10985L);
//        bean.setExtendFields(new HashMap<>());
//        when(userSession.getUserType()).thenReturn(UserLoginStatus.NEW_UNREGISTERED);
//        when(postAdWorkspace.getEditor(anyString())).thenReturn(advertEditor);
//        when(advertEditor.getPostAdFormBean()).thenReturn(bean);
//        when(userSession.getUser()).thenReturn(USER);
//        when(advertEditor.getCategoryId()).thenReturn(10985L);
//        when(categoryModel.getL1CategoryFor(10985L)).thenReturn(Optional.absent());
//        when(request.getHeader(anyString())).thenReturn("test-agent");
//        when(phoneVerifyService.getAuthenticationStatus(anyLong())).thenReturn(AuthenticationStatus.UNVERIFIED);
//        when(userSession.isProUser()).thenReturn(false);
//        customMetricRegistry.postAdSubmitPageTimer("resetEditor");
//        PostAdSubmitModel result = controller.postAdvert(bean, "editorId", remoteIP, request);
//        assertThat(result).isNotNull();
//    }

//    @Test
//    public void testPostAdvert_proUser() {
//        PostAdSubmitController.PostAdFormBeanWithAction bean = new PostAdSubmitController.PostAdFormBeanWithAction();
//        bean.setRemoveDraft(false);
//        bean.setCategoryId(1L);
//        bean.setExtendFields(new HashMap<>());
//        when(userSession.getUserType()).thenReturn(UserLoginStatus.EXISTING);
//        when(postAdWorkspace.getEditor(anyString())).thenReturn(advertEditor);
//        when(advertEditor.getPostAdFormBean()).thenReturn(bean);
//        when(advertEditor.getCategoryId()).thenReturn(1L);
//        when(categoryModel.getL1CategoryFor(1L)).thenReturn(Optional.absent());
//        when(userSession.isProUser()).thenReturn(true);
//        customMetricRegistry.postAdSubmitPageTimer("resetEditor");
//        PostAdSubmitModel result = controller.postAdvert(bean, "editorId", remoteIP, request);
//        assertThat(result).isNotNull();
//    }

    @Test
    public void stepsAreOrderedCorrectly() {
        assertThat(CategorySelectPostAdStep.ORDER).isEqualTo(1);
        assertThat(LegalPostAdStep.ORDER).isEqualTo(2);
        assertThat(LocationAndSellerTypePostAdStep.ORDER).isEqualTo(3);
        assertThat(RateLimiterPostAdStep.ORDER).isEqualTo(4);
        assertThat(ImagesPostAdStep.ORDER).isEqualTo(5);
        assertThat(LastPostAdStep.ORDER).isEqualTo(6);
    }
}