package com.gumtree.web.seller.page.registration;

import com.gumtree.api.User;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.model.UserResponse;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.registration.model.ConfirmationModel;
import com.gumtree.web.zeno.userregistration.UserActivationFailureZenoEvent;
import com.gumtree.web.zeno.userregistration.UserRegistrationSuccessZenoEvent;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.ui.Model;
import org.springframework.validation.support.BindingAwareModelMap;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.gumtree.web.seller.page.NoDependenciesController.ENCRYPTED_PARAMETER_MAP_NAME;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ConfirmationPageControllerTest extends BaseSellerControllerTest {

    private ConfirmationPageController controller;

    @Before
    public void init() {

        when(messageResolver.getMessage(anyString(), anyString())).thenAnswer((Answer<String>) invocationOnMock -> {
            Object[] args = invocationOnMock.getArguments();
            return (String) args[0];
        });
        controller = new ConfirmationPageController(cookieResolver, categoryModel, apiCallExecutor, messageResolver,
                urlScheme, zenoService, userSessionService, parameterEncryption, userServiceFacade);

        autowireAbExperimentsService(controller);
    }

    @Test
    public void verify500IsReturnedWhenNoBeanIsPresentInModel() throws Exception {

        // given
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setId(1234L);

        Map<String,String> data = new HashMap<>();
        data.put("userId",user.getId().toString());
        data.put("registrationFlow", "true");
        String encrypted = "encrypted..";

        when(request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME)).thenReturn(encrypted);

        when(parameterEncryption.decryptUrlEncodedParameterMap(encrypted)).thenReturn(data);

        HttpServletResponse response = mock(HttpServletResponse.class);
        when(request.getParameter("emailAddress")).thenReturn(null);

        // when
        ModelAndView actual = controller.renderPage(response, pageContext, request);

        // then
        assertThat(actual, equalTo(null));
        verify(response).sendError(500);
    }

    @Test
    public void verifyRegistrationSuccessFlowEvents() throws Exception {
        // given
        HttpServletResponse response = mock(HttpServletResponse.class);
        Model model = new BindingAwareModelMap();
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setId(1234L);

        Map<String,String> data = new HashMap<>();
        data.put("emailAddress", user.getEmail());
        data.put("userId",user.getId().toString());
        data.put("registrationFlow", "true");
        String encrypted = "encrypted..";
        when(parameterEncryption.decryptUrlEncodedParameterMap(encrypted)).thenReturn(data);

        when(request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME)).thenReturn(encrypted);

        when(pageContext.getUser()).thenReturn(user);
        // when
        ModelAndView view = controller.renderPage(response, pageContext, request);

        // then
        assertThat(view.getViewName(), equalTo(Page.RegistrationConfirmation.getTemplateName()));
        ConfirmationModel confirmationModel = (ConfirmationModel) view.getModelMap().get("model");
        assertThat(confirmationModel.getResendPath(), equalTo(ResendActivationEmailPageController.PAGE_PATH + "/"
                + pageContext.getUser().getId() + "/"));
        verify(zenoService, times(1)).logEvent(new UserRegistrationSuccessZenoEvent(user.getId(), user.getEmail()));
    }

    @Test
    public void verifyRegistrationWithUserAlreadyExistsSuccessFlowEvents() throws Exception {
        // given
        HttpServletResponse response = mock(HttpServletResponse.class);
        Model model = new BindingAwareModelMap();
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setId(1234L);

        Map<String,String> data = new HashMap<>();
        data.put("emailAddress", user.getEmail());
        // -1 when user already activated or some other status then 'AWAITING_ACTIVATION'
        data.put("userId",String.valueOf(-1L));
        data.put("registrationFlow", "true");
        String encrypted = "encrypted..";
        when(parameterEncryption.decryptUrlEncodedParameterMap(encrypted)).thenReturn(data);

        when(request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME)).thenReturn(encrypted);
        when(userServiceFacade.getUserByEmail(user.getEmail()))
        .thenReturn(ApiResponse.of(new UserResponse().userId(1234L)));

        when(pageContext.getUser()).thenReturn(user);
        // when
        ModelAndView view = controller.renderPage(response, pageContext, request);

        // then
        assertThat(view.getViewName(), equalTo(Page.RegistrationConfirmation.getTemplateName()));
        ConfirmationModel confirmationModel = (ConfirmationModel) view.getModelMap().get("model");
        assertThat(confirmationModel.getResendPath(), equalTo(ResendActivationEmailPageController.PAGE_PATH + "/"
                + pageContext.getUser().getId() + "/"));
        verify(zenoService, times(1)).logEvent(new UserRegistrationSuccessZenoEvent(user.getId(), user.getEmail()));
    }

    @Test
    public void verifyRegistrationFailFlowEvents() throws Exception {

        // given
        HttpServletResponse response = mock(HttpServletResponse.class);
        Model model = new BindingAwareModelMap();
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setId(1234L);

        Map<String,String> data = new HashMap<>();
        data.put("userId",user.getId().toString());
        data.put("registrationFlow", "true");
        String encrypted = "encrypted..";
        when(parameterEncryption.decryptUrlEncodedParameterMap(encrypted)).thenReturn(data);

        when(request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME)).thenReturn(encrypted);

        when(pageContext.getUser()).thenReturn(user);

        // when
        ModelAndView view = controller.renderPage(response, pageContext, request);

        // then
        verify(zenoService, times(1)).logEvent(new UserActivationFailureZenoEvent());
    }

    @Test
    public void shouldShowResetPageWhenEncryptedParametersMissing() throws IOException {
        // when
        ModelAndView result = controller.renderPage(response, pageContext, request);

        // then
        RedirectView view = (RedirectView) result.getView();
        assertThat(view.getUrl(), equalTo(RegistrationPageController.PAGE_PATH));
    }
}
