package com.gumtree.web.seller.page.reviews.model;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class UserRatingTest {

    @Test
    public void shouldFeelRatingWithZeros() {
        // when
        UserRating userRating = new UserRating();

        // then
        assertThat(userRating.getAverage()).isEqualTo(0f);
        assertThat(userRating.getTotal()).isEqualTo(0);
        assertThat(userRating.getFormattedAverage()).isEqualTo("0.0");
        assertThat(userRating.getBreakdown().get(1)).isEqualTo(0);
        assertThat(userRating.getBreakdown().get(2)).isEqualTo(0);
        assertThat(userRating.getBreakdown().get(3)).isEqualTo(0);
        assertThat(userRating.getBreakdown().get(4)).isEqualTo(0);
        assertThat(userRating.getBreakdown().get(5)).isEqualTo(0);
        assertThat(userRating.getBreakdown()).hasSize(5);
    }
}