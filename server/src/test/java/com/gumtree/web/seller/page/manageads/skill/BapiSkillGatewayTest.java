package com.gumtree.web.seller.page.manageads.skill;

import com.gumtree.bapi.SkillControllerApi;
import com.gumtree.bapi.model.*;
import com.gumtree.web.seller.service.skill.BapiSkillGateway;
import com.gumtree.web.seller.service.skill.SkillAttributeMetadataService;
import com.gumtree.web.seller.service.skill.model.SkillAttributeMetadata;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import rx.Single;

import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BapiSkillGatewayTest {

    @Mock
    private SkillControllerApi skillControllerApi;

    @Mock
    private SkillAttributeMetadataService skillAttributeMetadataService;

    @InjectMocks
    private BapiSkillGateway bapiSkillGateway;

    private static final Long TEST_ACCOUNT_ID = 123L;
    private static final Integer TEST_CATEGORY_ID = 11365;

    @Before
    public void setUp() {
        SkillResponse skillResponse = new SkillResponse();
        skillResponse.setIsEdited(true);
        skillResponse.setSkills(Arrays.asList(1, 2, 3));
        when(skillControllerApi.getSelectedSkills(TEST_ACCOUNT_ID, TEST_CATEGORY_ID))
            .thenReturn(Single.just(skillResponse));

        SkillUpdateResponse updateResponse = new SkillUpdateResponse();
        updateResponse.setSuccess(true);
        when(skillControllerApi.updateSkill(anyLong(), any(SkillRequest.class)))
            .thenReturn(Single.just(updateResponse));

        SkillCreateResponse createResponse = new SkillCreateResponse();
        createResponse.setSuccess(true);
        when(skillControllerApi.createSkill(anyLong(), any(SkillRequest.class)))
            .thenReturn(Single.just(createResponse));
    }

    @Test
    public void testGetSelectedSkills() {
        // When
        SkillResponse response = bapiSkillGateway.getSelectedSkills(TEST_ACCOUNT_ID, TEST_CATEGORY_ID);

        // Then
        assertNotNull(response);
        assertTrue(response.getIsEdited());
        assertEquals(Arrays.asList(1, 2, 3), response.getSkills());
        verify(skillControllerApi).getSelectedSkills(TEST_ACCOUNT_ID, TEST_CATEGORY_ID);
    }

    @Test
    public void testGetSelectedSkills_EmptyResponse() {
        // Given
        SkillResponse emptyResponse = new SkillResponse();
        emptyResponse.setIsEdited(false);
        emptyResponse.setSkills(Arrays.asList());
        when(skillControllerApi.getSelectedSkills(TEST_ACCOUNT_ID, TEST_CATEGORY_ID))
            .thenReturn(Single.just(emptyResponse));

        when(skillAttributeMetadataService.getSuggestedSkillIdsByCategoryId(TEST_CATEGORY_ID))
            .thenReturn(Arrays.asList(1, 2));

        // When
        SkillResponse response = bapiSkillGateway.getSelectedSkills(TEST_ACCOUNT_ID, TEST_CATEGORY_ID);

        // Then
        assertNotNull(response);
        assertFalse(response.getIsEdited());
        assertEquals(Arrays.asList(1, 2), response.getSkills());
        verify(skillControllerApi).getSelectedSkills(TEST_ACCOUNT_ID, TEST_CATEGORY_ID);
        verify(skillAttributeMetadataService).getSuggestedSkillIdsByCategoryId(TEST_CATEGORY_ID);
    }

    @Test
    public void testCreateSkill() {
        // Given
        SkillRequest request = new SkillRequest();
        request.setCategoryId(TEST_CATEGORY_ID);
        request.setSelectedSkills(Arrays.asList(1, 2, 3));

        // When
        Boolean result = bapiSkillGateway.createSkill(TEST_ACCOUNT_ID, request);

        // Then
        assertTrue(result);
        verify(skillControllerApi).createSkill(TEST_ACCOUNT_ID, request);
    }

    @Test
    public void testUpdateSkill() {
        // Given
        SkillRequest request = new SkillRequest();
        request.setCategoryId(TEST_CATEGORY_ID);
        request.setSelectedSkills(Arrays.asList(1, 2, 3));

        // When
        SkillUpdateResponse response = bapiSkillGateway.updateSkill(TEST_ACCOUNT_ID, request);

        // Then
        assertNotNull(response);
        assertTrue(response.getSuccess());
        verify(skillControllerApi).updateSkill(TEST_ACCOUNT_ID, request);
    }

    @Test
    public void testUpdateSkill_Failure() {
        // Given
        SkillRequest request = new SkillRequest();
        request.setCategoryId(TEST_CATEGORY_ID);
        request.setSelectedSkills(Arrays.asList(1, 2, 3));

        SkillUpdateResponse errorResponse = new SkillUpdateResponse();
        errorResponse.setSuccess(false);
        errorResponse.setMessage("Update failed");
        when(skillControllerApi.updateSkill(anyLong(), any(SkillRequest.class)))
            .thenReturn(Single.just(errorResponse));

        // When
        SkillUpdateResponse response = bapiSkillGateway.updateSkill(TEST_ACCOUNT_ID, request);

        // Then
        assertNotNull(response);
        assertFalse(response.getSuccess());
        assertEquals("Update failed", response.getMessage());
        verify(skillControllerApi).updateSkill(TEST_ACCOUNT_ID, request);
    }

    private SkillAttributeMetadata.SkillAttribute createSkill(Integer skillId, String name, boolean suggest) {
        SkillAttributeMetadata.SkillAttribute skill = new SkillAttributeMetadata.SkillAttribute();
        skill.setSkillId(String.valueOf(skillId));
        skill.setName(name);
        skill.setSuggested(suggest);
        return skill;
    }
} 
