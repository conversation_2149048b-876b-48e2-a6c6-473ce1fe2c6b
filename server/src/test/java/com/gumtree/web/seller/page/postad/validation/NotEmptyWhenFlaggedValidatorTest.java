package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintValidatorContext;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class NotEmptyWhenFlaggedValidatorTest {
    private NotEmptyWhenFlaggedValidator validator;
    private ConstraintValidatorContext context;
    private NotEmptyWhenFlagged newf;

    @Before
    public void setup() {
        validator = new NotEmptyWhenFlaggedValidator();
        context = mock(ConstraintValidatorContext.class);
        newf = mock(NotEmptyWhenFlagged.class);

        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.class);
        ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderDefinedContext nodeContext =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderDefinedContext.class);

        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(builder);
        when(builder.addNode(anyString())).thenReturn(nodeContext);

        when(newf.field()).thenReturn("contactTelephone");
        when(newf.flagField()).thenReturn("usePhone");
        when(newf.message()).thenReturn("postad.telephone.missing");

        validator.initialize(newf);
    }

    @Test
    public void testIsValidWithValueAndFlagSet() throws Exception {
        PostAdFormBean bean = makeTestBean("013234235", true);

        boolean result = validator.isValid(bean, context);
        assertTrue(result);
    }

    @Test
    public void testIsValidWithNoValueOrFlagSet() throws Exception {
        PostAdFormBean bean = makeTestBean(null, false);

        boolean result = validator.isValid(bean, context);
        assertTrue(result);
    }

    @Test
    public void testIsValidWithValueSetButFlagFalse() throws Exception {
        PostAdFormBean bean = makeTestBean("013234235", false);

        boolean result = validator.isValid(bean, context);
        assertTrue(result);
    }

    @Test
    public void testIsInvalidWithFlagSetButValueMissing() throws Exception {
        PostAdFormBean bean = makeTestBean(null, true);

        boolean result = validator.isValid(bean, context);
        assertFalse(result);
        verify(context).buildConstraintViolationWithTemplate("postad.telephone.missing");
        verify(context).disableDefaultConstraintViolation();
    }

    private PostAdFormBean makeTestBean(String value, boolean flag) {
        PostAdFormBean bean = new PostAdFormBean();
        bean.setContactTelephone(value);
        bean.setUsePhone(flag);

        return bean;
    }
}
