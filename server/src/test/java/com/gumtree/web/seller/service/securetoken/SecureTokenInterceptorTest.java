package com.gumtree.web.seller.service.securetoken;

import org.junit.Before;
import org.junit.Test;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.ModelAndViewDefiningException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 */
public class SecureTokenInterceptorTest {
    private static final String SECURE_TOKEN_VIEW = "view-name";
    private static final String SAMPLE_TOKEN = "my-token-value";

    private SecureTokenInterceptor interceptor;
    private SecureTokenService secureTokenService;
    private HttpServletRequest req;
    private HttpServletResponse resp;
    private HandlerMethod handler;
    private ModelAndView modelAndView;
    private SecureToken secureToken;

    @Before
    public void setup() {
        secureTokenService = mock(SecureTokenService.class);
        req = mock(HttpServletRequest.class);
        resp = mock(HttpServletResponse.class);
        handler = mock(HandlerMethod.class);
        modelAndView = mock(ModelAndView.class);

        secureToken = mock(SecureToken.class);
        when(secureToken.value()).thenReturn(SECURE_TOKEN_VIEW);

        interceptor = new SecureTokenInterceptor(secureTokenService);
    }

    @Test
    public void preHandleWhenNoSecureTokenNothingHappens() throws Exception {
        when(handler.getMethodAnnotation(SecureToken.class)).thenReturn(null);
        when(req.getMethod()).thenReturn("POST");

        boolean result = interceptor.preHandle(req, resp, handler);

        assertThat(result, equalTo(true));
        verifyZeroInteractions(secureTokenService);
    }

    @Test
    public void postHandleWhenNoSecureTokenNothingHappens() throws Exception {
        when(handler.getMethodAnnotation(SecureToken.class)).thenReturn(null);

        interceptor.postHandle(req, resp, handler, modelAndView);

        verifyZeroInteractions(req);
        verifyZeroInteractions(secureTokenService);
        verifyZeroInteractions(modelAndView);
    }

    @Test
    public void preHandleReturnsTrueForValidTokenPost() throws Exception {
        when(handler.getMethodAnnotation(SecureToken.class)).thenReturn(secureToken);
        when(req.getMethod()).thenReturn("POST");
        when(req.getParameter(SecureToken.TOKEN_PARAMETER_NAME)).thenReturn(SAMPLE_TOKEN);

        when(secureTokenService.checkToken(secureToken.value(), SAMPLE_TOKEN)).thenReturn(true);

        boolean result = interceptor.preHandle(req, resp, handler);

        assertThat(result, equalTo(true));
        verify(secureTokenService).checkToken(secureToken.value(), SAMPLE_TOKEN);
    }

    @Test
    public void preHandleReturnsTrueForValidTokenPut() throws Exception {
        when(handler.getMethodAnnotation(SecureToken.class)).thenReturn(secureToken);
        when(req.getMethod()).thenReturn("PUT");
        when(req.getParameter(SecureToken.TOKEN_PARAMETER_NAME)).thenReturn(SAMPLE_TOKEN);

        when(secureTokenService.checkToken(secureToken.value(), SAMPLE_TOKEN)).thenReturn(true);

        boolean result = interceptor.preHandle(req, resp, handler);

        assertThat(result, equalTo(true));
        verify(secureTokenService).checkToken(secureToken.value(), SAMPLE_TOKEN);
    }

    @Test
    public void preHandleReturns400WhenNoToken() throws Exception {
        when(handler.getMethodAnnotation(SecureToken.class)).thenReturn(secureToken);
        when(req.getMethod()).thenReturn("POST");
        when(req.getParameter(SecureToken.TOKEN_PARAMETER_NAME)).thenReturn(null);

        boolean result = interceptor.preHandle(req, resp, handler);

        assertThat(result, equalTo(false));
        verifyZeroInteractions(secureTokenService);
        verify(resp).sendError(400);
    }

    @Test(expected = ModelAndViewDefiningException.class)
    public void preHandleThrowsErrorWhenNoMatchToken() throws Exception {
        when(handler.getMethodAnnotation(SecureToken.class)).thenReturn(secureToken);
        when(req.getMethod()).thenReturn("POST");
        when(req.getParameter(SecureToken.TOKEN_PARAMETER_NAME)).thenReturn("not-the-right-token");

        interceptor.preHandle(req, resp, handler);
    }

    @Test
    public void postHandleAddsToModelWhenTokenIsNecessary() throws Exception {
        when(handler.getMethodAnnotation(SecureToken.class)).thenReturn(secureToken);
        when(req.getMethod()).thenReturn("POST");
        when(secureTokenService.getOrGenerateToken(secureToken.value())).thenReturn(SAMPLE_TOKEN);

        interceptor.postHandle(req, resp, handler, modelAndView);

        verify(secureTokenService).getOrGenerateToken(secureToken.value());
        verify(modelAndView).addObject(SecureToken.TOKEN_PARAMETER_NAME, SAMPLE_TOKEN);
    }
}
