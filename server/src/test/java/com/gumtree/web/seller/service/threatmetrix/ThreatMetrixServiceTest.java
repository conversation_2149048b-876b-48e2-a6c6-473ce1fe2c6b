package com.gumtree.web.seller.service.threatmetrix;

import com.gumtree.common.properties.Env;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.GumtreeCookieProperty;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookieCutter;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrixTracking;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Properties;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThreatMetrixServiceTest {

    static {
        // Set up environment configuration for tests
        Properties properties = new Properties();
        properties.setProperty("gumtree.env", Env.DEV.name());
        properties.setProperty(GumtreeCookieProperty.COOKIES_SECURE.getPropertyName(), "false");
        properties.setProperty(GumtreeCookieProperty.COOKIES_DOMAIN.getPropertyName(), "gumtree.com");
        ConfigurationManager.loadProperties(properties);

    }

    @Mock
    private CookieResolver cookieResolver;

    @Mock
    private ThreatMetrixCookieCutter threatMetrixCookieCutter;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private ThreatMetrixCookie threatMetrixCookie;


    private ThreatMetrixService threatMetrixService;

    private static final String TEST_ORG_ID = "test-org-123";
    private static final String TEST_WEB_BASE_URL = "https://test.gumtree.com";

    @Before
    public void setUp() {
        threatMetrixService = new ThreatMetrixService();
        ReflectionTestUtils.setField(threatMetrixService, "cookieResolver", cookieResolver);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", threatMetrixCookieCutter);
        ReflectionTestUtils.setField(threatMetrixService, "organisationId", TEST_ORG_ID);
        ReflectionTestUtils.setField(threatMetrixService, "webBaseUrl", TEST_WEB_BASE_URL);
        ReflectionTestUtils.setField(threatMetrixService, "enabled", true);
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenDisabled() {
        // Given
        ReflectionTestUtils.setField(threatMetrixService, "enabled", false);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertFalse(result.isEnabled());
        assertFalse(result.hasValidData());
        assertNull(result.getSessionId());
        assertNull(result.getTracking());

    }

    @Test
    public void testGetExistingThreatMetrixCookie_Success() {
        // Given - 使用真实的 ThreatMetrixCookie 实例
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();

        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixCookie result = threatMetrixService.getExistingThreatMetrixCookie(request);

        // Then
        assertNotNull(result);
        assertEquals(realThreatMetrixCookie, result);
        assertNotNull(result.getDefaultValue()); // 真实 cookie 有有效的 UUID
    }

    @Test
    public void testGetExistingThreatMetrixCookie_Exception() {
        // Given
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenThrow(new RuntimeException("Cookie not found"));

        // When
        ThreatMetrixCookie result = threatMetrixService.getExistingThreatMetrixCookie(request);

        // Then
        assertNull(result);
    }

    @Test
    public void testIsEnabled() {
        // When/Then
        assertTrue(threatMetrixService.isEnabled());

        // Given disabled
        ReflectionTestUtils.setField(threatMetrixService, "enabled", false);

        // When/Then
        assertFalse(threatMetrixService.isEnabled());
    }

    @Test
    public void testGetOrganisationId() {
        // When/Then
        assertEquals(TEST_ORG_ID, threatMetrixService.getOrganisationId());
    }

    @Test
    public void testGetWebBaseUrl() {
        // When/Then
        assertEquals(TEST_WEB_BASE_URL, threatMetrixService.getWebBaseUrl());
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_Success() {
        // Given -
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        //
        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertNotNull(result.getSessionId()); // 真实 cookie 生成的 UUID
        assertNotNull(result.getTracking());
        assertNotNull(result.getCookie());
        assertEquals(realThreatMetrixCookie, result.getCookie());

        // Verify tracking object properties
        ThreatMetrixTracking tracking = result.getTracking();
        assertEquals(TEST_ORG_ID, tracking.getOrgId());
        assertEquals(realThreatMetrixCookie.getDefaultValue(), tracking.getSessionId());
        assertEquals(TEST_WEB_BASE_URL, tracking.getWebBaseUrl());

        // Verify cookie was added to response
        verify(response).addCookie(any(Cookie.class));
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_WithEnvironmentAwareCookieName() {
        // Given -
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        //
        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertNotNull(result.getSessionId());
        assertEquals(realThreatMetrixCookie, result.getCookie());

        // Verify cookie was added to response (环境相关的命名由真实的 CookieCutter 处理)
        verify(response).addCookie(any(Cookie.class));
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_CookieResolverException() {
        // Given
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class))
                .thenThrow(new RuntimeException("Cookie resolution failed"));

        // When/Then - Should propagate the exception since this is the main business logic
        try {
            threatMetrixService.processThreatMetrixForApiResponse(request, response);
        } catch (RuntimeException e) {
            assertEquals("Cookie resolution failed", e.getMessage());
        }
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_NullSessionId() {
        // Given -
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);


        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(threatMetrixCookie);
        when(threatMetrixCookie.getDefaultValue()).thenReturn(null);
        when(threatMetrixCookie.getDomain()).thenReturn(testDomain);
        when(threatMetrixCookie.getPath()).thenReturn("/");
        when(threatMetrixCookie.getMaxAge()).thenReturn(-1);
        when(threatMetrixCookie.isHttpOnly()).thenReturn(true);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData()); // Still valid because cookie and tracking exist
        assertNull(result.getSessionId());
        assertNotNull(result.getTracking());
        assertNotNull(result.getCookie());

        // Verify tracking object with null session ID
        ThreatMetrixTracking tracking = result.getTracking();
        assertEquals(TEST_ORG_ID, tracking.getOrgId());
        assertNull(tracking.getSessionId());
        assertEquals(TEST_WEB_BASE_URL, tracking.getWebBaseUrl());

        // Verify cookie was still added to response
        verify(response).addCookie(any(Cookie.class));
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_EmptySessionId() {
        // Given -
        String testDomain = "gumtree.com";
        String emptySessionId = "";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        //
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(threatMetrixCookie);
        when(threatMetrixCookie.getDefaultValue()).thenReturn(emptySessionId);
        when(threatMetrixCookie.getDomain()).thenReturn(testDomain);
        when(threatMetrixCookie.getPath()).thenReturn("/");
        when(threatMetrixCookie.getMaxAge()).thenReturn(-1);
        when(threatMetrixCookie.isHttpOnly()).thenReturn(true);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertEquals(emptySessionId, result.getSessionId());
        assertNotNull(result.getTracking());

        // Verify tracking object with empty session ID
        ThreatMetrixTracking tracking = result.getTracking();
        assertEquals(TEST_ORG_ID, tracking.getOrgId());
        assertEquals(emptySessionId, tracking.getSessionId());
        assertEquals(TEST_WEB_BASE_URL, tracking.getWebBaseUrl());

        // Verify cookie was added to response
        verify(response).addCookie(any(Cookie.class));
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_VerifyAllInteractions() {
        // Given -
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);

        //
        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);

        // Verify expected interactions occurred
        verify(cookieResolver, times(1)).resolve(request, ThreatMetrixCookie.class);
        verify(response, times(1)).addCookie(any(Cookie.class));

        // Verify the result contains all expected data
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertEquals(realThreatMetrixCookie, result.getCookie());
        assertNotNull(result.getTracking());
        assertNotNull(result.getSessionId());
    }

    @Test
    public void testProcessThreatMetrixForApiResponse_WhenEnabled_SpecificEnvironments() {
        // Given -
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);


        ThreatMetrixCookie realThreatMetrixCookie = realCookieCutter.cutNew();
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(realThreatMetrixCookie);

        // When
        ThreatMetrixInfo result = threatMetrixService.processThreatMetrixForApiResponse(request, response);

        // Then
        assertNotNull(result);
        assertTrue(result.isEnabled());
        assertTrue(result.hasValidData());
        assertNotNull(result.getSessionId());
        verify(response).addCookie(any(Cookie.class));
    }

    @Test
    public void shouldDoNothingWhenDisabled() {
        ReflectionTestUtils.setField(threatMetrixService, "enabled", false);

        threatMetrixService.invalidateThreatMetrixCookie(request, response);

        verify(response, never()).addCookie(any(Cookie.class));
    }

    @Test
    public void testInvalidateThreatMetrixCookieWhenCookieExists() {
        // Arrange
        String testDomain = "gumtree.com";
        ThreatMetrixCookieCutter realCookieCutter = new ThreatMetrixCookieCutter(testDomain);
        ReflectionTestUtils.setField(threatMetrixService, "threatMetrixCookieCutter", realCookieCutter);
        String cookieName = realCookieCutter.getName(Env.DEV);
        Cookie existingCookie = new Cookie(cookieName, "testValue");
        existingCookie.setPath("/"); // ThreatMetrixCookieCutter.PATH 为 "/"
        MockHttpServletRequest request1 = new MockHttpServletRequest();
        request1.setCookies(existingCookie);
        MockHttpServletResponse response1 = new MockHttpServletResponse();

        // Act
        threatMetrixService.invalidateThreatMetrixCookie(request1, response1);

        // Assert
        Cookie invalidatedCookie = response1.getCookie(cookieName);
        assertNotNull("Invalidated cookie should be present in response",invalidatedCookie);
        assertEquals(0, invalidatedCookie.getMaxAge());
        assertEquals("", invalidatedCookie.getValue());
        assertFalse( "Cookie secure flag should be false",invalidatedCookie.getSecure());
        assertEquals("Cookie path should be /","/", invalidatedCookie.getPath());
    }

}