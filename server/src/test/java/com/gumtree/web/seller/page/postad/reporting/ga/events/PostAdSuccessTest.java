package com.gumtree.web.seller.page.postad.reporting.ga.events;

import com.gumtree.api.Ad;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PostAdSuccessTest {

    private ThirdPartyRequestContext ctx;
    private Category category;
    private Location location;
    private Order order;
    private Ad ad;

    @Before
    public void setUp(){
        ctx = mock(ThirdPartyRequestContext.class);
        category = mock(Category.class);
        location = mock(Location.class);
        order = mock(Order.class);
        ad = new Ad();
        when(ctx.getPageType()).thenReturn(PageType.PostAd);
        when(ctx.getCategory()).thenReturn(category);
        when(ctx.getLocation()).thenReturn(location);
        when(ctx.getOrder()).thenReturn(order);
        when(ctx.getPageModel()).thenReturn(ad);
        when(category.getId()).thenReturn(23L);
        when(location.getId()).thenReturn(42);
        ad.setId(64L);
    }

    @Test
    public void checkFreeValues() {

        when(order.isFree()).thenReturn(true);

        PostAdSuccess event = new PostAdSuccess(ctx);

        assertThat(event.getAction(), equalTo("PostAdFreeSuccess"));
        assertThat(event.getCategory(), equalTo("Post Ad Form"));
        assertThat(event.getLabel(), equalTo("catID=23;locID=42;adID=64"));
    }

    @Test
    public void checkPaidValues() {
        when(order.isFree()).thenReturn(false);

        PostAdSuccess event = new PostAdSuccess(ctx);

        assertThat(event.getAction(), equalTo("PostAdPaidSuccess"));
        assertThat(event.getCategory(), equalTo("Post Ad Form"));
        assertThat(event.getLabel(), equalTo("catID=23;locID=42;adID=64"));
    }


}
