package com.gumtree.web.seller.service.image;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.common.ConsoleNotifier;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.config.SellerProperty;
import com.gumtree.config.api.FeignClientConfiguration;
import com.gumtree.config.api.MediaProcessorApiConfig;
import com.gumtree.mediaprocessor.model.ApiError;
import com.gumtree.mediaprocessor.model.ApiErrorDetail;
import com.gumtree.mediaprocessor.model.ApiErrors;
import com.gumtree.mediaprocessor.model.Id;
import com.gumtree.web.seller.service.image.error.MediaProcessorServerException;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;
import feign.codec.Encoder;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertThrows;

public class MediaProcessorGatewayTest {

    private MediaProcessorGateway mediaProcessorGateway;
    private HystrixRequestContext context;

    @Rule
    public WireMockRule wireMockRule = new WireMockRule(wireMockConfig().dynamicPort().notifier(new ConsoleNotifier(true)));

    @Before
    public void setUp() throws Exception {
        GtPropManager.setProperty(SellerProperty.MEDIA_PROCESSOR_CONNECTION_TIMEOUT.getPropertyName(), "1000");
        GtPropManager.setProperty(SellerProperty.MEDIA_PROCESSOR_SOCKET_TIMEOUT.getPropertyName(), "1000");
        GtPropManager.setProperty(SellerProperty.MEDIA_PROCESSOR_HOST.getPropertyName(), wireMockRule.baseUrl());
        GtPropManager.setProperty(SellerProperty.MEDIA_PROCESSOR_MAX_ATTEMPTS.getPropertyName(), "2");
        GtPropManager.setProperty(SellerProperty.BAPI_SECRET_HEADER.getPropertyName(), "gumtree");

        MediaProcessorApiConfig mediaProcessorImageConfig = new MediaProcessorApiConfig(new SimpleMeterRegistry());
        FeignClientConfiguration feignClientConfiguration = new FeignClientConfiguration();
        Encoder encoder = feignClientConfiguration.feignEncoder(() -> null);
        mediaProcessorGateway = new MediaProcessorGateway(mediaProcessorImageConfig.getMediaProcessorImageApi(encoder));

        this.context = HystrixRequestContext.initializeContext();
    }

    @After
    public void tearDown() {
        this.context.shutdown();
    }

    @Test
    public void postFile() throws IOException {
        String testPath = "/images/upload";

        ObjectMapper objectMapper = new ObjectMapper();

        MockMultipartFile mockMultipartFile = getMultipartFile();

        stubFor(post(urlEqualTo(testPath))
                .willReturn(aResponse()
                        .withBody(objectMapper.writeValueAsString(new Id().id(1L)))
                        .withStatus(200)));

        Long id = mediaProcessorGateway.post(mockMultipartFile);

        assertThat(id, is(1L));

    }

    private MockMultipartFile getMultipartFile() {
        return new MockMultipartFile("blah.jpg", "some binary".getBytes());
    }

    @Test
    public void failedToPostFileHasApiErrors() throws IOException {
        String testPath = "/images/upload";

        MockMultipartFile mockMultipartFile = getMultipartFile();

        ObjectMapper objectMapper = new ObjectMapper();

        ApiErrors apiErrors = new ApiErrors().status(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .addErrorsItem(new ApiError().detail("detail #1").title("title #1")
                        .addDetailsItem(new ApiErrorDetail().field("file").value("not an image")));

        stubFor(post(urlEqualTo(testPath))
                .willReturn(aResponse()
                        .withBody(objectMapper.writeValueAsString(apiErrors))
                        .withStatus(500)));

        MediaProcessorServerException e = assertThrows(MediaProcessorServerException.class, () -> mediaProcessorGateway.post(mockMultipartFile));
        assertThat(e.getStatus(), is(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        assertThat(e.getApiErrors().getErrors().size(), is(1));
        assertThat(e.getApiErrors().getErrors().get(0).getTitle(), is("title #1"));
        assertThat(e.getApiErrors().getErrors().get(0).getDetail(), is("detail #1"));

    }

    @Test
    public void failedToPostFileNoApiErrors() {
        String testPath = "/images/upload";

        MockMultipartFile mockMultipartFile = getMultipartFile();

        stubFor(post(urlEqualTo(testPath))
                .willReturn(aResponse()
                        .withStatus(500)));

        MediaProcessorServerException e = assertThrows(MediaProcessorServerException.class, () -> mediaProcessorGateway.post(mockMultipartFile));
        assertThat(e.getApiErrors().getErrors().size(), is(1));
        assertThat(e.getStatus(), is(HttpStatus.INTERNAL_SERVER_ERROR.value()));
        assertThat(e.getApiErrors().getErrors().get(0).getDetail(), is("Unknown error"));
    }

}
