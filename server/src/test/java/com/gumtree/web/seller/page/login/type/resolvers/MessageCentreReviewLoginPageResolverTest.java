package com.gumtree.web.seller.page.login.type.resolvers;

import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.login.type.LoginPageResolverData;
import org.fest.assertions.api.Assertions;
import org.junit.Test;

import java.util.Optional;

@SuppressWarnings("ConstantConditions")
public class MessageCentreReviewLoginPageResolverTest {
    private MessageCentreReviewLoginPageResolver messageCentreReviewLoginPageResolver =
            new MessageCentreReviewLoginPageResolver();

    @Test
    public void shouldReturnPostAdLogin() {
        Optional<Page> page = messageCentreReviewLoginPageResolver
                .resolve(LoginPageResolverData.builder()
                        .withSavedRequestURI("/manage/messages/ABC123")
                        .withSavedQueryString("rvw=123")
                        .build());
        Assertions.assertThat(page.isPresent()).isTrue();
        Assertions.assertThat(page.get()).isEqualTo(Page.Login_MessageCentreReview);
    }

    @Test
    public void shouldReturnPostAdLoginGivenReviewConversationId() {
        Optional<Page> page = messageCentreReviewLoginPageResolver
                .resolve(LoginPageResolverData.builder()
                        .withSavedRequestURI("/manage/messages/ABC123")
                        .withSavedQueryString("rvw_conversationId=abc123")
                        .build());
        Assertions.assertThat(page.isPresent()).isTrue();
        Assertions.assertThat(page.get()).isEqualTo(Page.Login_MessageCentreReview);
    }

    @Test
    public void shouldReturnEmptyIfPartiallyMatched() {
        Optional<Page> page = messageCentreReviewLoginPageResolver
                .resolve(LoginPageResolverData.builder()
                        .withSavedRequestURI("/manage/messages/ABC123")
                        .withSavedQueryString("x=123")
                        .build());
        Assertions.assertThat(page.isPresent()).isFalse();
    }

    @Test
    public void shouldReturnEmptyIfNotMatched() {
        Optional<Page> page = messageCentreReviewLoginPageResolver
                .resolve(LoginPageResolverData.builder().withSavedRequestURI("/some/ABC123").build());
        Assertions.assertThat(page.isPresent()).isFalse();
    }
}