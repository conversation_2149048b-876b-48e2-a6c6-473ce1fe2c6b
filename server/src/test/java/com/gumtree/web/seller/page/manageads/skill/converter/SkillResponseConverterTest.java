package com.gumtree.web.seller.page.manageads.skill.converter;

import com.gumtree.bapi.model.SkillResponse;
import com.gumtree.web.seller.page.manageads.skill.model.FacadeSkillCreateResponse;
import com.gumtree.web.seller.page.manageads.skill.model.FacadeSkillQueryResponse;
import com.gumtree.web.seller.page.manageads.skill.model.FacadeSkillUpdateResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class SkillResponseConverterTest {

    @Test
    public void testToQueryResponse() {
        // Given
        SkillResponse response = new SkillResponse();
        response.setIsEdited(true);
        response.setSkills(Arrays.asList(1, 2, 3));

        // When
        FacadeSkillQueryResponse result = SkillResponseConverter.toQueryResponse(response);

        // Then
        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(200), result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEdited());
        assertEquals(Arrays.asList(1, 2, 3), result.getData().getSkills());
    }

    @Test
    public void testToQueryErrorResponse() {
        // When
        FacadeSkillQueryResponse result = SkillResponseConverter.toQueryErrorResponse(500, "Test error");

        // Then
        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(500), result.getCode());
        assertEquals("Test error", result.getMsg());
    }

    @Test
    public void testToCreateResponse() {
        // Given
        Integer categoryId = 123;
        List<Integer> skills = Arrays.asList(1, 2, 3);

        // When
        FacadeSkillCreateResponse result = SkillResponseConverter.toCreateResponse(categoryId, skills);

        // Then
        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(200), result.getCode());
        assertNotNull(result.getData());
        assertEquals("123", result.getData().getCategoryId());
        assertEquals(skills, result.getData().getSkills());
    }

    @Test
    public void testToCreateErrorResponse() {
        // When
        FacadeSkillCreateResponse result = SkillResponseConverter.toCreateErrorResponse(500, "Test error");

        // Then
        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(500), result.getCode());
        assertEquals("Test error", result.getMsg());
    }

    @Test
    public void testToUpdateResponse() {
        // Given
        Integer categoryId = 123;
        List<Integer> skills = Arrays.asList(1, 2, 3);

        // When
        FacadeSkillUpdateResponse result = SkillResponseConverter.toUpdateResponse(categoryId, skills);

        // Then
        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(200), result.getCode());
        assertNotNull(result.getData());
        assertEquals("123", result.getData().getCategoryId());
        assertEquals(skills, result.getData().getSkills());
    }

    @Test
    public void testToUpdateErrorResponse() {
        // When
        FacadeSkillUpdateResponse result = SkillResponseConverter.toUpdateErrorResponse(500, "Test error");

        // Then
        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(500), result.getCode());
        assertEquals("Test error", result.getMsg());
    }
} 
