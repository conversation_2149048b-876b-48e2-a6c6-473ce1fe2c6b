package com.gumtree.web.seller.page.password.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Mockito.*;

/**
 */
public class ValidateActivationKeyApiCallTest {

    private BushfireApi bushfireApi;

    private UserApi userApi;

    private ValidateActivationKeyApiCall apiCall;

    @Before
    public void init() {
        bushfireApi = mock(BushfireApi.class);
        userApi = mock(UserApi.class);
        when(bushfireApi.create(UserApi.class)).thenReturn(userApi);
    }

    @Test
    public void makesCorrectCallToApiToActivateUser() {
        apiCall = new ValidateActivationKeyApiCall("<EMAIL>", "some_key");
        apiCall.execute(bushfireApi);
        verify(userApi).validateActivationKey("<EMAIL>", "some_key");
    }
}
