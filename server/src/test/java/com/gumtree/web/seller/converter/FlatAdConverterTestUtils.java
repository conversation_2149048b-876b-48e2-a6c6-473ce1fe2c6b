package com.gumtree.web.seller.converter;

import com.gumtree.liveadsearch.model.Location;
import com.gumtree.liveadsearch.model.FlatAd;
import com.gumtree.liveadsearch.model.Feature;
import com.gumtree.liveadsearch.model.Category;

import com.gumtree.seller.domain.product.entity.ProductName;

import java.util.*;

/**
 * Utils for converting FlatAd to other advert types
 */
public class FlatAdConverterTestUtils {

    List<Feature> expirableFeatures(ProductName... productNames) {
        List<Feature> features = new ArrayList<>();
        for (ProductName productName : productNames) {
            features.add(new Feature().product(productName.name()));
        }
        return features;
    }

   List<Feature> urlFeature(String url) {
        List<Feature> features = new ArrayList<>();
        features.add(new Feature().url(url).product(ProductName.WEBSITE_URL.name()));
        return features;
    }

    Map<String, Object> setAttributes(String key, Object value){
        Map<String, Object> attributes=new HashMap<>();
        attributes.put(key,value);
        return attributes;
    }

    FlatAd flatAd(FlatAdData data) {
        FlatAd flatAd = new FlatAd();
        flatAd.setId(data.getId());
        flatAd.setTitle(data.getTitle());
        flatAd.setDescription(data.getDescription());
        flatAd.setPrimaryImageUrl(data.getPrimaryImageUrl());
        flatAd.setAdditionalImageUrls(data.getAdditionalImageUrls());
        flatAd.setYoutubeUrl(data.getYoutubeUrl());
        flatAd.setLocalArea(data.getLocalArea());
        flatAd.setCategories(data.getCategories());
        flatAd.setLocations(data.getLocations());
        flatAd.setContactEmail(data.getContactEmail());
        flatAd.setStatus(data.getStatus());
        flatAd.setCreatedDate(data.getCreatedDate());
        flatAd.setPublishedDate(data.getPublishedDate());
        flatAd.setLastModifiedDate(data.getLastModifiedDate());
        flatAd.setFeatures(data.getFeatures());
        flatAd.setContactTelephone(data.getContactTelephone());
        flatAd.setContactUrl(data.getContactUrl());
        flatAd.setVisibleOnMap(data.isVisibleOnMap());
        flatAd.setPaidFor(data.isPaidFor());
        return flatAd;
    }

    public static abstract class FlatAdData {
        public abstract Long getId();

        public abstract String getTitle();

        public abstract String getDescription();

        public abstract String getPrimaryImageUrl();

        public abstract String getLocalArea();

        public abstract List<Category> getCategories();

        public abstract List<Location> getLocations();

        public abstract String getContactEmail();

        public abstract String getStatus();

        public abstract Long getCreatedDate();

        public abstract Long getPublishedDate();

        public abstract Long getLastModifiedDate();

        public abstract List<Feature> getFeatures();

        public abstract Boolean isPaidFor();

        public abstract Boolean isVisibleOnMap();

        public String getContactTelephone() {
            return "1234";
        }

        public String getContactUrl() {
            return null;
        }

        public abstract List<String> getAdditionalImageUrls();

        public abstract String getYoutubeUrl();
    }

}
