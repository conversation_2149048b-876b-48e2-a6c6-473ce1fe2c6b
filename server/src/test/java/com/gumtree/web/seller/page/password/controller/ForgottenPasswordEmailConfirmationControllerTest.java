package com.gumtree.web.seller.page.password.controller;

import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.Page;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.ui.Model;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.Map;

import static com.gumtree.web.seller.page.password.controller.ForgottenPasswordController.ENCRYPTED_MAP_PARAMETER_NAME;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ForgottenPasswordEmailConfirmationControllerTest extends BaseSellerControllerTest {

    private ForgottenPasswordEmailConfirmationController controller;
    private Model model;
    private HttpServletResponse response;

    @Before
    public void init() {
        model = mock(Model.class);
        response = mock(HttpServletResponse.class);

        controller = new ForgottenPasswordEmailConfirmationController(cookieResolver,
                categoryModel,
                apiCallExecutor,
                messageResolver,
                urlScheme,
                userSessionService,
                parameterEncryption);

        autowireAbExperimentsService(controller);
    }

    @Test
    public void testShowPage() {
        String encrypted = "encrypted..";
        when(request.getParameter(ENCRYPTED_MAP_PARAMETER_NAME)).thenReturn(encrypted);
        Map<String, String> data = Collections.singletonMap("email", "<EMAIL>");
        when(parameterEncryption.decryptUrlEncodedParameterMap(encrypted)).thenReturn(data);
        ModelAndView view = controller.showPage(request);
        assertThat(view.getViewName(), equalTo(Page.ForgottenPasswordConfirmation.getTemplateName()));
    }

    @Test
    public void failAndRedirectBackToForgottenPasswordPageWhenEncryptedMapParameterMissing() {
        when(request.getParameter(ENCRYPTED_MAP_PARAMETER_NAME)).thenReturn(null);
        RedirectView view = (RedirectView) controller.showPage(request).getView();
        assertThat(view.getUrl(), equalTo(ForgottenPasswordController.PAGE_PATH));
    }
}
