package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintValidatorContext;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class AdTitleHasPhoneNumberValidatorTest extends AdTitleValidatorBase {

    private AdTitleHasPhoneNumberValidation validation;
    private AdTitleHasPhoneNumberValidator validator;
    private ConstraintValidatorContext context;

    @Before
    public void setup() {
        ErrorMessageResolver resolver = mock(ErrorMessageResolver.class);
        when(resolver.getMessage(ERR_MSG_CODE_PN, "")).thenReturn(ERR_MSG_PN);
        context = mock(ConstraintValidatorContext.class);
        validator = new AdTitleHasPhoneNumberValidator(resolver);
        validation = mock(AdTitleHasPhoneNumberValidation.class);
        when(validation.message()).thenReturn(ERR_MSG_CODE_PN);
        when(validation.fieldList()).thenReturn(new String[]{"description"});
        validator.initialize(validation);
        doNothing().when(context).disableDefaultConstraintViolation();

        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.class);
        ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext nodeContext =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext.class);

        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(builder);
        when(builder.addPropertyNode(anyString())).thenReturn(nodeContext);
        when(builder.addConstraintViolation()).thenReturn(context);
    }

    @Test
    public void isValidWithoutPhoneNumber() {
        assertTrue(validator.isValid(makeTestTitleDetail(TITLE_WITH_TEXT), context));
    }

    @Test
    public void isNotValidWithPhoneNumber1() {
        assertThatTitleWithPhoneNumberIsNotValid(TITLE_WITH_PHONE_NUMBER_1);
    }

    @Test
    public void isNotValidWithPhoneNumber2() {
        assertThatTitleWithPhoneNumberIsNotValid(TITLE_WITH_PHONE_NUMBER_2);
    }

    @Test
    public void isNotValidWithPhoneNumber3() {
        assertThatTitleWithPhoneNumberIsNotValid(TITLE_WITH_PHONE_NUMBER_3);
    }

    @Test
    public void isNotValidWithPhoneNumber4() {
        assertThatTitleWithPhoneNumberIsNotValid(TITLE_WITH_PHONE_NUMBER_4);
    }

    @Test
    public void isNotValidWithPhoneNumber5() {
        assertThatTitleWithPhoneNumberIsNotValid(TITLE_WITH_PHONE_NUMBER_5);
    }

    private void assertThatTitleWithPhoneNumberIsNotValid(String title) {
        PostAdDetail detail = makeTestTitleDetail(title);
        assertFalse(validator.isValid(detail, context));
        verify(context).buildConstraintViolationWithTemplate(ERR_MSG_PN);
        verify(context).disableDefaultConstraintViolation();
    }
}
