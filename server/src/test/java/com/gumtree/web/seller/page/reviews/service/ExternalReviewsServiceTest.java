package com.gumtree.web.seller.page.reviews.service;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.domain.category.Categories;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.fulladsearch.model.FullAdSearchRequest;
import com.gumtree.fulladsearch.model.FullAdSearchResult;
import com.gumtree.tns.api.ReviewConnectionsManagementApi;
import com.gumtree.tns.api.ReviewDisplayApi;
import com.gumtree.tns.dto.*;
import com.gumtree.web.seller.page.reviews.model.ExternalReviewManagement;
import com.gumtree.web.seller.page.reviews.model.google.AsyncResult;
import com.gumtree.web.seller.page.reviews.model.google.GoogleRelationListResponse;
import com.gumtree.web.seller.page.reviews.model.google.GoogleReviewStatus;
import com.gumtree.web.seller.page.reviews.model.google.GoogleReviews;
import com.gumtree.web.seller.page.reviews.model.google.oauth.GoogleOAuthWebRequest;
import com.gumtree.web.util.ResourceNotFoundException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import rx.Single;

import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.fest.assertions.api.Assertions.fail;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ExternalReviewsServiceTest {

    @InjectMocks
    private ExternalReviewsService externalReviewsService;

    @Mock
    private ReviewConnectionsManagementApi reviewConnectionsManagementApi;

    @Mock
    private ReviewDisplayApi reviewDisplayApi;

    @Mock
    private FullAdsSearchApi fullAdsSearchApi;

    @Mock
    private CategoryModel categoryModel;

    @Test
    public void isServiceShouldReturnTrueForServiceCategory() {
        // given
        Long serviceCategoryId = Categories.SERVICES.getId();

        // when
        boolean result = externalReviewsService.isService(serviceCategoryId);

        // then
        assertThat(result).isTrue();
    }

    @Test
    public void isServiceShouldReturnFalseForNonServiceCategory() {
        // given
        Long nonServiceCategoryId = Categories.MOTORS.getId();

        // when
        boolean result = externalReviewsService.isService(nonServiceCategoryId);

        // then
        assertThat(result).isFalse();
    }

    @Test
    public void isServiceShouldReturnFalseForNullCategory() {
        // given
        Long nullCategoryId = null;

        // when
        boolean result = externalReviewsService.isService(nullCategoryId);

        // then
        assertThat(result).isFalse();
    }

    @Test
    public void testSetGoogleDisplay() {
        // given
        Long accountId = 123L;
        String relationId = "456";
        Integer display = 1;
        ThirdReviewAccountRelationOutput output = new ThirdReviewAccountRelationOutput();
        output.setId(Long.valueOf(relationId));
        when(reviewDisplayApi.updateDisplayStatus(eq(String.valueOf(accountId)), eq(Long.valueOf(relationId)), any(UpdateDisplayStatusRequest.class)))
                .thenReturn(Single.just(output));

        // when
        String result = externalReviewsService.setGoogleDisplay(accountId, relationId, display);

        // then
        assertThat(result).isEqualTo(relationId);
    }

    @Test
    public void testReSyncGoogleData() {
        // given
        Long accountId = 123L;
        String authId = "789";
        OperationResult operationResult = new OperationResult();
        operationResult.setOperationId("op-1");
        when(reviewConnectionsManagementApi.syncRelation(String.valueOf(accountId), Long.parseLong(authId)))
                .thenReturn(Single.just(operationResult));

        // when
        AsyncResult result = externalReviewsService.reSyncGoogleData(accountId, authId);

        // then
        assertThat(result.getOperationId()).isEqualTo("op-1");
    }

    @Test
    public void testGetGoogleRelationList() {
        // given
        Long accountId = 123L;
        String authId = "789";
        ThirdReviewAccountRelationOutput relationOutput = new ThirdReviewAccountRelationOutput();
        relationOutput.setId(456L);
        relationOutput.setNameRedundant("Business Name");
        when(reviewConnectionsManagementApi.getRelationChoices(String.valueOf(accountId), ReviewProviderEnum.GBP, Long.parseLong(authId)))
                .thenReturn(Single.just(Collections.singletonList(relationOutput)));

        // when
        GoogleRelationListResponse result = externalReviewsService.getGoogleRelationList(accountId, authId);

        // then
        assertThat(result.getRelations()).hasSize(1);
        assertThat(result.getRelations().get(0).getRelationId()).isEqualTo("456");
        assertThat(result.getRelations().get(0).getBusinessName()).isEqualTo("Business Name");
    }

    @Test
    public void testSetGoogleRelation() {
        // given
        Long accountId = 123L;
        String relationId = "456";
        ThirdReviewAccountRelationOutput output = new ThirdReviewAccountRelationOutput();
        output.setId(Long.valueOf(relationId));
        when(reviewConnectionsManagementApi.setEffectiveRelation(String.valueOf(accountId), Long.parseLong(relationId)))
                .thenReturn(Single.just(output));

        // when
        String result = externalReviewsService.setGoogleRelation(accountId, relationId);

        // then
        assertThat(result).isEqualTo(relationId);
    }

    @Test
    public void testDoGoogleAuth() {
        // given
        Long accountId = 123L;
        GoogleOAuthWebRequest request = new GoogleOAuthWebRequest();
        request.setAuthCode("authCode");
        request.setRedirectUrl("redirectUrl");
        request.setClientId("clientId");
        request.setCodeVerifier("codeVerifier");

        OperationResult operationResult = new OperationResult();
        operationResult.setStatus(OperationResult.StatusEnum.COMPLETED);
        operationResult.setMessage("Success");
        operationResult.setTimestamp(OffsetDateTime.now());
        operationResult.setEstimatedCompletionTime(OffsetDateTime.now().plusSeconds(10));
        operationResult.setOperationId("op-123");

        when(reviewConnectionsManagementApi.authorizeReviewSource(eq(ReviewProviderEnum.GBP), any(GbpAuthorizationRequest.class)))
                .thenReturn(Single.just(operationResult));

        // when
        AsyncResult result = externalReviewsService.doGoogleAuth(accountId, request);

        // then
        assertThat(result.getStatus()).isEqualTo(operationResult.getStatus().getValue());
        assertThat(result.getMessage()).isEqualTo(operationResult.getMessage());
        assertThat(result.getTimestamp()).isNotNull();
        assertThat(result.getEstimatedCompletionTime()).isNotNull();
        assertThat(result.getOperationId()).isEqualTo(operationResult.getOperationId());
    }

    @Test
    public void testGetSummary_NoAuthorization() {
        // given
        Long accountId = 123L;
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(0L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new ResourceNotFoundException()));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));

        // when - The error is caught by onErrorReturn which logs it and throws RuntimeException
        // But the RuntimeException is caught by RxJava and the method continues with null
        try {
            externalReviewsService.getSummary(accountId, false);
            fail("Expected RuntimeException");
        } catch (RuntimeException e) {
            // Expected - handleError() throws RuntimeException which propagates from toBlocking().value()
            assertThat(e.getCause()).isInstanceOf(ResourceNotFoundException.class);
        }
    }

    @Test
    public void testGetSummary_DataSyncing() {
        // given
        Long accountId = 123L;
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.IN_PROGRESS);
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));

        // when
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);

        // then
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.DATA_SYNCING.getValue());
        assertThat(googleReviews.getAuth().getAuthid()).isEqualTo("1");
    }

    @Test
    public void testGetSummary_DataSyncingPending() {
        // given - test PENDING status (also in DATA_SYNC_ING array)
        Long accountId = 123L;
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.PENDING);
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));

        // when
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);

        // then
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.DATA_SYNCING.getValue());
        assertThat(googleReviews.getAuth().getAuthid()).isEqualTo("1");
    }

    @Test
    public void testGetSummary_NoMerchantInfo() {
        // given
        Long accountId = 123L;
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.COMPLETED);
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));

        // when
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);

        // then
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.NO_MERCHANT_INFO.getValue());
        assertThat(googleReviews.getAuth().getAuthid()).isEqualTo("1");
    }

    @Test
    public void testGetSummary_DoNotNeedShow() {
        // given
        Long accountId = 123L;
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.COMPLETED);
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(0L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));

        // when
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);

        // then
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.DO_NOT_NEED_SHOW.getValue());
    }

    @Test
    public void testIsPostSpecialCategory() {
        // given
        Long accountId = 123L;
        Long categoryId = 456L;
        FullAdSearchResult response = new FullAdSearchResult();
        response.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(response));

        // when
        Boolean result = externalReviewsService.isPostSpecialCategory(accountId, categoryId).toBlocking().value();

        // then
        assertThat(result).isTrue();
    }

    @Test
    public void testGetGoogleReviewSwitchAsync_HasPost() {
        // given
        Long accountId = 123L;
        // isBindGoogleReview = true
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput()));
        // isPostSpecialCategory = true
        FullAdSearchResult response = new FullAdSearchResult();
        response.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(response));

        // when
        Boolean result = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();

        // then
        assertThat(result).isFalse();
    }

    @Test
    public void testGetSummary_OKStatus() {
        // given
        Long accountId = 123L;
        
        // Setup oauth info
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.COMPLETED);
        
        // Setup relation info
        ThirdReviewAccountRelationOutput relation = new ThirdReviewAccountRelationOutput();
        relation.setId(456L);
        relation.setNameRedundant("Business Name");
        relation.setDisplayStatus(DisplayStatusEnum.DISPLAYED);
        
        // Setup review info
        ThirdReviewInfoOutput info = new ThirdReviewInfoOutput();
        info.setTotalReviews(100);
        info.setAvgRating(4.5f);
        info.setName("Business Name");
        
        ThirdReviewAccountRelationWithInfoOutput relationWithInfo = new ThirdReviewAccountRelationWithInfoOutput();
        relationWithInfo.setRelation(relation);
        relationWithInfo.setInfo(info);
        
        // Setup relation count
        RelationCountOutput relationCount = new RelationCountOutput();
        relationCount.setCount(2);
        
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(relationWithInfo));
        when(reviewConnectionsManagementApi.countRelations(String.valueOf(accountId), ReviewProviderEnum.GBP, oauthInfo.getId()))
                .thenReturn(Single.just(relationCount));

        // when
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);

        // then
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.OK.getValue());
        assertThat(googleReviews.getAuth().getAuthid()).isEqualTo("1");
        assertThat(googleReviews.getSummary().getRelationId()).isEqualTo("456");
        assertThat(googleReviews.getSummary().getBusinessName()).isEqualTo("Business Name");
        assertThat(googleReviews.getSummary().getDisplaySelected()).isEqualTo(1);
        assertThat(googleReviews.getSummary().getTotalNum()).isEqualTo("100");
        assertThat(googleReviews.getSummary().getAvgRate()).isEqualTo("4.5");
        assertThat(googleReviews.getSummary().getHasMultiRelation()).isEqualTo(1);
    }

    @Test
    public void testSetGoogleDisplay_NotDisplayed() {
        // given
        Long accountId = 123L;
        String relationId = "456";
        Integer display = 0;
        ThirdReviewAccountRelationOutput output = new ThirdReviewAccountRelationOutput();
        output.setId(Long.valueOf(relationId));
        when(reviewDisplayApi.updateDisplayStatus(eq(String.valueOf(accountId)), eq(Long.valueOf(relationId)), any(UpdateDisplayStatusRequest.class)))
                .thenReturn(Single.just(output));

        // when
        String result = externalReviewsService.setGoogleDisplay(accountId, relationId, display);

        // then
        assertThat(result).isEqualTo(relationId);
    }

    @Test
    public void testGetGoogleReviewSwitchInSyiFlow_NonServiceCategory() {
        // given
        Long accountId = 123L;
        Long nonServiceCategoryId = Categories.MOTORS.getId();
        
        com.gumtree.api.category.domain.Category category = new com.gumtree.api.category.domain.Category();
        category.setId(Categories.MOTORS.getId());
        
        when(categoryModel.getL1CategoryFor(nonServiceCategoryId)).thenReturn(com.google.common.base.Optional.of(category));

        // when
        Boolean result = externalReviewsService.getGoogleReviewSwitchInSyiFlow(accountId, nonServiceCategoryId);

        // then
        assertThat(result).isFalse();
    }

    @Test
    public void testGetGoogleReviewSwitchInSyiFlow_ServiceCategory_AlreadyBound() {
        // given
        Long accountId = 123L;
        Long serviceCategoryId = Categories.SERVICES.getId();
        
        com.gumtree.api.category.domain.Category category = new com.gumtree.api.category.domain.Category();
        category.setId(Categories.SERVICES.getId());
        
        when(categoryModel.getL1CategoryFor(serviceCategoryId)).thenReturn(com.google.common.base.Optional.of(category));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput()));

        // when
        Boolean result = externalReviewsService.getGoogleReviewSwitchInSyiFlow(accountId, serviceCategoryId);

        // then
        assertThat(result).isFalse();
    }

    @Test
    public void testGetGoogleReviewSwitchInSyiFlow_ServiceCategory_NotBound() {
        // given
        Long accountId = 123L;
        Long serviceCategoryId = Categories.SERVICES.getId();
        
        com.gumtree.api.category.domain.Category category = new com.gumtree.api.category.domain.Category();
        category.setId(Categories.SERVICES.getId());
        
        when(categoryModel.getL1CategoryFor(serviceCategoryId)).thenReturn(com.google.common.base.Optional.of(category));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new ResourceNotFoundException()));

        // when
        try {
            Boolean result = externalReviewsService.getGoogleReviewSwitchInSyiFlow(accountId, serviceCategoryId);
            fail("Expected RuntimeException");
        } catch (RuntimeException e) {
            // Expected - handleError() throws RuntimeException which propagates from toBlocking().value()
            assertThat(e.getCause()).isInstanceOf(ResourceNotFoundException.class);
        }
    }

    @Test
    public void testGetGoogleReviewSwitchInSyiFlow_CategoryNotFound() {
        // given - when getL1CategoryFor returns absent (empty Optional)
        Long accountId = 123L;
        Long categoryId = 999L;
        
        when(categoryModel.getL1CategoryFor(categoryId)).thenReturn(com.google.common.base.Optional.absent());

        // when
        Boolean result = externalReviewsService.getGoogleReviewSwitchInSyiFlow(accountId, categoryId);

        // then - should return false because isService(0L) returns false
        assertThat(result).isFalse();
    }

    @Test
    public void testGetGoogleReviewSwitchInSyiFlow_ServiceCategory_NotBoundSuccess() {
        // given
        Long accountId = 123L;
        Long serviceCategoryId = Categories.SERVICES.getId();
        
        com.gumtree.api.category.domain.Category category = new com.gumtree.api.category.domain.Category();
        category.setId(Categories.SERVICES.getId());
        
        when(categoryModel.getL1CategoryFor(serviceCategoryId)).thenReturn(com.google.common.base.Optional.of(category));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null)); // Not bound

        // when
        Boolean result = externalReviewsService.getGoogleReviewSwitchInSyiFlow(accountId, serviceCategoryId);

        // then
        assertThat(result).isTrue();
    }

    @Test
    public void testGetGoogleReviewSwitchInSyiFlow_TimeoutScenario() {
        // given
        Long accountId = 123L;
        Long serviceCategoryId = Categories.SERVICES.getId();
        
        com.gumtree.api.category.domain.Category category = new com.gumtree.api.category.domain.Category();
        category.setId(Categories.SERVICES.getId());
        
        when(categoryModel.getL1CategoryFor(serviceCategoryId)).thenReturn(com.google.common.base.Optional.of(category));
        // Make the call take longer than 100ms to trigger timeout
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput()).delay(150, TimeUnit.MILLISECONDS));

        // when - timeout will throw TimeoutException
        try {
            Boolean result = externalReviewsService.getGoogleReviewSwitchInSyiFlow(accountId, serviceCategoryId);
            fail("Expected TimeoutException");
        } catch (RuntimeException e) {
            // then - timeout causes RuntimeException wrapping TimeoutException
            assertThat(e.getCause()).isInstanceOf(TimeoutException.class);
        }
    }

    @Test
    public void testGetGoogleReviewMyDetailFlow() {
        // given
        Long accountId = 123L;
        final Boolean[] capturedSwitch = new Boolean[1];
        final Boolean[] capturedShowItem = new Boolean[1];
        final Boolean[] capturedIsBindGoogleReview = new Boolean[1];

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new ResourceNotFoundException()));

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            capturedSwitch[0] = switchVal;
            capturedShowItem[0] = showItem;
            capturedIsBindGoogleReview[0] = isBindGoogleReview;
        });

        // then - due to error in isBindGoogleReview, the method will log error but complete normally
        // The callback won't be called because onErrorReturn(e -> false) returns false
        assertThat(capturedSwitch[0]).isNull();
        assertThat(capturedShowItem[0]).isNull();
        assertThat(capturedIsBindGoogleReview[0]).isNull(); // Not bound

    }

    @Test
    public void testGetGoogleReviewSwitchAsync_NoPost() {
        // given
        Long accountId = 123L;
        // isBindGoogleReview = false
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new ResourceNotFoundException()));
        // isPostSpecialCategory = false
        FullAdSearchResult response = new FullAdSearchResult();
        response.setTotal(0L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(response));

        // when
        Boolean result = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();

        // then
        assertThat(result).isFalse();
    }

    @Test
    public void testGetGoogleReviewSwitchAsync_Timeout() {
        // given
        Long accountId = 123L;
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput()).delay(200, TimeUnit.MILLISECONDS));
        FullAdSearchResult response = new FullAdSearchResult();
        response.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(response));

        // when
        Boolean result = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();

        // then
        assertThat(result).isFalse();
    }

    @Test
    public void testGetGoogleReviewSwitchAsync_Exception() {
        // given
        Long accountId = 123L;
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new RuntimeException("API error")));
        FullAdSearchResult response = new FullAdSearchResult();
        response.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(response));

        // when
        Boolean result = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();

        // then - onErrorReturn should return false
        assertThat(result).isFalse();
    }

    @Test
    public void testGetSummary_NotMyDetailPageWithOauthInfo() {
        // Test checkIfShowItem when !isMyDetailPage && activeOauthInfo != null → false
        Long accountId = 123L;
        
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.COMPLETED);
        
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(0L);
        
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));
        
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, false); // isMyDetailPage = false
        
        // checkIfShowItem returns false, so status should be DO_NOT_NEED_SHOW
        assertThat(result.getGoogleReviews().getStatus()).isEqualTo(GoogleReviewStatus.DO_NOT_NEED_SHOW.getValue());
    }

    @Test
    public void testToGoogleReviewSummary_RelationNullInfoNotNull() {
        // Test when relation is null but info is not null
        Long accountId = 123L;
        
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.COMPLETED);
        
        ThirdReviewInfoOutput info = new ThirdReviewInfoOutput();
        info.setTotalReviews(50);
        info.setAvgRating(4.2f);
        info.setName("Info Business Name");
        
        ThirdReviewAccountRelationWithInfoOutput relationWithInfo = new ThirdReviewAccountRelationWithInfoOutput();
        relationWithInfo.setRelation(null); // null relation
        relationWithInfo.setInfo(info);
        
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(relationWithInfo));
        
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);
        
        // Should hit NO_MERCHANT_INFO because relation is null
        assertThat(result.getGoogleReviews().getStatus()).isEqualTo(GoogleReviewStatus.NO_MERCHANT_INFO.getValue());
    }

    @Test
    public void testToGoogleReviewSummary_InfoNullRelationNotNull() {
        // Test when info is null but relation is not null
        Long accountId = 123L;
        
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.COMPLETED);
        
        ThirdReviewAccountRelationOutput relation = new ThirdReviewAccountRelationOutput();
        relation.setId(456L);
        relation.setNameRedundant("Relation Business Name");
        relation.setDisplayStatus(DisplayStatusEnum.NOT_DISPLAYED);
        
        ThirdReviewAccountRelationWithInfoOutput relationWithInfo = new ThirdReviewAccountRelationWithInfoOutput();
        relationWithInfo.setRelation(relation);
        relationWithInfo.setInfo(null); // null info
        
        RelationCountOutput relationCount = new RelationCountOutput();
        relationCount.setCount(3); // Multiple relations
        
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(relationWithInfo));
        when(reviewConnectionsManagementApi.countRelations(String.valueOf(accountId), ReviewProviderEnum.GBP, oauthInfo.getId()))
                .thenReturn(Single.just(relationCount));
        when(reviewDisplayApi.getDisplayableReviewSummary(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new DisplayableGbpReviewSummaryOutput()));
        
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);
        
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.OK.getValue());
        assertThat(googleReviews.getSummary().getBusinessName()).isEqualTo("Relation Business Name");
        assertThat(googleReviews.getSummary().getTotalNum()).isEqualTo("0");
        assertThat(googleReviews.getSummary().getAvgRate()).isEqualTo("0");
        assertThat(googleReviews.getSummary().getHasMultiRelation()).isEqualTo(1); // Multiple relations
    }

    @Test
    public void testDoGoogleAuth_WithNullAuthCode() {
        // given
        Long accountId = 123L;
        GoogleOAuthWebRequest request = new GoogleOAuthWebRequest();
        request.setAuthCode(null);
        request.setRedirectUrl("redirectUrl");
        request.setClientId("clientId");
        request.setCodeVerifier("codeVerifier");

        OperationResult operationResult = new OperationResult();
        operationResult.setStatus(OperationResult.StatusEnum.FAILED);
        operationResult.setMessage("Auth code is required");
        operationResult.setOperationId("op-failed");

        when(reviewConnectionsManagementApi.authorizeReviewSource(eq(ReviewProviderEnum.GBP), any(GbpAuthorizationRequest.class)))
                .thenReturn(Single.just(operationResult));

        // when
        AsyncResult result = externalReviewsService.doGoogleAuth(accountId, request);

        // then
        assertThat(result.getStatus()).isEqualTo("FAILED");
        assertThat(result.getMessage()).isEqualTo("Auth code is required");
    }

    @Test
    public void testDoGoogleAuth_WithPendingStatus() {
        // given
        Long accountId = 123L;
        GoogleOAuthWebRequest request = new GoogleOAuthWebRequest();
        request.setAuthCode("authCode");
        request.setRedirectUrl("redirectUrl");
        request.setClientId("clientId");
        request.setCodeVerifier("codeVerifier");

        OperationResult operationResult = new OperationResult();
        operationResult.setStatus(OperationResult.StatusEnum.IN_PROGRESS);
        operationResult.setMessage("Processing");
        operationResult.setTimestamp(OffsetDateTime.now());
        operationResult.setEstimatedCompletionTime(OffsetDateTime.now().plusMinutes(1));
        operationResult.setOperationId("op-pending");

        when(reviewConnectionsManagementApi.authorizeReviewSource(eq(ReviewProviderEnum.GBP), any(GbpAuthorizationRequest.class)))
                .thenReturn(Single.just(operationResult));

        // when
        AsyncResult result = externalReviewsService.doGoogleAuth(accountId, request);

        // then
        assertThat(result.getStatus()).isEqualTo("IN_PROGRESS");
        assertThat(result.getMessage()).isEqualTo("Processing");
        assertThat(result.getOperationId()).isEqualTo("op-pending");
    }

    @Test
    public void testGetGoogleRelationList_EmptyList() {
        // given
        Long accountId = 123L;
        String authId = "789";
        when(reviewConnectionsManagementApi.getRelationChoices(String.valueOf(accountId), ReviewProviderEnum.GBP, Long.parseLong(authId)))
                .thenReturn(Single.just(Collections.emptyList()));

        // when
        GoogleRelationListResponse result = externalReviewsService.getGoogleRelationList(accountId, authId);

        // then
        assertThat(result.getRelations()).isEmpty();
    }

    @Test
    public void testGetGoogleRelationList_MultipleRelations() {
        // given
        Long accountId = 123L;
        String authId = "789";
        
        ThirdReviewAccountRelationOutput relation1 = new ThirdReviewAccountRelationOutput();
        relation1.setId(456L);
        relation1.setNameRedundant("Business 1");
        
        ThirdReviewAccountRelationOutput relation2 = new ThirdReviewAccountRelationOutput();
        relation2.setId(789L);
        relation2.setNameRedundant("Business 2");
        
        when(reviewConnectionsManagementApi.getRelationChoices(String.valueOf(accountId), ReviewProviderEnum.GBP, Long.parseLong(authId)))
                .thenReturn(Single.just(java.util.Arrays.asList(relation1, relation2)));

        // when
        GoogleRelationListResponse result = externalReviewsService.getGoogleRelationList(accountId, authId);

        // then
        assertThat(result.getRelations()).hasSize(2);
        assertThat(result.getRelations().get(0).getRelationId()).isEqualTo("456");
        assertThat(result.getRelations().get(0).getBusinessName()).isEqualTo("Business 1");
        assertThat(result.getRelations().get(1).getRelationId()).isEqualTo("789");
        assertThat(result.getRelations().get(1).getBusinessName()).isEqualTo("Business 2");
    }

    @Test
    public void testGetSummary_WithNullAccountId() {
        // given
        Long accountId = null;
        
        // when/then - should handle gracefully or throw expected exception
        try {
            externalReviewsService.getSummary(accountId, true);
        } catch (Exception e) {
            // Expected exception for null account ID
            assertThat(e).isNotNull();
        }
    }

    @Test
    public void testGetSummary_DataSyncStatusFailed() {
        // given
        Long accountId = 123L;
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.FAILED);
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));

        // when
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);

        // then
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.NO_MERCHANT_INFO.getValue());
    }

    @Test
    public void testGetGoogleReviewMyDetailFlow_NoServiceAds() {
        // given
        Long accountId = 123L;
        final Boolean[] capturedSwitch = new Boolean[1];
        final Boolean[] capturedShowItem = new Boolean[1];
        final Boolean[] capturedIsBindGoogleReview = new Boolean[1];

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(0L); // No service ads
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new ResourceNotFoundException()));

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            capturedSwitch[0] = switchVal;
            capturedShowItem[0] = showItem;
            capturedIsBindGoogleReview[0] = isBindGoogleReview;
        });

        // then - due to error in isBindGoogleReview, the method will log error but complete normally
        // The callback won't be called because onErrorReturn(e -> false) returns false
        assertThat(capturedSwitch[0]).isNull();
        assertThat(capturedShowItem[0]).isNull();
        assertThat(capturedIsBindGoogleReview[0]).isNull(); // Not bound

    }

    @Test
    public void testGetGoogleReviewMyDetailFlow_AlreadyBound() {
        // given
        Long accountId = 123L;
        final Boolean[] capturedSwitch = new Boolean[1];
        final Boolean[] capturedShowItem = new Boolean[1];
        final Boolean[] capturedIsBindGoogleReview = new Boolean[1];
        final Boolean[] callbackCalled = {false};

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput())); // Already bound

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            callbackCalled[0] = true;
            capturedSwitch[0] = switchVal;
            capturedShowItem[0] = showItem;
            capturedIsBindGoogleReview[0] = isBindGoogleReview;
        });

        // then - callback should be called when both operations succeed
        assertThat(callbackCalled[0]).isTrue();
        assertThat(capturedSwitch[0]).isFalse(); // getGoogleReviewSwitch(true, true) returns false
        assertThat(capturedShowItem[0]).isTrue(); // isBindGoogleReview || isPostSpecialCategory
        assertThat(capturedIsBindGoogleReview[0]).isTrue(); // Already bound
    }

    @Test
    public void testGetGoogleReviewMyDetailFlow_NotBoundWithServiceAds() {
        // given
        Long accountId = 123L;
        final Boolean[] callbackCalled = {false};

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L); // Has service ads
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new ResourceNotFoundException())); // Not bound

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            callbackCalled[0] = true;
            // If callback is called, verify the values
            assertThat(switchVal).isTrue(); // Should show switch since has service ads but not bound
            assertThat(showItem).isTrue(); // Should show item since has service ads
            assertThat(isBindGoogleReview).isFalse(); // Not bound
        });

        // then - When isBindGoogleReview returns error, zip operation fails
        // and onErrorReturn prevents the callback from being called
        assertThat(callbackCalled[0]).isFalse();
    }

    @Test
    public void testGetGoogleReviewMyDetailFlow_NotBoundNoServiceAds() {
        // given
        Long accountId = 123L;
        final Boolean[] callbackCalled = {false};

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(0L); // No service ads
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new ResourceNotFoundException())); // Not bound

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            callbackCalled[0] = true;
            // If callback is called, verify the values
            assertThat(switchVal).isFalse(); // Should not show switch since no service ads
            assertThat(showItem).isFalse(); // Should not show item since no service ads
            assertThat(isBindGoogleReview).isFalse(); // Not bound
        });

        // then - When isBindGoogleReview returns error, zip operation fails
        // and onErrorReturn prevents the callback from being called
        assertThat(callbackCalled[0]).isFalse();
    }


    @Test
    public void testGetGoogleReviewMyDetailFlow_TimeoutError() {
        // given
        Long accountId = 123L;
        final Boolean[] callbackCalled = {false};

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class)))
                .thenReturn(Single.just(fullAdSearchResult).delay(300, TimeUnit.MILLISECONDS)); // Delay to trigger timeout
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput()));

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            callbackCalled[0] = true;
        });

        // then - timeout causes onErrorReturn to be triggered
        assertThat(callbackCalled[0]).isFalse();
    }

    @Test
    public void testGetGoogleReviewMyDetailFlow_NotBoundWithServiceAdsSuccess() {
        // given
        Long accountId = 123L;
        final Boolean[] capturedSwitch = new Boolean[1];
        final Boolean[] capturedShowItem = new Boolean[1];
        final Boolean[] capturedIsBindGoogleReview = new Boolean[1];
        final Boolean[] callbackCalled = {false};

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L); // Has service ads
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        
        // Mock successful response for not bound (null means not bound)
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null)); // Returning null indicates not bound

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            callbackCalled[0] = true;
            capturedSwitch[0] = switchVal;
            capturedShowItem[0] = showItem;
            capturedIsBindGoogleReview[0] = isBindGoogleReview;
        });

        // then - callback should be called when both operations succeed
        assertThat(callbackCalled[0]).isTrue();
        assertThat(capturedSwitch[0]).isTrue(); // getGoogleReviewSwitch(true, false) returns true
        assertThat(capturedShowItem[0]).isTrue(); // false || (true != null && true) = true
        assertThat(capturedIsBindGoogleReview[0]).isFalse(); // Not bound
    }

    @Test
    public void testGetGoogleReviewMyDetailFlow_NotBoundNoServiceAdsSuccess() {
        // given
        Long accountId = 123L;
        final Boolean[] capturedSwitch = new Boolean[1];
        final Boolean[] capturedShowItem = new Boolean[1];
        final Boolean[] capturedIsBindGoogleReview = new Boolean[1];
        final Boolean[] callbackCalled = {false};

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(0L); // No service ads
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        
        // Mock successful response for not bound (null means not bound)
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null)); // Returning null indicates not bound

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            callbackCalled[0] = true;
            capturedSwitch[0] = switchVal;
            capturedShowItem[0] = showItem;
            capturedIsBindGoogleReview[0] = isBindGoogleReview;
        });

        // then - callback should be called when both operations succeed
        assertThat(callbackCalled[0]).isTrue();
        assertThat(capturedSwitch[0]).isFalse(); // getGoogleReviewSwitch(false, false) returns false
        assertThat(capturedShowItem[0]).isFalse(); // false || (false != null && false) = false
        assertThat(capturedIsBindGoogleReview[0]).isFalse(); // Not bound
    }

    @Test
    public void testGetGoogleReviewMyDetailFlow_AlreadyBoundNoServiceAds() {
        // given
        Long accountId = 123L;
        final Boolean[] capturedSwitch = new Boolean[1];
        final Boolean[] capturedShowItem = new Boolean[1];
        final Boolean[] capturedIsBindGoogleReview = new Boolean[1];
        final Boolean[] callbackCalled = {false};

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(0L); // No service ads
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput())); // Already bound

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            callbackCalled[0] = true;
            capturedSwitch[0] = switchVal;
            capturedShowItem[0] = showItem;
            capturedIsBindGoogleReview[0] = isBindGoogleReview;
        });

        // then - callback should be called when both operations succeed
        assertThat(callbackCalled[0]).isTrue();
        assertThat(capturedSwitch[0]).isFalse(); // getGoogleReviewSwitch(false, true) returns false
        assertThat(capturedShowItem[0]).isTrue(); // true || (false != null && false) = true
        assertThat(capturedIsBindGoogleReview[0]).isTrue(); // Already bound
    }

    @Test
    public void testGetGoogleReviewMyDetailFlow_NullPostSpecialCategory() {
        // given
        Long accountId = 123L;
        final Boolean[] capturedSwitch = new Boolean[1];
        final Boolean[] capturedShowItem = new Boolean[1];
        final Boolean[] capturedIsBindGoogleReview = new Boolean[1];
        final Boolean[] callbackCalled = {false};

        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(null); // Null total will make isPostSpecialCategory return null
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null)); // Not bound

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            callbackCalled[0] = true;
            capturedSwitch[0] = switchVal;
            capturedShowItem[0] = showItem;
            capturedIsBindGoogleReview[0] = isBindGoogleReview;
        });

        // then - callback should be called when both operations succeed
        assertThat(callbackCalled[0]).isTrue();
        assertThat(capturedSwitch[0]).isFalse(); // getGoogleReviewSwitch(null, false) returns false
        assertThat(capturedShowItem[0]).isFalse(); // false || (null != null && null) = false
        assertThat(capturedIsBindGoogleReview[0]).isFalse(); // Not bound
    }

    @Test
    public void testGetGoogleReviewSwitch_AllCombinations() {
        // This test indirectly tests the private getGoogleReviewSwitch method through getGoogleReviewSwitchAsync
        Long accountId = 123L;
        
        // Test case 1: isBindGoogleReview=true, isPostSpecialCategory=true
        // Expected: false (because isBindGoogleReview takes precedence)
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput())); // Bound
        FullAdSearchResult result1 = new FullAdSearchResult();
        result1.setTotal(1L); // Has service ads
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(result1));
        Boolean switchResult1 = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();
        assertThat(switchResult1).isFalse();
        
        // Test case 2: isBindGoogleReview=false, isPostSpecialCategory=true
        // Expected: true
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null)); // Not bound
        Boolean switchResult2 = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();
        assertThat(switchResult2).isTrue();
        
        // Test case 3: isBindGoogleReview=false, isPostSpecialCategory=false
        // Expected: false
        FullAdSearchResult result3 = new FullAdSearchResult();
        result3.setTotal(0L); // No service ads
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(result3));
        Boolean switchResult3 = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();
        assertThat(switchResult3).isFalse();
        
        // Test case 4: isBindGoogleReview=true, isPostSpecialCategory=false
        // Expected: false
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput())); // Bound
        Boolean switchResult4 = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();
        assertThat(switchResult4).isFalse();
        
        // Test case 5: isBindGoogleReview=false, isPostSpecialCategory=null
        // Expected: false
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null)); // Not bound
        FullAdSearchResult result5 = new FullAdSearchResult();
        result5.setTotal(null); // Null total
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(result5));
        Boolean switchResult5 = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();
        assertThat(switchResult5).isFalse();
    }

    @Test 
    public void testGetGoogleReviewMyDetailFlow_BothNullValues() {
        // Test when both isPostSpecialCategory and isBindGoogleReview could be null
        Long accountId = 123L;
        final Boolean[] capturedSwitch = new Boolean[1];
        final Boolean[] capturedShowItem = new Boolean[1];
        final Boolean[] capturedIsBindGoogleReview = new Boolean[1];
        final Boolean[] callbackCalled = {false};

        // Mock isPostSpecialCategory to return null (when total is null)
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(null);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        
        // Mock isBindGoogleReview to return null (not false/true)
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));

        // when
        externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
            callbackCalled[0] = true;
            capturedSwitch[0] = switchVal;
            capturedShowItem[0] = showItem; 
            capturedIsBindGoogleReview[0] = isBindGoogleReview;
        });

        // then
        assertThat(callbackCalled[0]).isTrue();
        assertThat(capturedSwitch[0]).isFalse(); // getGoogleReviewSwitch(null, false) returns false
        assertThat(capturedShowItem[0]).isFalse(); // false || (null != null && null) = false 
        assertThat(capturedIsBindGoogleReview[0]).isFalse(); // null is mapped to false in isBindGoogleReview
    }

    @Test
    public void testGetGoogleReviewMyDetailFlow_ExceptionInTriConsumer() {
        // Test exception handling when triConsumer throws exception
        Long accountId = 123L;
        
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput()));

        // when - triConsumer throws exception
        try {
            externalReviewsService.getGoogleReviewMyDetailFlow(accountId, (switchVal, showItem, isBindGoogleReview) -> {
                throw new RuntimeException("Consumer error");
            });
        } catch (Exception e) {
            // Exception should be caught and logged, not propagated
            fail("Exception should not propagate");
        }
        
        // Method should complete normally even if triConsumer throws
    }

    @Test(expected = NumberFormatException.class)
    public void testReSyncGoogleData_WithInvalidAuthId() {
        // given
        Long accountId = 123L;
        String authId = "invalid";
        
        // when
        externalReviewsService.reSyncGoogleData(accountId, authId);
    }

    @Test
    public void testSetGoogleRelation_WithError() {
        // given
        Long accountId = 123L;
        String relationId = "456";
        when(reviewConnectionsManagementApi.setEffectiveRelation(String.valueOf(accountId), Long.parseLong(relationId)))
                .thenReturn(Single.error(new RuntimeException("Failed to set relation")));

        // when/then
        try {
            externalReviewsService.setGoogleRelation(accountId, relationId);
        } catch (RuntimeException e) {
            assertThat(e.getMessage()).contains("Failed to set relation");
        }
    }

    @Test
    public void testCheckIfShowItem_AllConditions() {
        // Test getSummary with different combinations for checkIfShowItem
        Long accountId = 123L;
        
        // Case 1: !isMyDetailPage && activeOauthInfo == null → true
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(0L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new ResourceNotFoundException()));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));
        
        try {
            ExternalReviewManagement result = externalReviewsService.getSummary(accountId, false); // isMyDetailPage = false
            fail("Expected RuntimeException");
        } catch (RuntimeException e) {
            // Expected - handleError() throws RuntimeException
            assertThat(e.getCause()).isInstanceOf(ResourceNotFoundException.class);
        }
        
        // Case 2: isMyDetailPage && postSpecialCategory == null → false (DO_NOT_NEED_SHOW)
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new AccountOauthInfoOutput()));
        FullAdSearchResult nullResult = new FullAdSearchResult();
        nullResult.setTotal(null); // This will make postSpecialCategory null
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(nullResult));
        
        ExternalReviewManagement result2 = externalReviewsService.getSummary(accountId, true); // isMyDetailPage = true
        assertThat(result2.getGoogleReviews().getStatus()).isEqualTo(GoogleReviewStatus.DO_NOT_NEED_SHOW.getValue());
        
        // Case 3: isMyDetailPage && postSpecialCategory == false → false (DO_NOT_NEED_SHOW)
        FullAdSearchResult zeroResult = new FullAdSearchResult();
        zeroResult.setTotal(0L); // This will make postSpecialCategory false
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(zeroResult));
        
        ExternalReviewManagement result3 = externalReviewsService.getSummary(accountId, true); // isMyDetailPage = true
        assertThat(result3.getGoogleReviews().getStatus()).isEqualTo(GoogleReviewStatus.DO_NOT_NEED_SHOW.getValue());
    }

    @Test
    public void testIsBindGoogleReview_AllPaths() {
        Long accountId = 123L;
        
        // Test when findActiveOauthInfo returns non-null
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        
        // Call through a method that uses isBindGoogleReview
        FullAdSearchResult result = new FullAdSearchResult();
        result.setTotal(1L);
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(result));
        
        Boolean switchResult = externalReviewsService.getGoogleReviewSwitchAsync(accountId).toBlocking().value();
        assertThat(switchResult).isFalse(); // Because isBindGoogleReview is true
        
        // Test when findActiveOauthInfo returns null (covered in other tests)
        // Test when findActiveOauthInfo returns error (covered in other tests)
    }

    @Test
    public void testGetSummary_WithNullRelationInfo() {
        // Test when effectiveRelationWithInfo.getRelation() is null but effectiveRelationWithInfo is not null
        Long accountId = 123L;
        
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.COMPLETED);
        
        // Create a ThirdReviewAccountRelationWithInfoOutput with null relation
        ThirdReviewAccountRelationWithInfoOutput relationWithInfo = new ThirdReviewAccountRelationWithInfoOutput();
        relationWithInfo.setRelation(null); // null relation
        relationWithInfo.setInfo(new ThirdReviewInfoOutput());
        
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(relationWithInfo));
        
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);
        
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.NO_MERCHANT_INFO.getValue());
        assertThat(googleReviews.getAuth().getAuthid()).isEqualTo("1");
    }

    @Test
    public void testToGoogleReviewSummary_WithNullValues() {
        // Test toGoogleReviewSummary with various null conditions
        Long accountId = 123L;
        
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.COMPLETED);
        
        // Create relation with info but some null values
        ThirdReviewAccountRelationOutput relation = new ThirdReviewAccountRelationOutput();
        relation.setId(456L);
        relation.setNameRedundant(null); // null name
        relation.setDisplayStatus(null); // null display status
        
        ThirdReviewInfoOutput info = new ThirdReviewInfoOutput();
        info.setTotalReviews(null); // null total reviews
        info.setAvgRating(null); // null avg rating
        info.setName("Business Name");
        
        ThirdReviewAccountRelationWithInfoOutput relationWithInfo = new ThirdReviewAccountRelationWithInfoOutput();
        relationWithInfo.setRelation(relation);
        relationWithInfo.setInfo(info);
        
        RelationCountOutput relationCount = new RelationCountOutput();
        relationCount.setCount(1); // Single relation
        
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(relationWithInfo));
        when(reviewConnectionsManagementApi.countRelations(String.valueOf(accountId), ReviewProviderEnum.GBP, oauthInfo.getId()))
                .thenReturn(Single.just(relationCount));
        when(reviewDisplayApi.getDisplayableReviewSummary(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(new DisplayableGbpReviewSummaryOutput()));
        
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);
        
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.OK.getValue());
        assertThat(googleReviews.getSummary().getTotalNum()).isEqualTo("0"); // null mapped to "0"
        assertThat(googleReviews.getSummary().getAvgRate()).isEqualTo("0"); // null mapped to "0"
        assertThat(googleReviews.getSummary().getBusinessName()).isEqualTo("Business Name");
        assertThat(googleReviews.getSummary().getDisplaySelected()).isEqualTo(0); // null display status defaults to 0
        assertThat(googleReviews.getSummary().getHasMultiRelation()).isEqualTo(0); // Single relation
    }

    @Test
    public void testGetSummary_WithDisplayableReviewSummary() {
        // given
        Long accountId = 123L;
        
        // Setup oauth info
        AccountOauthInfoOutput oauthInfo = new AccountOauthInfoOutput();
        oauthInfo.setId(1L);
        oauthInfo.setDataSyncStatus(DataSyncStatusEnum.COMPLETED);
        
        // Setup relation info
        ThirdReviewAccountRelationOutput relation = new ThirdReviewAccountRelationOutput();
        relation.setId(456L);
        relation.setNameRedundant("Business Name");
        relation.setDisplayStatus(DisplayStatusEnum.DISPLAYED);
        
        // Setup review info
        ThirdReviewInfoOutput info = new ThirdReviewInfoOutput();
        info.setTotalReviews(100);
        info.setAvgRating(4.5f);
        info.setName("Business Name");
        
        ThirdReviewAccountRelationWithInfoOutput relationWithInfo = new ThirdReviewAccountRelationWithInfoOutput();
        relationWithInfo.setRelation(relation);
        relationWithInfo.setInfo(info);
        
        // Setup displayable review summary
        DisplayableGbpReviewSummaryOutput displayableSummary = new DisplayableGbpReviewSummaryOutput();
        displayableSummary.totalReviewCount(50); // Different from total reviews
        displayableSummary.setAverageRating(4.8f); // Different from avg rating
        
        FullAdSearchResult fullAdSearchResult = new FullAdSearchResult();
        fullAdSearchResult.setTotal(1L);
        
        when(fullAdsSearchApi.search(any(FullAdSearchRequest.class))).thenReturn(Single.just(fullAdSearchResult));
        when(reviewConnectionsManagementApi.findActiveOauthInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(oauthInfo));
        when(reviewConnectionsManagementApi.findEffectiveRelationWithInfo(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(relationWithInfo));
        RelationCountOutput relationCountOutput = new RelationCountOutput();
        relationCountOutput.setCount(2);
        when(reviewConnectionsManagementApi.countRelations(String.valueOf(accountId), ReviewProviderEnum.GBP, oauthInfo.getId()))
                .thenReturn(Single.just(relationCountOutput));

        // when
        ExternalReviewManagement result = externalReviewsService.getSummary(accountId, true);

        // then
        GoogleReviews googleReviews = result.getGoogleReviews();
        assertThat(googleReviews.getStatus()).isEqualTo(GoogleReviewStatus.OK.getValue());
        // Should use info values from relationWithInfo
        assertThat(googleReviews.getSummary().getTotalNum()).isEqualTo("100");
        assertThat(googleReviews.getSummary().getAvgRate()).isEqualTo("4.5");
        assertThat(googleReviews.getSummary().getBusinessName()).isEqualTo("Business Name");
        assertThat(googleReviews.getSummary().getRelationId()).isEqualTo("456");
        assertThat(googleReviews.getSummary().getDisplaySelected()).isEqualTo(1);
        assertThat(googleReviews.getSummary().getHasMultiRelation()).isEqualTo(1);
    }

    @Test
    public void unBind_shouldReturnAsyncResult() {
        // given
        Long accountId = 123L;
        String authId = "auth-123";
        OperationResult operationResult = new OperationResult();
        operationResult.setOperationId("op-unbind-123");
        operationResult.setStatus(OperationResult.StatusEnum.COMPLETED);
        
        when(reviewConnectionsManagementApi.unbindRelation(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(operationResult));

        // when
        AsyncResult result = externalReviewsService.unBind(accountId, authId);

        // then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo("COMPLETED");
        assertThat(result.getOperationId()).isEqualTo("op-unbind-123");
    }

    @Test
    public void unBind_shouldHandleFailedStatus() {
        // given
        Long accountId = 123L;
        String authId = "auth-456";
        OperationResult operationResult = new OperationResult();
        operationResult.setOperationId("op-unbind-failed");
        operationResult.setStatus(OperationResult.StatusEnum.FAILED);
        
        when(reviewConnectionsManagementApi.unbindRelation(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(operationResult));

        // when
        AsyncResult result = externalReviewsService.unBind(accountId, authId);

        // then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo("FAILED");
        assertThat(result.getOperationId()).isEqualTo("op-unbind-failed");
    }

    @Test
    public void unBind_shouldHandleInProgressStatus() {
        // given
        Long accountId = 123L;
        String authId = "auth-789";
        OperationResult operationResult = new OperationResult();
        operationResult.setOperationId("op-unbind-in-progress");
        operationResult.setStatus(OperationResult.StatusEnum.IN_PROGRESS);
        
        when(reviewConnectionsManagementApi.unbindRelation(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(operationResult));

        // when
        AsyncResult result = externalReviewsService.unBind(accountId, authId);

        // then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo("IN_PROGRESS");
        assertThat(result.getOperationId()).isEqualTo("op-unbind-in-progress");
    }

    @Test(expected = RuntimeException.class)
    public void unBind_shouldPropagateException() {
        // given
        Long accountId = 123L;
        String authId = "auth-error";
        
        when(reviewConnectionsManagementApi.unbindRelation(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.error(new RuntimeException("Unbind failed")));

        // when
        externalReviewsService.unBind(accountId, authId);
        
        // then - exception expected
    }

    @Test(expected = NullPointerException.class)
    public void unBind_shouldThrowNPEForNullAccountId() {
        // given
        Long accountId = null;
        String authId = "auth-null";
        
        // Note: String.valueOf(null) returns "null" string
        // The API might return null which causes NPE in toAsyncResult
        when(reviewConnectionsManagementApi.unbindRelation("null", ReviewProviderEnum.GBP))
                .thenReturn(Single.just(null));

        // when
        externalReviewsService.unBind(accountId, authId);
        
        // then - NPE expected
    }

    @Test
    public void unBind_shouldHandleEmptyAuthId() {
        // given
        Long accountId = 123L;
        String authId = "";
        OperationResult operationResult = new OperationResult();
        operationResult.setOperationId("op-unbind-empty");
        operationResult.setStatus(OperationResult.StatusEnum.COMPLETED);
        
        when(reviewConnectionsManagementApi.unbindRelation(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(operationResult));

        // when
        AsyncResult result = externalReviewsService.unBind(accountId, authId);

        // then - authId is not used in the implementation, so it should still work
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo("COMPLETED");
    }

    @Test
    public void unBind_shouldHandleNullAuthId() {
        // given
        Long accountId = 123L;
        String authId = null;
        OperationResult operationResult = new OperationResult();
        operationResult.setOperationId("op-unbind-null-auth");
        operationResult.setStatus(OperationResult.StatusEnum.COMPLETED);
        
        when(reviewConnectionsManagementApi.unbindRelation(String.valueOf(accountId), ReviewProviderEnum.GBP))
                .thenReturn(Single.just(operationResult));

        // when
        AsyncResult result = externalReviewsService.unBind(accountId, authId);

        // then - authId is not used in the implementation, so it should still work
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo("COMPLETED");
    }
} 