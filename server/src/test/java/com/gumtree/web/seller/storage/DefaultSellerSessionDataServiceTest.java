package com.gumtree.web.seller.storage;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSessionBeanImpl;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.manageads.model.ManageAdsFilterFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.storage.strategy.BothPersistenceStrategy;
import org.codehaus.jackson.map.ObjectMapper;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

public class DefaultSellerSessionDataServiceTest {
    private DefaultSellerSessionDataService service;
    private BothPersistenceStrategy syiPersistenceStrategy;
    private BothPersistenceStrategy checkoutPersistenceStrategy;
    private BothPersistenceStrategy checkoutPersistenceSimpleStrategy;
    private BothPersistenceStrategy userPersistenceStrategy;
    private BothPersistenceStrategy madFilterPersistenceStrategy;
    private UserSessionService userSessionService;

    private ObjectMapper mapper = new ObjectMapper();


    @Before
    public void setUp() {
        syiPersistenceStrategy = mock(BothPersistenceStrategy.class);
        checkoutPersistenceStrategy = mock(BothPersistenceStrategy.class);
        checkoutPersistenceSimpleStrategy = mock(BothPersistenceStrategy.class);
        userPersistenceStrategy = mock(BothPersistenceStrategy.class);
        madFilterPersistenceStrategy = mock(BothPersistenceStrategy.class);
        userSessionService = mock(UserSessionService.class);


        MetricRegistry metricRegistry = mock(MetricRegistry.class);
        Counter counter = mock(Counter.class);
        when(metricRegistry.counter(any())).thenReturn(counter);

        service = new DefaultSellerSessionDataService(new ObjectMapper(), syiPersistenceStrategy,syiPersistenceStrategy, checkoutPersistenceStrategy,checkoutPersistenceSimpleStrategy,
                userPersistenceStrategy, madFilterPersistenceStrategy, 30, 35, metricRegistry, userSessionService);
    }

    @Test
    public void setPostAdDataStoresInSyiStorage() {
        service.setPostAdData("x", new PostAdDetail());
        verify(syiPersistenceStrategy).writeOperation(any());
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void getPostAdDataReadsFromSyiStorage() {
        service.getPostAdData("x");
        verify(syiPersistenceStrategy).readOperation(eq("x"));
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void removePostAdDataRemovesFromSyiStorage() {
        service.removePostAdData("x");
        verify(syiPersistenceStrategy).writeOperation(any());
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void setCheckoutStoresInCheckoutStorage() {
        service.setCheckout("x", createCheckoutWithOrder());
        verify(checkoutPersistenceStrategy).writeOperation(any());
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void getCheckoutReadsFromCheckoutStorage() {
        service.getCheckout("x");
        verify(checkoutPersistenceStrategy).readOperation(eq("x"));
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void getCheckoutByOrderReadsFromCheckoutStorage() {
        service.getCheckoutByOrder(createCheckoutWithOrder().getOrder());
        verify(checkoutPersistenceStrategy).readOperation(eq("8null1502"));
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void setCheckoutCompleteReadsAndStoresInCheckoutStorage() throws IOException {
        when(checkoutPersistenceStrategy.readOperation(anyString())).thenReturn(mapper.writeValueAsString(createCheckoutWithOrder()));

        service.setCheckoutComplete("x");

        verify(checkoutPersistenceStrategy).readOperation(eq("x"));
        verify(checkoutPersistenceStrategy).writeOperation(any());
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void getUserSessionBeanReadsFromUserStorage() {
        service.getUserSessionBean("x");
        verify(userPersistenceStrategy).readOperation(eq("x"));
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void persistsSessionBeanStoresInUserStorage() {
        service.persistSessionBean("x", new UserSessionBeanImpl());
        verify(userPersistenceStrategy).writeOperation(any());
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void removeUserSessionBeanDataRemovesFromUserStorage() {
        service.removeUserSessionBean("x");
        verify(userPersistenceStrategy).writeOperation(any());
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void extendUserSessionBeanStoresInUserStorage() {
        service.extendUserSessionBean("x");
        verify(userPersistenceStrategy).writeOperation(any());
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(madFilterPersistenceStrategy);
    }

    @Test
    public void getManageAdsFilterFormReadsFromMadStorage() {
        service.getManageAdsFilterForm("x");
        verify(madFilterPersistenceStrategy).readOperation(eq("x"));
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
    }

    @Test
    public void setManageAdsFilterFormStoresInMadStorage() {
        service.setManageAdsFilterForm("x", new ManageAdsFilterFormBean());
        verify(madFilterPersistenceStrategy).writeOperation(any());
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
    }

    @Test
    public void removeManageAdsFilterFormDeletesFromMadStorage() {
        service.removeManageAdsFilterForm("x");
        verify(madFilterPersistenceStrategy).writeOperation(any());
        verifyZeroInteractions(syiPersistenceStrategy);
        verifyZeroInteractions(checkoutPersistenceStrategy);
        verifyZeroInteractions(userPersistenceStrategy);
    }

    private Checkout createCheckoutWithOrder() {
        Checkout checkout = new CheckoutImpl();

        ApiOrder order = new ApiOrder();
        order.setId(8L);
        order.setTotalIncVat(1502L);
        order.setTotalVat(599L);

        checkout.setOrder(order);

        return checkout;
    }

}