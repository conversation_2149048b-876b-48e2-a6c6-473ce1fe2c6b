package com.gumtree.web.seller.page.postad.controller;

import com.google.common.collect.Lists;
import com.gumtree.api.Account;
import com.gumtree.api.SellerType;
import com.gumtree.api.ValueResult;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.bapi.model.SellerTypeEnum;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdAttribute;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeValue;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.products.DefaultProductPrice;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.service.pricing.PricingService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static com.gumtree.mobile.test.Fixtures.SELLER_TYPE;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Created by reweber on 21/03/2016
 */
@RunWith(MockitoJUnitRunner.class)
public class MandatorySellerTypePanelTest {
    private static final Long CAT_ID = 1L;
    private static final Long ACCOUNT_ID = 1L;
    private MandatorySellerTypePanel panel;
    @Mock
    private BushfireApi bushfireApi;
    @Mock
    private AccountApi accountApi;
    @Mock
    private AdvertEditor editor;
    @Mock
    private PricingService pricingService;
    @Mock
    private CategoryModel categoryModel;

    private Account account;
    private PostAdFormBean postAdFormBean;

    @Before
    public void beforeEach() {
        postAdFormBean = new PostAdFormBean();
        account = new Account();
        account.setId(ACCOUNT_ID);

        // api
        when(bushfireApi.accountApi()).thenReturn(accountApi);
        when(accountApi.getAccount(ACCOUNT_ID)).thenReturn(account);

        // editor
        when(editor.getAccountId()).thenReturn(ACCOUNT_ID);
        when(editor.getCategoryId()).thenReturn(CAT_ID);
        when(editor.getPostAdFormBean()).thenReturn(postAdFormBean);
    }

    @Test
    public void privateIsDisableForForcedDealer() {
        // given
        when(accountApi.getSellerType(ACCOUNT_ID, CAT_ID)).thenReturn(new ValueResult<>(SellerType.BUSINESS));

        panel = new MandatorySellerTypePanel(bushfireApi, editor, SELLER_TYPE, pricingService, categoryModel);

        // when
        PostAdAttribute attrs = panel.getAttribute();

        // then
        assertThat(attrs.getValues()).isEqualTo(Lists.newArrayList(
                new PostAdAttributeValue("trade", "Trade", true, false),
                new PostAdAttributeValue("private", "Private", false, true)));
    }

    @Test
    public void shouldReturnDealerSellerTypeWhenSellerIsBusiness() throws Exception {
        // given
        when(accountApi.getSellerType(ACCOUNT_ID, CAT_ID)).thenReturn(new ValueResult<>(SellerType.BUSINESS));

        panel = new MandatorySellerTypePanel(bushfireApi, editor, SELLER_TYPE, pricingService, categoryModel);

        // when
        String sellerType = panel.getSellerType();

        // then
        assertThat(sellerType).isEqualTo("trade");
        assertThat(panel.getText()).isNull();
        assertThat(panel.getPopUp().get("title")).isEqualTo("Why is 'Trade' selected?");
        assertThat(panel.getPopUp().get("body")).isEqualTo("We selected 'Trade' based on your history of listing in this category (including if you listed as a trader in the past).");
    }

    @Test
    public void shouldReturnDefaultSellerTypeWhenSellerIsPrivate() throws Exception {
        // given
        PostAdFormBean postAdFormBeanUpdated = new PostAdFormBean();
        postAdFormBeanUpdated.setDefaultSellerType("PRIVATE");
        when(editor.getPostAdFormBean()).thenReturn(postAdFormBeanUpdated);
        when(accountApi.getSellerType(ACCOUNT_ID, CAT_ID)).thenReturn(new ValueResult<>(SellerType.PRIVATE_WITH_LIVE_AD));

        PricingMetadata pricingMetadata = mock(PricingMetadata.class);
        when(pricingMetadata.getInsertionPrice()).thenReturn(new DefaultProductPrice(ProductType.INSERTION, "", 1799));
        when(pricingService.getPriceInformation(any())).thenReturn(pricingMetadata);

        panel = new MandatorySellerTypePanel(bushfireApi, editor, SELLER_TYPE, pricingService, categoryModel);

        // when
        String sellerType = panel.getSellerType();

        // then
        assertThat(sellerType).isNull();
        assertThat(panel.getText()).isNull();
        assertThat(panel.getPopUp().get("title")).isEqualTo("How to post your ad for free");
        assertThat(panel.getPopUp().get("body")).contains("Private sellers can always have one ad active in this category for free");
    }

    @Test
    public void shouldReturnSelectedSellerType() {
        // given
        PostAdFormBean postAdFormBeanUpdated = new PostAdFormBean();
        postAdFormBeanUpdated.setDefaultSellerType("PRIVATE");
        postAdFormBeanUpdated.setAdvertLimit(2);
        postAdFormBeanUpdated.setAdvertCount(1);
        postAdFormBeanUpdated.addAttribute(CategoryConstants.Attribute.SELLER_TYPE.getName(), "trade");
        when(editor.getPostAdFormBean()).thenReturn(postAdFormBeanUpdated);
        when(accountApi.getSellerType(ACCOUNT_ID, CAT_ID)).thenReturn(new ValueResult<>(SellerType.NO_INFO));

        PricingMetadata pricingMetadata = mock(PricingMetadata.class);
        when(pricingMetadata.getInsertionPrice()).thenReturn(new DefaultProductPrice(ProductType.INSERTION, "", 1799));
        when(pricingService.getPriceInformation(any())).thenReturn(pricingMetadata);

        panel = new MandatorySellerTypePanel(bushfireApi, editor, SELLER_TYPE, pricingService, categoryModel);

        // when
        PostAdAttribute postAdAttribute = panel.getAttribute();

        // then
        assertThat(postAdAttribute.getId()).isEqualTo("seller-type-group");
        assertThat(postAdAttribute.isMandatory()).isEqualTo(true);
        assertThat(postAdAttribute.getPriceSensitive()).isEqualTo(true);
        assertThat(postAdAttribute.getValues().size()).isEqualTo(2);
        assertThat(postAdAttribute.getValues().get(0).getDisplayValue()).isEqualTo("Trade");
        assertThat(postAdAttribute.getValues().get(0).isSelected()).isTrue();
        assertThat(postAdAttribute.getValues().get(0).isDisabled()).isFalse();
        assertThat(postAdAttribute.getValues().get(1).getDisplayValue()).isEqualTo("Private");
        assertThat(postAdAttribute.getValues().get(1).isSelected()).isFalse();
        assertThat(postAdAttribute.getValues().get(1).isDisabled()).isFalse();

        assertThat(panel.getText()).isNull();
        assertThat(panel.getPopUp()).isNull();
    }

    @Test
    public void shouldReturnDealerSellerTypeWhenSuspectedDealer() throws Exception {
        // given
        when(accountApi.getSellerType(ACCOUNT_ID, CAT_ID)).thenReturn(new ValueResult<>(SellerType.SUSPECTED_BUSINESS));

        panel = new MandatorySellerTypePanel(bushfireApi, editor, SELLER_TYPE, pricingService, categoryModel);

        // when
        String sellerType = panel.getSellerType();

        // then
        assertThat(sellerType).isEqualTo("trade");
        assertThat(panel.getPopUp()).isNull();
        assertThat(panel.getText().get("title")).isEqualTo("We see that you post a lot of ads in this category, so we assume that you're selling as a business.");
        assertThat(panel.getText().get("body")).isEqualTo("If you are a private seller, you can change this to 'Private', but be " +
                "aware that it is against the law for a trader to call themselves a private seller.");
    }

    @Test
    public void shouldReturnNullSellerTypeAndPopupWithCorrectPricingWhenSellerIsPrivateWithAd() throws Exception {
        // given
        when(accountApi.getSellerType(ACCOUNT_ID, CAT_ID)).thenReturn(new ValueResult<>(SellerType.PRIVATE_WITH_LIVE_AD));

        PricingMetadata pricingMetadata = mock(PricingMetadata.class);
        when(pricingMetadata.getInsertionPrice()).thenReturn(new DefaultProductPrice(ProductType.INSERTION, "", 1799));
        when(pricingService.getPriceInformation(any())).thenReturn(pricingMetadata);

        panel = new MandatorySellerTypePanel(bushfireApi, editor, SELLER_TYPE, pricingService, categoryModel);

        // when
        String sellerType = panel.getSellerType();

        // then
        assertThat(sellerType).isNull();
        assertThat(panel.getText()).isNull();
        assertThat(panel.getPopUp().get("title")).isEqualTo("How to post your ad for free");
        assertThat(panel.getPopUp().get("body")).isEqualTo("Private sellers can always have one ad active in this category for free.\n" +
                "As you already have one ad active in this category, posting another will cost £17.99.\n" +
                "\n" +
                "If you don't need the other ad anymore, you can delete it in '<a href=\"/manage/ads\" target=\"blank\">Manage My Ads</a>' and post this ad for free.");
    }

    @Test
    public void shouldReturnSellerTypeWhenAccountNull() throws Exception {
        // given
        when(editor.getAccountId()).thenReturn(null);

        panel = new MandatorySellerTypePanel(bushfireApi, editor, SELLER_TYPE, pricingService, categoryModel);

        // when
        String sellerType = panel.getSellerType();

        // then
        assertThat(sellerType).isNull();
        assertThat(panel.getText()).isNull();
        assertThat(panel.getPopUp()).isNull();
    }

    @Test
    public void shouldAllowPrivateSellerTypeWhenProAccount() {
        // Given
        postAdFormBean.setAdvertLimit(2);
        postAdFormBean.setAdvertCount(3);
        account.setPro(true);
        when(editor.isEditMode()).thenReturn(false);
        when(accountApi.getSellerType(anyLong(), anyLong())).thenReturn(new ValueResult<>(SellerType.NO_INFO));

        panel = new MandatorySellerTypePanel(bushfireApi, editor, SELLER_TYPE, pricingService, categoryModel);

        // When
        PostAdAttribute attrs =  panel.getAttribute();

        // then
        assertThat(attrs.getValues()).isEqualTo(Lists.newArrayList(
                new PostAdAttributeValue("trade", "Trade", false, false),
                new PostAdAttributeValue("private", "Private", true, false)));
    }
}
