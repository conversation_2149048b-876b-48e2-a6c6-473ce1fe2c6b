package com.gumtree.web.seller.page.postad.controller;

import com.gumtree.api.Ad;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DeleteAdConfirmControllerTest extends BaseSellerControllerTest {
    private DeleteAdConfirmController controller;

    @Before
    public void init() {
        controller = new DeleteAdConfirmController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme,
                bushfireApi, mock(UserSessionService.class));
    }

    @Test
    public void testDeleteAdConfirmPage() {
        AdvertApi advertApi = mock(AdvertApi.class);
        Ad ad = new Ad();
        ad.setId(1L);
        ad.setTitle("hello");
        when(advertApi.getAdvert(1L)).thenReturn(ad);
        when(bushfireApi.advertApi()).thenReturn(advertApi);

        ModelAndView modelAndView = controller.deleteAdConfirm("1");
        assertThat(modelAndView.getViewName(), equalTo(DeleteAdConfirmController.VIEW_NAME));
        assertThat(modelAndView.getModel().size(), equalTo(2));
        assertThat((Long) modelAndView.getModel().get(DeleteAdConfirmController.ADVERT_ID), equalTo(1L));
        assertThat((String) modelAndView.getModel().get(DeleteAdConfirmController.ADVERT_TITLE), equalTo("hello"));
    }

}
