package com.gumtree.web.seller.page.payment.service;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.payment.services.PaymentSuccessService;
import com.gumtree.web.seller.page.postad.model.meta.MetaPathInfo;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;
import com.gumtree.web.seller.page.postad.model.meta.PagePaymentType;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.page.reviews.service.ExternalReviewsService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PaymentSuccessServiceTest {
    private PaymentSuccessService paymentSuccessService;
    private CategoryService categoryService;
    private ExternalReviewsService externalReviewsService;
    private Category categoryCars;
    private Category categoryNonCars;
    private Category categorySubCars;

    @Before
    public void setup() {
        categoryService = mock(CategoryService.class);
        externalReviewsService = mock(ExternalReviewsService.class);
        paymentSuccessService = new PaymentSuccessService(categoryService, externalReviewsService);
        categoryCars = new Category();
        categoryCars.setId(9311L);
        categoryCars.setSeoName("cars");
        categoryNonCars = new Category();
        categoryNonCars.setId(-9311L);

        categorySubCars = new Category();
        categorySubCars.setId(10432L);

        when(categoryService.getById(9311L)).thenReturn(Optional.of(categoryCars));
        when(categoryService.getById(-9311L)).thenReturn(Optional.of(categoryNonCars));

        when(categoryService.getCategoriesList(categoryCars)).thenReturn(Lists.newArrayList(categoryCars));
        when(categoryService.getCategoriesList(categoryNonCars)).thenReturn(Lists.newArrayList(categoryNonCars));


        when(categoryService.getById(10432L)).thenReturn(Optional.of(categorySubCars));
        when(categoryService.getCategoriesList(categorySubCars)).thenReturn(Lists.newArrayList(categoryCars));
    }

    @Test
    public void cars_trade_ad_should_return_url_except_carpopupEqualsFalse() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "trade");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void cars_private_ad_should_return_url_except_carpopupEqualsTrue() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, null, false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=none&type=BUMP_UP,INSERTION&multiple=no&poptype=carwow&advertId=10", url);
    }

    @Test
    public void cars_private_nonpost_ad_should_return_url_except_carpopupEqualsFalse() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.RELIST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=relist&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void cars_subcategory_private_ad_should_return_url_except_carpopupEqualsTrue() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(10432L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=carwow&advertId=10", url);
    }

    @Test
    public void noncars_private_ad_should_return_url_except_carpopupEqualsFalse() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void noncars_trade_ad_should_return_url_except_carpopupEqualsFalse() {
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        ad.setStatus(AdStatus.LIVE);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "trade");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
        assertEquals("/thankyou/thekey?action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no&poptype=&advertId=10", url);
    }

    private CheckoutAdvert getCheckoutAdvert(Ad ad, String sellerType) {
        return CheckoutAdvert.Builder.builder()
                .categoryId(ad.getCategoryId())
                .locationId(ad.getLocationId())
                .sellerType(sellerType)
                .id(ad.getId()).build();
    }

    @Test
    public void should_handle_exception_and_return_base_url() {
        // given
        Checkout checkout = new CheckoutImpl();
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(null); // This will cause NPE

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey", url);
    }

    @Test
    public void should_handle_null_advert() {
        // given
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(null);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), false));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=no&poptype=", url);
    }

    @Test
    public void should_handle_null_seller_type() {
        // given
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(9311L);
        CheckoutAdvert checkoutAdvert = CheckoutAdvert.Builder.builder()
                .categoryId(ad.getCategoryId())
                .sellerType(null)
                .id(ad.getId()).build();
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), false));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void should_handle_null_category_id() {
        // given
        when(categoryService.getById(0L)).thenReturn(Optional.<Category>absent());
        
        Ad ad = new Ad();
        ad.setId(10L);
        CheckoutAdvert checkoutAdvert = CheckoutAdvert.Builder.builder()
                .categoryId(null)
                .sellerType("private")
                .id(ad.getId()).build();
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), false));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void should_handle_category_not_found() {
        // given
        when(categoryService.getById(99999L)).thenReturn(Optional.<Category>absent());
        
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(99999L);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), false));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void should_handle_google_review_experiment_with_cookie() {
        // given
        HttpServletRequest mockRequest = mock(HttpServletRequest.class);
        ServletRequestAttributes attrs = new ServletRequestAttributes(mockRequest);
        RequestContextHolder.setRequestAttributes(attrs);
        
        Cookie experimentCookie = new Cookie("inExperiment_GoogleReview", "B");
        Cookie[] cookies = {experimentCookie};
        when(mockRequest.getCookies()).thenReturn(cookies);
        
        ApiOrder order = new ApiOrder();
        order.setAccountId(123L);
        
        when(externalReviewsService.getGoogleReviewSwitchInSyiFlow(123L, -9311L)).thenReturn(true);
        
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setOrder(order);
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), false));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=no&poptype=googlereview1&advertId=10", url);
        
        // cleanup
        RequestContextHolder.resetRequestAttributes();
    }

    @Test
    public void should_not_show_google_review_when_not_in_experiment() {
        // given
        HttpServletRequest mockRequest = mock(HttpServletRequest.class);
        ServletRequestAttributes attrs = new ServletRequestAttributes(mockRequest);
        RequestContextHolder.setRequestAttributes(attrs);
        
        Cookie experimentCookie = new Cookie("inExperiment_GoogleReview", "A");
        Cookie[] cookies = {experimentCookie};
        when(mockRequest.getCookies()).thenReturn(cookies);
        
        ApiOrder order = new ApiOrder();
        order.setAccountId(123L);
        
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setOrder(order);
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), false));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=no&poptype=&advertId=10", url);
        
        // cleanup
        RequestContextHolder.resetRequestAttributes();
    }

    @Test
    public void should_handle_google_review_when_no_cookies() {
        // given
        HttpServletRequest mockRequest = mock(HttpServletRequest.class);
        ServletRequestAttributes attrs = new ServletRequestAttributes(mockRequest);
        RequestContextHolder.setRequestAttributes(attrs);
        
        when(mockRequest.getCookies()).thenReturn(null);
        
        ApiOrder order = new ApiOrder();
        order.setAccountId(123L);
        
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setOrder(order);
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), false));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=no&poptype=&advertId=10", url);
        
        // cleanup
        RequestContextHolder.resetRequestAttributes();
    }

    @Test
    public void should_handle_google_review_when_no_order() {
        // given
        HttpServletRequest mockRequest = mock(HttpServletRequest.class);
        ServletRequestAttributes attrs = new ServletRequestAttributes(mockRequest);
        RequestContextHolder.setRequestAttributes(attrs);
        
        Cookie experimentCookie = new Cookie("inExperiment_GoogleReview", "B");
        Cookie[] cookies = {experimentCookie};
        when(mockRequest.getCookies()).thenReturn(cookies);
        
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setOrder(null);
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), false));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=no&poptype=&advertId=10", url);
        
        // cleanup
        RequestContextHolder.resetRequestAttributes();
    }

    @Test
    public void should_handle_no_request_context() {
        // given
        RequestContextHolder.resetRequestAttributes();
        
        ApiOrder order = new ApiOrder();
        order.setAccountId(123L);
        
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setOrder(order);
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), false));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=no&poptype=&advertId=10", url);
    }

    @Test
    public void should_handle_multiple_ads() {
        // given
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setCategoryId(-9311L);
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad, "private");
        Checkout checkout = new CheckoutImpl();
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(),
                PageActionType.POST, new ArrayList<PagePaymentType>(), true));

        // when
        String url = paymentSuccessService.getPaymentSuccessUrl(checkout);

        // then
        assertEquals("/thankyou/thekey?action=post&payment=none&type=none&multiple=yes&poptype=&advertId=10", url);
    }
}
