package com.gumtree.web.seller.page.motors;

import com.google.common.collect.Lists;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.motors.webapi.service.MotorsService;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardisedVehicleDataResponse;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.ajax.vrm.MotorsApiClient;
import com.gumtree.web.seller.page.motors.model.VehicleVerificationModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Collections;
import java.util.Optional;

import static com.google.common.collect.Lists.newArrayList;
import static java.util.Collections.emptyList;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.mock;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.when;

@RunWith(Parameterized.class)
public class VrmSanitizationTest extends BaseSellerControllerTest {

    private static final Long CATEGORY_ID = 9311L;

    @Parameterized.Parameters
    public static Collection<Object[]> DATA() {
        return newArrayList(new Object[][]{
                {"123", "123"},
                {"VALID", "VALID"},
                {"Y3455", "Y3455"},
                {"<br/>", ""},
                {"<svg/onload=", ""},
                {"<script>alert('Bad exploit!');</script>", ""},
                {"I<html/> AM <hr/>GOOD<dadd>!", "IAMGOOD"},
                {"<html><title>Hello</title><html>", "Hello"},
                {"XXX'};alert(document.cookie);var x ={x:'", "XXXalertdocumentcookievarxx"}
        });
    }

    private final CategoryModel categoryModelMock = mock(CategoryModel.class);
    private final MotorsApiClient motorsApiClient = mock(MotorsApiClient.class);

    private final MotorsService motorsService = new MotorsService(
            motorsApiClient,
            categoryModelMock,
            bushfireApi,
            null,
            null
    );

    private final String vrm, sanitized;
    private final VehicleVerificationController vehicleVerificationController;

    public VrmSanitizationTest(String input, String output) {
        this.vrm = input;
        this.sanitized = output;
        this.vehicleVerificationController = new VehicleVerificationController(
                motorsService,
                cookieResolver,
                categoryModelMock,
                apiCallExecutor,
                messageResolver,
                urlScheme,
                userSessionService
        );
    }

    @Before
    public void setup() {
        when(categoryModelMock.getCategoryAttributes(CATEGORY_ID))
                .thenReturn(com.google.common.base.Optional.of(emptyList()));

        autowireAbExperimentsService(vehicleVerificationController);
    }

    @Test
    public void testVrmSanitization() throws Exception {
        when(motorsApiClient.lookupVehicleData(vrm, CATEGORY_ID))
                .thenReturn(Optional.of(new StandardisedVehicleDataResponse()));

        ModelAndView response = vehicleVerificationController.verify(CATEGORY_ID, vrm, request);
        VehicleVerificationModel model = extractModel(response, VehicleVerificationModel.class);
        assertThat(model.getEnteredVrm(), is(sanitized));
    }
}
