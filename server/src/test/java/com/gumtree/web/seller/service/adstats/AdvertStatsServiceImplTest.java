package com.gumtree.web.seller.service.adstats;

import com.google.common.collect.Lists;
import com.gumtree.api.Ad;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import io.micrometer.core.instrument.Timer;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.CoreMatchers.is;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

@RunWith(MockitoJUnitRunner.class)
public class AdvertStatsServiceImplTest {

    @Mock
    private AdvertApi advertApi;

    @Mock
    private BatchAdStatsProvider batchAdStatsProvider;

    @Mock
    private BushfireApi bushfireApi;

    @Mock
    private CustomMetricRegistry metrics;

    @Mock
    Timer mockTimer;

    @InjectMocks
    private AdvertStatsServiceImpl advertStatsServiceImpl;

    @Before
    public void init() {
        initMocks(this);
        initBapi();
    }

    @Test
    public void getStatisticForAdvertList_nullAdverts() {
        List<AdvertStatisticData> result = advertStatsServiceImpl.getStatisticForAdvertList(null);
        assertThat(result.size(), is(0));

        verifyZeroInteractions(metrics);
    }

    @Test
    public void getStatisticForAdvertList_emptyAdverts() {
        List<AdvertStatisticData> result = advertStatsServiceImpl.getStatisticForAdvertList(new ArrayList<Ad>());
        assertThat(result.size(), is(0));

        verifyZeroInteractions(metrics);
    }

    @Test
    public void getStatisticForAdvertList_withAdverts() {
        List<Ad> advertList = Lists.newArrayList(new Ad(), new Ad());
        List<AdvertStatisticData> advertStatisticData = Lists.newArrayList(
                new AdvertStatisticData(), new AdvertStatisticData()
        );

        when(metrics.madPageTimer("batchAdStatsProvider.getStatsFor")).thenReturn(mockTimer);

        when(mockTimer.record(any(Supplier.class))).thenAnswer(invocation -> {
            // Execute the actual lambda passed to Timer.record() and return the result
            Supplier<List<AdvertStatisticData>> supplier = (Supplier<List<AdvertStatisticData>>) invocation.getArguments()[0];
            return supplier.get();
        });

        when(batchAdStatsProvider.getStatsFor(advertList)).thenReturn(advertStatisticData);

        List<AdvertStatisticData> result = advertStatsServiceImpl.getStatisticForAdvertList(advertList);

        assertThat(result.size(), is(2));
    }

    private void initBapi() {
        when(bushfireApi.advertApi()).thenReturn(advertApi);
    }
}
