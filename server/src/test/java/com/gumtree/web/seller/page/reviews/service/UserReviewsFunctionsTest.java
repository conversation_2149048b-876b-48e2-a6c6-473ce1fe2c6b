package com.gumtree.web.seller.page.reviews.service;

import com.google.common.collect.Lists;
import com.gumtree.mobile.test.Fixtures;
import com.gumtree.userreviewsservice.client.exception.ClientException;
import com.gumtree.userreviewsservice.client.exception.ServerException;
import com.gumtree.userreviewsservice.client.model.CreateReviewRequest;
import com.gumtree.userreviewsservice.client.model.ErrorItem;
import com.gumtree.userreviewsservice.client.model.ErrorResponse;
import com.gumtree.web.api.WebApiError;
import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.common.domain.messagecentre.Message;
import com.gumtree.web.common.domain.messagecentre.MessageDirection;
import com.gumtree.web.common.domain.messagecentre.Messages;
import com.gumtree.web.common.domain.messagecentre.Role;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.junit.Test;
import org.springframework.http.HttpStatus;
import rx.Single;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.gumtree.mobile.test.Fixtures.Convers.reviewAllowingConversation;
import static com.gumtree.mobile.test.Fixtures.Convers.reviewAllowingConversationMessages;
import static com.gumtree.web.seller.page.reviews.service.UserReviewsFunctions.isConversationReviewable;
import static org.fest.assertions.api.Assertions.assertThat;
import static util.RxAssertions.verifyError;

public class UserReviewsFunctionsTest {

    @Test
    public void shouldAllowReviewIfAllIsOk() {
        // when
        boolean allowed = isConversationReviewable(reviewAllowingConversation().build());

        // then
        assertThat(allowed).isTrue();
    }

    @Test
    public void shouldNotAllowReviewIfNotEnoughBuyerMessages() {
        // given
        ArrayList<Message> messages = Lists.newArrayList(
                new Message.Builder().setDirection(MessageDirection.OUTBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.OUTBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.OUTBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.OUTBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.INBOUND).build()
        );

        // when
        boolean allowed = isConversationReviewable(reviewAllowingConversation().setMessages(messages).build());

        // then
        assertThat(allowed).isFalse();
    }

    @Test
    public void shouldNotAllowReviewIfNotEnoughSellerMessages() {
        // given
        ArrayList<Message> messages = Lists.newArrayList(
                new Message.Builder().setDirection(MessageDirection.INBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.INBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.INBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.OUTBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.INBOUND).build()
        );

        // when
        boolean allowed = isConversationReviewable(reviewAllowingConversation().setMessages(messages).build());

        // then
        assertThat(allowed).isFalse();
    }

    @Test
    public void shouldNotAllowReviewIfConverseeIdIsMissing() {
        // when
        boolean allowed = isConversationReviewable(reviewAllowingConversation().setConverseeId(Optional.empty()).build());

        // then
        assertThat(allowed).isFalse();
    }

    @Test
    public void shouldNotAllowReviewIfConversationStartedMoreThan28DaysAgo() {
        // given
        String conversationTime = new DateTime().minusDays(29).toString("yyyy-MM-dd'T'HH:mm:ss.SSS'Z");
        Messages conversation = reviewAllowingConversation()
                .setMessages(reviewAllowingConversationMessages(conversationTime))
                .build();

        // when
        boolean allowed = isConversationReviewable(conversation);

        // then
        assertThat(allowed).isFalse();
    }

    @Test
    public void shouldAllowReviewIfConversationHasNotStartedMoreThan28DaysAgo() {
        // given
        String conversationTime = new DateTime().minusDays(27).toString("yyyy-MM-dd'T'HH:mm:ss.SSS'Z");
        Messages conversation = reviewAllowingConversation()
                .setMessages(reviewAllowingConversationMessages(conversationTime))
                .build();

        // when
        boolean allowed = isConversationReviewable(conversation);

        // then
        assertThat(allowed).isTrue();
    }

    @Test
    public void countSellerMessagesIfNoSellerMessage() {
        // given
        ArrayList<Message> msgList = Lists.newArrayList(
                new Message.Builder().setDirection(MessageDirection.OUTBOUND).build()
        );

        Messages messages = new Messages.Builder()
                .setUserRole(Role.Buyer)
                .setMessages(msgList)
                .build();

        // when
        long count = UserReviewsFunctions.countSellerMessages(messages);

        // then
        assertThat(count).isEqualTo(0);
    }

    @Test
    public void countSellerMessagesInConversation() {
        // given
        ArrayList<Message> msgList = Lists.newArrayList(
                new Message.Builder().setDirection(MessageDirection.INBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.INBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.OUTBOUND).build()
        );

        Messages messages = new Messages.Builder()
                .setUserRole(Role.Buyer)
                .setMessages(msgList)
                .build();

        // when
        long count = UserReviewsFunctions.countSellerMessages(messages);

        // then
        assertThat(count).isEqualTo(2);
    }

    @Test
    public void countBuyerMessagesInConversation() {
        // given
        ArrayList<Message> msgList = Lists.newArrayList(
                new Message.Builder().setDirection(MessageDirection.INBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.INBOUND).build(),
                new Message.Builder().setDirection(MessageDirection.OUTBOUND).build()
        );

        Messages messages = new Messages.Builder()
                .setUserRole(Role.Buyer)
                .setMessages(msgList)
                .build();

        // when
        long count = UserReviewsFunctions.countBuyerMessages(messages);

        // then
        assertThat(count).isEqualTo(1);
    }

    @Test
    public void convertRoleToDirection() {
        assertThat(UserReviewsFunctions.convert2Direction(Role.Buyer)).isEqualTo(CreateReviewRequest.DirectionEnum.B2S);
        assertThat(UserReviewsFunctions.convert2Direction(Role.Seller)).isEqualTo(CreateReviewRequest.DirectionEnum.S2B);
    }

    @Test
    public void convertErrorResponse() {
        // given
        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setErrorCode("error-code");
        errorResponse.setMessage("error message");

        // when
        WebApiErrorResponse apiError = UserReviewsFunctions.convertErrorResponseToWebApiErrorResponse(HttpStatus.OK, errorResponse);

        // then
        assertThat(apiError.getStatus()).isEqualTo(HttpStatus.OK);
        assertThat(apiError.getCode()).isEqualTo("error-code");
        assertThat(apiError.getMessage()).isEqualTo("error message");
        assertThat(apiError.getErrors()).isEmpty();
    }

    @Test
    public void convertErrorResponseWithErrorItems() {
        // given
        ErrorResponse errorResponse = Fixtures.ReviewApi.errorResponseWithErrors();

        // when
        WebApiErrorResponse apiError = UserReviewsFunctions.convertErrorResponseToWebApiErrorResponse(HttpStatus.OK, errorResponse);

        // then
        assertThat(apiError.getStatus()).isEqualTo(HttpStatus.OK);
        assertThat(apiError.getCode()).isEqualTo("invalid-request");
        assertThat(apiError.getMessage()).isEqualTo("Create review request is not valid");
        assertThat(apiError.getErrors()).hasSize(1);
        assertThat(apiError.getErrors().get(0).getCode()).isEqualTo("rating.invalid");
        assertThat(apiError.getErrors().get(0).getMessage()).isEqualTo("Rating value is invalid.");
        assertThat(apiError.getErrors().get(0).getField()).isEqualTo("rating");
    }

    @Test
    public void shouldGetConversationAgeIfDateStrIsParsable() {
        DateTime tenDaysAgo = new DateTime().minusDays(10).minusHours(5);
        String tenDaysAgoStr = tenDaysAgo.toString("yyyy-MM-dd'T'HH:mm:ss.SSS'Z");
        int expectedAge = Days.daysBetween(tenDaysAgo, new DateTime()).getDays();

        // given
        List<Message> msgList = Lists.newArrayList(
                new Message.Builder().setDirection(MessageDirection.INBOUND).setTime(tenDaysAgoStr).build()
        );

        // when
        Optional<Integer> conversationAge = UserReviewsFunctions.getConversationAgeInDays(msgList);

        // then
        assertThat(conversationAge).isEqualTo(Optional.of(expectedAge));
    }

    @Test
    public void shouldNotGetConversationAgeInDateStrIsNotParsable() {
        // given
        List<Message> msgList = Lists.newArrayList(
                new Message.Builder().setDirection(MessageDirection.INBOUND).setTime("invalid datetime").build()
        );

        // when
        Optional<Integer> conversationAge = UserReviewsFunctions.getConversationAgeInDays(msgList);

        // then
        assertThat(conversationAge).isEqualTo(Optional.empty());
    }

    @Test
    public void conversationNotTooOldToBeReviewed() {
        String tenDaysAgo = new DateTime().minusDays(5).toString("yyyy-MM-dd'T'HH:mm:ss.SSS'Z");

        // given
        List<Message> msgList = Lists.newArrayList(
                new Message.Builder().setDirection(MessageDirection.INBOUND).setTime(tenDaysAgo).build()
        );

        // when
        boolean tooOld = UserReviewsFunctions.isConversationIsOlderThan(msgList, 6);

        // then
        assertThat(tooOld).isEqualTo(false);
    }

    @Test
    public void conversationNotTooOldToBeReviewedIfCreationDateIsNotParsable() {
        // given
        List<Message> msgList = Lists.newArrayList(
                new Message.Builder().setDirection(MessageDirection.INBOUND).setTime("invalid date").build()
        );

        // when
        boolean tooOld = UserReviewsFunctions.isConversationIsOlderThan(msgList, 6);

        // then
        assertThat(tooOld).isEqualTo(false);
    }

    @Test
    public void shouldHandleUnexpectedSubmitReviewException() {
        // when
        Single<Long> result = UserReviewsFunctions.handleSubmitReviewError(new RuntimeException("fake error"));

        // then
        WebApiErrorResponse expectedError = new WebApiErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "internal-error",
                "Unable to submit a review");

        verifyError(result, (error) -> {
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            assertThat(((WebApiErrorException) error).getError()).isEqualTo(expectedError);
        });
    }

    @Test
    public void shouldHandleSubmitReviewException_ApiClient400() {
        // when
        ErrorItem errorItem = new ErrorItem().code("not-null").field("text").message("Text is required");
        ErrorResponse errorResponse = new ErrorResponse().errorCode("invalid-request").message("Request is not valid").addErrorsItem(errorItem);
        Single<Long> result = UserReviewsFunctions.handleSubmitReviewError(new ClientException(errorResponse));

        // then
        WebApiErrorResponse expectedError = new WebApiErrorResponse(
                HttpStatus.BAD_REQUEST,
                "invalid-request",
                "Request is not valid",
                Lists.newArrayList(new WebApiError("not-null", "Text is required", "text")));

        verifyError(result, (error) -> {
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            assertThat(((WebApiErrorException) error).getError()).isEqualTo(expectedError);
        });
    }

    @Test
    public void shouldHandleSubmitReviewException_ApiClient500() {
        // when
        ErrorResponse errorResponse = new ErrorResponse().errorCode("server-down").message("Server is down");
        Single<Long> result = UserReviewsFunctions.handleSubmitReviewError(new ServerException(errorResponse));

        // then
        WebApiErrorResponse expectedError = new WebApiErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "internal-error",
                "Unable to submit a review");

        verifyError(result, (error) -> {
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            assertThat(((WebApiErrorException) error).getError()).isEqualTo(expectedError);
        });
    }

}