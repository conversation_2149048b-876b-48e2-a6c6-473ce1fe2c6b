package com.gumtree.web.seller.service.pricing;

import com.google.common.base.Optional;
import com.google.common.base.Predicate;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Iterables;
import com.gumtree.api.ApiProductPrice;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.SellerType;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.PriceApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.domain.attribute.ApiAttribute;
import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.products.FeatureOption;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.hamcrest.CoreMatchers;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Matchers;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ApiPricingServiceTest {

    private BushfireApi bushfireApi;

    private PriceApi priceApi;

    private Clock clock;

    private PricingContext context;

    private ApiPricingService pricingService;

    private ArrayList<ApiProductPrice> apiProductPrices;

    @Before
    public void init() {
        bushfireApi = mock(BushfireApi.class);
        priceApi = mock(PriceApi.class);
        clock = mock(Clock.class);
        context = mock(PricingContext.class);

        BushfireApiKey bushfireApiKey = new BushfireApiKey();
        UserSession userSession = mock(UserSession.class);
        when(userSession.getApiKey()).thenReturn(bushfireApiKey);
        pricingService = new ApiPricingService(bushfireApi, userSession, clock);

        when(clock.now()).thenReturn(1334217162000L);

        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setAttributes(ImmutableMap.of(CategoryConstants.Attribute.PRICE.getName(), "100000"));

        PostAdDetail postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(postAdFormBean);

        AdvertEditor advertEditor = mock(AdvertEditor.class);
        when(advertEditor.getAdvertDetail()).thenReturn(postAdDetail);

        when(context.getCategoryId()).thenReturn(1L);
        when(context.getLocationId()).thenReturn(2L);
        when(context.getAccountId()).thenReturn(3L);
        when(context.isProductActive(Matchers.<ProductType>any())).thenReturn(false);
        when(context.getProductExpiryDate(Matchers.<ProductType>any())).thenReturn(null);
        when(context.getAdvertEditor()).thenReturn(advertEditor);
        when(context.getSellerType()).thenReturn(Optional.absent());

        when(priceApi.getPricesForCategoryIdLocationIdAccountId(eq(1L), eq(2L), eq(3L), any(Map.class))).thenReturn(createPriceTestData());

        apiProductPrices = new ArrayList<>();
        apiProductPrices.add(new ApiProductPrice(54L, 999L, 54L, ProductName.INSERTION, ""));
        apiProductPrices.add(new ApiProductPrice(54L, 299L, 54L, ProductName.BUMP_UP, ""));
        apiProductPrices.add(new ApiProductPrice(54L, 499L, 54L, ProductName.URGENT, ""));
        apiProductPrices.add(new ApiProductPrice(54L, 299L, 54L, ProductName.FEATURE_3_DAY, ""));
        apiProductPrices.add(new ApiProductPrice(54L, 499L, 54L, ProductName.FEATURE_7_DAY, ""));
        apiProductPrices.add(new ApiProductPrice(54L, 999L, 54L, ProductName.FEATURE_14_DAY, ""));
        apiProductPrices.add(new ApiProductPrice(54L, 1499L, 54L, ProductName.HOMEPAGE_SPOTLIGHT, ""));

        when(bushfireApi.priceApi()).thenReturn(priceApi);
        when(bushfireApi.create(PriceApi.class, bushfireApiKey)).thenReturn(priceApi);
        when(bushfireApi.priceApi().getPricesForAdvert(anyLong(), any(PostAdvertBean.class))).thenReturn(apiProductPrices);
        when(bushfireApi.priceApi().getPricesForAdvert(any(PostAdvertBean.class))).thenReturn(apiProductPrices);
    }

    @Test
    public void extractsInsertionPriceCorrectlyWhenNoPackage() {
        // when
        ProductPrice price = pricingService.getPriceInformation(context).getInsertionPrice();

        // then
        assertThat(price.getProductName(), equalTo(ProductName.INSERTION.name()));
        assertThat(price.getDisplayValue(), equalTo("&pound;9.99"));
        assertThat(price.getPrice(), equalTo(new BigDecimal("9.99")));
        assertThat(price.getProductType(), equalTo(ProductType.INSERTION));
    }

    @Test
    public void extractsBumpUpPriceCorrectly() {
        // when
        ProductPrice price = pricingService.getPriceInformation(context).getBumpUpPrice();

        // then
        assertThat(price.getProductName(), equalTo(ProductName.BUMP_UP.name()));
        assertThat(price.getDisplayValue(), equalTo("&pound;2.99"));
        assertThat(price.getPrice(), equalTo(new BigDecimal("2.99")));
        assertThat(price.getProductType(), equalTo(ProductType.BUMP_UP));
    }

    @Test
    public void extractsFeatureOptionsWhenFeaturesAreNotCurrentlyActive() {
        // when
        PricingMetadata metadata = pricingService.getPriceInformation(context);
        Map<ProductType, FeatureOption> featureOptions = metadata.getFeatureOptions();

        FeatureOption urgentOption = featureOptions.get(ProductType.URGENT);
        FeatureOption featuredOption = featureOptions.get(ProductType.FEATURED);
        FeatureOption spotlightOption = featureOptions.get(ProductType.SPOTLIGHT);

        // then

        // URGENT
        assertThat(urgentOption.getType(), equalTo(ProductType.URGENT));
        assertThat(urgentOption.getDescription(), equalTo(ProductType.URGENT.getDescription()));
        assertThat(urgentOption.getExpiryDescription(), equalTo(null));
        assertThat(urgentOption.isActive(), equalTo(false));
        assertThat(urgentOption.getPrices().size(), equalTo(1));
        assertProductPrice(urgentOption.getPrices(), ProductName.URGENT, ProductType.URGENT, new BigDecimal("4.99"), 7);
        assertThat(metadata.getFeaturePrice(ProductType.URGENT, ProductName.URGENT.name()).getPrice(), equalTo(new BigDecimal("4.99")));

        // FEATURED
        assertThat(featuredOption.getType(), equalTo(ProductType.FEATURED));
        assertThat(featuredOption.getDescription(), equalTo(ProductType.FEATURED.getDescription()));
        assertThat(featuredOption.getExpiryDescription(), equalTo(null));
        assertThat(featuredOption.isActive(), equalTo(false));
        assertProductPrice(featuredOption.getPrices(), ProductName.FEATURE_3_DAY, ProductType.FEATURED, new BigDecimal("2.99"), 3);
        assertThat(metadata.getFeaturePrice(ProductType.FEATURED, ProductName.FEATURE_3_DAY.name()).getPrice(), equalTo(new BigDecimal("2.99")));
        assertProductPrice(featuredOption.getPrices(), ProductName.FEATURE_7_DAY, ProductType.FEATURED, new BigDecimal("4.99"), 7);
        assertThat(metadata.getFeaturePrice(ProductType.FEATURED, ProductName.FEATURE_7_DAY.name()).getPrice(), equalTo(new BigDecimal("4.99")));
        assertProductPrice(featuredOption.getPrices(), ProductName.FEATURE_14_DAY, ProductType.FEATURED, new BigDecimal("9.99"), 14);
        assertThat(metadata.getFeaturePrice(ProductType.FEATURED, ProductName.FEATURE_14_DAY.name()).getPrice(), equalTo(new BigDecimal("9.99")));

        // SPOTLIGHT
        assertThat(spotlightOption.getType(), equalTo(ProductType.SPOTLIGHT));
        assertThat(spotlightOption.getDescription(), equalTo(ProductType.SPOTLIGHT.getDescription()));
        assertThat(spotlightOption.getExpiryDescription(), equalTo(null));
        assertThat(spotlightOption.isActive(), equalTo(false));
        assertProductPrice(spotlightOption.getPrices(), ProductName.HOMEPAGE_SPOTLIGHT, ProductType.SPOTLIGHT, new BigDecimal("14.99"), 7);
        assertThat(metadata.getFeaturePrice(ProductType.SPOTLIGHT, ProductName.HOMEPAGE_SPOTLIGHT.name()).getPrice(), equalTo(new BigDecimal("14.99")));
    }

    @Test
    public void addsSellerTypeToAdvertIfSet() {
        when(context.getSellerType()).thenReturn(Optional.of(SellerType.PRIVATE_WITH_LIVE_AD));
        ProductPrice price = pricingService.getPriceInformation(context).getBumpUpPrice();

        // then
        ArgumentCaptor<PostAdvertBean> captor = ArgumentCaptor.forClass(PostAdvertBean.class);
        verify(bushfireApi.priceApi()).getPricesForAdvert(eq(0L), captor.capture());
        ApiAttribute sellerTypeAttr = captor.getValue().getAttributes()
                .stream()
                .filter(attr -> Objects.equals(attr.getName(), CategoryConstants.Attribute.SELLER_TYPE.getName()))
                .findFirst()
                .get();
        assertThat(sellerTypeAttr.getValue(), equalTo(SellerType.PRIVATE_WITH_LIVE_AD.name()));
    }

    @Test
    public void extractsFeatureOptionsWhenFeaturesAreCurrentlyActive() {
        // given
        when(context.isProductActive(Matchers.<ProductType>any())).thenReturn(true);
        when(context.getProductExpiryDate(ProductType.URGENT)).thenReturn(new DateTime(1334476361000L));
        when(context.getProductExpiryDate(ProductType.FEATURED)).thenReturn(new DateTime(1334735561000L));
        when(context.getProductExpiryDate(ProductType.SPOTLIGHT)).thenReturn(new DateTime(1335081161000L));

        // when
        PricingMetadata metadata = pricingService.getPriceInformation(context);
        Map<ProductType, FeatureOption> featureOptions = metadata.getFeatureOptions();

        FeatureOption urgentOption = featureOptions.get(ProductType.URGENT);
        FeatureOption featuredOption = featureOptions.get(ProductType.FEATURED);
        FeatureOption spotlightOption = featureOptions.get(ProductType.SPOTLIGHT);

        // then
        assertThat(urgentOption.getType(), equalTo(ProductType.URGENT));
        assertThat(urgentOption.getDescription(), equalTo(ProductType.URGENT.getDescription()));
        assertThat(urgentOption.getExpiryDescription(), equalTo("3 days left"));
        assertThat(urgentOption.isActive(), equalTo(true));
        assertThat(urgentOption.getPrices(), equalTo(null));
        assertThat(metadata.getFeaturePrice(ProductType.URGENT, ProductName.URGENT.name()), CoreMatchers.nullValue());

        assertThat(featuredOption.getType(), equalTo(ProductType.FEATURED));
        assertThat(featuredOption.getDescription(), equalTo(ProductType.FEATURED.getDescription()));
        assertThat(featuredOption.getExpiryDescription(), equalTo("6 days left"));
        assertThat(featuredOption.isActive(), equalTo(true));
        assertThat(featuredOption.getPrices(), equalTo(null));
        assertThat(metadata.getFeaturePrice(ProductType.FEATURED, ProductName.FEATURE_3_DAY.name()), CoreMatchers.nullValue());
        assertThat(metadata.getFeaturePrice(ProductType.FEATURED, ProductName.FEATURE_7_DAY.name()), CoreMatchers.nullValue());
        assertThat(metadata.getFeaturePrice(ProductType.FEATURED, ProductName.FEATURE_14_DAY.name()), CoreMatchers.nullValue());

        assertThat(spotlightOption.getType(), equalTo(ProductType.SPOTLIGHT));
        assertThat(spotlightOption.getDescription(), equalTo(ProductType.SPOTLIGHT.getDescription()));
        assertThat(spotlightOption.getExpiryDescription(), equalTo("10 days left"));
        assertThat(spotlightOption.isActive(), equalTo(true));
        assertThat(spotlightOption.getPrices(), equalTo(null));
        assertThat(metadata.getFeaturePrice(ProductType.SPOTLIGHT, ProductName.HOMEPAGE_SPOTLIGHT.name()), CoreMatchers.nullValue());
    }

    private void assertProductPrice(List<ProductPrice> prices, ProductName productName, ProductType type, BigDecimal price, Integer days) {

        ProductPrice spotlightPrice = getProductPrice(prices, productName);

        assertThat(spotlightPrice.getProductType(), equalTo(type));
        assertThat(spotlightPrice.getProductName(), equalTo(productName.name()));
        assertThat(spotlightPrice.getPrice(), equalTo(price));

        assertThat(spotlightPrice.getValue(), equalTo(productName.name()));

        if (days != null) {
            assertThat(spotlightPrice.getDisplayValue(), equalTo(days + " days - " + "&pound;" + price.toString()));
        } else {
            assertThat(spotlightPrice.getDisplayValue(), equalTo("&pound;" + price.toString()));
        }
    }

    private ProductPrice getProductPrice(List<ProductPrice> prices, ProductName productName) {
        return Iterables.find(prices, new ProductPriceMatcher(productName.name()));
    }

    private List<ApiProductPrice> createPriceTestData() {

        return Arrays.asList(
                createApiProductPrice(ProductName.INSERTION, 10L, 999L),
                createApiProductPrice(ProductName.BUMP_UP, 11L, 299L),
                createApiProductPrice(ProductName.URGENT, 1L, 499L),
                createApiProductPrice(ProductName.FEATURE_3_DAY, 2L, 299L),
                createApiProductPrice(ProductName.FEATURE_7_DAY, 3L, 499L),
                createApiProductPrice(ProductName.FEATURE_14_DAY, 4L, 999L),
                createApiProductPrice(ProductName.HOMEPAGE_SPOTLIGHT, 5L, 1499L)
        );
    }

    private ApiProductPrice createApiProductPrice(ProductName productName, Long id, Long incVat) {
        ApiProductPrice price = new ApiProductPrice();
        price.setProductName(productName);
        price.setId(id);
        price.setIncVat(incVat);
        return price;
    }

    private static class ProductPriceMatcher implements Predicate<ProductPrice> {

        private String productName;

        private ProductPriceMatcher(String productName) {
            this.productName = productName;
        }

        @Override
        public boolean apply(@Nullable ProductPrice input) {
            return productName.equals(input.getProductName());
        }
    }

}
