package com.gumtree.web.seller.converter;

import com.google.common.base.Optional;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.Attribute;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.domain.advert.DeleteReason;
import com.gumtree.common.format.PriceFormatter;
import com.gumtree.common.format.PriceFormatterImpl;
import com.gumtree.domain.newattribute.AttributeService;
import com.gumtree.domain.newattribute.DisplayAttribute;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.seller.model.AdPreview;
import org.hamcrest.CoreMatchers;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import org.joda.time.DateTimeZone;
import org.joda.time.format.ISODateTimeFormat;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.TimeZone;

import static junit.framework.Assert.assertEquals;
import static junit.framework.Assert.assertFalse;
import static junit.framework.Assert.assertTrue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


public class DefaultAdToAdPreviewConverterTest {

    private DefaultAdToAdPreviewConverter converter;

    private UrlScheme mockedUrlScheme;

    private LocationService mockedLocationService;

    private AttributeService mockedAttributeService;

    private CategoryService mockedCategoryService;

    private PriceFormatter priceFormatter;

    @Before
    public void init() throws Exception {

        mockedUrlScheme = mock(UrlScheme.class);
        mockedLocationService = mock(LocationService.class);
        mockedAttributeService = mock(AttributeService.class);
        mockedCategoryService = mock(CategoryService.class);
        converter = new DefaultAdToAdPreviewConverter();
        priceFormatter = new PriceFormatterImpl();


        when(mockedUrlScheme.urlFor(any(Ad.class))).thenReturn(null);
        when(mockedUrlScheme.urlToReportAd(anyLong())).thenReturn("report_ad_url");
        when(mockedAttributeService.getDisplayAttribute(any(com.gumtree.domain.newattribute.Attribute.class), any(Long.class)))
                .thenReturn(Optional.<DisplayAttribute>absent());
        when(mockedLocationService.getById(any(Integer.class))).thenReturn(null);
        when(mockedCategoryService.getById(any(Long.class))).thenReturn(null);
        when(mockedCategoryService.getById(1L)).thenReturn(Optional.of(aDomainCategory()));
        when(mockedCategoryService.getL1Category(anyLong())).thenReturn(Optional.<Category>absent());
        when(mockedCategoryService.getL1Category(1L)).thenReturn(Optional.of(aDomainCategory(2)));
        when(mockedCategoryService.getById(2L)).thenReturn(Optional.<Category>absent());

        ReflectionTestUtils.setField(converter, "urlScheme", mockedUrlScheme);
        ReflectionTestUtils.setField(converter, "locationService", mockedLocationService);
        ReflectionTestUtils.setField(converter, "attributeService", mockedAttributeService);
        ReflectionTestUtils.setField(converter, "categoryService", mockedCategoryService);
        ReflectionTestUtils.setField(converter, "priceFormatter", priceFormatter);
    }


    @Test
    public void testEmptyAd() {
        assertThat(1L, equalTo(1L));
    }

    @Test
    public void setsAdvertDescription() {
        Ad ad = makeAd();
        DateTime dateTime = new DateTime(1983, DateTimeConstants.MAY, 17, 13, 50,
                0, 0, DateTimeZone.forTimeZone(TimeZone.getTimeZone("BRT")));
        ad.setLiveDate(dateTime);
        ad.setDescription("Advert description");

        AdPreview adPreview = converter.convert(ad);
        assertThat(adPreview.getDescription(), CoreMatchers.equalTo("Advert description"));
        assertEquals(ISODateTimeFormat.dateTimeNoMillis().print(dateTime.toDate().getTime()),
                adPreview.getSeoTimeStamp());
    }

    @Test
    public void nullCreationDateConvertsAsNull() {
        Ad ad = makeAd();
        ad.setCreationDate(null);
        ad.setLastModifiedDate(null);
        ad.setId(99L);
        AdPreview adPreview = converter.convert(ad);
        assertThat(adPreview.getCreatedTime(), equalTo(null));
        assertThat(adPreview.getLastModifiedTime(), equalTo(null));
    }

    @Test
    public void creationAndLastModifiedDatesAreConverted() {
        Ad ad = makeAd();
        AdPreview adPreview = converter.convert(ad);
        assertThat(adPreview.getCreatedTime(), notNullValue());
        assertThat(adPreview.getCreatedTimeAsDate(), notNullValue());
        assertThat(adPreview.getLastModifiedTime(), notNullValue());
        assertThat(adPreview.getLastModifiedTimeAsDate(), notNullValue());
    }

    @Test
    public void shouldSetReportAdUrl() {
        // given
        Ad ad = makeAd();

        // when
        AdPreview adPreview = converter.convert(ad);

        // then
        assertThat(adPreview.getReportAdUrl(), is("report_ad_url"));
    }

    @Test
    public void readOnlyCategoryShouldBeUsedInAdPreview() {
        // given
        Category category = aDomainCategory(235);
        category.setReadOnly(true);
        category.setParentId(2L);
        when(mockedCategoryService.getById((235L))).thenReturn(Optional.of(category));
        Ad ad = makeAd(235);

        // when
        AdPreview adPreview = converter.convert(ad);

        // then
        assertTrue(adPreview.getCategoryReadOnly());
    }

    @Test
    public void readOnlyCategoryShouldBeFalseInAdPreview_ifCategoryIsNotReadOnly() {
        // given
        Category category = aDomainCategory(235);
        category.setReadOnly(false);
        category.setParentId(2L);
        when(mockedCategoryService.getById((235L))).thenReturn(Optional.of(category));
        Ad ad = makeAd(235);

        // when
        AdPreview adPreview = converter.convert(ad);

        // then
        assertFalse(adPreview.getCategoryReadOnly());
    }

    @Test
    public void shouldMarkAsSold() {
        // given
        // given
        Ad ad = makeDeleteAd();

        // when
        AdPreview adPreview = converter.convert(ad);

        // then
        assertTrue(adPreview.isMarkedAsSold());
    }

    @Test
    public void convertAdWithAttributesSuccessful() {
        // given
        Attribute[] attributes = {new Attribute("seller_type", "private"), new Attribute("Year", "2019")};
        Ad ad = makeAd(1L, attributes);

        // when
        AdPreview adPreview = converter.convert(ad);

        // then
        assertEquals("private", adPreview.getSellerType());
    }


    private Ad makeAd(Long catId, Attribute[] attributes) {
        Ad ad = new Ad();
        ad.setId(99l);
        ad.setCreationDate(new DateTime());
        ad.setLastModifiedDate(new DateTime());
        ad.setCategoryId(catId);
        ad.setAttributes(attributes);
        return ad;
    }

    private Ad makeAd() {
        return makeAd(1);
    }

    private Ad makeAd(int catId) {
        Ad ad = new Ad();
        ad.setId(99l);
        ad.setCreationDate(new DateTime());
        ad.setLastModifiedDate(new DateTime());
        ad.setCategoryId((long) catId);
        return ad;
    }

    private com.gumtree.api.category.domain.Category aDomainCategory(int id, int parentId) {
        com.gumtree.api.category.domain.Category entity = aDomainCategory(id);
        entity.setParentId((long) parentId);
        return entity;
    }

    private com.gumtree.api.category.domain.Category aDomainCategory() {
        return aDomainCategory(1, 2);
    }

    private com.gumtree.api.category.domain.Category aDomainCategory(int id) {
        com.gumtree.api.category.domain.Category cat = new com.gumtree.api.category.domain.Category();
        cat.setId((long) id);
        cat.setReadOnly(false);
        return cat;
    }

    private Ad makeDeleteAd() {
        Ad ad = new Ad();
        ad.setId(99L);
        ad.setDeleteDate(new DateTime());
        ad.setDeleteReason(DeleteReason.SOLD);
        ad.setStatus(AdStatus.DELETED_USER);
        return ad;
    }

}
