package com.gumtree.web.seller.page.ajax.category;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.mobile.test.Fixtures;
import com.gumtree.web.seller.service.category.CategorySuggesterService;
import com.gumtree.web.seller.service.jobs.JobsSuggesterService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.google.common.collect.Lists.newArrayList;
import static com.gumtree.mobile.test.Fixtures.createCarsCategory;
import static com.gumtree.util.HeadersExtensions.EXPERIMENTS_HEADER;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyListOf;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CategorySuggesterControllerTest {

    @InjectMocks
    private CategorySuggesterController controller;
    @Mock
    private CategorySuggesterService categorySuggesterService;
    @Mock
    private CategoryModel categoryModel;
    @Mock
    private JobsSuggesterService jobsSuggesterService;

    private HttpServletRequest request;

    private List<SuggestedCategory> inputList;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        categorySetUp();
        jobsSuggestionsSetup();
        request = mock(HttpServletRequest.class);
        inputList = new ArrayList<>();
    }

    @Test
    public void shouldReturnSuggestedCategories() {
        //given
        String searchTerm = "Car";
        when(categorySuggesterService.getSuggestedCategories(searchTerm)).thenReturn(getCategoriesList());

        //when
        SuggestedCategories suggestedCategories = controller.suggestCategory(searchTerm, request);

        //then
        assertThat(suggestedCategories.getCategories().size()).isEqualTo(5);
        assertThat(suggestedCategories.getCategories().get(0).getDisplayName()).isEqualToIgnoringCase("Cars");
    }

    @Test
    public void shouldReturnSuggestedCategoriesExperimentB() {
        //given
        String searchTerm = "Car";
        int top = 5;

        inputList.add(new SuggestedCategory(0L, "ExperimentB", "tree1"));
        inputList.add(new SuggestedCategory(0L, "Category2", "tree2"));
        inputList.add(new SuggestedCategory(0L, "Category3", "tree3"));

        when(request.getHeader(EXPERIMENTS_HEADER)).thenReturn("syi_cate_sug_flag.B");

        when(categorySuggesterService.getSuggestedCategoriesAllName(searchTerm,1)).thenReturn(inputList);
        when(categorySuggesterService.getSuggestedTextMatchCategories(searchTerm,true, top)).thenReturn(inputList);
        when(categorySuggesterService.getCategoryPredictApi(searchTerm,top)).thenReturn(inputList);


        when(categoryModel.getCategory(0L)).thenReturn(getCategory(0L, "Cars", "cars", false));
        //when
        SuggestedCategories suggestedCategories = controller.suggestCategory(searchTerm, request);

        //then
        assertThat(suggestedCategories.getCategories().size()).isEqualTo(1);
        assertThat(suggestedCategories.getCategories().get(0).getDisplayName()).isEqualToIgnoringCase("ExperimentB");
    }

    @Test
    public void shouldReturnSuggestedCategoriesExperimentC() {
        //given
        String searchTerm = "Car";
        int top = 5;

        inputList.add(new SuggestedCategory(0L, "ExperimentC", "tree1", new HashMap<>()));
        inputList.add(new SuggestedCategory(0L, "Category2", "tree2", new HashMap<>()));
        inputList.add(new SuggestedCategory(0L, "Category3", "tree3", new HashMap<>()));

        when(request.getHeader(EXPERIMENTS_HEADER)).thenReturn("syi_cate_sug_flag.C");

        when(categorySuggesterService.getSuggestedCategoriesAllName(searchTerm,1)).thenReturn(inputList);
        when(categorySuggesterService.getSuggestedTextMatchCategories(searchTerm,true, top)).thenReturn(inputList);
        when(categorySuggesterService.getCategoryPredictApi(searchTerm,top)).thenReturn(inputList);


        when(categoryModel.getCategory(0L)).thenReturn(getCategory(0L, "Cars", "cars", false));
        //when
        SuggestedCategories suggestedCategories = controller.suggestCategory(searchTerm, request);

        //then
        assertThat(suggestedCategories.getCategories().size()).isEqualTo(1);
        assertThat(suggestedCategories.getCategories().get(0).getDisplayName()).isEqualToIgnoringCase("ExperimentC");
    }

    @Test
    public void shouldReturnSuggestedCategoriesForSpecialCharacter() {
        //given
        String searchTerm = "Car/Truck";
        when(categorySuggesterService.getSuggestedCategories(searchTerm)).thenReturn(getCategoriesList());

        //when
        SuggestedCategories suggestedCategories = controller.suggestCategory(searchTerm, request);

        //then
        assertThat(suggestedCategories.getCategories().size()).isEqualTo(5);
        assertThat(suggestedCategories.getCategories().get(0).getDisplayName()).isEqualToIgnoringCase("Cars");
    }

    @Test
    public void shouldNotReturnSuggestedCategoriesIfKeywordNotPresent() {
        //given
        String searchTerm = "Spider";
        when(categorySuggesterService.getSuggestedCategories(searchTerm)).thenReturn(Collections.emptyList());

        //when
        SuggestedCategories suggestedCategories = controller.suggestCategory(searchTerm, request);

        //then
        assertThat(suggestedCategories.getCategories().size()).isEqualTo(0);
    }

    @Test
    public void shouldReturnSuggestedCategoriesWithJobs() {
        //given
        String searchTerm = "Car Jobs";
        List<SuggestedCategory> suggestedCategoryList = Collections.singletonList
                (new SuggestedCategory(89L, searchTerm, "Jobs"));
        when(categorySuggesterService.getSuggestedCategories(searchTerm)).thenReturn(getCategoriesList());
        when(jobsSuggesterService.getSuggestedJobCategories(anyString(), any())).thenReturn(suggestedCategoryList);

        when(categoryModel.getCategory(89L)).thenReturn(getCategory(89L, "Jobs", "jobs", false));

        //when
        SuggestedCategories suggestedCategories = controller.suggestCategory(searchTerm, request);

        //then
        assertThat(suggestedCategories.getCategories().size()).isEqualTo(5);
        assertThat(suggestedCategories.getCategories().get(0).getDisplayName()).isEqualToIgnoringCase("Cars");
        assertThat(suggestedCategories.getCategories().get(4).getTree()).isEqualToIgnoringCase("Jobs");
    }

    @Test
    public void shouldReturnSuggestedCategoriesWithOnlyJobs() {
        //given
        String searchTerm = "Bakery Jobs";
        List<SuggestedCategory> suggestedCategoryList = Collections.singletonList
                (new SuggestedCategory(89L, "Bakery Jobs", "Jobs"));

        when(categorySuggesterService.getSuggestedCategories("Bakery+Jobs")).thenReturn(Collections.emptyList());
        when(jobsSuggesterService.getSuggestedJobCategories(anyString(), any())).thenReturn(suggestedCategoryList);

        when(categoryModel.getCategory(89L)).thenReturn(getCategory(89L, "Jobs", "jobs", false));

        //when
        SuggestedCategories suggestedCategories = controller.suggestCategory(searchTerm, request);

        //then
        assertThat(suggestedCategories.getCategories().size()).isEqualTo(1);
        assertThat(suggestedCategories.getCategories().get(0).getDisplayName()).isEqualToIgnoringCase("Bakery Jobs");
        assertThat(suggestedCategories.getCategories().get(0).getTree()).isEqualToIgnoringCase("Jobs");
    }

    @Test
    public void shouldNotAllowPostingToNonLeafCategory() {
        // given
        Category category = Fixtures.createRootCategory()
                .withEnabled(true)
                .withReadOnly(false)
                .withHidden(false)
                .withChildren(newArrayList(createCarsCategory().withEnabled(true).withReadOnly(false).withHidden(false).build()))
                .build();

        // when
        boolean res = CategorySuggesterController.isAdPostingPermitted(category);

        // then
        assertThat(res).isFalse();
    }

    @Test
    public void shouldNotAllowPostingToDisabledCategory() {
        // given
        Category category = Fixtures.createRootCategory()
                .withEnabled(false)
                .withReadOnly(false)
                .withHidden(false)
                .build();

        // when
        boolean res = CategorySuggesterController.isAdPostingPermitted(category);

        // then
        assertThat(res).isFalse();
    }

    @Test
    public void shouldNotAllowPostingToReadonlyCategory() {
        // given
        Category category = Fixtures.createRootCategory()
                .withEnabled(true)
                .withReadOnly(true)
                .withHidden(false)
                .build();

        // when
        boolean res = CategorySuggesterController.isAdPostingPermitted(category);

        // then
        assertThat(res).isFalse();
    }

    @Test
    public void shouldNotAllowPostingToHiddenCategory() {
        // given
        Category category = Fixtures.createRootCategory()
                .withEnabled(true)
                .withReadOnly(false)
                .withHidden(true)
                .build();

        // when
        boolean res = CategorySuggesterController.isAdPostingPermitted(category);

        // then
        assertThat(res).isFalse();
    }

    @Test
    public void shouldAllowPostingToLeafCategory() {
        // given
        Category category = Fixtures.createRootCategory()
                .withEnabled(true)
                .withReadOnly(false)
                .withHidden(false)
                .withChildren(newArrayList())
                .build();

        // when
        boolean res = CategorySuggesterController.isAdPostingPermitted(category);

        // then
        assertThat(res).isTrue();
    }

    @Test
    public void shouldAllowPostingToNonLeafButWithHiddenChildrenCategory() {
        // given
        Category category = Fixtures.createRootCategory()
                .withEnabled(true)
                .withReadOnly(false)
                .withHidden(false)
                .withChildren(newArrayList(createCarsCategory().withEnabled(true).withReadOnly(false).withHidden(true).build()))
                .build();

        // when
        boolean res = CategorySuggesterController.isAdPostingPermitted(category);

        // then
        assertThat(res).isTrue();
    }

    @Test
    public void shouldAllowPostingToNonLeafButWithDisabledChildrenCategory() {
        // given
        Category category = Fixtures.createRootCategory()
                .withEnabled(true)
                .withReadOnly(false)
                .withHidden(false)
                .withChildren(newArrayList(createCarsCategory().withEnabled(false).withReadOnly(false).withHidden(true).build()))
                .build();

        // when
        boolean res = CategorySuggesterController.isAdPostingPermitted(category);

        // then
        assertThat(res).isTrue();
    }

    @Test
    public void shouldAllowPostingToNonLeafButWithReadonlyChildrenCategory() {
        // given
        Category category = Fixtures.createRootCategory()
                .withEnabled(true)
                .withReadOnly(false)
                .withHidden(false)
                .withChildren(newArrayList(createCarsCategory().withEnabled(true).withReadOnly(true).withHidden(true).build()))
                .build();

        // when
        boolean res = CategorySuggesterController.isAdPostingPermitted(category);

        // then
        assertThat(res).isTrue();
    }

    @Test
    public void shouldLogCategoryWithHierarchy() {
        String searchTerm = "Car";
        List<Long> suggestedCatIds = Collections.singletonList(9311L);

        when(categorySuggesterService.getSuggestedCategories(searchTerm)).thenReturn(suggestedCatIds);
        when(categoryModel.getCategory(9311L)).thenReturn(getCategory(9311L, "Cars", "cars", false));

        List<Category> hierarchyList = new ArrayList<>();
        Category root = new Category(1L, "Motors", "motors");
        Category cars = new Category(9311L, "Cars", "cars");
        hierarchyList.add(root);
        hierarchyList.add(cars);
        when(categoryModel.getFullPath(9311L)).thenReturn(hierarchyList);

        // when
        SuggestedCategories result = controller.suggestCategory(searchTerm, request);

        // then
        Map<String, String> crumbMap = result.getCategories().get(0).getCategoryCrumb();
        assertThat(crumbMap).containsKey("ad_category_id");
        assertThat(crumbMap).containsKey("ad_category");
        assertThat(crumbMap).containsKey("ad_subcategory1");
    }

    @Test
    public void shouldSkipLoggingWhenCategoryIsAbsent() {
        // given
        String searchTerm = "Unknown";
        List<Long> suggestedCatIds = Collections.singletonList(99999L);

        when(categorySuggesterService.getSuggestedCategories(searchTerm)).thenReturn(suggestedCatIds);
        when(categoryModel.getCategory(99999L)).thenReturn(Optional.absent());
        when(categoryModel.getFullPath(99999L)).thenReturn(Collections.emptyList());

        // when
        SuggestedCategories result = controller.suggestCategory(searchTerm, request);

        // then
        assertThat(result.getCategories()).isEmpty();
    }



    private List<Long> getCategoriesList() {
        return Arrays.asList(1L, 9311L, 10022L, 10301L, 120L, 94L, 2551L, 11983L);
    }

    private void categorySetUp() {
        when(categoryModel.getCategory(1L)).thenReturn(getCategory(1L, "All Categories", "all", true));
        when(categoryModel.getCategory(9311L)).thenReturn(getCategory(9311L, "Cars", "cars", false));
        when(categoryModel.getCategory(120L)).thenReturn(getCategory(120L, "Freebies", "free", false));
        when(categoryModel.getCategory(10022L)).thenReturn(getCategory(10022L, "Trucks", "trucks", false));
        when(categoryModel.getCategory(10301L)).thenReturn(getCategory(10301L, "Vans", "vans", false));
        when(categoryModel.getCategory(10302L)).thenReturn(getCategory(10302L, "Audi", "Audi", true));
        when(categoryModel.getCategory(10322L)).thenReturn(getCategory(10322L, "Vauxhall", "vauxhall", true));
        when(categoryModel.getCategory(94L)).thenReturn(getCategory(94L, "Wanted", "wanted", false));
        when(categoryModel.getCategory(11983L)).thenReturn(getCategory(11983L, "Other Vehicles", "vehicles", false));
        when(categoryModel.getCategory(2551L)).thenReturn(getCategory(2551L, "Motors", "motors", false));

        when(categoryModel.getBreadcrumb(9311L, ",")).thenReturn("Motors,Car");
        when(categoryModel.getBreadcrumb(120L, ",")).thenReturn("For Sale,Freebies");
        when(categoryModel.getBreadcrumb(10022L, ",")).thenReturn("Motors,Trucks");
        when(categoryModel.getBreadcrumb(10301L, ",")).thenReturn("Motors,Vans");
        when(categoryModel.getBreadcrumb(94L, ",")).thenReturn("Motors,Wanted");
        when(categoryModel.getBreadcrumb(11983L, ",")).thenReturn("Motors,Other Vehicles");
    }

    private void jobsSuggestionsSetup() {
        when(jobsSuggesterService.getSuggestedJobCategories(anyString(), any())).thenReturn(Collections.emptyList());
        when(jobsSuggesterService.getIndexPositionToInsert(anyListOf(SuggestedCategory.class), anyString())).thenReturn(OptionalInt.empty());
    }

    private Optional<Category> getCategory(Long catId, String name, String seoName, boolean hidden) {
        Category category = new Category(catId, name, seoName);
        category.setEnabled(true);
        category.setReadOnly(false);
        category.setHidden(hidden);
        return Optional.of(category);
    }

    @Test
    public void testDeduplicateByIdKeepFirst_withNullInput_shouldReturnEmptyList() {
        List<SuggestedCategory> result = controller.deduplicateByIdKeepFirst(null);
        assertThat(result).isEmpty();
    }

    @Test
    public void testDeduplicateByIdKeepFirst_withEmptyList_shouldReturnEmptyList() {
        List<SuggestedCategory> result = controller.deduplicateByIdKeepFirst(Collections.emptyList());
        assertThat(result).isEmpty();
    }

    @Test
    public void testDeduplicateByIdKeepFirst_withUniqueIds_shouldReturnSameList() {
        inputList.add(new SuggestedCategory(1L, "Category1", "tree1"));
        inputList.add(new SuggestedCategory(2L, "Category2", "tree2"));
        inputList.add(new SuggestedCategory(3L, "Category3", "tree3"));

        List<SuggestedCategory> result = CategorySuggesterController.deduplicateByIdKeepFirst(inputList);

        assertThat(result).hasSize(3);
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(2).getId()).isEqualTo(3L);
    }

    @Test
    public void testDeduplicateByIdKeepFirst_withDuplicateIds_shouldKeepFirstOccurrence() {
        inputList.add(new SuggestedCategory(1L, "Category1", "tree1"));
        inputList.add(new SuggestedCategory(2L, "Category2", "tree2"));
        inputList.add(new SuggestedCategory(1L, "Category1_duplicate", "tree1_dup")); // duplicate
        inputList.add(new SuggestedCategory(3L, "Category3", "tree3"));

        List<SuggestedCategory> result = CategorySuggesterController.deduplicateByIdKeepFirst(inputList);

        assertThat(result).hasSize(3);
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getDisplayName()).isEqualTo("Category1"); // 第一次出现的值
        assertThat(result.get(1).getId()).isEqualTo(2L);
        assertThat(result.get(2).getId()).isEqualTo(3L);
    }

    @Test
    public void testDeduplicateByIdKeepFirst_withAllSameId_shouldKeepOnlyFirst() {
        inputList.add(new SuggestedCategory(1L, "Category1", "tree1"));
        inputList.add(new SuggestedCategory(1L, "Category2", "tree2"));
        inputList.add(new SuggestedCategory(1L, "Category3", "tree3"));

        List<SuggestedCategory> result = CategorySuggesterController.deduplicateByIdKeepFirst(inputList);

        assertThat(result).hasSize(1);
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getDisplayName()).isEqualTo("Category1");
    }
}