package com.gumtree.web.seller.page.activation.controller;

import com.google.common.collect.Sets;
import com.gumtree.api.User;
import com.gumtree.api.client.executor.ApiCall;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.command.GetUserApiCall;
import com.gumtree.common.util.error.ReportableErrorsArguments;
import com.gumtree.web.common.page.util.RedirectViewWithRequiredModelKeys;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.activation.ActivationResendBean;
import com.gumtree.web.seller.page.activation.api.ResendActivationEmailApiCall;
import com.gumtree.web.seller.page.activation.model.ActivationFailureModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.registration.ConfirmationPageController;
import com.gumtree.web.zeno.userregistration.UserActivationFailureZenoEvent;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Matchers;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.ui.Model;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.gumtree.api.error.ApiErrorCode.ALREADY_ACTIVATED;
import static com.gumtree.api.error.ApiErrorCode.NOT_FOUND;
import static com.gumtree.web.seller.page.NoDependenciesController.ENCRYPTED_PARAMETER_MAP_NAME;
import static com.gumtree.web.seller.page.activation.controller.ActivationPageController.USER_ID_PARAM;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.hasItems;
import static org.hamcrest.CoreMatchers.instanceOf;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class ActivationFailureControllerTest extends BaseSellerControllerTest {
    private Validator validator;
    private ActivationResendBean bean;
    private ActivationFailureController controller;

    @Before
    public void setup() {

        bean = new ActivationResendBean();
        bean.setUsername("someUser");
        validator = mock(Validator.class);
        when(validator.validate(eq(bean))).thenReturn(Sets.<ConstraintViolation<ActivationResendBean>>newHashSet());
        controller = new ActivationFailureController(cookieResolver, categoryModel, apiCallExecutor, messageResolver,
                userSessionService, urlScheme, validator, zenoService, parameterEncryption);

        autowireAbExperimentsService(controller);
    }

    @Test
    public void testViewActivationFailedPage() {
        String email = "<EMAIL>";
        Map<String,String> data = new HashMap<>();
        data.put(USER_ID_PARAM, email);
        String encrypted = "encrypted";
        when(request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME)).thenReturn(encrypted);
        when(parameterEncryption.decryptUrlEncodedParameterMap(encrypted)).thenReturn(data);
        ModelAndView view = controller.viewActivationFailedPage(request);
        verify(zenoService, times(1)).logEvent(new UserActivationFailureZenoEvent(email));
        assertThat(view.getViewName(), equalTo(Page.ActivationFailure.getTemplateName()));
    }

    @Test
    public void testViewActivationFailedPageDoesntBlowIfNullUsername() {
        Model model = mock(Model.class);
        Map<String, Object> map = new HashMap<>();
        String email = null;
        map.put(ActivationPageController.USER_ID_PARAM, email);
        when(model.asMap()).thenReturn(map);
        ModelAndView view = controller.viewActivationFailedPage(request);
        verifyZeroInteractions(zenoService);
        assertThat(view.getViewName(), equalTo(Page.ActivationFailure.getTemplateName()));
    }

    @Test
    public void testViewActivationFailedPageDoesntBlowIfEmptyUsername() {
        when(request.getParameter(USER_ID_PARAM)).thenReturn("");
        ModelAndView view = controller.viewActivationFailedPage(request);
        assertThat(view.getViewName(), equalTo(Page.ActivationFailure.getTemplateName()));
    }

    @Test
    public void testViewActivationFailedPageDoesntBlowIfNoUsernameEntry() {
        Model model = mock(Model.class);
        when(model.asMap()).thenReturn(Collections.emptyMap());
        ModelAndView view = controller.viewActivationFailedPage(request);
        verifyZeroInteractions(zenoService);
        assertThat(view.getViewName(), equalTo(Page.ActivationFailure.getTemplateName()));
    }

    @Test
    public void testActivationFailedFormAction() {
        assertThat("/activation-failed", equalTo(controller.activationFailedFormAction()));
    }

    @Test
    public void testResendActivationFailsWhenEmailBlank() {
        ActivationResendBean bean = new ActivationResendBean();
        bean.setUsername("");
        Set<ConstraintViolation<ActivationResendBean>> errorSet = new HashSet<>();
        errorSet.add(mock(ConstraintViolation.class));
        when(validator.validate(eq(bean))).thenReturn(errorSet);
        ActivationFailureModel model = new ActivationFailureModel();
        model.setForm(bean);

        ModelAndView view = controller.resendActivation(model, request);

        verifyZeroInteractions(apiCallExecutor);
        assertThat(view.getViewName(), equalTo(Page.ActivationFailure.getTemplateName()));
    }

    @Test
    public void testResendActivationSuccess() {
        ApiCallResponse<Void> response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(false);
        when(apiCallExecutor.call(Matchers.<ApiCall>any())).thenReturn(response);
        ArgumentCaptor<ApiCall> captor = ArgumentCaptor.forClass(ApiCall.class);
        ActivationFailureModel model = new ActivationFailureModel();
        model.setForm(bean);

        View view = controller.resendActivation(model, request).getView();
        verify(apiCallExecutor, times(2)).call(captor.capture());
        ApiCall<Void> apiCall = captor.getAllValues().get(0);

        assertThat(apiCall, instanceOf(ResendActivationEmailApiCall.class));
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(ConfirmationPageController.PAGE_PATH));
    }

    @Test
    public void testResendActivationSuccessPopulatesFlashAttributes() {
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setId(12L);

        ApiCallResponse<User> response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(false);
        when(apiCallExecutor.call(Matchers.<ApiCall>any())).thenReturn(response);
        when(response.getResponseObject()).thenReturn(user);
        ActivationFailureModel model = new ActivationFailureModel();
        model.setForm(bean);

        ArgumentCaptor<ApiCall> captor = ArgumentCaptor.forClass(ApiCall.class);

        RedirectViewWithRequiredModelKeys view = (RedirectViewWithRequiredModelKeys)controller.resendActivation(model, request).getView();
        verify(apiCallExecutor, times(2)).call(captor.capture());
        ApiCall<User> apiCall = captor.getAllValues().get(1);

        assertThat(apiCall, instanceOf(GetUserApiCall.class));

        assertTrue(view.isRedirectView());
        assertThat(view.getUrl(), equalTo(ConfirmationPageController.PAGE_PATH));
        assertThat(view.getRequiredParameterKeys(), hasItems(ActivationFailureController.PARAMETER_NAME));
    }

    @Test
    public void testResendActivationFailsWhenBapiSaysInvalid() {
        ApiCallResponse<Void> response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(true);
        when(apiCallExecutor.call(Matchers.<ApiCall>any())).thenReturn(response);
        ArgumentCaptor<ApiCall> captor = ArgumentCaptor.forClass(ApiCall.class);
        ActivationFailureModel model = new ActivationFailureModel();
        model.setForm(bean);

        ModelAndView view = controller.resendActivation(model, request);
        verify(apiCallExecutor).call(captor.capture());
        ApiCall<Void> apiCall = captor.getValue();

        assertThat(apiCall, instanceOf(ResendActivationEmailApiCall.class));
        assertThat(view.getViewName(), equalTo(Page.ActivationFailure.getTemplateName()));
    }

    @Test
    public void modelContainsUnknownUserErrorWhenBapiSaysUnknownUser() {
        ApiCallResponse<Void> response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(true);
        when(response.containsMessageCode("activateUser.username.invalid")).thenReturn(true);

        when(apiCallExecutor.call(Matchers.<ApiCall>any())).thenReturn(response);
        ArgumentCaptor<ReportableErrorsArguments> argsCaptor = ArgumentCaptor.forClass(
                ReportableErrorsArguments.class);
        ActivationFailureModel model = new ActivationFailureModel();
        model.setForm(bean);

        ModelAndView view = controller.resendActivation(model, request);

        assertThat(view.getViewName(), equalTo(Page.ActivationFailure.getTemplateName()));
    }

    @Test
    public void shouldSendUserToLoginPageWhenAlreadyActivated() {
        ApiCallResponse<Void> response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(true);
        when(response.getErrorCode()).thenReturn(ALREADY_ACTIVATED);
        when(apiCallExecutor.call(Matchers.<ApiCall>any())).thenReturn(response);

        ArgumentCaptor<ApiCall> captor = ArgumentCaptor.forClass(ApiCall.class);
        ActivationFailureModel model = new ActivationFailureModel();
        model.setForm(bean);

        ModelAndView view1 = controller.resendActivation(model, request);

        verify(apiCallExecutor).call(captor.capture());
        ApiCall<Void> apiCall = captor.getValue();
        assertThat(apiCall, instanceOf(ResendActivationEmailApiCall.class));

        RedirectView view = (RedirectView) view1.getView();
        assertThat(view.getUrl(), equalTo("/login"));
    }

    @Test
    public void shouldSendUserToLoginPageWhenNotFound() {
        ApiCallResponse<Void> response = mock(ApiCallResponse.class);
        when(response.isErrorResponse()).thenReturn(true);
        when(response.getErrorCode()).thenReturn(NOT_FOUND);
        when(apiCallExecutor.call(Matchers.<ApiCall>any())).thenReturn(response);

        ArgumentCaptor<ApiCall> captor = ArgumentCaptor.forClass(ApiCall.class);
        ActivationFailureModel model = new ActivationFailureModel();
        model.setForm(bean);

        ModelAndView view1 = controller.resendActivation(model, request);

        verify(apiCallExecutor).call(captor.capture());
        ApiCall<Void> apiCall = captor.getValue();
        assertThat(apiCall, instanceOf(ResendActivationEmailApiCall.class));

        RedirectView view = (RedirectView) view1.getView();
        assertThat(view.getUrl(), equalTo("/login"));
    }
}
