package com.gumtree.web.seller.page.postad.controller.steps;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.executor.impl.DefaultApiCallResponse;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.common.util.error.ReportableResponse;
import com.gumtree.common.util.error.SimpleError;
import com.gumtree.common.util.time.Clock;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.abtest.Experiments;
import com.gumtree.web.abtest.ExperimentsProvider;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.metric.AdvertValidationMetric;
import com.gumtree.web.seller.page.postad.controller.SellerTypePanel;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.CategorySpecificPostAdFormPanels;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeGroup;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.seller.page.postad.model.PriceGuidance;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceContext;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.products.DefaultPricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.DefaultProductPrice;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService;
import com.gumtree.web.seller.service.postad.priceguidance.PriceGuidanceService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import com.gumtree.web.seller.service.pricing.PricingService;
import com.gumtree.web.service.ContactEmailService;
import com.gumtree.web.storage.ratelimit.RateLimiter;
import com.gumtree.zeno.core.service.ZenoService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.gumtree.mobile.test.Fixtures.STUB_CATEGORY_MODEL;
import static com.gumtree.web.seller.page.postad.controller.PostAdSubmitController.IS_PHONE_VERIFICATION_MANDATORY;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.Matchers.samePropertyValuesAs;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LastPostAdStepTest {

    @Mock
    ZenoService zenoService;

    @Mock
    PricingService pricingService;

    @Mock
    ErrorMessageResolver errorMessageResolver;

    @Mock
    UserSession authenticatedUserSession;

    @Mock
    PostAdFormDescriptionHintService descriptionHintService;

    @Mock
    AttributePresentationService attributePresentationService;

    @Mock
    PriceGuidanceService priceGuidanceService;

    @Mock
    private AdvertEditor advertEditor;

    @Mock
    ContactEmailService contactEmailService;

    @Mock
    RateLimiter rateLimiter;

    @Mock
    private LoginUtils loginUtils;

    @Mock
    private ExperimentsProvider experimentsProvider;

    @Mock
    private CustomMetricRegistry metrics;

    private static final Long CAR_CATEGORY_ID = 9311L;
    private static final Long DOG_CATEGORY_ID = 9389L;
    private static final Long CAT_CATEGORY_ID = 9390L;

    private static final String ATTR_SELLER_TYPE = "seller_type";
    private static final String SELLER_TYPE_PRIAVTE = "private";
    private static final String SELLER_TYPE_TRADE = "trade";

    private static final String ADDITIONAL_FEATURES_AVAILABLE = "additional_features_available";
    private static final String SPLIT_FOLDING_REAR_SEAT = "split_folding_rear_seat";
    private static final String BOOLEAN_YES = "Y";

    private static final Long BUMP_UP_PRICE = 1000L;
    private static final Long INSERTION_PRICE = 600L;

    @InjectMocks
    LastPostAdStep lastPostAdStep;

    @Mock
    CategoryService categoryService;

    @Before
    public void setup() {
        ReflectionTestUtils.setField(lastPostAdStep, "categoryService", categoryService);
        Map<String, String> emptyExperimentMap = new HashMap<>();
        Experiments experiments = new Experiments(emptyExperimentMap);
        when(experimentsProvider.get()).thenReturn(experiments);
        when(categoryService.getCategoryModel()).thenReturn(STUB_CATEGORY_MODEL);

        when(metrics.counter(eq(AdvertValidationMetric.ADVERT_VALIDATION.getName()), anyString(), eq(AdvertValidationMetric.Status.SUCCESS.name())))
                .thenReturn(new CustomMetricRegistry(new SimpleMeterRegistry()).counter(AdvertValidationMetric.ADVERT_VALIDATION.getName(), "result", AdvertValidationMetric.Status.SUCCESS.name()));
        when(metrics.counter(eq(AdvertValidationMetric.ADVERT_VALIDATION.getName()), anyString(), eq(AdvertValidationMetric.Status.FAILURE.name())))
                .thenReturn(new CustomMetricRegistry(new SimpleMeterRegistry()).counter(AdvertValidationMetric.ADVERT_VALIDATION.getName(), "result", AdvertValidationMetric.Status.FAILURE.name()));

    }

    private ArrayList<PostAdAttributeGroup> getAttributeGroup(){
        PostAdAttributeGroup genericGroup1 =
                PostAdAttributeGroup.builder()
                        .setId("g1")
                        .setLabel("Group 1")
                        .setPanelId(PostAdFormPanel.ATTRIBUTE_PANEL.getId())
                        .build();

        PostAdAttributeGroup genericGroup2 =
                PostAdAttributeGroup.builder()
                        .setId("g2")
                        .setLabel("Group 2")
                        .setPanelId(PostAdFormPanel.ATTRIBUTE_PANEL.getId())
                        .build();

        PostAdAttributeGroup brandsGroup =
                PostAdAttributeGroup.builder()
                        .setId("p1")
                        .setLabel("Brands")
                        .setPanelId(PostAdFormPanel.BRANDS.getId())
                        .build();

        return Lists.newArrayList(genericGroup1, brandsGroup, genericGroup2);
    }

    private ArrayList<PostAdAttributeGroup> getPetsAttributeGroup(){
        PostAdAttributeGroup doBgroup =
                PostAdAttributeGroup.builder()
                        .setId("g1")
                        .setLabel("DOB")
                        .setPanelId(PostAdFormPanel.PETS_BIRTHDAY.getId())
                        .build();
        PostAdAttributeGroup priceGroup =
                PostAdAttributeGroup.builder()
                        .setId("g2")
                        .setLabel("Price")
                        .setPanelId(PostAdFormPanel.PRICE.getId())
                        .build();
        PostAdAttributeGroup dogBreedGroup =
                PostAdAttributeGroup.builder()
                        .setId("g3")
                        .setLabel("Dog Breed")
                        .setPanelId(PostAdFormPanel.DOG_BREED.getId())
                        .build();

        PostAdAttributeGroup catBreedGroup =
                PostAdAttributeGroup.builder()
                        .setId("g4")
                        .setLabel("Cat Breed")
                        .setPanelId(PostAdFormPanel.CAT_BREED.getId())
                        .build();

        return Lists.newArrayList(doBgroup, priceGroup, dogBreedGroup, catBreedGroup);
    }

    private ArrayList<PostAdAttributeGroup> getCatsAttributeGroup(){
        PostAdAttributeGroup doBgroup =
                PostAdAttributeGroup.builder()
                        .setId("g1")
                        .setLabel("DOB")
                        .setPanelId(PostAdFormPanel.PETS_BIRTHDAY.getId())
                        .build();

        PostAdAttributeGroup priceGroup =
                PostAdAttributeGroup.builder()
                        .setId("g2")
                        .setLabel("Price")
                        .setPanelId(PostAdFormPanel.PRICE.getId())
                        .build();

        PostAdAttributeGroup catBreedGroup =
                PostAdAttributeGroup.builder()
                        .setId("g4")
                        .setLabel("Cat Breed")
                        .setPanelId(PostAdFormPanel.CAT_BREED.getId())
                        .build();

        PostAdAttributeGroup petHealthGroup =
                PostAdAttributeGroup.builder()
                        .setId("g5")
                        .setLabel("Pet Health and Documentation")
                        .setPanelId(PostAdFormPanel.PET_HEALTH.getId())
                        .build();

        return Lists.newArrayList(doBgroup, priceGroup, catBreedGroup);
    }



    @Test
    public void shouldProvideProperAttributesAndPanels() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();

        ArrayList<PostAdAttributeGroup> attributeGroups = getAttributeGroup();

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, attributeGroups, UserLoginStatus.EXISTING);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), contains(
                PostAdFormPanel.VEHICLE_SPECIFICATIONS.getId(),
                PostAdFormPanel.AD_TITLE.getId(),
                PostAdFormPanel.IMAGES.getId(),
                PostAdFormPanel.BRANDS.getId(),
                PostAdFormPanel.DESCRIPTION.getId(),
                PostAdFormPanel.ATTRIBUTE_PANEL.getId(),
                PostAdFormPanel.PETS_BIRTHDAY.getId(),
                PostAdFormPanel.WEBSITE_LINK.getId(),
                PostAdFormPanel.BUMP.getId(),
                PostAdFormPanel.CONTACT_DETAILS.getId(),
                PostAdFormPanel.OVERALL_PRICE.getId(),
                PostAdFormPanel.CONFIRMATION.getId()));

        //check if reference data backing attribute panel are correct
        assertFalse(model.getPanelAttributes().containsKey(PostAdFormPanel.ATTRIBUTE_PANEL.getId()));
        assertThat(model.getPostAdAttributeGroups().get(0), samePropertyValuesAs(attributeGroups.get(0)));
        assertThat(model.getPostAdAttributeGroups().get(1), samePropertyValuesAs(attributeGroups.get(2)));

        //check if reference data backing the other panels are correct
        assertTrue(model.getPanelAttributes().containsKey(PostAdFormPanel.BRANDS.getId()));
        assertThat(model.getPanelAttributes().get(PostAdFormPanel.BRANDS.getId()),
                samePropertyValuesAs(attributeGroups.get(1)));
    }

    @Test
    public void shouldAddInsertionAndBumpPanelIfInsertingAdvert() {

        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        when(advertEditor.isCreateMode()).thenReturn(true);

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.EXISTING);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels().get(0), equalTo(PostAdFormPanel.INSERTION.getId()));
        assertThat(model.getPanels(), hasItem(PostAdFormPanel.BUMP.getId()));
    }

    @Test
    public void shouldShowRegistrationPanelForUnregisteredUser() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setEmailAddress("<EMAIL>");

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.NEW_UNREGISTERED);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), hasItem(PostAdFormPanel.REGISTRATION.getId()));
    }

    @Test
    public void shouldStoreNewUsernameInSessionForUnregisteredUser() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setEmailAddress("<EMAIL>");
        postAdFormBean.setSubmitForm(Boolean.TRUE);
        ReportableResponse reportableResponse = mock(ReportableResponse.class);
        when(advertEditor.validate()).thenReturn(reportableResponse);
        when(reportableResponse.isErrorResponse()).thenReturn(false);

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.NEW_UNREGISTERED);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), hasItem(PostAdFormPanel.REGISTRATION.getId()));
        verify(loginUtils).storeNewUserEmailAddressInSession(postAdFormBean.getEmailAddress());
    }

    @Test
    public void shouldShowProContactsPanelForProUser() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        when(authenticatedUserSession.isProUser()).thenReturn(false);

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.EXISTING);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), not(hasItem(PostAdFormPanel.CONTACT_DETAILS_PRO.getId())));
        assertThat(model.getPanels(), not(hasItem(PostAdFormPanel.CONTACT_DETAILS_PRO.getId())));
    }

    @Test
    public void shouldRedirectToBumpUpPageIfSubmitted() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setSubmitForm(true);
        when(advertEditor.validate()).thenReturn(new DefaultApiCallResponse<>("OK"));

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.EXISTING);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getRedirectTo(), equalTo("/postad/bumpup"));
        assertMetricValue(1, AdvertValidationMetric.ADVERT_VALIDATION.getName(), AdvertValidationMetric.Status.SUCCESS);
    }

    /*
     * This test is to cover the scenario where the user has submitted the form and the phone verification is mandatory.
     */
    @Test
    public void shouldRedirectToBumpUpForPhoneVerifyPageIfSubmittedAndNeedPhoneVerification() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setSubmitForm(true);
        postAdFormBean.handleUnknownProperties(IS_PHONE_VERIFICATION_MANDATORY, true);
        when(advertEditor.validate()).thenReturn(new DefaultApiCallResponse<>("OK"));

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.EXISTING);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getRedirectTo(), equalTo("/postad/bumpup/phoneverification"));
        assertMetricValue(1, AdvertValidationMetric.ADVERT_VALIDATION.getName(), AdvertValidationMetric.Status.SUCCESS);
    }

    @Test
    public void shouldNotRedirectToBumpUpPageSubmittedWithErrors() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setSubmitForm(true);
        when(advertEditor.validate()).thenReturn(new DefaultApiCallResponse<>(ApiErrorCode.IMAGE_EMPTY, Lists.newArrayList(
                new SimpleError("image", "image", "image")
        )));

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.EXISTING);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getRedirectTo(), nullValue());
        assertMetricValue(1, AdvertValidationMetric.ADVERT_VALIDATION.getName(), AdvertValidationMetric.Status.FAILURE);
    }

    @Test
    public void shouldNotRedirectToBumpUpPageIfNotSubmitted() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setSubmitForm(false);

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.EXISTING);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getRedirectTo(), nullValue());
    }

    @Test
    public void shouldNotIncrementRateLimiterWhenShowingForm() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setSubmitForm(false);

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.EXISTING);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        verifyZeroInteractions(rateLimiter);
    }

    @Test
    public void shouldIncrementRateLimiterWhenSubmittingForm() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setSubmitForm(true);
        when(advertEditor.validate()).thenReturn(new DefaultApiCallResponse<>("OK"));

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.EXISTING);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        verify(rateLimiter).incRateCounter(any());
        verifyNoMoreInteractions(rateLimiter);
        assertMetricValue(1, AdvertValidationMetric.ADVERT_VALIDATION.getName(), AdvertValidationMetric.Status.SUCCESS);
    }

    @Test
    public void shouldAddPriceGuidanceIfVrnIsPresent() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setEmailAddress("<EMAIL>");
        postAdFormBean.setCategoryId(CAR_CATEGORY_ID);

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.NEW_UNREGISTERED);
        PriceGuidance priceGuidance = givenVrnDataPresentWithPriceGuidance(postAdFormBean);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPriceGuidance(), equalTo(priceGuidance));
    }

    @Test
    public void shouldNotAddPriceGuidanceIfVrnIsEmpty() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setEmailAddress("<EMAIL>");

        givenVrnDataIsEmpty(postAdFormBean);
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.NEW_UNREGISTERED);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPriceGuidance(), equalTo(null));
    }

    @Test
    public void shouldReturnBumpUpPriceForRepostExpiredAds() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setEmailAddress("<EMAIL>");

        givenVrnDataIsEmpty(postAdFormBean);
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.NEW_UNREGISTERED);
        when(advertEditor.showInsertionPrice()).thenReturn(false);
        when(advertEditor.requiresBumpUp()).thenReturn(true);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertEquals(10, model.getTotalPrice().longValue());
    }

    @Test
    public void shouldReturnInsertionPrice() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setEmailAddress("<EMAIL>");

        givenVrnDataIsEmpty(postAdFormBean);
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.NEW_UNREGISTERED);
        when(advertEditor.showInsertionPrice()).thenReturn(true);
        when(advertEditor.requiresBumpUp()).thenReturn(false);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertEquals(6, model.getTotalPrice().longValue());
    }

    @Test
    public void shouldReturnFreePriceOnRelistForFree() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setEmailAddress("<EMAIL>");

        givenVrnDataIsEmpty(postAdFormBean);
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.NEW_UNREGISTERED);
        when(advertEditor.showInsertionPrice()).thenReturn(true);
        when(advertEditor.requiresBumpUp()).thenReturn(false);
        when(advertEditor.isRelistForFree()).thenReturn(true);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertEquals(0, model.getTotalPrice().longValue());
    }

    @Test
    public void shouldReturnInsertionPricePlusFeaturesPrice() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        postAdFormBean.setEmailAddress("<EMAIL>");
        FeatureBean urgentFeature = new FeatureBean();
        urgentFeature.setSelected(true);
        urgentFeature.setProductName(ProductType.URGENT.name());

        Map<ProductType, FeatureBean> featureBeans = Maps.newHashMap();
        featureBeans.put(ProductType.URGENT, urgentFeature);
        postAdFormBean.setFeatures(featureBeans);

        givenVrnDataIsEmpty(postAdFormBean);

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, Lists.newArrayList(), UserLoginStatus.NEW_UNREGISTERED);

        ProductPrice defaultPrice = new DefaultProductPrice(ProductType.URGENT, ProductType.URGENT.name(), 30, 2000L);

        when(pricingService.getPriceInformation(any())).thenReturn(
                new DefaultPricingMetadata.Builder(new StoppedClock()).
                        withInsertionPrice(
                                new DefaultProductPrice(ProductType.INSERTION, "Insertion", INSERTION_PRICE)
                        ).withBumpUpPrice(new DefaultProductPrice(ProductType.BUMP_UP, "Bump up", BUMP_UP_PRICE))
                        .withNonActiveFeature(ProductType.URGENT, Arrays.asList(defaultPrice))
                        .build()
        );

        when(advertEditor.showInsertionPrice()).thenReturn(true);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertEquals(26, model.getTotalPrice().longValue());
    }

    @Test
    public void shouldShowTheRightPanelsForProUser() {

        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();

        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, getAttributeGroup(), UserLoginStatus.EXISTING);

        when(authenticatedUserSession.isProUser()).thenReturn(true);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), contains(
                PostAdFormPanel.VEHICLE_SPECIFICATIONS.getId(),
                PostAdFormPanel.AD_TITLE.getId(),
                PostAdFormPanel.IMAGES.getId(),
                PostAdFormPanel.BRANDS.getId(),
                PostAdFormPanel.DESCRIPTION.getId(),
                PostAdFormPanel.ATTRIBUTE_PANEL.getId(),
                PostAdFormPanel.PETS_BIRTHDAY.getId(),
                PostAdFormPanel.WEBSITE_LINK.getId(),
                PostAdFormPanel.BUMP.getId(),
                PostAdFormPanel.CONTACT_DETAILS_PRO.getId(),
                PostAdFormPanel.OVERALL_PRICE.getId(),
                PostAdFormPanel.CONFIRMATION.getId()));
    }

    /*
        ======================================
        Additional car features - posting new advert
        ======================================
     */

    @Test
    public void shouldReturnPanelWithoutAdditionalFeaturesWhenNotNewEditor() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, getAttributeGroup(), UserLoginStatus.EXISTING);

        // and
        setupPresentationServiceForCarsSpecificAttributePannles();

        // and
        when(advertEditor.getCategoryId()).thenReturn(CAR_CATEGORY_ID);

        // and
        when(advertEditor.isCreateMode()).thenReturn(false);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), contains(
                PostAdFormPanel.VEHICLE_SPECIFICATIONS.getId(),
                PostAdFormPanel.AD_TITLE.getId(),
                PostAdFormPanel.IMAGES.getId(),
                PostAdFormPanel.BRANDS.getId(),
                PostAdFormPanel.DESCRIPTION.getId(),
                PostAdFormPanel.PRICE.getId(),
                PostAdFormPanel.WEBSITE_LINK.getId(),
                PostAdFormPanel.BUMP.getId(),
                PostAdFormPanel.CONTACT_DETAILS.getId(),
                PostAdFormPanel.OVERALL_PRICE.getId(),
                PostAdFormPanel.CONFIRMATION.getId()));
    }

    /*
        ======================================
        Additional car features - modyfing exiting advert with additional features set
        ======================================
     */

    @Test
    public void shouldReturnPanelWithAdditionalFeaturesWhenEditorInDraftAndHasAdditionalFeatures() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, getAttributeGroup(), UserLoginStatus.EXISTING);

        // and
        setupPresentationServiceForCarsSpecificAttributePannles();

        // and
        when(advertEditor.getCategoryId()).thenReturn(CAR_CATEGORY_ID);

        // and
        when(advertEditor.isCreateMode()).thenReturn(false);
        when(advertEditor.isDraftMode()).thenReturn(true);
        when(advertEditor.isEditMode()).thenReturn(false);

        // and
        postAdFormBean.addAttribute(ADDITIONAL_FEATURES_AVAILABLE, BOOLEAN_YES);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), contains(
                PostAdFormPanel.VEHICLE_SPECIFICATIONS.getId(),
                PostAdFormPanel.AD_TITLE.getId(),
                PostAdFormPanel.IMAGES.getId(),
                PostAdFormPanel.ADDITIONAL_FEATURES.getId(),
                PostAdFormPanel.BRANDS.getId(),
                PostAdFormPanel.DESCRIPTION.getId(),
                PostAdFormPanel.PRICE.getId(),
                PostAdFormPanel.WEBSITE_LINK.getId(),
                PostAdFormPanel.BUMP.getId(),
                PostAdFormPanel.CONTACT_DETAILS.getId(),
                PostAdFormPanel.OVERALL_PRICE.getId(),
                PostAdFormPanel.CONFIRMATION.getId()));
    }

    @Test
    public void shouldReturnPanelWithAdditionalFeaturesWhenEditorInEditModeAndHasAdditionalFeaturesAvailableSetButNoOtherBoolAttribute() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, getAttributeGroup(), UserLoginStatus.EXISTING);

        // and
        setupPresentationServiceForCarsSpecificAttributePannles();

        // and
        when(advertEditor.getCategoryId()).thenReturn(CAR_CATEGORY_ID);

        // and
        when(advertEditor.isCreateMode()).thenReturn(false);
        when(advertEditor.isDraftMode()).thenReturn(false);
        when(advertEditor.isEditMode()).thenReturn(true);

        // and
        postAdFormBean.addAttribute(ADDITIONAL_FEATURES_AVAILABLE, BOOLEAN_YES);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), contains(
                PostAdFormPanel.VEHICLE_SPECIFICATIONS.getId(),
                PostAdFormPanel.AD_TITLE.getId(),
                PostAdFormPanel.IMAGES.getId(),
                PostAdFormPanel.ADDITIONAL_FEATURES.getId(),
                PostAdFormPanel.BRANDS.getId(),
                PostAdFormPanel.DESCRIPTION.getId(),
                PostAdFormPanel.PRICE.getId(),
                PostAdFormPanel.WEBSITE_LINK.getId(),
                PostAdFormPanel.BUMP.getId(),
                PostAdFormPanel.CONTACT_DETAILS.getId(),
                PostAdFormPanel.OVERALL_PRICE.getId(),
                PostAdFormPanel.CONFIRMATION.getId()));
    }

    @Test
    public void shouldReturnPanelWithAdditionalFeaturesWhenEditorAnyBooleanAttributeIsSet() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, getAttributeGroup(), UserLoginStatus.EXISTING);

        // and
        setupPresentationServiceForCarsSpecificAttributePannles();

        // and
        when(advertEditor.getCategoryId()).thenReturn(CAR_CATEGORY_ID);

        // and
        when(advertEditor.isCreateMode()).thenReturn(false);
        when(advertEditor.isDraftMode()).thenReturn(true);
        when(advertEditor.isEditMode()).thenReturn(false);

        // and
        postAdFormBean.addAttribute(SPLIT_FOLDING_REAR_SEAT, BOOLEAN_YES);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), contains(
                PostAdFormPanel.VEHICLE_SPECIFICATIONS.getId(),
                PostAdFormPanel.AD_TITLE.getId(),
                PostAdFormPanel.IMAGES.getId(),
                PostAdFormPanel.ADDITIONAL_FEATURES.getId(),
                PostAdFormPanel.BRANDS.getId(),
                PostAdFormPanel.DESCRIPTION.getId(),
                PostAdFormPanel.PRICE.getId(),
                PostAdFormPanel.WEBSITE_LINK.getId(),
                PostAdFormPanel.BUMP.getId(),
                PostAdFormPanel.CONTACT_DETAILS.getId(),
                PostAdFormPanel.OVERALL_PRICE.getId(),
                PostAdFormPanel.CONFIRMATION.getId()));
    }

    @Test
    public void shouldReturnPanelWithDogBreedsWhenEditor() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, getPetsAttributeGroup(), UserLoginStatus.EXISTING);

        // and
        setupPresentationServiceForDogsSpecificAttributePanels();

        // and
        when(advertEditor.getCategoryId()).thenReturn(DOG_CATEGORY_ID);

        // and
        when(advertEditor.isCreateMode()).thenReturn(true);
        when(advertEditor.isDraftMode()).thenReturn(false);
        when(advertEditor.isEditMode()).thenReturn(false);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), contains(
                PostAdFormPanel.INSERTION.getId(),
                PostAdFormPanel.AD_TITLE.getId(),
                PostAdFormPanel.IMAGES.getId(),
                PostAdFormPanel.DESCRIPTION.getId(),
                PostAdFormPanel.DOG_BREED.getId(),
                PostAdFormPanel.PETS_BIRTHDAY.getId(),
                PostAdFormPanel.PRICE.getId(),
                PostAdFormPanel.PET_HEALTH.getId(),
                PostAdFormPanel.WEBSITE_LINK.getId(),
                PostAdFormPanel.BUMP.getId(),
                PostAdFormPanel.CONTACT_DETAILS.getId(),
                PostAdFormPanel.OVERALL_PRICE.getId(),
                PostAdFormPanel.CONFIRMATION.getId()));
    }

    @Test
    public void shouldReturnPanelWithCatBreedsWhenEditor() {
        //given
        PostAdFormBean postAdFormBean = new PostAdFormBean();
        PostAdSubmitModel.Builder builder = setupMocks(postAdFormBean, getCatsAttributeGroup(), UserLoginStatus.EXISTING);

        // and
        setupPresentationServiceForCatsSpecificAttributePanels();

        // and
        when(advertEditor.getCategoryId()).thenReturn(CAT_CATEGORY_ID);

        // and
        when(advertEditor.isCreateMode()).thenReturn(true);
        when(advertEditor.isDraftMode()).thenReturn(false);
        when(advertEditor.isEditMode()).thenReturn(false);

        //when
        lastPostAdStep.execute(builder, advertEditor);

        //then
        PostAdSubmitModel model = builder.build();
        assertThat(model.getPanels(), contains(
                PostAdFormPanel.INSERTION.getId(),
                PostAdFormPanel.AD_TITLE.getId(),
                PostAdFormPanel.IMAGES.getId(),
                PostAdFormPanel.DESCRIPTION.getId(),
                PostAdFormPanel.CAT_BREED.getId(),
                PostAdFormPanel.PETS_BIRTHDAY.getId(),
                PostAdFormPanel.PRICE.getId(),
                PostAdFormPanel.PET_HEALTH.getId(),
                PostAdFormPanel.WEBSITE_LINK.getId(),
                PostAdFormPanel.BUMP.getId(),
                PostAdFormPanel.CONTACT_DETAILS.getId(),
                PostAdFormPanel.OVERALL_PRICE.getId(),
                PostAdFormPanel.CONFIRMATION.getId()));
    }

    /*
        utils
     */

    private void setupPresentationServiceForCarsSpecificAttributePannles() {
        List<PostAdFormPanel> highPriorityCategorySpecificPanels = Lists.newArrayList(PostAdFormPanel.VEHICLE_SPECIFICATIONS);
        List<PostAdFormPanel> lowPriorityCategorySpecificPanels = Lists.newArrayList(PostAdFormPanel.PRICE);
        when(attributePresentationService.loadPrioritisedCategorySpecificFormPanels(Mockito.anyLong(), Mockito.any())).
                thenReturn(new CategorySpecificPostAdFormPanels(highPriorityCategorySpecificPanels, lowPriorityCategorySpecificPanels));
    }

    private void setupPresentationServiceForDogsSpecificAttributePanels() {
        List<PostAdFormPanel> highPriorityCategorySpecificPanels = Lists.newArrayList();
        List<PostAdFormPanel> lowPriorityCategorySpecificPanels = Lists.newArrayList(PostAdFormPanel.DOG_BREED, PostAdFormPanel.PETS_BIRTHDAY, PostAdFormPanel.PRICE, PostAdFormPanel.PET_HEALTH);
        when(attributePresentationService.loadPrioritisedCategorySpecificFormPanels(Mockito.anyLong(), Mockito.any())).
                thenReturn(new CategorySpecificPostAdFormPanels(highPriorityCategorySpecificPanels, lowPriorityCategorySpecificPanels));
    }

    private void setupPresentationServiceForCatsSpecificAttributePanels() {
        List<PostAdFormPanel> highPriorityCategorySpecificPanels = Lists.newArrayList();
        List<PostAdFormPanel> lowPriorityCategorySpecificPanels = Lists.newArrayList(PostAdFormPanel.CAT_BREED, PostAdFormPanel.PETS_BIRTHDAY, PostAdFormPanel.PRICE, PostAdFormPanel.PET_HEALTH);
        when(attributePresentationService.loadPrioritisedCategorySpecificFormPanels(Mockito.anyLong(), Mockito.any())).
                thenReturn(new CategorySpecificPostAdFormPanels(highPriorityCategorySpecificPanels, lowPriorityCategorySpecificPanels));
    }

    private PriceGuidance givenVrnDataPresentWithPriceGuidance(PostAdFormBean postAdFormBean) {
        HashMap<String, String> attributes = new HashMap<>();
        String vrn = "W90MOG";

        when(advertEditor.getCategoryId()).thenReturn(CAR_CATEGORY_ID);
        attributes.put(CategoryConstants.Attribute.VRN.getName(), vrn);
        postAdFormBean.setAttributes(attributes);
        PriceGuidance priceGuidance = new PriceGuidance(54L, Collections.emptyList());
        PriceGuidanceContext priceGuidanceContext = new PriceGuidanceContext(priceGuidance, Collections.emptyMap());
        when(priceGuidanceService.getForCarAd(vrn, CAR_CATEGORY_ID)).thenReturn(Optional.of(priceGuidanceContext));

        return priceGuidance;
    }

    private void givenVrnDataIsEmpty(PostAdFormBean postAdFormBean) {
        HashMap<String, String> attributes = new HashMap<>();
        String vrn = "";
        attributes.put(CategoryConstants.Attribute.VRN.getName(), vrn);
        postAdFormBean.setAttributes(attributes);
    }

    private PostAdSubmitModel.Builder setupMocks(PostAdFormBean postAdFormBean,
                                                 ArrayList<PostAdAttributeGroup> attributeGroups,
                                                 UserLoginStatus userType) {
        Clock clock = new StoppedClock();
        when(advertEditor.getCategoryId()).thenReturn(CategoryConstants.ALL_ID);
        when(advertEditor.getPostAdFormBean()).thenReturn(postAdFormBean);
        when(advertEditor.isEditMode()).thenReturn(true);
        when(advertEditor.isDraftMode()).thenReturn(true);
        when(advertEditor.isUserLoggedIn()).thenReturn(true);


        when(pricingService.getPriceInformation(any())).thenReturn(
                new DefaultPricingMetadata.Builder(clock).
                        withInsertionPrice(
                                new DefaultProductPrice(ProductType.INSERTION, "Insertion", INSERTION_PRICE)
                        ).withBumpUpPrice(new DefaultProductPrice(ProductType.BUMP_UP, "Bump up", BUMP_UP_PRICE))
                        .build()
        );

        when(authenticatedUserSession.getUserType()).thenReturn(userType);


        when(attributePresentationService.loadAttributeGroups(Mockito.anyLong(), Mockito.any())).thenReturn(attributeGroups);

        List<PostAdFormPanel> highPriorityCategorySpecificPanels = Lists.newArrayList(PostAdFormPanel.VEHICLE_SPECIFICATIONS);
        List<PostAdFormPanel> lowPriorityCategorySpecificPanels =
                Lists.newArrayList(PostAdFormPanel.ATTRIBUTE_PANEL, PostAdFormPanel.PETS_BIRTHDAY);
        when(attributePresentationService.loadPrioritisedCategorySpecificFormPanels(Mockito.anyLong(), Mockito.any())).
                thenReturn(new CategorySpecificPostAdFormPanels(highPriorityCategorySpecificPanels, lowPriorityCategorySpecificPanels));

        when(descriptionHintService.getHint(any(), any(), any())).
                thenAnswer(call -> StringUtils.join(call.getArguments(), ";"));

        when(contactEmailService.getForPosting(Mockito.any())).thenReturn(Lists.newArrayList("<EMAIL>"));

        SellerTypePanel sellerPanel = mock(SellerTypePanel.class);
        when(sellerPanel.isSellerTypeSelected()).thenReturn(true);

        return new PostAdSubmitModel.Builder()
                .withForm(postAdFormBean)
                .withSellerType(sellerPanel);
    }

    private void assertMetricValue(int expectedValue, String name, AdvertValidationMetric.Status status) {
        verify(metrics).counter(name, "result", status.name());
        Counter counter = metrics.counter(AdvertValidationMetric.ADVERT_VALIDATION.getName(), "result", status.name());
        Assert.assertEquals(expectedValue, counter.count(), 0.1D);
    }
}