package com.gumtree.web.seller.page.manageads.mydetails;

import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.seller.domain.deactivation.entity.DeactivationReason;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.model.MyDetailsModel;
import com.gumtree.web.seller.page.reviews.model.UserRating;
import com.gumtree.web.seller.page.reviews.service.ExternalReviewsService;
import com.gumtree.web.seller.page.reviews.service.TriConsumer;
import com.gumtree.web.seller.page.reviews.service.UserReviewsService;
import com.gumtree.web.service.ContactEmailService;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;
import rx.Single;

import java.util.Optional;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.BDDMockito.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.never;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.times;
import static org.mockito.BDDMockito.verify;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MyDetailsControllerTest extends BaseSellerControllerTest {

    private static final Long TEST_USER_ID = 12345L;
    private static final Long TEST_ACCOUNT_ID = 1099L;

    @Mock
    private ManageAdsHelper manageAdsHelper;

    @Mock
    private UserSession userSession;

    @Mock
    private ContactEmailService contactEmailService;

    @Mock
    private UserReviewsService userReviewsService;

    @Mock
    private UrlScheme urlScheme;

    @Mock
    private UserServiceFacade userServiceFacade;

    @Mock
    private ExternalReviewsService externalReviewsService;

    @InjectMocks
    private MyDetailsController myDetailsController;

    @Spy
    private CustomMetricRegistry customMetricRegistry = new CustomMetricRegistry(new SimpleMeterRegistry());

    @Before
    public void setup() {
        User user = new User();
        user.setId(TEST_USER_ID);
        when(manageAdsHelper.getSessionUser(any(UserSession.class))).thenReturn(user);
        autowireAbExperimentsService(myDetailsController);
    }

    @Test
    public void showPageWithUserRating() {
        // given
        Account userAccount = new Account();
        userAccount.setPublicId("pub123");
        UserRating expectedUserRating = new UserRating();
        given(userSession.getSelectedAccountId()).willReturn(TEST_ACCOUNT_ID);
        given(userReviewsService.getUserRating(TEST_ACCOUNT_ID)).willReturn(Single.just(Optional.of(expectedUserRating)));
        given(manageAdsHelper.getSelectedAccount(any(UserSession.class))).willReturn(userAccount);

        // when
        ModelAndView mav = myDetailsController.showPage(request, "");

        // then
        then(userReviewsService).should().getUserRating(TEST_ACCOUNT_ID);
        Object myDetailsModelObj = mav.getModel().get(CommonModel.MODEL_KEY);
        assertThat(myDetailsModelObj).isInstanceOf(MyDetailsModel.class);
        UserRating modelUserRating = ((MyDetailsModel) myDetailsModelObj).getUserRating();
        assertThat(modelUserRating).isNotNull();
        assertThat(modelUserRating).isEqualTo(expectedUserRating);

        // and
        then(manageAdsHelper).should().getSelectedAccount(any(UserSession.class));
        assertThat(mav.getModel().get("account")).isEqualTo(userAccount);
    }

    @Test
    public void showPageWithoutUserRating() {
        // given
        given(userSession.getSelectedAccountId()).willReturn(TEST_ACCOUNT_ID);
        given(userReviewsService.getUserRating(TEST_ACCOUNT_ID)).willReturn(Single.just(Optional.empty()));

        // when
        ModelAndView mav = myDetailsController.showPage(request, "");

        // then
        then(userReviewsService).should().getUserRating(TEST_ACCOUNT_ID);
        Object myDetailsModelObj = mav.getModel().get(CommonModel.MODEL_KEY);
        assertThat(myDetailsModelObj).isInstanceOf(MyDetailsModel.class);
        UserRating modelUserRating = ((MyDetailsModel) myDetailsModelObj).getUserRating();
        assertThat(modelUserRating).isNotNull();
        assertThat(modelUserRating).isEqualTo(new UserRating());

        // and
        then(manageAdsHelper).should(never()).getSelectedAccount(any(UserSession.class));
        assertThat(mav.getModel().get("account")).isNull();
    }

    @Test
    public void showPageWithNullSelectedAccount() {
        given(userSession.getSelectedAccountId()).willReturn(null);

        ModelAndView mav = myDetailsController.showPage(request, "");

        then(userReviewsService).should(never()).getUserRating(TEST_ACCOUNT_ID);
        then(manageAdsHelper).should(never()).getSelectedAccount(any(UserSession.class));
        Object myDetailsModelObj = mav.getModel().get(CommonModel.MODEL_KEY);
        assertThat(myDetailsModelObj).isInstanceOf(MyDetailsModel.class);
        UserRating userRating = ((MyDetailsModel) myDetailsModelObj).getUserRating();
        assertThat(userRating).isNull();
    }

    @Test
    public void shouldDeactivateAccount() {
        // Given
        DeactivateAccountBean bean = new DeactivateAccountBean();
        bean.setDeactivationReason(DeactivationReason.NOT_SAY.getValue());
        given(userServiceFacade.initiateAccountDeactivation(TEST_USER_ID, DeactivationReason.NOT_SAY)).willReturn(ApiResponse.of(true));
        given(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ACCOUNT)).willReturn("/manage-account/");

        //When
        ModelAndView mav = myDetailsController.initiateDeactivateAccount(bean, null, request);

        // Then
        verify(userServiceFacade, times(1)).initiateAccountDeactivation(eq(TEST_USER_ID), eq(DeactivationReason.NOT_SAY));
    }

    @Test
    public void showPageWithGoogleReviewDetailsIncludingIsBindGbp() {
        // given
        Account userAccount = new Account();
        userAccount.setPublicId("pub123");
        UserRating expectedUserRating = new UserRating();
        given(userSession.getSelectedAccountId()).willReturn(TEST_ACCOUNT_ID);
        given(userReviewsService.getUserRating(TEST_ACCOUNT_ID)).willReturn(Single.just(Optional.of(expectedUserRating)));
        given(manageAdsHelper.getSelectedAccount(any(UserSession.class))).willReturn(userAccount);

        // when
        ModelAndView mav = myDetailsController.showPage(request, "");

        // then
        then(externalReviewsService).should().getGoogleReviewMyDetailFlow(eq(TEST_ACCOUNT_ID), any(TriConsumer.class));
        
        Object myDetailsModelObj = mav.getModel().get(CommonModel.MODEL_KEY);
        assertThat(myDetailsModelObj).isInstanceOf(MyDetailsModel.class);
        MyDetailsModel model = (MyDetailsModel) myDetailsModelObj;
        
        // Note: Since we're mocking externalReviewsService and not actually calling the TriConsumer,
        // the values will be null unless we simulate the callback
    }

    @Test
    public void showPageSetsGoogleReviewPropertiesViaTriConsumer() {
        // given
        Account userAccount = new Account();
        userAccount.setPublicId("pub123");
        UserRating expectedUserRating = new UserRating();
        given(userSession.getSelectedAccountId()).willReturn(TEST_ACCOUNT_ID);
        given(userReviewsService.getUserRating(TEST_ACCOUNT_ID)).willReturn(Single.just(Optional.of(expectedUserRating)));
        given(manageAdsHelper.getSelectedAccount(any(UserSession.class))).willReturn(userAccount);

        // Configure externalReviewsService to call the TriConsumer with test values
        Boolean expectedGoogleReviewBar = true;
        Boolean expectedShowGoogleReviewItem = false;
        Boolean expectedIsBindGbp = true;
        
        // Use doAnswer to capture and call the TriConsumer
        org.mockito.Mockito.doAnswer(invocation -> {
            Long accountId = invocation.getArgumentAt(0, Long.class);
            TriConsumer<Boolean, Boolean, Boolean> triConsumer = invocation.getArgumentAt(1, TriConsumer.class);
            triConsumer.accept(expectedGoogleReviewBar, expectedShowGoogleReviewItem, expectedIsBindGbp);
            return null;
        }).when(externalReviewsService).getGoogleReviewMyDetailFlow(eq(TEST_ACCOUNT_ID), any(TriConsumer.class));

        // when
        ModelAndView mav = myDetailsController.showPage(request, "");

        // then
        then(externalReviewsService).should().getGoogleReviewMyDetailFlow(eq(TEST_ACCOUNT_ID), any(TriConsumer.class));
        
        Object myDetailsModelObj = mav.getModel().get(CommonModel.MODEL_KEY);
        assertThat(myDetailsModelObj).isInstanceOf(MyDetailsModel.class);
        MyDetailsModel model = (MyDetailsModel) myDetailsModelObj;
        
        assertThat(model.getGoogleReviewBar()).isEqualTo(expectedGoogleReviewBar);
        assertThat(model.getShowGoogleReviewItem()).isEqualTo(expectedShowGoogleReviewItem);
        assertThat(model.getIsBindGbp()).isEqualTo(expectedIsBindGbp);
    }

    @Test
    public void showPageWithNullSelectedAccountDoesNotCallGoogleReviewFlow() {
        // given
        given(userSession.getSelectedAccountId()).willReturn(null);

        // when
        ModelAndView mav = myDetailsController.showPage(request, "");

        // then
        then(externalReviewsService).should(never()).getGoogleReviewMyDetailFlow(any(Long.class), any(TriConsumer.class));
        
        Object myDetailsModelObj = mav.getModel().get(CommonModel.MODEL_KEY);
        assertThat(myDetailsModelObj).isInstanceOf(MyDetailsModel.class);
        MyDetailsModel model = (MyDetailsModel) myDetailsModelObj;
        
        // Google review properties should be null when no account is selected
        assertThat(model.getGoogleReviewBar()).isNull();
        assertThat(model.getShowGoogleReviewItem()).isNull();
        assertThat(model.getIsBindGbp()).isNull();
    }

}
