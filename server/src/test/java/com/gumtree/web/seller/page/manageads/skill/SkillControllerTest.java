package com.gumtree.web.seller.page.manageads.skill;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.bapi.model.SkillResponse;
import com.gumtree.bapi.model.SkillRequest;
import com.gumtree.bapi.model.SkillUpdateResponse;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.service.skill.BapiSkillGateway;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.service.skill.SkillAttributeMetadataService;
import com.gumtree.web.seller.service.skill.model.SkillAttributeMetadata;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver;
import org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(MockitoJUnitRunner.class)
public class SkillControllerTest extends BaseSellerControllerTest {

    private static final Long ACCOUNT_ID = 1L;
    private static final Integer CATEGORY_ID = 538;
    private static final Integer NON_GRAY_RELEASE_CATEGORY_ID = 1;
    private static final Long USER_ID = 1L;
    private static final Logger log = LoggerFactory.getLogger(SkillControllerTest.class);

    @Mock
    private BapiSkillGateway skillGateway;
    
    @Mock
    private UserSession userSession;
    
    @Mock
    private CookieResolver cookieResolver;
    
    @Mock
    private CategoryModel categoryModel;
    
    @Mock
    private ApiCallExecutor apiCallExecutor;
    
    @Mock
    private ErrorMessageResolver messageResolver;
    
    @Mock
    private UrlScheme urlScheme;
    
    @Mock
    private UserSessionService userSessionService;

    @Mock
    private ManageAdsHelper manageAdsHelper;
    
    @Mock
    private SkillAttributeMetadataService skillAttributeMetadataService;

    private MockMvc mockMvc;
    private SkillController skillController;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        objectMapper = new ObjectMapper();
        skillController = new SkillController(
            skillGateway,
            cookieResolver,
            categoryModel,
            apiCallExecutor,
            messageResolver,
            urlScheme,
            userSessionService,
            userSession,
            manageAdsHelper,
            skillAttributeMetadataService
        );

        ExceptionHandlerExceptionResolver exceptionResolver = new ExceptionHandlerExceptionResolver();
        exceptionResolver.afterPropertiesSet();
        
        mockMvc = MockMvcBuilders.standaloneSetup(skillController)
                .setHandlerExceptionResolvers(exceptionResolver, new DefaultHandlerExceptionResolver())
                .build();
        
        User user = new User();
        user.setId(USER_ID);
        when(userSession.getUser()).thenReturn(user);
        
        Account account = new Account();
        account.setId(ACCOUNT_ID);
        when(userSession.getSelectableAccounts()).thenReturn(Collections.singletonList(account));
        when(userSession.getSelectedAccountId()).thenReturn(ACCOUNT_ID);

        autowireAbExperimentsService(skillController);
    }

    @Test
    public void testGetSelectedSkills() throws Exception {
        when(userSession.isLoggedIn()).thenReturn(true);
        
        SkillResponse response = new SkillResponse();
        response.setSkills(Arrays.asList(1, 2));
        when(skillGateway.getSelectedSkills(ACCOUNT_ID, CATEGORY_ID)).thenReturn(response);

        mockMvc.perform(get("/api/skill/selected/" + CATEGORY_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.skills").isArray());
    }

    @Test
    public void testGetSelectedSkills_Error() throws Exception {
        when(userSession.isLoggedIn()).thenReturn(true);
        when(skillGateway.getSelectedSkills(ACCOUNT_ID, CATEGORY_ID))
                .thenThrow(new RuntimeException("Test error"));

        mockMvc.perform(get("/api/skill/selected/" + CATEGORY_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    public void testCreateSkill_InvalidRequest() throws Exception {
        when(userSession.isLoggedIn()).thenReturn(true);

        SkillRequest request = new SkillRequest();
        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/api/skill/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));
    }


    @Test
    public void testUpdateSkill_InvalidRequest() throws Exception {
        when(userSession.isLoggedIn()).thenReturn(true);

        SkillRequest request = new SkillRequest();
        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/api/skill/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    public void testGetSkillsByCategoryId() throws Exception {
        when(userSession.isLoggedIn()).thenReturn(true);

        List<SkillAttributeMetadata.SkillAttribute> skills = Arrays.asList(
            createSkillAttribute("580016", "Java"),
            createSkillAttribute("580017", "Python")
        );
        when(skillAttributeMetadataService.getSkillsByCategoryId(CATEGORY_ID)).thenReturn(skills);

        mockMvc.perform(get("/api/skill/attributes/" + CATEGORY_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.skilll-attributes").isArray())
                .andExpect(jsonPath("$.skilll-attributes[0].categoryId").value(CATEGORY_ID.toString()))
                .andExpect(jsonPath("$.skilll-attributes[0].skills").isArray());
    }

    @Test
    public void testCreateSkill_InvalidSkills() throws Exception {
        when(userSession.isLoggedIn()).thenReturn(true);

        // 设置一个非法的技能ID
        SkillRequest request = new SkillRequest();
        request.setCategoryId(CATEGORY_ID);
        request.setSelectedSkills(Arrays.asList(999)); // 使用一个不存在的技能ID

        // 模拟skillAttributeMetadataService返回的技能列表
        List<SkillAttributeMetadata.SkillAttribute> skills = Arrays.asList(
            createSkillAttribute("580016", "Java"),
            createSkillAttribute("580017", "Python")
        );
        when(skillAttributeMetadataService.getSkillsByCategoryId(CATEGORY_ID)).thenReturn(skills);

        String requestJson = objectMapper.writeValueAsString(request);
        mockMvc.perform(post("/api/skill/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("Invalid request"));
    }

    @Test
    public void testUpdateSkill_InvalidSkills() throws Exception {
        when(userSession.isLoggedIn()).thenReturn(true);

        // 设置一个非法的技能ID
        SkillRequest request = new SkillRequest();
        request.setCategoryId(CATEGORY_ID);
        request.setSelectedSkills(Arrays.asList(999)); // 使用一个不存在的技能ID

        // 模拟skillAttributeMetadataService返回的技能列表
        List<SkillAttributeMetadata.SkillAttribute> skills = Arrays.asList(
            createSkillAttribute("580016", "Java"),
            createSkillAttribute("580017", "Python")
        );
        when(skillAttributeMetadataService.getSkillsByCategoryId(CATEGORY_ID)).thenReturn(skills);

        String requestJson = objectMapper.writeValueAsString(request);
        mockMvc.perform(post("/api/skill/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.msg").value("Invalid request"));
    }

    @Test
    public void testCreateSkill_ValidSkills() throws Exception {
        when(userSession.isLoggedIn()).thenReturn(true);

        SkillRequest request = new SkillRequest();
        request.setCategoryId(CATEGORY_ID);
        request.setSelectedSkills(Arrays.asList(580016, 580017)); // 使用存在的技能ID

        List<SkillAttributeMetadata.SkillAttribute> skills = Arrays.asList(
            createSkillAttribute("580016", "Java"),
            createSkillAttribute("580017", "Python")
        );
        when(skillAttributeMetadataService.getSkillsByCategoryId(CATEGORY_ID)).thenReturn(skills);
        when(skillGateway.createSkill(eq(ACCOUNT_ID), any(SkillRequest.class))).thenReturn(true);

        String requestJson = objectMapper.writeValueAsString(request);
        mockMvc.perform(post("/api/skill/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.categoryId").value(CATEGORY_ID.toString()))
                .andExpect(jsonPath("$.data.skills").isArray());
    }

    @Test
    public void testUpdateSkill_ValidSkills() throws Exception {
        when(userSession.isLoggedIn()).thenReturn(true);

        SkillRequest request = new SkillRequest();
        request.setCategoryId(CATEGORY_ID);
        request.setSelectedSkills(Arrays.asList(580016, 580017)); // 使用存在的技能ID

        List<SkillAttributeMetadata.SkillAttribute> skills = Arrays.asList(
            createSkillAttribute("580016", "Java"),
            createSkillAttribute("580017", "Python")
        );
        when(skillAttributeMetadataService.getSkillsByCategoryId(CATEGORY_ID)).thenReturn(skills);

        SkillUpdateResponse skillUpdateResponse = new SkillUpdateResponse();
        skillUpdateResponse.setSuccess(Boolean.TRUE);
        skillUpdateResponse.setMessage("update success");
        when(skillGateway.updateSkill(eq(ACCOUNT_ID), any(SkillRequest.class))).thenReturn(skillUpdateResponse);

        String requestJson = objectMapper.writeValueAsString(request);
        mockMvc.perform(post("/api/skill/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.categoryId").value(CATEGORY_ID.toString()))
                .andExpect(jsonPath("$.data.skills").isArray());
    }

    private SkillAttributeMetadata.SkillAttribute createSkillAttribute(String id, String name) {
        SkillAttributeMetadata.SkillAttribute skill = new SkillAttributeMetadata.SkillAttribute();
        skill.setSkillId(id);
        skill.setName(name);
        skill.setCategoryId(CATEGORY_ID.toString());
        return skill;
    }
} 
