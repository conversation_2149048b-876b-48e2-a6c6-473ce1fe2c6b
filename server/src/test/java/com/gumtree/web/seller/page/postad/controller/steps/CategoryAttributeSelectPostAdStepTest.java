package com.gumtree.web.seller.page.postad.controller.steps;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.mobile.web.category.BrowseCategory;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.postad.controller.SellerTypePanel;
import com.gumtree.web.seller.page.postad.converter.PostAdImageConverter;
import com.gumtree.web.seller.page.postad.model.*;
import com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import net.sf.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CategoryAttributeSelectPostAdStepTest {

    @InjectMocks
    private CategoryAttributeSelectPostAdStep categoryAttributeSelectPostAdStep;

    @Mock
    private CategoryService categoryService;

    @Mock
    private AttributePresentationService attributePresentationService;

    @Mock
    private AdvertEditor advertEditor;

    @Mock
    private PostAdImageConverter postAdImageConverter;

    @Mock
    private PostAdFormDescriptionHintService descriptionHintService;

    private PostAdSubmitModel.Builder postAdSubmitModelBuilder;



    @Before
    public void setUp() {
        // 手动注入依赖
        categoryAttributeSelectPostAdStep = new CategoryAttributeSelectPostAdStep(categoryService, attributePresentationService, postAdImageConverter,descriptionHintService);

        // 构建 PostAdFormBean
        String jsonBean = "{\n" +
                "    \"formErrors\": {},\n" +
                "    \"categoryId\": 10205,\n" +
                "    \"locationId\": 203,\n" +
                "    \"postcode\": \"TW91EJ\",\n" +
                "    \"visibleOnMap\": true,\n" +
                "    \"area\": null,\n" +
                "    \"termsAgreed\": null,\n" +
                "    \"title\": null,\n" +
                "    \"description\": null,\n" +
                "    \"previousContactName\": null,\n" +
                "    \"contactName\": \"Nancy\",\n" +
                "    \"previousContactEmail\": null,\n" +
                "    \"contactEmail\": \"<EMAIL>\",\n" +
                "    \"contactTelephone\": null,\n" +
                "    \"contactUrl\": null,\n" +
                "    \"useEmail\": true,\n" +
                "    \"usePhone\": false,\n" +
                "    \"useUrl\": false,\n" +
                "    \"checkoutVariationId\": null,\n" +
                "    \"mainImageId\": 80500,\n" +
                "    \"imageIds\": [80500],\n" +
                "    \"youtubeLink\": null,\n" +
                "    \"websiteUrl\": \"http://\",\n" +
                "    \"firstName\": null,\n" +
                "    \"lastName\": null,\n" +
                "    \"emailAddress\": \"<EMAIL>\",\n" +
                "    \"telephoneNumber\": null,\n" +
                "    \"password\": null,\n" +
                "    \"optInMarketing\": true,\n" +
                "    \"inAutobizFlow\": false,\n" +
                "    \"vrmStatus\": \"VRM_NONE\",\n" +
                "    \"attributes\": {},\n" +
                "    \"features\": {\n" +
                "        \"FEATURED\": {\n" +
                "            \"selected\": false,\n" +
                "            \"productName\": \"FEATURE_7_DAY\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"extendFields\": {}\n" +
                "}";

        try {
            PostAdFormBean postAdFormBean = (PostAdFormBean) JSONObject.toBean(JSONObject.fromObject(jsonBean), PostAdFormBean.class);
            advertEditor.setPostAdFormBean(postAdFormBean);
            when(advertEditor.getPostAdFormBean()).thenReturn(postAdFormBean);
            when(advertEditor.getCategoryId()).thenReturn(postAdFormBean.getCategoryId());

            SellerTypePanel sellerPanel = mock(SellerTypePanel.class);
            when(sellerPanel.isSellerTypeSelected()).thenReturn(true);



            postAdSubmitModelBuilder = new PostAdSubmitModel.Builder()
                    .withForm(postAdFormBean)
                    .withSellerType(sellerPanel);

            // 模拟 hasBrands 为 true，且满足添加条件
            when(postAdSubmitModelBuilder.hasBrands()).thenReturn(true);

            // 模拟 loadAttributeGroups 返回空值
            when(attributePresentationService.loadAttributeGroups(anyLong(), anyMap()))
                    .thenReturn(Collections.emptyList());

            // 执行方法
            boolean result = categoryAttributeSelectPostAdStep.execute(postAdSubmitModelBuilder, advertEditor);

            // 验证是否调用 addPanels，且包含 BRANDS
            assertTrue(result);

            when(postAdImageConverter.convertImageToPostAdImage(advertEditor.getImages().get(0))).thenReturn(new PostAdImage());

            List<PostAdImage> imageResult = categoryAttributeSelectPostAdStep.convertImages(advertEditor.getImages());
            assertNotNull(imageResult);

        } catch (Exception e) {
            e.printStackTrace();
        }

        // 设置 category model
        CategoryModel stubCategoryModel = mock(CategoryModel.class);
        when(categoryService.getCategoryModel()).thenReturn(stubCategoryModel);
    }


    @Test
    public void testGetOrder() {
        assertEquals( 2, categoryAttributeSelectPostAdStep.getOrder());
    }

    @Test
    public void testExecute() {
//        when(advertEditor.getImages()).thenReturn(new ArrayList<>());
        boolean result = categoryAttributeSelectPostAdStep.execute(postAdSubmitModelBuilder, advertEditor);

        assertTrue(result);
    }

    @Test
    public void testGetPanelsInCreateMode() {
        when(advertEditor.isCreateMode()).thenReturn(true);
        when(advertEditor.getCategoryId()).thenReturn(1L);

        Map<String, String> formAttributes = new HashMap<>();
        CategorySpecificPostAdFormPanels mockPanels = mock(CategorySpecificPostAdFormPanels.class);
        List<PostAdFormPanel> highPriorityPanels = new ArrayList<>();
        highPriorityPanels.add(PostAdFormPanel.DESCRIPTION);
        when(mockPanels.getHighPriorityPanels()).thenReturn(highPriorityPanels);

        when(attributePresentationService.loadPrioritisedCategorySpecificFormPanels(1L, formAttributes))
                .thenReturn(mockPanels);

        List<PostAdFormPanel> panels = categoryAttributeSelectPostAdStep.getPanels(advertEditor);
        assertNotNull(panels);
        assertFalse(panels.isEmpty());
    }

    @Test
    public void testGetPanelsInEditMode() {
        when(advertEditor.isCreateMode()).thenReturn(false);
        when(advertEditor.getCategoryId()).thenReturn(2L);

        Map<String, String> formAttributes = new HashMap<>();
        CategorySpecificPostAdFormPanels mockPanels = mock(CategorySpecificPostAdFormPanels.class);

        List<PostAdFormPanel> lowPriorityPanels = Arrays.asList(PostAdFormPanel.AD_TITLE, PostAdFormPanel.DESCRIPTION);
        when(mockPanels.getLowPriorityPanels()).thenReturn(lowPriorityPanels);
        when(mockPanels.getHighPriorityPanels()).thenReturn(Collections.emptyList());

        when(attributePresentationService.loadPrioritisedCategorySpecificFormPanels(2L, formAttributes))
                .thenReturn(mockPanels);

        List<PostAdFormPanel> panels = categoryAttributeSelectPostAdStep.getPanels(advertEditor);
        assertNotNull(panels);
        assertTrue(panels.contains(PostAdFormPanel.AD_TITLE));
        assertTrue(panels.contains(PostAdFormPanel.DESCRIPTION));
        assertTrue(panels.contains(PostAdFormPanel.PRE_CONFIRM));
    }

    @Test
    public void testFilterPanels() {
        List<PostAdFormPanel> input = Arrays.asList(
                PostAdFormPanel.AD_TITLE,
                PostAdFormPanel.PRICE,
                PostAdFormPanel.SELLER_TYPE,
                PostAdFormPanel.DESCRIPTION
        );

        List<PostAdFormPanel> filtered = categoryAttributeSelectPostAdStep.filterPanels(input);
        assertNotNull(filtered);
        assertFalse(filtered.contains(PostAdFormPanel.PRICE));
        assertFalse(filtered.contains(PostAdFormPanel.SELLER_TYPE));
        assertTrue(filtered.contains(PostAdFormPanel.AD_TITLE));
        assertTrue(filtered.contains(PostAdFormPanel.DESCRIPTION));
    }

    @Test
    public void testFilterPanels_Empty() {
        List<PostAdFormPanel> input = new ArrayList<>();

        List<PostAdFormPanel> filtered = categoryAttributeSelectPostAdStep.filterPanels(input);
        assertNotNull(filtered);
        assertEquals(0, filtered.size());
    }

    @Test
    public void testGetCategoryCrumb() {
        Category category = mock(Category.class);
        when(category.getId()).thenReturn(1L);
        when(category.getName()).thenReturn("TestCategory");
        when(category.isHidden()).thenReturn(false);

        Map<Integer, Category> hierarchy = new HashMap<>();
        hierarchy.put(1, category);
        hierarchy.put(2, mock(Category.class));

        when(categoryService.getLevelHierarchy(category)).thenReturn(hierarchy);

        List<BrowseCategory> crumb = categoryAttributeSelectPostAdStep.getCategoryCrumb(advertEditor);
        assertNotNull(crumb);
    }

    @Test
    public void testConvertImages_NullInput() {
        List<PostAdImage> result = categoryAttributeSelectPostAdStep.convertImages(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExecute_addBrandsPanelWhenHasBrands() {
        // Arrange
        AdvertEditor editor = mock(AdvertEditor.class);
        PostAdFormBean formBean = mock(PostAdFormBean.class);
        when(editor.getPostAdFormBean()).thenReturn(formBean);
        when(editor.getCategoryId()).thenReturn(1L);
        when(formBean.getAttributes()).thenReturn(new HashMap<>());
        when(editor.getImages()).thenReturn(new ArrayList<>());
        when(editor.getLocationId()).thenReturn(1L);

        // 模拟 getPanels 返回包含 DESCRIPTION
        CategoryAttributeSelectPostAdStep step = new CategoryAttributeSelectPostAdStep(categoryService, attributePresentationService, postAdImageConverter, descriptionHintService);
        List<PostAdFormPanel> panels = new ArrayList<>();
        panels.add(PostAdFormPanel.DESCRIPTION);
        CategoryAttributeSelectPostAdStep spyStep = spy(step);
        doReturn(panels).when(spyStep).getPanels(editor);

        // Mock filterPanels 直接返回 panels
        doReturn(panels).when(spyStep).filterPanels(panels);

        // sellerTypeAttribute = null，hasBrands = true
        when(categoryService.getCategoryModel()).thenReturn(mock(CategoryModel.class));
        when(postAdSubmitModelBuilder.isSellerTypeSelected()).thenReturn(false);

        // Mock其它依赖
        when(attributePresentationService.loadAttributeGroups(anyLong(), anyMap())).thenReturn(Collections.emptyList());
        when(descriptionHintService.getHint(any(), anyLong(), anyLong())).thenReturn("");

        // Act
        boolean result = spyStep.execute(postAdSubmitModelBuilder, editor);

        // Assert
        assertTrue(result);
//        assertTrue(panels.contains(PostAdFormPanel.BRANDS));
    }

}
