package com.gumtree.web.seller.security.shiro;

import com.gumtree.api.Account;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.UserType;
import com.gumtree.api.domain.user.beans.RegisterUserBean;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.UserSessionBean;
import com.gumtree.web.security.UserSessionBeanImpl;
import com.gumtree.web.security.exception.UserNotRecognisedException;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.security.shiro.ShiroSubjectUserSession;
import com.gumtree.web.seller.builder.UserBuilder;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import org.apache.shiro.subject.Subject;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ShiroSubjectUserSessionTest extends BaseShiroTest {

    private final static long STANDARD_USER_ACCOUNT_ID = 10234L;
    private final static long PRO_USER_ACCOUNT_ID = 30948L;
    private final static long PRO_USER_MULTIUSER_ACCOUNT_ID = 411876L;

    private ShiroSubjectUserSession session;

    private Subject subject;

    private User user;

    private BushfireApiKey apiKey;

    private UserSecurityManager userSecurityManager;

    private LoginUtils loginUtils;

    private SellerSessionDataService sessionDataService;

    private ApiCallResponse registerUserApiCallResponse;

    private UserSessionBean userSessionBean;

    private UserServiceFacade userServiceFacade;

    @Before
    public void init() {
        loginUtils = mock(LoginUtils.class);
        subject = mock(Subject.class);
        userSecurityManager = mock(UserSecurityManager.class);
        registerUserApiCallResponse = mock(ApiCallResponse.class);
        sessionDataService = mock(SellerSessionDataService.class);
        userServiceFacade = mock(UserServiceFacade.class);
        userSessionBean = new UserSessionBeanImpl();
        session = new ShiroSubjectUserSession(userSecurityManager, loginUtils, sessionDataService, userServiceFacade);

        apiKey = new BushfireApiKey();
        apiKey.setPrivateKey("privateKey");
        apiKey.setAccessKey("accessKey");

        user = new User();
        user.setFirstName("Dirk");
        user.setLastName("Blonkenburger");
        user.setEmail("<EMAIL>");
        user.setApiKey(apiKey);
        user.setAccountIds(Arrays.asList(1L, 2L, 3L));
        user.setStatus(UserStatus.ACTIVE);

        when(sessionDataService.getUserSessionBean("<EMAIL>")).thenReturn(userSessionBean);
    }

    private void setupUser(String principal, List<Long> accountIds){
        setSubject(principal, true, true);
        when(userSecurityManager.getUserAccounts(eq(principal), anyObject()))
                .thenReturn(accountIds.stream().map(x -> getAccountFromId(x)).collect(Collectors.toList()));
        when(session.isAuthenticated()).thenReturn(true);
        when(sessionDataService.getUserSessionBean(eq(principal))).thenReturn(userSessionBean);
        when(userSecurityManager.getExistingUser(eq(principal))).thenReturn(user);
    }

    public void setupStandardUser() {

        user = UserBuilder.builder()
                .withFirstName("Eugene")
                .withLastName("Blacksmith")
                .withEmail("<EMAIL>")
                .withApiKey(apiKey)
                .withAccountIds(Arrays.asList(STANDARD_USER_ACCOUNT_ID))
                .withStatus(UserStatus.ACTIVE)
                .withType(UserType.STANDARD)
                .build();

        setupUser("<EMAIL>", Arrays.asList(STANDARD_USER_ACCOUNT_ID));
    }

    public void setupProUser(List<Long> accountIds) {

        user = UserBuilder.builder()
                .withFirstName("Fitzgerald")
                .withLastName("Pheasanthunter")
                .withEmail("<EMAIL>")
                .withApiKey(apiKey)
                .withAccountIds(accountIds)
                .withStatus(UserStatus.ACTIVE)
                .withType(UserType.PRO)
                .build();

        setupUser("<EMAIL>", accountIds);
    }

    private Account getAccountFromId(long accountId){

        Account account = new Account();

        if (accountId == STANDARD_USER_ACCOUNT_ID){
            account.setPro(false);
            account.setUserIds(Arrays.asList(1L));
        }
        else if (accountId == PRO_USER_ACCOUNT_ID){
            // Pro account
            account.setPro(true);
            account.setUserIds(Arrays.asList(1L));
        }
        else if (accountId == PRO_USER_MULTIUSER_ACCOUNT_ID){
            account.setPro(true);
            // Account linked to two users
            account.setUserIds(Arrays.asList(1L, 2L));
        }
        return account;
    }

    @Test
    public void whenTheSuperUserOverrideIsSetThenTheSelectedAccountIdShouldBeTheOverrideId() {
        // Given
        user.setType(UserType.STANDARD);
        subject = setSubject("<EMAIL>", true, false);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenReturn(user);
        when(userSecurityManager.getAccount(eq(9191L),any())).thenReturn(createAccount(9191L, true));

        // When
        session.setSuperUserOverrideAccountId(9191L);

        // Then
        assertThat(session.getSelectedAccountId(), equalTo(9191L));
        assertThat(session.isProUser(), equalTo(Boolean.TRUE));
    }

    @Test
    public void whenTheSuperUserOverrideIsNotSetThenTheSelectedAccountIdShouldBeTheDefaultId() {
        // Given
        subject = setSubject("<EMAIL>", true, false);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenReturn(user);

        // When & Then
        assertThat(session.getSelectedAccountId(), equalTo(1L));
    }

    @Test
    public void whenTheSuperUserOverrideIsSetThenSelectableAccountsShouldContainTheOverrideAccount() {
        // Given
        List<Account> accounts = new ArrayList<Account>();
        accounts.add(createAccount(1L, false));
        accounts.add(createAccount(2L, false));
        user.setType(UserType.STANDARD);

        subject = setSubject("<EMAIL>", true, false);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenReturn(user);
        when(userSecurityManager.getUserAccounts("<EMAIL>", session)).thenReturn(accounts);
        when(userSecurityManager.getAccount(150L, session)).thenReturn(createAccount(150L, false));

        // When
        session.setSuperUserOverrideAccountId(150L);
        List<Account> selectableAccounts = session.getSelectableAccounts();

        // Then
        assertThat(selectableAccounts.size(), equalTo(3));
        assertThat(selectableAccounts.get(0).getId(), equalTo(1L));
        assertThat(selectableAccounts.get(1).getId(), equalTo(2L));
        assertThat(selectableAccounts.get(2).getId(), equalTo(150L));
        assertThat(session.isProUser(), equalTo(Boolean.FALSE));
    }

    @Test
    public void whenTheSuperUserOverrideIsNotSetThenSelectableAccountsShouldContainTheDefaultAccounts() {
        // Given
        List<Account> accounts = new ArrayList<Account>();
        accounts.add(createAccount(1L, false));
        accounts.add(createAccount(2L, true));
        user.setType(UserType.PRO);

        subject = setSubject("<EMAIL>", true, false);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenReturn(user);
        when(userSecurityManager.getUserAccounts("<EMAIL>", session)).thenReturn(accounts);

        // When
        List<Account> selectableAccounts = session.getSelectableAccounts();

        // Then
        assertThat(selectableAccounts.size(), equalTo(2));
        assertThat(selectableAccounts.get(0).getId(), equalTo(1L));
        assertThat(selectableAccounts.get(1).getId(), equalTo(2L));
        assertThat(session.isProUser(), equalTo(Boolean.TRUE));
    }

    @Test
    public void whenTheSuperUserOverrideIsSetAndTheAccountSelectionChangesThenTheSuperUserOverrideShouldBeRemoved() {
        // Given
        List<Account> accounts = new ArrayList<Account>();
        accounts.add(createAccount(1L, true));
        accounts.add(createAccount(2L, false));
        user.setType(UserType.PRO);

        subject = setSubject("<EMAIL>", true, false);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenReturn(user);
        when(userSecurityManager.getUserAccounts("<EMAIL>", session)).thenReturn(accounts);
        when(userSecurityManager.getAccount(150L, session)).thenReturn(createAccount(150L, false));

        session.setSuperUserOverrideAccountId(150L);
        session.setSelectedAccountId(1L);

        // When
        List<Account> selectableAccounts = session.getSelectableAccounts();

        // Then
        assertThat(selectableAccounts.size(), equalTo(2));
        assertThat(selectableAccounts.get(0).getId(), equalTo(1L));
        assertThat(selectableAccounts.get(1).getId(), equalTo(2L));
        assertThat(session.isProUser(), equalTo(Boolean.TRUE));
    }

    @Test(expected = UserNotRecognisedException.class)
    public void initializationFailsWhenNoValidUserFound() {
        setSubject("<EMAIL>", false, false);
        session.getUsername();
    }

    @Test
    public void initializationSucceedsForExistingRememberedUser() {
        setSubject("<EMAIL>", true, false);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenReturn(user);
        assertThat(session.getUsername(), equalTo("<EMAIL>"));
        assertThat(session.getApiKey(), equalTo(apiKey));
        assertThat(session.getSelectedAccountId(), equalTo(1L));
        assertThat(session.getUserType(), equalTo(UserLoginStatus.EXISTING));
        assertThat(session.getUser(), equalTo(user));
    }

    @Test
    public void initializationSucceedsForExistingAuthenticatedUser() {
        setSubject("<EMAIL>", false, true);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenReturn(user);
        assertThat(session.getUsername(), equalTo("<EMAIL>"));
        assertThat(session.getApiKey(), equalTo(apiKey));
        assertThat(session.getSelectedAccountId(), equalTo(1L));
        assertThat(session.getUserType(), equalTo(UserLoginStatus.EXISTING));
        assertThat(session.getUser(), equalTo(user));
    }

    @Test(expected = UserNotRecognisedException.class)
    public void throwsUserNotRecognisedExceptionForRememberedUserThatNoLongerExists() {
        setSubject("<EMAIL>", true, false);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenThrow(new RuntimeException());
        session.getUsername();
    }

    @Test
    public void initializationSucceedsForNewUser() {
        setSubject("<EMAIL>", false, false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn("<EMAIL>");
        when(userSecurityManager.getDefaultApiKey()).thenReturn(apiKey);
        assertThat(session.getUsername(), equalTo("<EMAIL>"));
        assertThat(session.getApiKey(), equalTo(apiKey));
        assertThat(session.getSelectedAccountId(), Matchers.nullValue());
        assertThat(session.getUserType(), equalTo(UserLoginStatus.NEW_UNREGISTERED));
        assertThat(session.getUser().getEmail(), equalTo("<EMAIL>"));
        assertThat(session.getUser().getApiKey(), equalTo(apiKey));
        verify(loginUtils).setNewUserMustLoginToStartPostAdFlow(true);
        verify(userSecurityManager).getExistingUser(anyString());
    }

    @Test
    public void logoutForExistingUserLogsOutSubjectClearsNewUserCookiesAndClearsSessionData() {
        Subject subject = setSubject("<EMAIL>", true, false);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenReturn(user);
        session.logout();
        verify(subject).logout();
        verify(loginUtils).clearNewUserEmailAddressFromSession();
        verify(loginUtils).setNewUserMustLoginToStartPostAdFlow(true);
        verify(sessionDataService).removeUserSessionBean("<EMAIL>");
    }

    @Test
    public void logoutForNewUserJustClearsNewUserCookiesAndClearsSessionData() {
        Subject subject = setSubject("<EMAIL>", false, false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn("<EMAIL>");
        session.logout();
        verify(subject, never()).logout();
        verify(loginUtils).clearNewUserEmailAddressFromSession();
        verify(loginUtils, times(2)).setNewUserMustLoginToStartPostAdFlow(true); // twice, once for init(), once for logout
        verify(sessionDataService).removeUserSessionBean("<EMAIL>");
    }

    @Test(expected = IllegalStateException.class)
    public void registerNewUserFailsIfCurrentTypeIsExisting() {
        setSubject("<EMAIL>", true, false);
        when(userSecurityManager.getExistingUser("<EMAIL>")).thenReturn(user);
        session.getUsername();
        session.registerNewUser(new RegisterUserBean());
    }

    @Test
    public void newUserIsPopulatedIntoSessionOnSuccessfulRegistration() {
        RegisterUserBean registerUserBean = new RegisterUserBean();
        setSubject("<EMAIL>", false, false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn("<EMAIL>");
        when(userSecurityManager.registerNewUser(registerUserBean)).thenReturn(registerUserApiCallResponse);
        when(registerUserApiCallResponse.isErrorResponse()).thenReturn(false);
        when(registerUserApiCallResponse.getResponseObject()).thenReturn(user);

        // Pre-register (and initialise)
        assertThat(session.getUsername(), equalTo("<EMAIL>"));

        assertThat(session.registerNewUser(registerUserBean), equalTo(registerUserApiCallResponse));

        // Assert post-register
        assertThat(session.getUsername(), equalTo("<EMAIL>"));
        assertThat(session.getApiKey(), equalTo(apiKey));
        assertThat(session.getSelectedAccountId(), equalTo(1L));
        assertThat(session.getUserType(), equalTo(UserLoginStatus.NEW_REGISTERED));
        assertThat(session.getUser(), equalTo(user));

        // Once for init(), once for setSelectedAccount(), once for registration success
        verify(sessionDataService, times(3)).persistSessionBean(eq("<EMAIL>"), eq(userSessionBean));
    }

    @Test
    public void newUserIsNotPopulatedIntoSessionOnRegistrationFailure() {
        RegisterUserBean registerUserBean = new RegisterUserBean();
        setSubject("<EMAIL>", false, false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn("<EMAIL>");
        when(userSecurityManager.getDefaultApiKey()).thenReturn(apiKey);
        when(userSecurityManager.registerNewUser(registerUserBean)).thenReturn(registerUserApiCallResponse);
        when(registerUserApiCallResponse.isErrorResponse()).thenReturn(true);

        assertThat(session.registerNewUser(registerUserBean), equalTo(registerUserApiCallResponse));

        // Still un-registered user
        assertThat(session.getUsername(), equalTo("<EMAIL>"));
        assertThat(session.getApiKey(), equalTo(apiKey));
        assertThat(session.getSelectedAccountId(), Matchers.nullValue());
        assertThat(session.getUserType(), equalTo(UserLoginStatus.NEW_UNREGISTERED));
        assertThat(session.getUser().getEmail(), equalTo("<EMAIL>"));
        assertThat(session.getUser().getApiKey(), equalTo(apiKey));
    }

    @Test(expected = IllegalStateException.class)
    public void registerNewUserFailsIfCurrentTypeIsNewRegistered() {
        RegisterUserBean registerUserBean = new RegisterUserBean();
        setSubject("<EMAIL>", false, false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn("<EMAIL>");
        when(userSecurityManager.registerNewUser(registerUserBean)).thenReturn(registerUserApiCallResponse);
        when(registerUserApiCallResponse.isErrorResponse()).thenReturn(false);
        when(registerUserApiCallResponse.getResponseObject()).thenReturn(user);
        assertThat(session.registerNewUser(registerUserBean), equalTo(registerUserApiCallResponse));
        assertThat(session.getUserType(), equalTo(UserLoginStatus.NEW_REGISTERED));
        assertThat(session.registerNewUser(registerUserBean), equalTo(registerUserApiCallResponse));
    }

    @Test
    public void shouldReturnIsProUserFalseForStandardUser(){
        // Given
        setupStandardUser();

        // When
        boolean isPro = session.isProUser();

        // Then
        assertThat(isPro, equalTo(false));
    }

    @Test
    public void shouldReturnIsProTrueForProUser() {
        // Given
        setupProUser(Arrays.asList(PRO_USER_ACCOUNT_ID));

        // When
        boolean isPro = session.isProUser();

        // Then
        assertThat(isPro, equalTo(true));
    }

    private Subject setSubject(String principals, boolean remembered, boolean authenticated) {
        Subject subject = mock(Subject.class);
        when(subject.isRemembered()).thenReturn(remembered);
        when(subject.isAuthenticated()).thenReturn(authenticated);
        when(subject.getPrincipal()).thenReturn(principals);
        setSubject(subject);
        return subject;
    }

    private Account createAccount(long accountId, boolean isPro) {
        Account account = new Account();
        account.setId(accountId);
        account.setName("Account " + accountId);
        account.setPro(isPro);
        return account;
    }
}
