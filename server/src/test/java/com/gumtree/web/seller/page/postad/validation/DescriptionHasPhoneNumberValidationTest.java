package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintValidatorContext;

import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertFalse;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * +44 1632960001
 * +441632960001
 * (01632) 960 001
 * 01632 960001
 * 01632960001
 */
public class DescriptionHasPhoneNumberValidationTest {
    private static final String DESCRIPTION_WITHOUT_PHONE = "Some test description in plain text. That text should pass validation. There is nothing else rather than text";
    private static final String DESCRIPTION_WITH_PHONE_1 = DESCRIPTION_WITHOUT_PHONE + " Call me: +44 1632960001";
    private static final String DESCRIPTION_WITH_PHONE_2 = DESCRIPTION_WITHOUT_PHONE + " Call me: +441632960001";
    private static final String DESCRIPTION_WITH_PHONE_3 = DESCRIPTION_WITHOUT_PHONE + " Call me: (01632) 960 001";
    private static final String DESCRIPTION_WITH_PHONE_4 = DESCRIPTION_WITHOUT_PHONE + " Call me: 01632 960001";
    private static final String DESCRIPTION_WITH_PHONE_5 = DESCRIPTION_WITHOUT_PHONE + " Call me: 01632960001";
    private static final String ERROR_CODE = "postad.description.containPhoneNumber";
    private static final String ERROR_MESSAGE = "Description cannot contain phone numbers";

    private ConstraintValidatorContext context;
    private DescriptionHasPhoneNumberValidation validation;
    private DescriptionHasPhoneNumberValidator validator;

    @Before
    public void setup() {
        ErrorMessageResolver resolver = mock(ErrorMessageResolver.class);
        when(resolver.getMessage(ERROR_CODE, "")).thenReturn(ERROR_MESSAGE);
        context = mock(ConstraintValidatorContext.class);
        validator = new DescriptionHasPhoneNumberValidator(resolver);
        validation = mock(DescriptionHasPhoneNumberValidation.class);
        when(validation.message()).thenReturn(ERROR_CODE);
        when(validation.fieldList()).thenReturn(new String[]{"description"});
        validator.initialize(validation);
        doNothing().when(context).disableDefaultConstraintViolation();

        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.class);
        ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext nodeContext =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext.class);

        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(builder);
        when(builder.addPropertyNode(anyString())).thenReturn(nodeContext);
        when(builder.addConstraintViolation()).thenReturn(context);
    }

    @Test
    public void testValidDescription() throws Exception {
        assertTrue(validator.isValid(makeTestObject(DESCRIPTION_WITHOUT_PHONE), context));
    }

    @Test
    public void testDescriptionWithPhoneNumber1() {
        assertThatDescriptionWithPhoneNumberIsNotValid(DESCRIPTION_WITH_PHONE_1);
    }

    @Test
    public void testDescriptionWithPhoneNumber2() {
        assertThatDescriptionWithPhoneNumberIsNotValid(DESCRIPTION_WITH_PHONE_2);
    }

    @Test
    public void testDescriptionWithPhoneNumber3() {
        assertThatDescriptionWithPhoneNumberIsNotValid(DESCRIPTION_WITH_PHONE_3);
    }

    @Test
    public void testDescriptionWithPhoneNumber4() {
        assertThatDescriptionWithPhoneNumberIsNotValid(DESCRIPTION_WITH_PHONE_4);
    }

    @Test
    public void testDescriptionWithPhoneNumber5() {
        assertThatDescriptionWithPhoneNumberIsNotValid(DESCRIPTION_WITH_PHONE_5);
    }

    private void assertThatDescriptionWithPhoneNumberIsNotValid(String description) {
        boolean result = validator.isValid(makeTestObject(description), context);
        assertFalse(result);
        verify(context).buildConstraintViolationWithTemplate(ERROR_MESSAGE);
        verify(context).disableDefaultConstraintViolation();
    }

    private PostAdDetail makeTestObject(String description) {
        PostAdDetail detail = new PostAdDetail();
        PostAdFormBean bean = new PostAdFormBean();
        bean.setDescription(description);
        detail.setPostAdFormBean(bean);
        return detail;
    }
}
