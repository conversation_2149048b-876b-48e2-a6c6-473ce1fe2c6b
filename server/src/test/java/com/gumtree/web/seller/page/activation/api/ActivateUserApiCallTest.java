package com.gumtree.web.seller.page.activation.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.domain.user.beans.UserStatusBean;
import com.gumtree.seller.domain.deactivation.entity.DeactivationReason;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.web.seller.page.manageads.mydetails.api.DeactivateMyAccountApiCall;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class ActivateUserApiCallTest {

    private BushfireApi bushfireApi;

    private UserApi userApi;

    private ActivateUserApiCall activateUserApiCall;

    private com.gumtree.api.User user;

    @Before
    public void init() {
        bushfireApi = mock(BushfireApi.class);
        userApi = mock(UserApi.class);
        user = new com.gumtree.api.User();
        when(userApi.getUser(anyString())).thenReturn(user);
        when(bushfireApi.create(UserApi.class)).thenReturn(userApi);
    }

    @Test
    public void makesCorrectCallToApiToActivateUser() {
        activateUserApiCall = new ActivateUserApiCall("<EMAIL>", "some_key");
        activateUserApiCall.execute(bushfireApi);
        ArgumentCaptor<UserStatusBean> statusBeanCaptor = ArgumentCaptor.forClass(UserStatusBean.class);
        verify(userApi).newStatus(eq("<EMAIL>"), statusBeanCaptor.capture());
        UserStatusBean statusBean = statusBeanCaptor.getValue();
        assertThat(statusBean.getActivationKey(), equalTo("some_key"));
        assertThat(statusBean.getStatus(), equalTo(UserStatus.ACTIVE));
    }
}
