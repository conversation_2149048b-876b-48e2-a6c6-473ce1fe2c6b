package com.gumtree.web.seller.page.payment.converter;

import com.gumtree.api.domain.payment.ApiPayPalIPNBean;
import com.gumtree.web.filter.paypal.PayPalIPNRequest;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.*;

public class PayPalIPNRequestToApiPayPalIPNBeanConverterTest {

    private PayPalIPNRequestToApiPayPalIPNBeanConverter converter;

    @Before
    public void setUp() throws Exception {
        converter = new PayPalIPNRequestToApiPayPalIPNBeanConverter();
    }

    @Test
    public void testConvert() throws Exception {
        String body = "thebody";
        String contenType = "thecontenttype";
        PayPalIPNRequest request = new PayPalIPNRequest();
        request.setBody(body);
        request.setContentType(contenType);

        ApiPayPalIPNBean apiPayPalIPNBean = converter.convert(request);

        assertThat(body, equalTo(apiPayPalIPNBean.getRequestBody()));
        assertThat(contenType, equalTo(apiPayPalIPNBean.getContentType()));
    }
}
