package com.gumtree.web.seller.service;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.common.properties.GtPropertiesInitializer;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardisedVehicleDataResponse;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.VehicleAttribute;
import com.gumtree.service.category.CategoryService;
import com.gumtree.util.url.CdnImageUrlProvider;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.ajax.vrm.MotorsApiClient;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.service.CommonAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.PetAttributesValidationService;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.exception.UnknownAdvertEditorException;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState;
import com.gumtree.web.seller.page.postad.service.PhoneAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.legal.PostAdLegalService;
import com.gumtree.web.seller.service.location.PostAdLocationService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import com.gumtree.web.seller.service.pricing.PricingService;
import com.gumtree.web.seller.service.postad.UserPostcodeLookupService;
import com.gumtree.web.seller.storage.DraftAdvertService;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import com.gumtree.web.service.ContactEmailService;
import com.gumtree.zeno.core.service.ZenoService;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.configuration.ConfigurationException;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import javax.validation.Validator;

import static com.gumtree.mobile.test.Fixtures.*;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertNotSame;
import static org.junit.Assert.assertSame;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;


public class PostAdWorkspaceImplTest {
    private PostAdWorkspaceImpl workspace;
    private UserSession userSession;
    private UserSecurityManager userSecurityManager;
    private AttributePresentationService attributePresentationService;
    private BushfireApi bushfireApi;
    private PostAdLocationService locationService;
    private SellerSessionDataService sessionDataService;
    private Ad apiAdvert;
    private AdvertApi advertApi;
    private OrderApi orderApi;
    private UserApi userApi;
    private CategoryService categoryService;
    private CategoryModel categoryModel;
    private PricingService pricingService;
    private ContactEmailService contactEmailService;
    private DraftAdvertService draftAdvertService;
    private MotorsApiClient motorsApiClient;
    private CdnImageUrlProvider cdnImageUrlProvider;
    private PetAttributesValidationService petAttributesValidationService;
    private PhoneAttributesValidationService phoneAttributesValidationService;
    private CommonAttributesValidationService commonAttributesValidationService;

    @Mock
    private ZenoService zenoService;

    @BeforeClass
    public static void initProperties() throws ConfigurationException {
        GtPropertiesInitializer.init("seller-server");
    }

    @Before
    public void setup() {
        initMocks(this);
        bushfireApi = mock(BushfireApi.class);
        attributePresentationService = mock(AttributePresentationService.class);
        userSession = mock(UserSession.class);
        userSecurityManager = mock(UserSecurityManager.class);
        locationService = mock(PostAdLocationService.class);
        sessionDataService = mock(SellerSessionDataService.class);
        categoryService = mock(CategoryService.class);
        categoryModel = mock(CategoryModel.class);
        pricingService = mock(PricingService.class);
        contactEmailService = mock(ContactEmailService.class);
        draftAdvertService = mock(DraftAdvertService.class);
        motorsApiClient = mock(MotorsApiClient.class);
        CustomMetricRegistry metrics = new CustomMetricRegistry(new SimpleMeterRegistry());


        workspace = new PostAdWorkspaceImpl(sessionDataService, userSession, userSecurityManager, attributePresentationService, bushfireApi,
                locationService, mock(ApiCallExecutor.class), mock(Validator.class), categoryService, mock(UserPostcodeLookupService.class),
                mock(PostAdLegalService.class), pricingService, categoryModel, contactEmailService, draftAdvertService, motorsApiClient,
                zenoService, metrics, cdnImageUrlProvider, petAttributesValidationService,phoneAttributesValidationService,
                commonAttributesValidationService);

        advertApi = mock(AdvertApi.class);
        orderApi = mock(OrderApi.class);
        userApi = mock(UserApi.class);
        when(bushfireApi.advertApi()).thenReturn(advertApi);
        when(bushfireApi.orderApi()).thenReturn(orderApi);
        when(bushfireApi.userApi()).thenReturn(userApi);

        apiAdvert = new Ad();
        apiAdvert.setId(1234L);
        apiAdvert.setCategoryId(56L);
        apiAdvert.setPostcode("TW9 1EH");
        apiAdvert.setAccountId(10101L);
        apiAdvert.setStatus(AdStatus.DRAFT);
        when(advertApi.getAdvert(1234L)).thenReturn(apiAdvert);

        when(locationService.lookupPostcode("TW9 1EH")).thenReturn(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_RECOGNISED, "TW9 1EH"));

        when(userSession.getUser()).thenReturn(mock(User.class));
        when(userSession.getSelectedAccountId()).thenReturn(10101L);
        when(categoryService.getById(anyLong())).thenReturn(Optional.absent());
        when(categoryModel.getByName(anyString())).thenReturn(Optional.of(new Category(123L, "", "")));
        when(draftAdvertService.retrieve()).thenReturn(Optional.absent());
    }

    @Test(expected = UnsupportedOperationException.class)
    public void editAdThrowsUnsupportedOperationExceptionForUnrecognisedAdvertId() {
        when(advertApi.getAdvert(anyLong())).thenReturn(null);
        workspace.createAndPersistEditor(10L, null, REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);
    }

    @Test(expected = UnsupportedOperationException.class)
    public void editAdThrowsUnsupportedOperationExceptionWhenLoggedInAccountDidNotPostAd() {
        when(userSession.getSelectedAccountId()).thenReturn(20202L);
        workspace.createAndPersistEditor(10L, null, REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);
    }

    @Test(expected = UnknownAdvertEditorException.class)
    public void exceptionThrownWhenTryToGetEditorUsingUnknownId() {
        workspace.getEditor("unknown101");
    }

    @Test
    public void creatingEmptyEditorReturnsDraft() {
        // user logged in & create mode & draft storage returns stuff
        when(userSession.isLoggedIn()).thenReturn(true);
        when(draftAdvertService.retrieve()).thenReturn(Optional.of(createDetail(null)));

        // when
        AdvertEditor editor = workspace.createAndPersistEditor(null, null, REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);

        // then
        assertThat(editor.getAdvertDetail().isDraft()).isTrue();
    }

    @Test
    public void creatingEmptyEditorDoesNotReturnDraftForNotLoggedUser() {
        // user not logged in & create mode & draft storage returns stuff
        when(userSession.isLoggedIn()).thenReturn(false);
        when(draftAdvertService.retrieve()).thenReturn(Optional.of(createDetail(null)));

        // when
        AdvertEditor editor = workspace.createAndPersistEditor(null, null, REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);

        // then
        assertThat(editor.getAdvertDetail().isDraft()).isFalse();
    }

    @Test
    public void creatingEmptyEditorDoesNotReturnDraftWhenEditingExistingAd() {
        // user logged in  & edition mode & draft storage returns stuff
        when(draftAdvertService.retrieve()).thenReturn(Optional.of(createDetail(null)));

        // when
        AdvertEditor editor = workspace.createAndPersistEditor(1234L, null, REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);

        // then
        assertThat(editor.getAdvertDetail().isDraft()).isFalse();
    }

    @Test
    public void editorDependenciesArePassedProperlyWhenGettingAnExistingEditor() {
        PostAdDetail detail = new PostAdDetail();
        detail.setPostAdFormBean(new PostAdFormBean());
        when(sessionDataService.getPostAdData("123edit")).thenReturn(detail);
        User user = new User();
        user.setEmail(userSession.getUsername());
        user.setFirstName("first name");
        when(userApi.getUser(userSession.getUsername())).thenReturn(user);
        AdvertEditor editor = workspace.getEditor("123edit");

        PostAdDetail editorDetail = (PostAdDetail) ReflectionTestUtils.getField(editor, "advertDetail");
        BushfireApi editorBushfireApi = (BushfireApi) ReflectionTestUtils.getField(editor, "bushfireApi");
        AttributePresentationService editorAttributePresentationService = (AttributePresentationService) ReflectionTestUtils.getField(editor, "attributePresentationService");
        PostAdLocationService editorPostAdLocationService = (PostAdLocationService) ReflectionTestUtils.getField(editor, "locationService");

        assertSame(editorDetail, detail);
        assertSame(editorBushfireApi, bushfireApi);
        assertSame(editorAttributePresentationService, attributePresentationService);
        assertSame(editorPostAdLocationService, locationService);
    }

    @Test
    public void testNoAdNotLoadedWhenCreatingANewEditorWithANullAdvertId() {
        workspace.createAndPersistEditor(null, null, REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);
        verify(userSession).validate();
        verifyZeroInteractions(bushfireApi);
    }

    @Test
    public void testAdLoadedWhenAdIdGiven() {
        AdvertEditor editor = workspace.createAndPersistEditor(1234L, null, REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);
        verify(userSession).validate();
        verify(advertApi).getAdvert(1234L);
        PostAdDetail detail = (PostAdDetail) ReflectionTestUtils.getField(editor, "advertDetail");
        assertThat(detail.getAdvertId(), equalTo(1234L));
    }

    @Test
    public void testNewEditorMadeWhenNoEditorId() {
        workspace.createAndPersistEditor(1234L, null, REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);
        verify(userSession).validate();
        verify(sessionDataService).setPostAdData(anyString(), Matchers.<PostAdDetail>any());
    }

    @Test
    public void testUpdateEditorPersistsDraft() {
        // user logged in & editor in create mode
        when(userSession.isLoggedIn()).thenReturn(true);

        AdvertEditor editor = mock(AdvertEditor.class);
        when(editor.isCreateMode()).thenReturn(true);
        when(editor.getAdvertDetail()).thenReturn(createDetail(null));

        // when
        workspace.updateEditor("any-id", editor);

        // then
        verify(draftAdvertService).persist(any(PostAdDetail.class));
    }

    @Test
    public void testUpdateEditorDoesNotPersistDraftForLoggedOutUser() {
        // user not logged & editor in create mode
        when(userSession.isLoggedIn()).thenReturn(false);

        AdvertEditor editor = mock(AdvertEditor.class);
        when(editor.getAdvertDetail()).thenReturn(createDetail(null));

        // when
        workspace.updateEditor("any-id", editor);

        // then
        verify(draftAdvertService, never()).persist(any(PostAdDetail.class));
    }

    @Test
    public void testUpdateEditorDoesNotPersistDraftForExistingAd() {
        // user logged in  & editor in edition mode
        AdvertEditor editor = mock(AdvertEditor.class);
        when(editor.getAdvertDetail()).thenReturn(createDetail(123L));

        // when
        workspace.updateEditor("any-id", editor);

        // then
        verify(draftAdvertService, never()).persist(any(PostAdDetail.class));
    }

    @Test
    public void testUpdateEditorDoesNotPersistDraftForDisabledExperiment() {
        // user logged in & editor in create mode
        AdvertEditor editor = mock(AdvertEditor.class);
        when(editor.getAdvertDetail()).thenReturn(createDetail(null));

        // when
        workspace.updateEditor("any-id", editor);

        // then
        verify(draftAdvertService, never()).persist(any(PostAdDetail.class));
    }

    @Test
    public void testUpdateEditorClearsDraftStorage() {
        // user logged in & editor in create mode
        when(sessionDataService.getPostAdData(anyString())).thenReturn(createDetail(null));

        // when
        workspace.removeEditor("any-id", true);

        // then
        verify(draftAdvertService).clear();
    }

    @Test
    public void testUpdateEditorDoesNotClearDraftStorageInEditionMode() {
        // user logged in & editor in edition mode
        when(sessionDataService.getPostAdData(anyString())).thenReturn(createDetail(123L));

        // when
        workspace.removeEditor("any-id", false);

        // then
        verify(draftAdvertService, never()).clear();
    }

    @Test
    public void testResetEditorCreatesNewOneForGivenId() {
        // create editor | setPostAdDate first time
        AdvertEditor editor = workspace.createAndPersistEditor(null, null, REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);

        // when | setPostAdDate second time
        AdvertEditor editorAfterReset = workspace.resetEditor(editor.getEditorId(), REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE);

        // then
        assertNotSame(editor, editorAfterReset);

        // and
        verify(sessionDataService, times(2)).setPostAdData(eq(editor.getEditorId()), any(PostAdDetail.class));
    }

    @Test
    public void adAttributesArePreLoadedIfVrmIsProvidedAndVrnIsFound() {
        when(categoryModel.isSupportedAttribute(CategoryConstants.Attribute.VRN.getName(), CategoryConstants.CARS_ID)).thenReturn(true);
        when(motorsApiClient.lookupVehicleData("YY63XEZ", CategoryConstants.CARS_ID))
                .thenReturn(java.util.Optional.of(new StandardisedVehicleDataResponse()
                        .attributes(Lists.newArrayList(new VehicleAttribute().name(CategoryConstants.Attribute.VEHICLE_ESTIMATED_MILEAGE.getName()).value("15000")))));

        // when
        AdvertEditor editor = workspace.createAndPersistEditor(null, CategoryConstants.CARS_ID, java.util.Optional.of("YY63XEZ"),
                REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE, false);

        // then
        verify(userSession).validate();
        verify(motorsApiClient).lookupVehicleData("YY63XEZ", CategoryConstants.CARS_ID);
        verifyZeroInteractions(bushfireApi);

        // and
        assertThat(editor.getAdvertDetail().isVrnWidgetFlow()).isTrue();

        // and
        assertThat(editor.getPostAdFormBean().getAttributes().get(CategoryConstants.Attribute.VEHICLE_ESTIMATED_MILEAGE.getName())).isEqualTo("15000");
        assertThat(editor.getPostAdFormBean().getAttributes().get(CategoryConstants.Attribute.VEHICLE_MILEAGE.getName())).isEqualTo("15000");
    }

    @Test
    public void adAttributesAreNotPreLoadedIfVrmIsProvidedButVrnIsNotFound() {
        when(categoryModel.isSupportedAttribute(CategoryConstants.Attribute.VRN.getName(), CategoryConstants.CARS_ID)).thenReturn(true);
        when(motorsApiClient.lookupVehicleData("YY63XEZ", CategoryConstants.CARS_ID)).thenReturn(java.util.Optional.empty());

        // when
        AdvertEditor editor = workspace.createAndPersistEditor(null, CategoryConstants.CARS_ID, java.util.Optional.of("YY63XEZ"),
                REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE, false);

        // then
        verify(userSession).validate();
        verify(motorsApiClient).lookupVehicleData("YY63XEZ", CategoryConstants.CARS_ID);
        verifyZeroInteractions(bushfireApi);

        // and
        assertThat(editor.getAdvertDetail().isVrnWidgetFlow()).isFalse();

    }

    @Test
    public void adAttributesAreNotPreLoadedIfVrmIsProvidedButNotSupportedByCategory() {
        when(categoryModel.isSupportedAttribute(CategoryConstants.Attribute.VRN.getName(), CategoryConstants.CARS_ID)).thenReturn(false);

        // when
        AdvertEditor editor = workspace.createAndPersistEditor(null, CategoryConstants.CARS_ID, java.util.Optional.of("YY63XEZ"),
                REMOTE_IP, PERM_COOKIE, THREAT_METRIX_COOKIE, false);

        // then
        verify(userSession).validate();
        verifyZeroInteractions(motorsApiClient);
        verifyZeroInteractions(bushfireApi);

        // and
        assertThat(editor.getAdvertDetail().isVrnWidgetFlow()).isFalse();
    }

    private PostAdDetail createDetail(Long advertId) {
        PostAdDetail detail = new PostAdDetail();
        detail.setAdvertId(advertId);
        return detail;
    }
}
