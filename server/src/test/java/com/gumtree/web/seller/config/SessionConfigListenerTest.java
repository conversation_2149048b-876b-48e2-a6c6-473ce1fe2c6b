package com.gumtree.web.seller.config;

import com.gumtree.config.session.SessionConfigListener;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.SessionCookieConfig;

import static com.gumtree.config.session.SessionConfigListener.DOMAIN_PROD_PLACEHOLDER;
import static com.gumtree.config.session.SessionConfigListener.SESSION_COOKIE_NAME;
import static com.gumtree.config.session.SessionConfigListener.SESSION_COOKIE_PATH;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class SessionConfigListenerTest {

    private static final String TEST_ENV_DOMAIN = "staging.gumtree.com";
    private static final String TEST_SESSION_COOKIE_NAME = "testSessionCookie";

    @Mock
    private ServletContextEvent servletContextEvent;

    @Mock
    private ServletContext servletContext;

    @Mock
    private SessionCookieConfig sessionCookieConfig;

    @InjectMocks
    private SessionConfigListener sessionConfigListener;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(servletContextEvent.getServletContext()).thenReturn(servletContext);
        when(servletContext.getSessionCookieConfig()).thenReturn(sessionCookieConfig);
        when(servletContext.getInitParameter(SESSION_COOKIE_NAME)).thenReturn(TEST_SESSION_COOKIE_NAME);
    }

    @Test
    public void contextInitialized_withEnvVariable_setsSessionCookieConfig() {
        //given
        sessionConfigListener = new SessionConfigListener() {
            @Override
            protected String getDomainFromEnv() {
                return TEST_ENV_DOMAIN;
            }
        };

        //when
        sessionConfigListener.contextInitialized(servletContextEvent);

        //then
        verify(sessionCookieConfig).setName(TEST_SESSION_COOKIE_NAME);
        verify(sessionCookieConfig).setDomain(TEST_ENV_DOMAIN);
        verify(sessionCookieConfig).setPath(SESSION_COOKIE_PATH);
        verify(sessionCookieConfig).setHttpOnly(true);
        verify(sessionCookieConfig).setSecure(true);
    }

    @Test
    public void contextInitialized_withoutEnvVariable_setsDefaultDomain() {
        //given
        sessionConfigListener = new SessionConfigListener() {
            @Override
            protected String getDomainFromEnv() {
                return null;
            }
        };

        //when
        sessionConfigListener.contextInitialized(servletContextEvent);

        //then
        verify(sessionCookieConfig).setName(TEST_SESSION_COOKIE_NAME);
        verify(sessionCookieConfig).setDomain(DOMAIN_PROD_PLACEHOLDER);
        verify(sessionCookieConfig).setPath(SESSION_COOKIE_PATH);
        verify(sessionCookieConfig).setHttpOnly(true);
        verify(sessionCookieConfig).setSecure(true);
    }

    @Test
    public void contextDestroyed_doesNothing() {
        sessionConfigListener.contextDestroyed(servletContextEvent);
    }
}
