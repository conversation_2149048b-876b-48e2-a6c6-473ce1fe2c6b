package com.gumtree.web.seller.service.image;

import com.gumtree.api.BushfireApi<PERSON>ey;
import com.gumtree.api.Image;
import com.gumtree.api.Images;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.config.SellerProperty;
import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.api.PostImageApiCall;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.junit.Before;
import org.junit.Test;
import org.springframework.http.HttpStatus;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import rx.Single;

import java.io.File;
import java.util.Collections;
import java.util.Random;

import static com.gumtree.web.seller.service.image.ImageUploadServiceImpl.VALIDATION_ERROR_CODE;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static util.RxAssertions.verifyError;

public class ImageUploadServiceImplTest {

    private static final int MAX_FILE_SIZE = 100000;

    private BapiImageGateway bapiImageGateway;
    private MediaProcessorGateway mediaProcessorGateway;
    private ApiCallExecutor apiCallExecutor;
    private UserSession authenticatedUserSession;
    private ImageUploadService imageUploadService;

    @Before
    public void init() {
        GtPropManager.setProperty(SellerProperty.MAX_IMAGE_UPLOAD_SIZE.getPropertyName(), MAX_FILE_SIZE);
        bapiImageGateway = mock(BapiImageGateway.class);
        mediaProcessorGateway = mock(MediaProcessorGateway.class);
        apiCallExecutor = mock(ApiCallExecutor.class);
        authenticatedUserSession = mock(UserSession.class);

        imageUploadService = new ImageUploadServiceImpl(bapiImageGateway, mediaProcessorGateway,
                apiCallExecutor, authenticatedUserSession);
    }

    @Test
    public void testUploadImage() {
        HystrixRequestContext context = HystrixRequestContext.initializeContext();
        DiskFileItem fileItem = mock(DiskFileItem.class);
        CommonsMultipartFile commonsMultipartFile = new CommonsMultipartFile(fileItem);
        File file = mock(File.class);
        when(fileItem.getStoreLocation()).thenReturn(file);
        when(authenticatedUserSession.getApiKey()).thenReturn(new BushfireApiKey("accessKey","privateKey"));
        when(mediaProcessorGateway.post(any())).thenReturn(1L);
        Image image = new Image();
        when(bapiImageGateway.get(1L)).thenReturn(image);
        Image response = imageUploadService.postImage(commonsMultipartFile);
        assertThat(response.getId()).isEqualTo(image.getId());
        context.shutdown();
    }

    @Test
    public void shouldBeAbleToRxUploadImage() {
        //        Given
        MultipartFile imageFile = givenFileSizeIsNotTooBig();
        Image image = givenBapiUploadReturnsAnImage();

        //        When
        Image response = imageUploadService.uploadImageRX(imageFile).toBlocking().value();

        //        Then
        assertThat(response.getId()).isEqualTo(image.getId());
    }

    @Test
    public void shouldReturnValidationErrorIfImageIsTooLarge() {
        //        Given
        MultipartFile imageFile = givenFileSizeIsTooBig();

        //        When
        Single<Image> response = imageUploadService.uploadImageRX(imageFile);

        //        Then
        verifyError(response, (error) -> {
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            WebApiErrorResponse webApiErrorResponse = ((WebApiErrorException) error).getError();
            assertThat(webApiErrorResponse.getStatus()).isEqualTo(HttpStatus.BAD_REQUEST);
            assertThat(webApiErrorResponse.getCode()).isEqualTo(VALIDATION_ERROR_CODE);
        });
    }

    @Test
    public void shouldReturnInternalErrorIfExceptionThrownWhileBapiCall() {
        //        Given
        MultipartFile imageFile = givenFileSizeIsNotTooBig();
        when(apiCallExecutor.call(any(PostImageApiCall.class))).thenThrow(new RuntimeException());


        //        When
        Single<Image> response = imageUploadService.uploadImageRX(imageFile);

        //        Then
        internalErrorReturned(response);
    }

    @Test
    public void shouldReturnInternalErrorIfBapiCallReturnNoImage() {
        //        Given
        MultipartFile imageFile = givenFileSizeIsNotTooBig();
        givenBapiUploadReturnsNoImage();


        //        When
        Single<Image> response = imageUploadService.uploadImageRX(imageFile);

        //        Then
        internalErrorReturned(response);
    }

    @Test
    public void shouldReturnInternalErrorIfBapiCallFails() {
        //        Given
        MultipartFile imageFile = givenFileSizeIsNotTooBig();
        givenBapiUploadReturnsAnError();

        //        When
        Single<Image> response = imageUploadService.uploadImageRX(imageFile);

        //        Then
        internalErrorReturned(response);
    }

    private Image givenBapiUploadReturnsAnImage() {
        ApiCallResponse<Images> apiCallResponse = (ApiCallResponse<Images>) mock(ApiCallResponse.class);
        when(apiCallExecutor.call(any(PostImageApiCall.class))).thenReturn(apiCallResponse);
        Images images = new Images();
        Image image = new Image();
        image.setId(new Random().nextLong());
        images.setImages(Collections.singletonList(image));
        when(apiCallResponse.getResponseObject()).thenReturn(images);
        return image;
    }

    private void givenBapiUploadReturnsNoImage() {
        ApiCallResponse<Images> apiCallResponse = (ApiCallResponse<Images>) mock(ApiCallResponse.class);
        when(apiCallExecutor.call(any(PostImageApiCall.class))).thenReturn(apiCallResponse);
        Images images = new Images();
        when(apiCallResponse.getResponseObject()).thenReturn(images);
    }

    private void givenBapiUploadReturnsAnError() {
        ApiCallResponse<Images> apiCallResponse = (ApiCallResponse<Images>) mock(ApiCallResponse.class);
        when(apiCallExecutor.call(any(PostImageApiCall.class))).thenReturn(apiCallResponse);
        when(apiCallResponse.isErrorResponse()).thenReturn(true);
    }

    private MultipartFile givenFileSizeIsNotTooBig() {
        MultipartFile imageFile = mock(MultipartFile.class);
        when(imageFile.getSize()).thenReturn(99999L);
        return imageFile;
    }

    private MultipartFile givenFileSizeIsTooBig() {
        MultipartFile imageFile = mock(MultipartFile.class);
        when(imageFile.getSize()).thenReturn(Long.valueOf(MAX_FILE_SIZE + 1));
        return imageFile;
    }

    private void internalErrorReturned(Single<Image> response) {
        verifyError(response, (error) -> {
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            WebApiErrorResponse webApiErrorResponse = ((WebApiErrorException) error).getError();
            assertThat(webApiErrorResponse.getStatus()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
            assertThat(webApiErrorResponse.getCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR.toString());
        });
    }
}
