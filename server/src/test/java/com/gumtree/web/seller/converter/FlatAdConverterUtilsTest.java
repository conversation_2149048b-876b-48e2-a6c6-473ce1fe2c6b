package com.gumtree.web.seller.converter;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.SearchAttribute;
import com.gumtree.liveadsearch.model.*;
import com.gumtree.domain.advert.entity.PriceEntity;
import com.gumtree.domain.location.entity.LocationCentroidEntity;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.Video;
import com.gumtree.domain.newattribute.AttributeService;
import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;
import com.gumtree.domain.newattribute.internal.value.BoolValue;
import com.gumtree.domain.newattribute.internal.value.LongValue;
import com.gumtree.domain.user.entity.UserEntity;

import com.gumtree.liveadsearch.model.Feature;

import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.category.CategoryService;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Matchers;

import java.math.BigDecimal;
import java.util.*;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.isIn;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class FlatAdConverterUtilsTest {
    FlatAdConverterUtils flatAdConverterUtils;
    FlatAdConverterTestUtils testUtils;
    private final List<Feature> NO_FEATURES = null;
    private class TestData extends FlatAdConverterTestUtils.FlatAdData {
        Date now = new Date();
        public Long getId() {return 99L;}
        public String getTitle() {return "New BMW";}
        public String getDescription() {return "deal of your life";}
        public String getPrimaryImageUrl() {return "primary_image.jpg";}
        public String getLocalArea() {return "My lovely area";}
        public List<Category> getCategories() {
            List<Category> categories=new ArrayList<>();
            categories.add(new Category().id(1L).primary(true).name("Cars"));
            return categories;
        }

        public List<Location> getLocations() {
            List<Location> locations=new ArrayList<>();
            locations.add(new Location().id(1L).displayName("richmond"));
            locations.add(new Location().id(2L).displayName("Kew"));
            return locations;
        }
        public String getContactEmail() {return "<EMAIL>";}
        public String getStatus() {return "LIVE";}
        public Long getCreatedDate() {return now.getTime();}
        public Long getPublishedDate() {return DateUtils.addMinutes(now, -1).getTime();}
        public Long getLastModifiedDate() {return DateUtils.addMinutes(now, -2).getTime();}
        public List<Feature> getFeatures() { return NO_FEATURES; }
        public Boolean isPaidFor() {return true; }
        public Boolean isVisibleOnMap() {return true; }

        @Override
        public List<String> getAdditionalImageUrls() {
            return null;
        }

        @Override
        public String getYoutubeUrl() {
            return null;
        }
    }

    private FlatAdConverterTestUtils.FlatAdData data(final List<Feature> features) {
        return new TestData() {
            @Override
            public List<Feature> getFeatures() {
                return features;
            }
        };
    }

    @Before
    public void setUp() throws Exception {
        testUtils = new FlatAdConverterTestUtils();
        AttributeService attributeService = mock(AttributeService.class);
        CategoryService categoryService = mock(CategoryService.class);
        AttributeMetadata priceMetadata = getAttributeMetadata("price", AttributeType.CURRENCY);
        when(attributeService.createAttribute(eq(priceMetadata), eq(1250L))).thenReturn(new com.gumtree.domain.newattribute.Attribute() {
            @Override
            public String getType() {
                return "price";
            }

            @Override
            public AttributeValue getValue() {
                try {
                    return LongValue.create(1250, "");
                } catch (InvalidAttributeValueException e) {
                    throw new RuntimeException(e);
                }
            }
        });
        AttributeMetadata propertyCouplesMetadata = getAttributeMetadata("property_couples", AttributeType.STRING);
        when(attributeService.createAttribute(propertyCouplesMetadata, (Object)"Y")).thenReturn(new com.gumtree.domain.newattribute.Attribute() {
            @Override
            public String getType() {
                return "property_type";
            }

            @Override
            public AttributeValue getValue() {
                try {
                    return BoolValue.create("Y");
                } catch (InvalidAttributeValueException e) {
                    throw new RuntimeException(e);
                }
            }
        });
        com.gumtree.api.category.domain.Category category = new com.gumtree.api.category.domain.Category();
        category.setAttributeMetadata(Lists.newArrayList(priceMetadata, propertyCouplesMetadata));
        when(categoryService.getById(Matchers.any(Long.class))).thenReturn(Optional.of(category));


        flatAdConverterUtils = new FlatAdConverterUtils(attributeService, categoryService);
    }

    @Test
    public void testGetPrice() throws Exception {
        FlatAd flatAd = new FlatAd().categories(Collections.singletonList(new Category().primary(true).displayName("property")))
                .attribute(testUtils.setAttributes("price",1250L));
        Map<String, com.gumtree.domain.newattribute.Attribute> map = flatAdConverterUtils.getAdAttributeMap(flatAd);
        BigDecimal price = flatAdConverterUtils.getPrice(map);
        assertThat(price, is(new BigDecimal(12.5)));
    }

    @Test
    public void testGetBooleanAttribute() throws Exception {
        FlatAd flatAd = new FlatAd().categories(Collections.singletonList(new Category().primary(true).displayName("property"))
        ).attribute(testUtils.setAttributes("property_couples","Y"));
        Map<String, com.gumtree.domain.newattribute.Attribute> map = flatAdConverterUtils.getAdAttributeMap(flatAd);
        com.gumtree.domain.newattribute.Attribute booleanProperty = map.get("property_couples");
        assertThat(booleanProperty.getValue().getName(), is("yes"));
    }

    @Test
    public void testGetPrimaryImage() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        Image primaryImage = flatAdConverterUtils.getPrimaryImage(flatAd);
        assertThat(primaryImage.getBaseUrl(), is("primary_image.jpg"));
    }

    @Test
    public void testIsSpotlighted() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        boolean spotlighted = flatAdConverterUtils.isSpotlighted(flatAd);
        assertFalse(spotlighted);
    }


    @Test
    public void testIsSpotlightedTrue() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(testUtils.expirableFeatures(ProductName.HOMEPAGE_SPOTLIGHT)));
        boolean spotlighted = flatAdConverterUtils.isSpotlighted(flatAd);
        assertTrue(spotlighted);
    }

    @Test
    public void testIsUrgent() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        boolean urgent = flatAdConverterUtils.isUrgent(flatAd);
        assertFalse(urgent);
    }

    @Test
    public void testIsUrgentTrue() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(testUtils.expirableFeatures(ProductName.URGENT)));
        boolean urgent = flatAdConverterUtils.isUrgent(flatAd);
        assertTrue(urgent);
    }

    @Test
    public void testIsFeatured() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        boolean urgent = flatAdConverterUtils.isFeatured(flatAd);
        assertFalse(urgent);
    }

    @Test
    public void testIsFeaturedTrue() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(testUtils.expirableFeatures(ProductName.FEATURE_3_DAY)));
        boolean urgent = flatAdConverterUtils.isFeatured(flatAd);
        assertTrue(urgent);
    }


    @Test
    public void testGetVideos() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        flatAd.setYoutubeUrl("new_video_url");
        List<Video> videos = flatAdConverterUtils.getVideos(flatAd);
        assertThat(videos.size(), is(1));
        assertThat(videos.get(0).getUrl(), is("new_video_url"));

    }

    @Test
    public void testGetPriceEntity() throws Exception {
        FlatAd flatAd = new FlatAd().categories(Collections.singletonList(new Category().primary(true).displayName("cars")))
                .attribute(testUtils.setAttributes("price",1250L));
        Map<String, com.gumtree.domain.newattribute.Attribute> map = flatAdConverterUtils.getAdAttributeMap(flatAd);
        PriceEntity priceEntity = flatAdConverterUtils.getPriceEntity(map);
        assertThat(priceEntity.getAmount(), is(new BigDecimal(12.5)));
        assertThat(priceEntity.getCurrency(), is(Currency.getInstance("GBP")));
    }

    @Test
    public void testGetPriceEntityNull() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        Map<String, com.gumtree.domain.newattribute.Attribute> map = flatAdConverterUtils.getAdAttributeMap(flatAd);
        PriceEntity priceEntity = flatAdConverterUtils.getPriceEntity(map);
        assertNull(priceEntity);
    }

    @Test
    public void testGetUserEntityNull() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        UserEntity userEntity = flatAdConverterUtils.getUserEntity(flatAd);
        assertNotNull(userEntity);
        assertThat(userEntity.getEmailAddress(), is("<EMAIL>"));
    }

    @Test
    public void testGetUserEntityNoContactPhone() throws Exception {
        FlatAdConverterTestUtils.FlatAdData data = new TestData(){
            public String getContactEmail() {return "<EMAIL>";}
            public String getContactTelephone() {return null;}
        };
        FlatAd flatAd = testUtils.flatAd(data);
        User user = user();
        flatAd.setCreatedBy(user);
        UserEntity userEntity = flatAdConverterUtils.getUserEntity(flatAd);
        assertNotNull(userEntity);
        assertNotNull(userEntity.getType());
        assertNull(userEntity.getContactTelephone());
        assertThat(userEntity.getEmailAddress(), is("<EMAIL>"));
        assertThat(userEntity.getId(), is("1"));
    }

    @Test
    public void testGetUserEntityNoContactEmail() throws Exception {
        FlatAdConverterTestUtils.FlatAdData data = new TestData(){
            public String getContactEmail() {return null;}
            public String getContactTelephone() {return "12345";}
        };
        FlatAd flatAd = testUtils.flatAd(data);
        User user = user();
        flatAd.setCreatedBy(user);
        UserEntity userEntity = flatAdConverterUtils.getUserEntity(flatAd);
        assertNotNull(userEntity);
        assertNotNull(userEntity.getType());
        assertThat(userEntity.getContactTelephone(), is("12345"));
        assertNull(userEntity.getEmailAddress());
        assertThat(userEntity.getId(), is("1"));
    }

    @Test
    public void testGetUserEntityBothContacts() throws Exception {
        FlatAdConverterTestUtils.FlatAdData data = new TestData(){
            public String getContactEmail() {return "<EMAIL>";}
            public String getContactTelephone() { return "12345"; }
        };
        FlatAd flatAd = testUtils.flatAd(data);
        User user = user();
        flatAd.setCreatedBy(user);
        UserEntity userEntity = flatAdConverterUtils.getUserEntity(flatAd);
        assertNotNull(userEntity);
        assertThat(userEntity.getContactTelephone(), is("12345"));
        assertThat(userEntity.getEmailAddress(), is("<EMAIL>"));
        assertThat(userEntity.getId(), is("1"));
    }

    @Test
    public void testGetUserEntityUrlContact() throws Exception {
        FlatAdConverterTestUtils.FlatAdData data = new TestData(){
            public String getContactEmail() {return "<EMAIL>";}
            public String getContactTelephone() { return "12345"; }
            public String getContactUrl() {return "http://test.gumtree.com"; }
        };
        FlatAd flatAd = testUtils.flatAd(data);
        User user = user();
        flatAd.setCreatedBy(user);
        UserEntity userEntity = flatAdConverterUtils.getUserEntity(flatAd);
        assertNotNull(userEntity);
        assertThat(userEntity.getContactTelephone(), is("12345"));
        assertThat(userEntity.getContactUrl(), is("http://test.gumtree.com"));
        assertThat(userEntity.getEmailAddress(), is("<EMAIL>"));
        assertThat(userEntity.getId(), is("1"));
    }

    @Test
    public void testGetUserEntityWithContactPhone() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        User user = user();
        flatAd.setCreatedBy(user);
        flatAd.setContactTelephone("1111");
        UserEntity userEntity = flatAdConverterUtils.getUserEntity(flatAd);
        assertNotNull(userEntity);
        assertThat(userEntity.getContactTelephone(), is("1111"));
        assertThat(userEntity.getEmailAddress(), is("<EMAIL>"));
        assertThat(userEntity.getId(), is("1"));
    }

    private User user() {
        User user = new User();
        user.setContactTelephone("1234");
        user.setCreatedDate(new Date().getTime());
        user.setEmailAddress("<EMAIL>");
        user.setForename("Elvis");
        user.setId(1L);
        user.setSurname("Presley");
        user.setVerifiedPhoneNumber("12345");
        return user;
    }

    @Test
    public void testGetPointNull() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        LocationCentroidEntity point = flatAdConverterUtils.getPoint(flatAd);
        assertNull(point);
    }

    @Test
    public void testGetPoint() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        flatAd.setCentroid(new GeoLocation().latitude(123d).longitude(456d));
        LocationCentroidEntity point = flatAdConverterUtils.getPoint(flatAd);
        assertNotNull(point);
        assertThat(point.getLatitude(), is(123d));
        assertThat(point.getLongitude(), is(456d));
    }

    @Test
    public void testGetLocationIdsNull() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        flatAd.setLocations(null);
        List<Integer> locationIds = flatAdConverterUtils.getLocationIds(flatAd);
        List<Integer> expected = Lists.newArrayList();
        assertThat(locationIds, is(expected));
    }

    @Test
    public void testGetLocationIds() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        List<Integer> locationIds = flatAdConverterUtils.getLocationIds(flatAd);
        assertThat(1, isIn(locationIds));
        assertThat(2, isIn(locationIds));

    }

    @Test
    public void testGetImagesNull() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        List<Image> images = flatAdConverterUtils.getImages(flatAd);
        assertThat(images, is(Collections.<Image>emptyList()));
    }

    @Test
    public void testGetImages() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(NO_FEATURES));
        flatAd.setAdditionalImageUrls(Lists.newArrayList("image1.png", "image2.png"));
        List<Image> images = flatAdConverterUtils.getImages(flatAd);
        assertThat(images.size(), is(2));
        assertThat(images.get(0).getBaseUrl(), is("image1.png"));
        assertThat(images.get(1).getBaseUrl(), is("image2.png"));
    }

    @Test
    public void testGetWebsiteUrl() throws Exception {
        FlatAd flatAd = testUtils.flatAd(data(testUtils.urlFeature("http://blahblah.com")));
        String actual = flatAdConverterUtils.getWebsiteUrl(flatAd);
        assertThat(actual, equalTo("http://blahblah.com"));
    }

    private AttributeMetadata getAttributeMetadata(String name, AttributeType type) {
        AttributeMetadata newMetadata = new AttributeMetadata();
        newMetadata.setName(name);
        newMetadata.setSearchStyle(SearchAttribute.Style.EQ);
        newMetadata.setType(type);
        return newMetadata;
    }
}
