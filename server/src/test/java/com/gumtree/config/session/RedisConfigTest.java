package com.gumtree.config.session;

import com.google.common.collect.Sets;
import com.gumtree.web.storage.RedisTemplate;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;

import java.util.Properties;

import static org.fest.assertions.api.Assertions.assertThat;

// TODO: We cannot unit test the creation of JedisSentinelPool because on creation it will try to connect to them
public class RedisConfigTest {
    private static final String primaryHost = "1nd-host";
    private static final Integer primaryPort = 11111;
    private static final Integer primaryTimeout = 1000;
    private static final String primaryMaster = "1nd-master";
    private static final String primarySentinels = "";

    private static final String secondaryHost = "2nd-host";
    private static final Integer secondaryPort = 22222;
    private static final Integer secondaryTimeout = 1000;
    private static final String secondaryMaster = "2nd-master";
    private static final String secondarySentinels = "";
    private static final Boolean secondaryEnabled = true;

    private static Properties properties;

    @Before
    public void setup() {
        properties = new Properties();

        // primary default setting
        properties.put("gumtree.redis.host", primaryHost);
        properties.put("gumtree.redis.port", primaryPort.toString());
        properties.put("gumtree.redis.timeout", primaryTimeout.toString());
        properties.put("gumtree.redis.master.name", primaryMaster);
        properties.put("gumtree.redis.sentinel.hostList", primarySentinels);

        // secondary default setting
        properties.put("gumtree.redis.secondary.host", secondaryHost);
        properties.put("gumtree.redis.secondary.port", secondaryPort.toString());
        properties.put("gumtree.redis.secondary.timeout", secondaryTimeout.toString());
        properties.put("gumtree.redis.secondary.master.name", secondaryMaster);
        properties.put("gumtree.redis.secondary.sentinel.hostList", secondarySentinels);
        properties.put("gumtree.redis.secondary.enabled", secondaryEnabled.toString());
    }

    @Configuration
    public static class RedisTestConfig {

        @Bean
        public static PropertyPlaceholderConfigurer propertyPlaceholderConfigurer() {
            PropertyPlaceholderConfigurer configurer = new PropertyPlaceholderConfigurer();
            configurer.setProperties(properties);
            return configurer;
        }

        @Bean
        public static MeterRegistry meterRegistry() {
            return new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());
        }
    }

    @Test
    public void shouldReadSettingCorrectly() {
        // when
        AnnotationConfigApplicationContext appContext = new AnnotationConfigApplicationContext(RedisTestConfig.class, RedisConfig.class);

        // then
        RedisClusterSettings.Primary primarySetting = appContext.getBean(RedisClusterSettings.Primary.class);
        assertThat(primarySetting.getHost()).isEqualTo(primaryHost);
        assertThat(primarySetting.getPort()).isEqualTo(primaryPort);
        assertThat(primarySetting.getConnectionTimeout()).isEqualTo(primaryTimeout);
        assertThat(primarySetting.getMasterName()).isEqualTo(primaryMaster);
        assertThat(primarySetting.getSentinels()).isEqualTo(Sets.<String>newHashSet(""));
        assertThat(primarySetting.isEnabled()).isTrue();

        // then
        RedisClusterSettings.Secondary secondarySetting = appContext.getBean(RedisClusterSettings.Secondary.class);
        assertThat(secondarySetting.getHost()).isEqualTo(secondaryHost);
        assertThat(secondarySetting.getPort()).isEqualTo(secondaryPort);
        assertThat(secondarySetting.getConnectionTimeout()).isEqualTo(secondaryTimeout);
        assertThat(secondarySetting.getMasterName()).isEqualTo(secondaryMaster);
        assertThat(secondarySetting.getSentinels()).isEqualTo(Sets.<String>newHashSet(""));
        assertThat(secondarySetting.isEnabled()).isTrue();
    }

    @Test
    public void testJedisPoolUsedWhenSentinelsNotSupplied() {
        // when
        AnnotationConfigApplicationContext appContext = new AnnotationConfigApplicationContext(RedisTestConfig.class, RedisConfig.class);

        // then
        RedisClusterSettings.Primary primarySetting = appContext.getBean(RedisClusterSettings.Primary.class);
        assertThat(primarySetting.isEnabled()).isTrue();

        // then
        RedisClusterSettings.Secondary secondarySetting = appContext.getBean(RedisClusterSettings.Secondary.class);
        assertThat(secondarySetting.isEnabled()).isTrue();

        // then
        RedisTemplate redisTemplate = appContext.getBean(RedisTemplate.class);
        assertThat(redisTemplate.getPrimaryPool()).isInstanceOf(JedisPool.class);
        assertThat(redisTemplate.getSecondaryPool()).isInstanceOf(JedisPool.class);
    }

    @Test
    public void shouldParseSentinels() {
        // given
        properties.put("gumtree.redis.secondary.sentinel.hostList", "host1:port1,host2:port2");
        properties.put("gumtree.redis.secondary.enabled", "false");

        // when
        AnnotationConfigApplicationContext appContext = new AnnotationConfigApplicationContext(RedisTestConfig.class, RedisConfig.class);

        // then
        RedisClusterSettings.Secondary secondarySetting = appContext.getBean(RedisClusterSettings.Secondary.class);
        assertThat(secondarySetting.isEnabled()).isFalse();
        assertThat(secondarySetting.getSentinels()).isEqualTo(Sets.newHashSet("host1:port1", "host2:port2"));

        // then
        RedisTemplate redisTemplate = appContext.getBean(RedisTemplate.class);
        assertThat(redisTemplate.getPrimaryPool()).isInstanceOf(JedisPool.class);
        assertThat(redisTemplate.getSecondaryPool()).isNull();
    }
}
