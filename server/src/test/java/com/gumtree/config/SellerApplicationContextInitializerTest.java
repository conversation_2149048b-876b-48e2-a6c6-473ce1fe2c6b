package com.gumtree.config;

import com.gumtree.config.profiles.CommonConfigProfiles;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.mock.env.MockEnvironment;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.arrayContainingInAnyOrder;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SellerApplicationContextInitializerTest {
    private SellerApplicationContextInitializer initializer;
    @Mock ConfigurableApplicationContext applicationContext;
    @Mock MutablePropertySources mutablePropertySources;
    MockEnvironment environment;

    @Before
    public void before() {
        environment = new MockEnvironment();
        initializer = new SellerApplicationContextInitializer() {
            @Override
            protected void loadSpringProperties(ConfigurableEnvironment configurableEnvironment) {
                // do nothing
            }
        };
    }

    @After
    public void after() {
        environment = null;
        initializer = null;
        applicationContext = null;
    }

    @Test
    public void shouldConfigureActiveProfileCorrectlyIfNotActiveProfilesAreSetExplicitly() {
        // given
        when(applicationContext.getEnvironment()).thenReturn(environment);

        // when
        initializer.initialize(applicationContext);

        // then
        assertThat(environment.getActiveProfiles(), arrayContainingInAnyOrder(new String[]{"bushfire-api", "production-models", "elastic-search", "redis", "category-api"}));
    }

    @Test
    public void shouldNotChangeActiveProfilesIfTheyAreSetAndContainsAllMandatoryProfiles() {
        // given
        when(applicationContext.getEnvironment()).thenReturn(environment);
        String[] activeProfiles = {"bushfire-api", "elastic-search", CommonConfigProfiles.SESSION_PERSISTENCE_STUB};
        environment.setActiveProfiles(activeProfiles);

        // when
        initializer.initialize(applicationContext);

        // then
        assertThat(environment.getActiveProfiles(), arrayContainingInAnyOrder(activeProfiles));
    }

    @Test
    public void shouldAddMandatoryProfilesIfTheyAreNotActivatedImplicitly() {
        // given
        when(applicationContext.getEnvironment()).thenReturn(environment);
        environment.setActiveProfiles("bushfire-api", "elastic-search");

        // when
        initializer.initialize(applicationContext);

        // then
        assertThat(environment.getActiveProfiles(), arrayContainingInAnyOrder(new String[]{"bushfire-api", "elastic-search",
                CommonConfigProfiles.SESSION_PERSISTENCE_REDIS}));
    }
}
