## Buyer

**Dependency score:**
* External: 9
* Internal: 1

**Contract score: 2 / 0** 
(*Service dependencies + 3rd party dependencies / contract based*)

###Contract dependencies

|Dependency     | Internal (1)   | External (1) | Note|
| ------------- |:-------------:| :-----:|:-----|
|internal-api-contracts | - | Y ||
|motors-api-contracts | Y | - ||

###Library dependencies

|Dependency     | Internal (0)   | External (3) | Note|
| ------------- |:-------------:| :-----:|:-----|
|shared-commons | - | Y | Fixed version. *Is it perm*:question:|
|category/read-client | - | Y | Fixed version. *Is it perm*:question:|
|metrics/metrics-core | - | Y | Fixed version. *Is it perm*:question:|
|esearch api client | - | Y | |
|zeno | - | Y | |
|search index config | - | Y | |

###Service dependencies

|Dependency     | Internal (0)   | External (1) | Integration | Note|
| ------------- |:-------------:| :-----:|:-----:|:-----|
|category-api | - | Y | java client  | *we don't have full contract definition for category-api*:exclamation:|
|cv store | - | Y | java client  | *we don't have full contract definition *:exclamation:|
|stats | - | Y | java client  | *we don't have full contract definition *:exclamation:|
|gumshield api | - | Y | java client  | *we don't have full contract definition *:exclamation:|
|bapi | - | Y | java client  | *we don't have full contract definition *:exclamation:|
|user service | - | Y | java client  | *we don't have full contract definition *:exclamation:|

###3rd party dependencies

|Dependency     | Integration | Note|
| ------------- | :-----:|:-----|

###Supporting infrastructure dependencies
|Dependency     |  Note|
| ------------- | -----|
|puppet | - |
|gtprops-dev | - | 
|gtprops-so | - |
|influxdb | - |
|grafana | - |
|sentry | - |
|consul | - |
|deployer | - |
|jenkins-jobs | - |
|logstash | - |
|shared jetty | - |

