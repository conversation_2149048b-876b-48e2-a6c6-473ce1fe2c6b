global
    log 127.0.0.1 local0
    log 127.0.0.1 local1 notice
    chroot /var/lib/haproxy
    user haproxy
    group haproxy
    # daemon

defaults
    log global
    mode http
    option httplog
    option dontlognull
    timeout connect 5000ms
    timeout client 50000s
    timeout server 50000s
    errorfile 400 /etc/haproxy/errors/400.http
    errorfile 403 /etc/haproxy/errors/403.http
    errorfile 408 /etc/haproxy/errors/408.http
    errorfile 500 /etc/haproxy/errors/500.http
    errorfile 502 /etc/haproxy/errors/502.http
    errorfile 503 /etc/haproxy/errors/503.http
    errorfile 504 /etc/haproxy/errors/504.http

listen stats1 :8082
    stats enable
    stats uri /

listen stats2 :1082
    stats enable
    stats uri /

frontend seller
    bind 0.0.0.0:1081
    acl mweb path_beg -i /ajax/location/
    acl mweb path_beg -i /ajax/category/
    acl mweb path_beg -i /ajax/suggestions/

    use_backend srvs_qa_mweb if mweb
    default_backend srvs_qa_seller
    #use_backend srvs_loc_mweb if mweb
    #default_backend srvs_loc_seller

frontend mweb
    bind 0.0.0.0:1080

    default_backend srvs_qa_mweb
    #default_backend srvs_loc_mweb

backend srvs_loc_mweb
    balance roundrobin
    server loc_mweb docker_host:8080

backend srvs_qa_mweb
    balance roundrobin
    rspirep ^Location:\s*https?://www\.qa[0-9].gumtree.com(/.*)$ Location:\ http://dev.gumtree.com:1080\1
    rspirep ^Location:\s*https?://my\.qa[0-9].gumtree.com(/.*)$ Location:\ http://dev.gumtree.com:1081\1
    rspirep ^Set-Cookie:\s*(.*)\ Domain=\s*qa[0-9].gumtree.com(.*) Set-Cookie:\ \1\ Domain=dev.gumtree.com\2
    #modify following two lines to use another qa env
    reqirep ^Host: Host:\ www.qa3.gumtree.com
    server qa3_mweb www.qa3.gumtree.com:80

backend srvs_loc_seller
    balance roundrobin
    server loc_seller docker_host:8081

backend srvs_qa_seller
    balance roundrobin
    rspirep ^Location:\s*https?://www\.qa[0-9].gumtree.com(/.*)$ Location:\ http://dev.gumtree.com:1080\1
    rspirep ^Location:\s*https?://my\.qa[0-9].gumtree.com(/.*)$ Location:\ http://dev.gumtree.com:1081\1
    rspirep ^Set-Cookie:\s*(.*)\ Domain=\s*qa[0-9].gumtree.com(.*) Set-Cookie:\ \1\ Domain=dev.gumtree.com\2
    #modify following two lines to use another qa env
    reqirep ^Host: Host:\ my.qa3.gumtree.com
    server qa3_seller my.qa3.gumtree.com:443 ssl verify none

